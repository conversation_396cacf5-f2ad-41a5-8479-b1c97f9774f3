plugins {
    alias(libs.plugins.android.application)
    alias(libs.plugins.jetbrains.kotlin.android)
    alias(libs.plugins.kotlinSerialization)
    id("com.google.dagger.hilt.android")
    id("kotlin-kapt")
    id("com.google.gms.google-services")
}


android {
    namespace = "com.siriustech.merit"
    compileSdk = 34

    defaultConfig {
        applicationId = "com.siriustech.merit"
        minSdk = 24
        targetSdk = 34
        versionCode = 90
        versionName = "1.0"

        testInstrumentationRunner = "androidx.test.runner.AndroidJUnitRunner"
        vectorDrawables {
            useSupportLibrary = true
        }

        setFlavorDimensions(listOf("default"))


        ndk {
            abiFilters.addAll(listOf("armeabi", "armeabi-v7a", "arm64-v8a"))
        }
        manifestPlaceholders["JPUSH_PKGNAME"] = "com.merit_am.merit"
        manifestPlaceholders["JPUSH_APPKEY"] = "d733ba82d878ba46bc9ebf5a"
        manifestPlaceholders["JPUSH_CHANNEL"] = "default_developer"
    }

    sourceSets {
        getByName("main") {
            jniLibs.srcDirs(listOf("libs"))
        }
    }

    compileOptions {
        sourceCompatibility = JavaVersion.VERSION_1_8
        targetCompatibility = JavaVersion.VERSION_1_8
    }
    kotlinOptions {
        jvmTarget = "1.8"
    }
    buildFeatures {
        compose = true
        buildConfig = true
        dataBinding = true
    }
    dataBinding {
        enable = true
    }
    composeOptions {
        kotlinCompilerExtensionVersion = "1.5.1"
    }
    packaging {
        resources {
            excludes += "/META-INF/{AL2.0,LGPL2.1}"
        }
    }
    kapt {
        correctErrorTypes = true
    }

    productFlavors {
        create("uat") {
            applicationId = "com.merit_am.merit"
            buildConfigField("String", "BASE_URL", "\"https://merit-dev.internal.siriustech.io/\"")
            buildConfigField("String", "APP_CENTER_ID", "\"549d429d-e0a6-4af9-9854-a3967384c213\"")
            buildConfigField("String", "FLAVOR", "\"UAT\"")
        }
        create("prod") { // SiriusTech pass/storepass
            applicationId = "com.merit_am.merit"
            buildConfigField("String", "BASE_URL", "\"https://app.merit-am.com/api/\"")
            buildConfigField("String", "APP_CENTER_ID", "\"549d429d-e0a6-4af9-9854-a3967384c213\"")
            buildConfigField("String", "FLAVOR", "\"PROD\"")
        }
        create("uatProd") { // SiriusTech pass/storepass
            applicationId = "com.merit_am.merit"
//            buildConfigField("String", "BASE_URL", "\"https://app-merit-am.siriustech.io/api/\"")
            buildConfigField("String", "BASE_URL", "\"https://app.merit-am.com/api/\"")
            buildConfigField("String", "APP_CENTER_ID", "\"549d429d-e0a6-4af9-9854-a3967384c213\"")
            buildConfigField("String", "FLAVOR", "\"UAT_PROD\"")

        }
    }
    signingConfigs {
        create("release"){
            keyAlias = "Merit"
            keyPassword = "SiriusTech"
            storeFile = file("../production.keystore.key")
            storePassword = "SiriusTech"
        }
    }
    buildTypes {
        release {
            isMinifyEnabled = false
            proguardFiles(
                getDefaultProguardFile("proguard-android-optimize.txt"),
                "proguard-rules.pro"
            )
            signingConfig = signingConfigs.getByName("release")
        }
        debug {

        }
    }
}

dependencies {
    implementation(fileTree(mapOf("dir" to "libs", "include" to listOf("*.jar", "*.aar"))))

    implementation(project(":authentication"))
    implementation(project(":core-ui-compose"))
    implementation(project(":portfolio"))
    implementation(project(":market"))
    implementation(project(":wealthplan"))
    implementation(project(":home"))
    implementation(project(":history"))
    implementation(project(":settings"))
    implementation(project(":onboarding"))
    implementation(project(":app-common"))
    implementation(project(":apiLayer"))
    testImplementation(libs.junit)
    implementation(libs.hilt.android)
    kapt(libs.hilt.compiler)
    implementation(libs.androidx.core.splashscreen)
    implementation(libs.jpush)
    implementation(libs.jcore)
    implementation(libs.appcenter.analytics)
    implementation (libs.appcenter.crashes)
    implementation("ch.acra:acra-mail:5.12.0")
    implementation(platform("com.google.firebase:firebase-bom:33.7.0"))
    implementation("com.google.firebase:firebase-analytics")
}