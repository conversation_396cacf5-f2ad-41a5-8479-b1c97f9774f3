<?xml version="1.0" encoding="utf-8"?>
<resources>

<!--    <style name="Theme.Merit" parent="android:Theme.Material.Light.NoActionBar" />-->
    <style name="Theme.Merit" parent="Theme.MaterialComponents.Light.NoActionBar.Bridge" />

    <style name="Theme.SplashScreen.MySplash" parent="Theme.SplashScreen">
        <item name="windowSplashScreenBackground">@color/bgDefault</item>
        <item name="windowSplashScreenAnimatedIcon">@drawable/splash</item>
        <item name="postSplashScreenTheme">@style/Theme.Merit</item>
    </style>


<!--    jpush-->
    <style name="JPushTheme">
        <item name="android:windowNoTitle">true</item>
        <item name="android:windowActionBar">false</item>
        <item name="android:windowFullscreen">true</item>
        <item name="android:windowContentOverlay">@null</item>
        <item name="android:textAllCaps">false</item>
        <item name="android:windowIsTranslucent">true</item>
        <item name="android:windowBackground">@android:color/transparent</item>
    </style>
    <style name="MyDialogStyle">
        <item name="android:windowBackground">@android:color/transparent</item>
        <item name="android:windowFrame">@null</item>
        <item name="android:windowNoTitle">true</item>
        <item name="android:windowIsFloating">true</item>
        <item name="android:windowIsTranslucent">true</item>
        <item name="android:windowContentOverlay">@null</item>
        <item name="android:windowAnimationStyle">@android:style/Animation.Dialog</item>
        <item name="android:backgroundDimEnabled">true</item>
    </style>

</resources>