package com.siriustech.merit

import android.app.Application
import android.content.Context
import cn.jpush.android.api.JPushInterface
import com.jakewharton.threetenabp.AndroidThreeTen
import com.microsoft.appcenter.AppCenter
import com.microsoft.appcenter.analytics.Analytics
import com.microsoft.appcenter.crashes.Crashes
import com.siriustech.merit.app_common.Constants
import dagger.hilt.android.HiltAndroidApp
import org.acra.config.mailSender
import org.acra.data.StringFormat
import org.acra.ktx.initAcra
import timber.log.Timber


/**
 * Created by He<PERSON> Htet
 */
@HiltAndroidApp
class MeritApp : Application() {
    override fun onCreate() {
        super.onCreate()
        Timber.plant(Timber.DebugTree())
        AndroidThreeTen.init(this)
        JPushInterface.setDebugMode(true)
        JPushInterface.init(this)
        JPushInterface.initCrashHandler(this)
        AppCenter.start(
            this, BuildConfig.APP_CENTER_ID,
            Analytics::class.java, Crashes::class.java
        )
    }


    override fun attachBaseContext(base: Context) {
        super.attachBaseContext(base)
        if(BuildConfig.FLAVOR == Constants.FLAVOR_UAT){
            initAcra {
                buildConfigClass = BuildConfig::class.java
                reportFormat = StringFormat.JSON
                mailSender {
                    //required
                    mailTo = "<EMAIL>"
                    reportAsFile = true
                    reportFileName = "Crash.txt"
                    subject = "Merit application crash report"
                    body = "Please send application crash report"
                    enabled = true
                }
            }
        }
    }

}
