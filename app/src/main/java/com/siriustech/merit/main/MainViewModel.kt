package com.siriustech.merit.main


import com.siriustech.merit.apilayer.service.authentication.updatejpush.UpdateJPushRegIDUseCase
import com.siriustech.merit.apilayer.service.authentication.userinfo.GetUserInfoUseCase
import com.siriustech.merit.apilayer.service.home.notification.NotificationRequest
import com.siriustech.merit.apilayer.service.home.notification.NotificationUseCase
import com.siriustech.merit.apilayer.service.user.userinfo.GetUserBasicInfoUseCase
import com.siriustech.merit.app_common.component.common.NotificationTabType
import com.siriustech.merit.app_common.data.AppCache
import com.siriustech.merit.app_common.data.CommonSharedPreferences
import com.siriustech.merit.app_common.data.display.UserBasicInfoDisplay
import com.siriustech.merit.app_common.ext.displayAppVersioning
import com.siriustech.merit.app_common.theme.AppViewModel
import com.siriustech.merit.app_common.typeenum.UserStatus
import dagger.hilt.android.lifecycle.HiltViewModel
import javax.inject.Inject
import javax.inject.Named
import kotlinx.coroutines.ExperimentalCoroutinesApi
import kotlinx.coroutines.delay
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.catch
import kotlinx.coroutines.flow.collectLatest
import kotlinx.coroutines.flow.flatMapLatest
import kotlinx.coroutines.flow.map
import kotlinx.coroutines.flow.onCompletion
import kotlinx.coroutines.flow.onStart
import kotlinx.coroutines.launch

/**
 * Created by Hein Htet
 */
@HiltViewModel
class MainViewModel @Inject constructor(
    @Named("DeviceID") private val deviceId: String,
    private val commonSharedPreferences: CommonSharedPreferences,
    private val updateJPushRegIDUseCase: UpdateJPushRegIDUseCase,
    private val appCache: AppCache,
    private val getUserBasicInfoUseCase: GetUserBasicInfoUseCase,
    private val getUserInfoUseCase: GetUserInfoUseCase,
    private val getNotificationUseCase: NotificationUseCase,
) : AppViewModel() {

    private val _unreadNotificationCount = MutableStateFlow(0)
    private val _currentLocale = MutableStateFlow(commonSharedPreferences.appLocale)
    val localeChanged = MutableStateFlow(false)
    private var _neededToFetchNotification = true

    inner class MainInputs : BaseInputs() {

        fun onGetUserBasicInfo() = getUserBasicInfo()

        init {
            _currentLocale.value = commonSharedPreferences.appLocale
        }

        fun updateListenNotification(value : Boolean) {
            _neededToFetchNotification = value
            listenNotification()
        }

        fun updateAppVersion(mainActivity: MainActivity) {
            commonSharedPreferences.setAppVersion(mainActivity.displayAppVersioning())
        }
    }

    inner class MainOutputs : BaseOutputs() {
        val isPinLoginEnabled: Boolean
            get() {
                return commonSharedPreferences.loginPinCode.isNotEmpty() && commonSharedPreferences.alreadyPinSetup
            }
        val isLoggedIn : Boolean
            get() {
                return commonSharedPreferences.sessionId.isNotEmpty()
            }
    }


    override val inputs = MainInputs()
    override val outputs = MainOutputs()


    private fun listenNotification() {
        scope.launch {
            while (_neededToFetchNotification) {
                getNotification()
                delay(10_000L)
            }
        }
    }

    private fun stopListenNotification() {
        _neededToFetchNotification = false
    }

    @OptIn(ExperimentalCoroutinesApi::class)
    private fun getUserBasicInfo() {
        if (commonSharedPreferences.sessionId.isNotEmpty() && appCache.userBasicInfoDisplay == null
        ) {
            scope.launch {
                getUserBasicInfoUseCase(param = Unit)
                    .onStart { inputs.emitLoading(true) }
                    .onCompletion { inputs.emitLoading(false) }
                    .catch { }
                    .map {
                        val info = UserBasicInfoDisplay(
                            name = it.fullName.orEmpty(),
                            email = it.email.orEmpty(),
                            mobile = it.mobile.orEmpty(),
                            language = it.language.orEmpty(),
                            profileImage = it.profilePicture.orEmpty()
                        )
                        info
                    }
                    .flatMapLatest { user ->
                        getUserInfoUseCase(param = Unit)
                            .map {
                                user.copy(
                                    userStatus = UserStatus.fromParam(it.accountStatus.orEmpty())
                                )
                            }
                    }
                    .collectLatest {
                        appCache.updateUserBasicInfo(it)
                        onTriggerActions(
                            MainActions.OnLoadUserInfo(
                                appCache.userBasicInfoDisplay ?: UserBasicInfoDisplay()
                            )
                        )
                    }
            }
        }
    }

    private fun getNotification() {
        if (commonSharedPreferences.sessionId.isNotEmpty()) {
            scope.launch {
                getNotificationUseCase(
                    param = NotificationRequest(
                        limit = 0,
                        messageType = NotificationTabType.NOTIFICATIONS.value
                    )
                )
                    .onStart { }
                    .onCompletion { }
                    .catch { }
                    .collectLatest {
                        val unread = it.list.orEmpty().filter { !it.isRead }.size
                        _unreadNotificationCount.value = unread
                        appCache.updateUnReadNotificationCount(unread)
                    }
            }
        }
    }

    fun checkLocale() {
        if (commonSharedPreferences.appLocale != _currentLocale.value) {
            _currentLocale.value = commonSharedPreferences.appLocale
            localeChanged.value = true
        }
    }
}