package com.siriustech.merit.main

import android.Manifest
import android.content.BroadcastReceiver
import android.content.Context
import android.content.Intent
import android.content.IntentFilter
import android.os.Build
import android.os.Bundle
import androidx.activity.compose.setContent
import androidx.activity.result.contract.ActivityResultContracts
import androidx.activity.viewModels
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.material3.Scaffold
import androidx.compose.runtime.Composable
import androidx.compose.runtime.CompositionLocalProvider
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.toArgb
import androidx.compose.ui.tooling.preview.Preview
import androidx.core.content.ContextCompat
import androidx.core.splashscreen.SplashScreen.Companion.installSplashScreen
import androidx.navigation.compose.rememberNavController
import cn.jpush.android.api.JPushInterface
import com.siriustech.core_ui_compose.base.SingleEventEffect
import com.siriustech.core_ui_compose.ext.ChangeSystemBarsTheme
import com.siriustech.core_ui_compose.theme.CoreAppTheme
import com.siriustech.merit.app_common.Constants
import com.siriustech.merit.app_common.Constants.CHANGE_LANGUAGE
import com.siriustech.merit.app_common.data.AppCache
import com.siriustech.merit.app_common.data.CommonSharedPreferences
import com.siriustech.merit.app_common.data.display.UserBasicInfoDisplay
import com.siriustech.merit.app_common.navigation.RouteName
import com.siriustech.merit.app_common.theme.AppComposeActivity
import com.siriustech.merit.app_common.theme.LocalAppColor
import com.siriustech.merit.app_common.theme.getAppThemeConfig
import com.siriustech.merit.navigation.AppNavGraph
import com.siriustech.merit.navigation.LocalNavController
import dagger.hilt.android.AndroidEntryPoint
import javax.inject.Inject
import timber.log.Timber

@AndroidEntryPoint
class MainActivity : AppComposeActivity() {

    private val viewModel: MainViewModel by viewModels()


    @Inject
    lateinit var appCache: AppCache

    @Inject
    lateinit var commonSharedPreferences: CommonSharedPreferences


    private val requestPermissionLauncher = registerForActivityResult(
        ActivityResultContracts.RequestPermission()
    ) {

    }

    private val receiver = object : BroadcastReceiver() {
        override fun onReceive(context: Context?, intent: Intent?) {
            Timber.d("ACTION_403_AUTH")

            val routeType = if (commonSharedPreferences.alreadyPinSetup || commonSharedPreferences.alreadyBiometricSetup) {
                RouteName.PIN_LOGIN
            } else {
                RouteName.LOGIN_ROUTE
            }
            val i = Intent(this@MainActivity, MainActivity::class.java).apply {
                putExtra(EXTRA_ROUTE_TYPE, routeType)
                addFlags(Intent.FLAG_ACTIVITY_CLEAR_TOP)
                addFlags(Intent.FLAG_ACTIVITY_CLEAR_TASK)
            }
            startActivity(i)
            finish()
        }
    }


    override fun onResume() {
        super.onResume()
        viewModel.inputs.updateListenNotification(true)
        ContextCompat.registerReceiver(
            this,
            receiver,
            IntentFilter(Constants.AUTH_403_ACTION),
            ContextCompat.RECEIVER_EXPORTED
        )
        Timber.d("ON_RESUME_MAIN_ACTIVITY")
        viewModel.checkLocale()
    }

    private val languageChangeReceiver = object : BroadcastReceiver() {
        override fun onReceive(p0: Context?, p1: Intent?) {
            Timber.d("LANGUAGE_CHANGED")
            <EMAIL>()
        }
    }

    override fun onDestroy() {
        super.onDestroy()
        unregisterReceiver(languageChangeReceiver)
    }

    override fun onPause() {
        super.onPause()
        unregisterReceiver(receiver)
        viewModel.inputs.updateListenNotification(false)
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        installSplashScreen()
        checkPermission()
        val routeType = intent.getStringExtra(EXTRA_ROUTE_TYPE)
        if (intent.hasExtra(EXTRA_FULL_ACCESS)) {
            intent.getBooleanExtra(EXTRA_FULL_ACCESS, true).also {
                appCache.updateFullAccess(it)
            }
        }
        viewModel.inputs.updateAppVersion(this)
        setContent {
            val navController = rememberNavController()
            ChangeSystemBarsTheme(lightTheme = true, Color.White.toArgb())
            val navHostController = rememberNavController()
            CoreAppTheme(themeConfigs = getAppThemeConfig()) {
                var userInfo by remember {
                    mutableStateOf(UserBasicInfoDisplay())
                }
                SingleEventEffect(sideEffectFlow = viewModel.appAction) {
                    when (it) {
                        is MainActions.OnLoadUserInfo -> {
                            userInfo = it.data
                        }
                    }
                }

                Scaffold(
                    modifier = Modifier.fillMaxSize(),
                    contentColor = LocalAppColor.current.bgDefault,
                    containerColor = LocalAppColor.current.bgDefault,
                ) { _ ->
                    Box(
                        modifier = Modifier
                            .fillMaxSize()
                            .background(LocalAppColor.current.bgDefault)
//                            .padding(0)
                    ) {
                        CompositionLocalProvider(LocalNavController provides navController) {
                            AppNavGraph(
                                navHostController,
                                routeType
                                    ?: if(viewModel.outputs.isLoggedIn) RouteName.DASHBOARD else RouteName.LOGIN_ROUTE
//                                    ?: if (viewModel.outputs.isPinLoginEnabled)
//                                        RouteName.PIN_LOGIN else RouteName.LOGIN_ROUTE,
                            )
                        }
                    }
                }
            }
        }
        Timber.d("JPush RegID ${JPushInterface.getRegistrationID(this)}")
        ContextCompat.registerReceiver(
            this,
            languageChangeReceiver,
            IntentFilter(CHANGE_LANGUAGE),
            ContextCompat.RECEIVER_EXPORTED
        )
    }


    private fun checkPermission() {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU) {
            requestPermissionLauncher.launch(Manifest.permission.POST_NOTIFICATIONS)
        }
    }


    companion object {
        const val EXTRA_ROUTE_TYPE = "EXTRA_ROUTE_TYPE"
        const val EXTRA_FULL_ACCESS = "EXTRA_FULL_ACCESS"
    }
}

@Preview(showBackground = true)
@Composable
fun GreetingPreview() {
    CoreAppTheme(themeConfigs = getAppThemeConfig()) {
        AppNavGraph(rememberNavController())
    }
}