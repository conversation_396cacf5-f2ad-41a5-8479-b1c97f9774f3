package com.siriustech.merit.jpush

import android.content.Context
import cn.jpush.android.api.CustomMessage
import cn.jpush.android.api.NotificationMessage
import cn.jpush.android.service.JPushMessageReceiver
import cn.jpush.android.service.JCommonService
import timber.log.Timber

class MeritJPushReceiver : JPushMessageReceiver() {
    override fun onMessage(context: Context, customMessage: CustomMessage) {
        Timber.tag("JPush").d("Custom message received: %s", customMessage.message)
    }

    override fun onNotifyMessageOpened(context: Context, message: NotificationMessage) {
        Timber.tag("JPush").d("Notification clicked: %s", message.notificationTitle)
        // Handle redirection, e.g., open an Activity
    }


}

class MeritJCommonService : JCommonService() {

}