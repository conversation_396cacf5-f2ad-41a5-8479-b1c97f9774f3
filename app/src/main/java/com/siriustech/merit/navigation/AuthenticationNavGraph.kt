package com.siriustech.merit.navigation

import android.util.Log
import androidx.compose.foundation.layout.Column
import androidx.compose.material3.Text
import androidx.compose.ui.Alignment
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.hilt.navigation.compose.hiltViewModel
import androidx.navigation.NavGraphBuilder
import androidx.navigation.NavHostController
import androidx.navigation.navigation
import androidx.navigation.toRoute
import com.siriustech.merit.app_common.ext.colorTxtParagraph
import com.siriustech.merit.app_common.ext.composableWithTransaction
import com.siriustech.merit.app_common.ext.navigateReplaceAll
import com.siriustech.merit.app_common.navigation.AgentSignUp
import com.siriustech.merit.app_common.navigation.AgentSignupLanding
import com.siriustech.merit.app_common.navigation.Authentication
import com.siriustech.merit.app_common.navigation.CreateNewPin
import com.siriustech.merit.app_common.navigation.CreateNewPinSuccess
import com.siriustech.merit.app_common.navigation.Dashboard
import com.siriustech.merit.app_common.navigation.ForgotPassword
import com.siriustech.merit.app_common.navigation.ForgotPasswordUpdatedSuccess
import com.siriustech.merit.app_common.navigation.ForgotPin
import com.siriustech.merit.app_common.navigation.ForgotPinUpdatedSuccess
import com.siriustech.merit.app_common.navigation.Login
import com.siriustech.merit.app_common.navigation.PinLogin
import com.siriustech.merit.app_common.navigation.RegisterAgentSignupSuccess
import com.siriustech.merit.app_common.navigation.Setup2FA
import com.siriustech.merit.app_common.navigation.SetupPin
import com.siriustech.merit.app_common.navigation.SuccessfullySetup2FA
import com.siriustech.merit.app_common.navigation.Verification2FA
import com.siriustech.merit.app_common.navigation.argument.authentication.AgentSignupLandingArguments
import com.siriustech.merit.app_common.navigation.argument.authentication.Verification2FAArguments
import com.siriustech.merit.app_common.screen.CommonSuccessScreen
import com.siriustech.merit.app_common.screen.CommonSuccessScreenProperties
import com.siriustech.merit.app_common.screen.createpin.CreateNewPinScreen
import com.siriustech.merit.app_common.screen.createpin.CreateNewPinViewModel
import com.siriustech.merit.app_common.screen.forgotpin.ForgotPinScreen
import com.siriustech.merit.app_common.screen.forgotpin.ForgotPinViewModel
import com.siriustech.merit.app_common.screen.setup2FA.Setup2FALandingScreen
import com.siriustech.merit.app_common.screen.setup2FA.Verification2FAScreen
import com.siriustech.merit.app_common.screen.setup2FA.Verification2FAViewModel
import com.siriustech.merit.app_common.screen.setuppin.SetupPinLandingScreen
import com.siriustech.merit.app_common.theme.LocalTypography
import com.siriustech.merit.authentication.AuthenticationRouteName
import com.siriustech.merit.authentication.screen.agentsignup.AgentSignupLandingScreen
import com.siriustech.merit.authentication.screen.agentsignup.AgentSignupScreen
import com.siriustech.merit.authentication.screen.agentsignup.AgentSignupViewModel
import com.siriustech.merit.authentication.screen.forgotpassword.ForgotPasswordScreen
import com.siriustech.merit.authentication.screen.forgotpassword.ForgotPasswordViewModel
import com.siriustech.merit.authentication.screen.login.LoginScreen
import com.siriustech.merit.authentication.screen.login.LoginViewModel
import com.siriustech.merit.authentication.screen.usersignup.landing.SignUpLandingScreen
import com.siriustech.merit.authentication.screen.usersignup.landing.SignupLandingArguments
import com.siriustech.merit.authentication.screen.usersignup.signup.SignupArguments
import com.siriustech.merit.authentication.screen.usersignup.signup.SignupScreen
import com.siriustech.merit.authentication.screen.usersignup.signup.step.identification.page.selfverification.SelfVerificationCameraPage
import com.siriustech.merit.app_common.R as AppCommonR

/**
 * Created by Hein Htet
 */
fun NavGraphBuilder.authNavigation(navHostController: NavHostController) {
    navigation<Authentication>(startDestination = Login) {

        composableWithTransaction<Login> {
            LoginScreen(
                backStackEntry = it,
                viewModel = hiltViewModel<LoginViewModel>(),
                navController = navHostController
            )
        }
        composableWithTransaction<Setup2FA>(
            typeMap = Verification2FAArguments.typeMap
        ) { backStackEntry ->
            val arguments = backStackEntry.toRoute<Verification2FA>().args
            Setup2FALandingScreen(navController = navHostController, arguments)
        }
        composableWithTransaction<Verification2FA>(
            typeMap = Verification2FAArguments.typeMap
        ) { backStackEntry ->
            val arguments = backStackEntry.toRoute<Verification2FA>().args
            Log.d("Verification2FA", "Verification2FA NAV: $arguments")
            Verification2FAScreen(
                viewModel = hiltViewModel<Verification2FAViewModel>(),
                navController = navHostController,
            )
        }
        composableWithTransaction<SuccessfullySetup2FA> {
            CommonSuccessScreen(properties = CommonSuccessScreenProperties(
                title = stringResource(id = AppCommonR.string.key0209),
                iconPainter = painterResource(
                    id = AppCommonR.drawable.ic_2fa_verify_success,
                ),
                secondaryButtonText = stringResource(id = AppCommonR.string.key0211)
            ), onSecondaryButtonClick = {
                navHostController.navigate(SetupPin)
            }) {
                Column {
                    Text(
                        text = stringResource(id = AppCommonR.string.key0210),
                        style = LocalTypography.current.text14.light.colorTxtParagraph()
                    )
//                    Spacer(modifier = Modifier.height(LocalDimens.current.dimen8))
//                    Text(
//                        text = stringResource(id = AppCommonR.string._key0026),
//                        style = LocalTypography.current.text14.light.colorTxtParagraph()
//                    )
                }
            }
        }
        composableWithTransaction<SetupPin> {
            SetupPinLandingScreen(navController = navHostController)
        }
        composableWithTransaction<CreateNewPin> {
            CreateNewPinScreen(
                viewModel = hiltViewModel<CreateNewPinViewModel>(),
                navHostController,
            )
        }
        composableWithTransaction<CreateNewPinSuccess> {
            CommonSuccessScreen(properties = CommonSuccessScreenProperties(
                title = stringResource(id = AppCommonR.string.key0224),
                iconPainter = painterResource(
                    id = AppCommonR.drawable.ic_green_success,
                ),
                secondaryButtonText = stringResource(id = AppCommonR.string.key0211)
            ), onSecondaryButtonClick = {
                navHostController.navigateReplaceAll(Dashboard)
            }) {
                Column(horizontalAlignment = Alignment.Start) {
                    Text(
                        text = stringResource(id = AppCommonR.string.key0225),
                        style = LocalTypography.current.text14.light.colorTxtParagraph()
                    )
                }
            }
        }

        composableWithTransaction<AgentSignupLanding>(
            typeMap = AgentSignupLandingArguments.typeMap
        ) { backStackEntry ->
            val arguments = backStackEntry.toRoute<AgentSignupLanding>().args
            AgentSignupLandingScreen(
                navController = navHostController,
                viewModel = hiltViewModel<AgentSignupViewModel>(),
                arguments = arguments
            )
        }

        composableWithTransaction<AgentSignUp>(
            typeMap = AgentSignupLandingArguments.typeMap
        ) { backStackEntry ->
            val arguments = backStackEntry.toRoute<AgentSignUp>().args
            AgentSignupScreen(
                navController = navHostController,
                viewModel = hiltViewModel<AgentSignupViewModel>(),
                arguments = arguments
            )
        }

        composableWithTransaction<RegisterAgentSignupSuccess> {
            val context = LocalContext.current
            CommonSuccessScreen(properties = CommonSuccessScreenProperties(
                title = stringResource(id = AppCommonR.string.key0224),
                iconPainter = painterResource(
                    id = AppCommonR.drawable.ic_register_success,
                ),
                secondaryButtonText = stringResource(id = AppCommonR.string.key0260)
            ), onSecondaryButtonClick = {
                navHostController.navigateReplaceAll(Dashboard)
            }) {
                Column(horizontalAlignment = Alignment.Start) {
                    Text(
                        text = context.getString(AppCommonR.string.key0188).plus("\n\n")
                            .plus(context.getString(AppCommonR.string.key0189)).plus("\n\n")
                            .plus(context.getString(AppCommonR.string.key0190)),
                        style = LocalTypography.current.text14.light.colorTxtParagraph()
                    )
                }
            }
        }

        composableWithTransaction<AuthenticationRouteName.SignupLanding>(
            typeMap = SignupLandingArguments.typeMap
        ) {
            SignUpLandingScreen(navHostController, viewModel = hiltViewModel())
        }

        composableWithTransaction<AuthenticationRouteName.Signup>(
            typeMap = SignupArguments.typeMap
        ) {backStackEntry ->
            SignupScreen(navHostController, viewModel = hiltViewModel())
        }
//        composableWithTransaction<Dashboard> {
//            TempDashboard()
//        }

        composableWithTransaction<AuthenticationRouteName.SelfVerificationCamera> {
            SelfVerificationCameraPage(navHostController)
        }

        composableWithTransaction<ForgotPin> {
            ForgotPinScreen(
                viewModel = hiltViewModel<ForgotPinViewModel>(),
                navController = navHostController
            )
        }

        composableWithTransaction<ForgotPassword> {
            ForgotPasswordScreen(
                viewModel = hiltViewModel<ForgotPasswordViewModel>(),
                navController = navHostController
            )
        }

        composableWithTransaction<ForgotPinUpdatedSuccess> {
            CommonSuccessScreen(properties = CommonSuccessScreenProperties(
                title = stringResource(id = AppCommonR.string.key0224),
                iconPainter = painterResource(
                    id = AppCommonR.drawable.ic_green_success,
                ),
                secondaryButtonText = stringResource(id = AppCommonR.string.key0421)
            ), onSecondaryButtonClick = {
                navHostController.navigateReplaceAll(PinLogin)
            }) {
                Column(horizontalAlignment = Alignment.Start) {
                    Text(
                        text = stringResource(id = AppCommonR.string.key0419),
                        style = LocalTypography.current.text14.light.colorTxtParagraph()
                    )
                    Text(
                        text = stringResource(id = AppCommonR.string.key0420),
                        style = LocalTypography.current.text14.light.colorTxtParagraph()
                    )
                }
            }
        }

        composableWithTransaction<ForgotPasswordUpdatedSuccess> {
            CommonSuccessScreen(properties = CommonSuccessScreenProperties(
                title = stringResource(id = AppCommonR.string.key0224),
                iconPainter = painterResource(
                    id = AppCommonR.drawable.ic_green_success,
                ),
                secondaryButtonText = stringResource(id = AppCommonR.string.key0421)
            ), onSecondaryButtonClick = {
                navHostController.navigateReplaceAll(Login)
            }) {
                Column(horizontalAlignment = Alignment.Start) {
                    Text(
                        text = stringResource(id = AppCommonR.string.key0261),
                        style = LocalTypography.current.text14.light.colorTxtParagraph()
                    )
                }
            }
        }
    }
}