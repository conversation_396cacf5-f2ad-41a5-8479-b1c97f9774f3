package com.siriustech.merit.navigation

import androidx.navigation.NavGraphBuilder
import androidx.navigation.NavHostController
import androidx.navigation.compose.composable
import androidx.navigation.navigation
import androidx.navigation.toRoute
import com.siriustech.history.HistoryRouteName
import com.siriustech.history.screen.historydetails.HistoryDetailsArguments
import com.siriustech.history.screen.historydetails.HistoryDetailsScreen
import com.siriustech.history.screen.historylist.HistoryScreen
import com.siriustech.merit.app_common.ext.composableWithTransaction

/**
 * Created by <PERSON><PERSON>tet
 */


fun NavGraphBuilder.historyNavigation(
    navHostController: NavHostController,
) {
    navigation<DashboardScreen.History>(startDestination = HistoryRouteName.HistoryLanding) {
        composable<HistoryRouteName.HistoryLanding> { HistoryScreen(navController = navHostController) }
        composableWithTransaction<HistoryRouteName.HistoryDetails>(
            typeMap = HistoryDetailsArguments.typeMap
        ) { backStackEntry ->
            val arguments = backStackEntry.toRoute<HistoryRouteName.HistoryDetails>().args
            HistoryDetailsScreen(navHostController, arguments.historyItemDisplayData)
        }
    }
}