package com.siriustech.merit.navigation.navigator

import android.content.Intent
import androidx.activity.compose.ManagedActivityResultLauncher
import androidx.activity.result.ActivityResult
import androidx.core.app.ActivityOptionsCompat
import androidx.fragment.app.FragmentActivity
import androidx.navigation.NavController
import com.sirius.useronboarding.screen.consentwebview.ConsentWebViewActivity
import com.sirius.useronboarding.screen.steplanding.SignUpLandingActivity
import com.siriustech.merit.app_common.Constants
import com.siriustech.merit.app_common.component.common.bank.BankAccountItemDisplayModel
import com.siriustech.merit.app_common.navigation.ChangePasswordLanding
import com.siriustech.merit.app_common.navigation.ChangePinLanding
import com.siriustech.merit.app_common.navigation.SetupPin
import com.siriustech.merit.app_common.screen.appwebview.AppWebViewActivity
import com.siriustech.merit.app_common.screen.appwebview.AppWebViewArgument
import com.siriustech.merit.app_common.screen.emailverify.CommonEmailOtpVerificationActivity
import com.siriustech.merit.app_common.screen.emailverify.CommonOTPVerificationArguments
import com.siriustech.merit.main.MainActivity
import com.siriustech.merit.shareactivity.NotificationActivity
import com.siriustech.settings.SettingsRouteName
import com.siriustech.settings.navigation.SettingsNavigation
import com.siriustech.settings.screen.editprofile.EditProfileActivity
import com.siriustech.settings.screen.modifybank.ModifyBankArgument
import javax.inject.Inject

/**
 * Created by Hein Htet
 */
class SettingsNavigator @Inject constructor() : SettingsNavigation {
    override fun onNavigateToLoginScreen(activity: FragmentActivity) {
        val i = Intent(activity, MainActivity::class.java).apply {
            addFlags(Intent.FLAG_ACTIVITY_CLEAR_TOP)
            addFlags(Intent.FLAG_ACTIVITY_CLEAR_TASK)
        }
        activity.startActivity(i)
        activity.finish()
    }

    override fun onNavigateToEditProfileScreen(activity: FragmentActivity) {
        val option = ActivityOptionsCompat.makeCustomAnimation(
            activity,
            com.siriustech.merit.app_common.R.anim.slide_in_right,
            com.siriustech.merit.app_common.R.anim.slide_out_right,
        )
        activity.startActivity(
            Intent(
                activity,
                EditProfileActivity::class.java,
            ), option.toBundle()
        )
    }

    override fun onNavigateToBankAccountDetails(navController: NavController) {
        navController.navigate(SettingsRouteName.BankAccountDetails)
    }

    override fun onNavigateToModifyBankAccount(
        navController: NavController,
        bankAccountItemDisplayModel: BankAccountItemDisplayModel?,
    ) {
        navController.navigate(
            SettingsRouteName.ModifyBankScreen(
                ModifyBankArgument(
                    bankAccountItemDisplayModel
                )
            )
        )
    }

    override fun onNavigateToCommonOtpVerification(
        resultCallback: ManagedActivityResultLauncher<Intent, ActivityResult>,
        activity: FragmentActivity,
        args: CommonOTPVerificationArguments,
    ) {

        val option = ActivityOptionsCompat.makeCustomAnimation(
            activity,
            com.siriustech.merit.app_common.R.anim.slide_in_right,
            com.siriustech.merit.app_common.R.anim.slide_out_right,
        )
        resultCallback.launch(
            Intent(
                activity,
                CommonEmailOtpVerificationActivity::class.java
            ).apply {
                putExtra(Constants.KEY_INTENT_EXTRA_COMMON_EMAIL_OTP_ARGUMENT, args)
            }, option
        )
    }

    override fun onNavigateToSecurityScreen(navController: NavController) {
        navController.navigate(SettingsRouteName.SecurityLanding)
    }

    override fun onNavigateToNotificationActivity(activity: FragmentActivity) {
        val option = ActivityOptionsCompat.makeCustomAnimation(
            activity,
            com.siriustech.merit.app_common.R.anim.slide_in_right,
            com.siriustech.merit.app_common.R.anim.slide_out_right,
        )
        activity.startActivity(
            Intent(
                activity,
                NotificationActivity::class.java
            ), option.toBundle()
        )
    }

    override fun onNavigateToSetupPin(navController: NavController) {
        navController.navigate(SetupPin)
    }

    override fun onNavigateToChangePinLanding(navController: NavController) {
        navController.navigate(ChangePinLanding)
    }

    override fun onNavigateToChangePasswordLanding(navController: NavController) {
        navController.navigate(ChangePasswordLanding)
    }

    override fun onNavigateToContinueSignUp(activity: FragmentActivity) {
        activity.startActivity(
            Intent(
                activity,
                SignUpLandingActivity::class.java
            ).apply {
                putExtra(
                    SignUpLandingActivity.EXTRA_IS_FROM_LIMIT_FLOW,
                    true
                )
            }
        )
    }

    override fun onNavigateToAppWebView(activity: FragmentActivity, argument: AppWebViewArgument) {
        val option = ActivityOptionsCompat.makeCustomAnimation(
            activity,
            com.siriustech.merit.app_common.R.anim.slide_in_right,
            com.siriustech.merit.app_common.R.anim.slide_out_right,
        )
        activity.startActivity(
            Intent(
                activity,
                AppWebViewActivity::class.java
            ).apply {
                putExtra(
                    ConsentWebViewActivity.EXTRA_WEB_VIEW_ARGUMENT,
                    argument
                )
            },option.toBundle()
        )
    }
}