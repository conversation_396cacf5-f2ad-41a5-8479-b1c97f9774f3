package com.siriustech.merit.navigation

import android.content.Context
import android.content.Intent
import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.RowScope
import androidx.compose.foundation.layout.WindowInsets
import androidx.compose.foundation.layout.defaultMinSize
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.heightIn
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.layout.windowInsetsPadding
import androidx.compose.foundation.selection.selectableGroup
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.NavigationBarDefaults
import androidx.compose.material3.NavigationBarItem
import androidx.compose.material3.NavigationBarItemDefaults
import androidx.compose.material3.Surface
import androidx.compose.material3.Text
import androidx.compose.material3.contentColorFor
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableIntStateOf
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.ColorFilter
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.Dp
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.navigation.NavController
import androidx.navigation.NavGraph.Companion.findStartDestination
import androidx.navigation.NavHostController
import androidx.navigation.compose.NavHost
import androidx.navigation.compose.composable
import androidx.navigation.compose.currentBackStackEntryAsState
import androidx.navigation.compose.rememberNavController
import androidx.navigation.toRoute
import com.sirius.useronboarding.screen.steplanding.SignUpLandingActivity
import com.siriustech.core_ui_compose.ext.topStroke
import com.siriustech.history.HistoryRouteName
import com.siriustech.home.HomeRouteName
import com.siriustech.home.screen.notification.NotificationScreen
import com.siriustech.market.MarketRouteName
import com.siriustech.market.screen.marketprofile.MarketProfileScreen
import com.siriustech.market.screen.searchinstrument.SearchInstrumentScreen
import com.siriustech.merit.R
import com.siriustech.merit.app_common.component.container.PaddingTop
import com.siriustech.merit.app_common.component.modalbts.LimitedAccessBottomSheet
import com.siriustech.merit.app_common.ext.colorTxtTitle
import com.siriustech.merit.app_common.ext.composableWithTransaction
import com.siriustech.merit.app_common.ext.noRippleClickable
import com.siriustech.merit.app_common.navigation.InstrumentSearch
import com.siriustech.merit.app_common.navigation.MarketProfile
import com.siriustech.merit.app_common.navigation.NotificationListing
import com.siriustech.merit.app_common.navigation.argument.market.MarketProfileArgument
import com.siriustech.merit.app_common.theme.LocalAppColor
import com.siriustech.merit.app_common.theme.LocalDimens
import com.siriustech.merit.app_common.theme.LocalTypography
import com.siriustech.merit.app_common.typeenum.UserStatus
import com.siriustech.merit.app_common.typeenum.UserStatus.Companion.isLimitedAccess
import com.siriustech.portfolio.PortfolioRouteName
import com.siriustech.wealthplan.WealthPlanRouteName
import kotlinx.serialization.Serializable
import timber.log.Timber
import com.siriustech.merit.app_common.R as AppCommonR


/**
 * Created by Hein Htet
 */


@Composable
fun CustomNavigationBar(
    modifier: Modifier = Modifier,
    containerColor: Color = NavigationBarDefaults.containerColor,
    contentColor: Color = MaterialTheme.colorScheme.contentColorFor(containerColor),
    tonalElevation: Dp = NavigationBarDefaults.Elevation,
    windowInsets: WindowInsets = NavigationBarDefaults.windowInsets,
    content: @Composable RowScope.() -> Unit,
) {
    Surface(
        color = LocalAppColor.current.bgDefault,
        contentColor = contentColor,
        tonalElevation = tonalElevation,
        modifier = modifier
    ) {
        Row(
            modifier =
            Modifier
                .fillMaxWidth()
                .windowInsetsPadding(windowInsets)
                .defaultMinSize(minHeight = 64.dp)
                .selectableGroup(),
            verticalAlignment = Alignment.CenterVertically,
            content = content
        )
    }
}

@Composable
fun DashboardBottomNavigation(
    selectedIndex: Int,
    navController: NavController,
    userStatus: UserStatus,
    onItemSelectedIndex: (Int) -> Unit = {},
) {
    val context = LocalContext.current

    val items = remember {
        listOf(
            DashboardScreen.Home,
            DashboardScreen.Market,
            DashboardScreen.WealthPlan,
            DashboardScreen.Portfolio,
            DashboardScreen.History,
        )
    }

    val titles = remember {
        listOf(
            context.getString(AppCommonR.string.key0399),
            context.getString(AppCommonR.string.key0395),
            context.getString(AppCommonR.string.key0397),
            context.getString(AppCommonR.string.key0396),
            context.getString(AppCommonR.string.key0398),
        )
    }

    val limitedMenu = remember {
        listOf(
            DashboardScreen.WealthPlan,
            DashboardScreen.Portfolio,
            DashboardScreen.History,
        )
    }

    var navigationSelectedItem by remember {
        mutableIntStateOf(selectedIndex)
    }

    LaunchedEffect(selectedIndex) {
        navigationSelectedItem = selectedIndex
    }

    var showResumeOnboardingFlowModal by remember {
        mutableStateOf(false)
    }


    navController.addOnDestinationChangedListener { controller, destination, arguments ->
        Timber.d("DESTINATION_CHANGED ${destination.route}")
        val index = when (destination.route) {
            HomeRouteName.HomeLanding.javaClass.name.replace("$",".") -> 0
            MarketRouteName.MarketAssetListing.javaClass.name.replace("$",".") -> 1
            WealthPlanRouteName.WealthPlanListing.javaClass.name.replace("$",".") -> 2
            PortfolioRouteName.PortfolioListing.javaClass.name.replace("$",".") -> 3
            HistoryRouteName.HistoryLanding.javaClass.name.replace("$",".") -> 3
            else -> selectedIndex
        }
        navigationSelectedItem = index
    }

    CustomNavigationBar(
        containerColor = Color.Transparent,
        modifier = Modifier.fillMaxWidth(),
    ) {
        val currentRoute = currentRoute(navController)?.substringAfterLast(".")
        Timber.d("NavGraph CurrentRoute $currentRoute ")
        items.forEachIndexed { index, item ->
//            val isSelected = currentRoute?.lowercase().orEmpty().contains(item.route.lowercase())
            val isSelected = index == navigationSelectedItem
            Timber.d("Current Route : ${currentRoute} ${item.route} isSelected: $isSelected Index $index SelectedIndex $selectedIndex")
            val color =
                if (isSelected) LocalAppColor.current.txtLabel else LocalAppColor.current.txtInactive
            if (item == DashboardScreen.WealthPlan) {
                WealthPlanMenu(modifier = Modifier.noRippleClickable {
                    if (!userStatus.isLimitedAccess()) {
                        navigationSelectedItem = index
                        onItemSelectedIndex(index)
                        navController.navigate(DashboardScreen.WealthPlan)
                        // navigate to wealth plan screen
//                        if (currentRoute != item.route) {
//                            navigationSelectedItem = index
//                            onItemSelectedIndex(index)
//                            navController.navigate(item) {
//                                popUpTo(navController.graph.findStartDestination().id) {
//                                    saveState = true
//                                }
//                                launchSingleTop = true
//                                restoreState = true
//                            }
//                        }
                    } else {
                        showResumeOnboardingFlowModal = true
                    }
                }, isSelected = isSelected)
            } else {
                NavigationBarItem(
                    colors = NavigationBarItemDefaults.colors(
                        indicatorColor = Color.Transparent,
                    ),
                    modifier = Modifier
                        .align(Alignment.Bottom)
                        .height(LocalDimens.current.dimen56)
                        .topStroke(LocalAppColor.current.bgAccent, LocalDimens.current.dimen4)
                        .weight(1f)
                        .background(LocalAppColor.current.bgDefault),
                    icon = {
                        Image(
                            colorFilter = ColorFilter.tint(color),
                            painter = painterResource(id = item.icon),
                            contentDescription = item.title,
                        )
                    },
                    label = {
                        Text(
                            text = titles[index],
                            style = LocalTypography.current.text12.semiBold.copy(
                                color
                            )
                        )
                    },
                    selected = isSelected,
                    onClick = {
                        if (!userStatus.isLimitedAccess()) {
//                        val isSelected =
//                            currentRoute?.lowercase().orEmpty().contains(item.route.lowercase())
                            navigationSelectedItem = index
                            onItemSelectedIndex(index)
//                        if (currentRoute != item.route) {
                            if (!isSelected) {
                                navController.navigate(item)
                            } else if (index == navigationSelectedItem && !navController.currentDestination?.route.orEmpty()
                                    .contains(item.route)
                            ) {
                                navController.navigate(item)
                            }
                        } else {
                            if (!limitedMenu.contains(item)) {
                                navController.navigate(item)
                                navigationSelectedItem = index
                                onItemSelectedIndex(index)
                            } else {
                                showResumeOnboardingFlowModal = true
                            }
                        }
                    }
                )
            }
        }
    }

    if (showResumeOnboardingFlowModal) {
        LimitedAccessBottomSheet(
            userStatus = userStatus,
            onDismissed = {
                showResumeOnboardingFlowModal = false
            }, onResumeOnboardingStep = {
                context.navigateToContinueSignUp()
            }
        )
    }
}

fun Context.navigateToContinueSignUp() {
    this.startActivity(
        Intent(
            this,
            SignUpLandingActivity::class.java
        ).apply {
            putExtra(
                SignUpLandingActivity.EXTRA_IS_FROM_LIMIT_FLOW,
                true
            )
        }
    )
}

fun NavController.onNavigate(item: Any) {
    this.navigate(item) {
        popUpTo(<EMAIL>().id) {
            saveState = true
        }
        launchSingleTop = true
        restoreState = true
    }
}


@Composable
fun WealthPlanMenu(modifier: Modifier = Modifier, isSelected: Boolean = false) {
    val color =
        if (isSelected) LocalAppColor.current.txtInverted else LocalAppColor.current.txtInactive

    Column(
        modifier = Modifier
            .width(50.dp)
            .heightIn(min = 80.dp),
        verticalArrangement = Arrangement.Center,
        horizontalAlignment = Alignment.CenterHorizontally
    ) {
        Box(
            modifier = Modifier
                .height(LocalDimens.current.dimen44)
                .width(LocalDimens.current.dimen44)
                .clip(RoundedCornerShape(50))
                .background(if (isSelected) LocalAppColor.current.txtLabel else LocalAppColor.current.bgAccent)
                .then(modifier),
            contentAlignment = Alignment.Center
        ) {
            Image(
                colorFilter = ColorFilter.tint(color),
                painter = painterResource(id = AppCommonR.drawable.ic_wealth_plan),
                contentDescription = "Wealth Plan Image Resource"
            )
        }
        PaddingTop(value = LocalDimens.current.dimen2)
        Text(
            maxLines = 2,
            overflow = TextOverflow.Ellipsis,
            text = stringResource(id = AppCommonR.string.key0397),
            textAlign = TextAlign.Center,
            lineHeight = 14.sp,
            style = LocalTypography.current.text10.semiBold.copy(
                color = if (isSelected) LocalAppColor.current.txtLabel else LocalAppColor.current.txtInactive
            )
        )
    }
}

@Composable
fun DashboardNavHost(
    navController: NavHostController,
    modifier: Modifier = Modifier,
    startDestination: Any,
    onSelected: (Int) -> Unit = {},
) {
    NavHost(
        modifier = modifier,
        navController = navController,
        startDestination = startDestination
    ) {
        homeNavigation(navController)
        composable<DashboardScreen.Market> { ProfileScreen() }
        marketNavigation(navController)
        portfolioNavigation(navController)
        wealthPlanNavigation(navController)
        historyNavigation(navController)


        // commonRoute route
        composableWithTransaction<MarketProfile>(
            typeMap = MarketProfileArgument.typeMap
        ) { backStackEntry ->
            val arguments = backStackEntry.toRoute<MarketProfile>().args
            MarketProfileScreen(navController = navController, data = arguments.data)
        }

        composableWithTransaction<NotificationListing> {
            NotificationScreen(
                navController = navController,
                ignorePadding = true,
                onBackPressed = {
                    navController.popBackStack()
                },
            )
        }

        composableWithTransaction<InstrumentSearch> {
            SearchInstrumentScreen(navController = navController)
        }
    }
}


@Composable
fun currentRoute(navController: NavController): String? {
    val navBackStackEntry = navController.currentBackStackEntryAsState().value
    return navBackStackEntry?.destination?.route
}


@Composable
fun ProfileScreen() {
    Box(
        modifier = Modifier
            .fillMaxSize()
            .background(LocalAppColor.current.bgDefault)
    ) {
        Text(
            text = "Coming Soon",
            modifier = Modifier.align(Alignment.Center),
            style = LocalTypography.current.text14.medium.colorTxtTitle()
        )
    }
}

@Serializable
sealed class DashboardScreen(val route: String, val icon: Int, val title: String) {
    @Serializable
    data object Home :
        DashboardScreen("home", R.drawable.ic_nav_home_default, "Home")

    @Serializable
    data object Market : DashboardScreen(
        "market",
        R.drawable.ic_nav_market_default,
        "Market"
    )

    @Serializable
    data object WealthPlan : DashboardScreen(
        "wealthPlan",
        R.drawable.ic_nav_market_default,
        "Wealth Plan"
    )

    @Serializable
    data object Portfolio : DashboardScreen(
        "portfolio",
        R.drawable.ic_nav_portfolio_default,
        "Portfolio"
    )

    @Serializable
    data object History : DashboardScreen(
        "history",
        R.drawable.ic_nav_history_default,
        "History"
    )
}


@Preview
@Composable
fun PreviewDashboardBottomNavigation() {
    DashboardNavHost(
        navController = rememberNavController(),
        startDestination = DashboardScreen.Home
    )
}


@Preview()
@Composable
fun PreviewWealthPlanMenu() {
    WealthPlanMenu()
}