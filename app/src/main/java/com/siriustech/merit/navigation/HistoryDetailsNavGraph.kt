package com.siriustech.merit.navigation


import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.platform.LocalContext
import androidx.fragment.app.FragmentActivity
import androidx.navigation.NavHostController
import androidx.navigation.compose.NavHost
import androidx.navigation.toRoute
import com.siriustech.history.component.HistoryItemDisplayData
import com.siriustech.history.screen.historydetails.HistoryDetailsScreen
import com.siriustech.market.screen.marketprofile.MarketProfileScreen
import com.siriustech.merit.app_common.ext.composableWithTransaction
import com.siriustech.merit.app_common.navigation.MarketProfile
import com.siriustech.merit.app_common.navigation.argument.market.MarketProfileArgument
import com.siriustech.merit.app_common.typeenum.HistoryFilterType
import com.siriustech.merit.shareactivity.HistoryDetailsActivity
import com.siriustech.merit.shareactivity.HistoryDetailsRouteName

@Composable
fun HistoryDetailsNavGraph(modifier: Modifier = Modifier, navController: NavHostController) {
    val activity = LocalContext.current as FragmentActivity
    NavHost(
        modifier = modifier,
        navController = navController,
        startDestination = HistoryDetailsRouteName.HistoryDetailsScreen
    ) {
        composableWithTransaction<HistoryDetailsRouteName.HistoryDetailsScreen> {
            val data =
                activity.intent.getSerializableExtra(HistoryDetailsActivity.EXTRA_HISTORY_ITEM) as? HistoryItemDisplayData?
            HistoryDetailsScreen(
                navController,
                data ?: HistoryItemDisplayData(historyFilterType = HistoryFilterType.TRADE_HISTORY)
            )
        }
        composableWithTransaction<MarketProfile>(
            typeMap = MarketProfileArgument.typeMap
        ) { backStackEntry ->
            val arguments = backStackEntry.toRoute<MarketProfile>().args
            MarketProfileScreen(navController = navController, data = arguments.data)
        }
    }
}