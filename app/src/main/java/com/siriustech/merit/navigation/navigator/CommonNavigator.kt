package com.siriustech.merit.navigation.navigator

import dagger.hilt.EntryPoint
import dagger.hilt.InstallIn
import dagger.hilt.components.SingletonComponent
import javax.inject.Inject

/**
 * Created by <PERSON><PERSON> Htet
 */
class CommonNavigator @Inject constructor() : CommonNavigation {

}


interface CommonNavigation {


}


@EntryPoint
@InstallIn(SingletonComponent::class)
interface CommonNavigationEntryPoint {
    fun commonNavigation(): CommonNavigation
}