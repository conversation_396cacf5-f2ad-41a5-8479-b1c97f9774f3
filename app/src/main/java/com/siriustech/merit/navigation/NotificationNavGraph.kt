package com.siriustech.merit.navigation

import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.platform.LocalContext
import androidx.fragment.app.FragmentActivity
import androidx.navigation.NavHostController
import androidx.navigation.compose.NavHost
import androidx.navigation.toRoute
import com.siriustech.history.screen.historylist.HistoryScreen
import com.siriustech.home.screen.notification.NotificationScreen
import com.siriustech.home.screen.notification.messagedetails.MessageCenterDetailsScreen
import com.siriustech.merit.app_common.ext.composableWithTransaction
import com.siriustech.merit.app_common.navigation.MessageCenterDetails
import com.siriustech.merit.app_common.navigation.NotificationLanding
import com.siriustech.merit.app_common.navigation.OrderHistoryList
import com.siriustech.merit.app_common.navigation.argument.history.OrderHistoryListArgument
import com.siriustech.merit.app_common.navigation.argument.notification.MessageCenterDetailsArgument

/**
 * Created by <PERSON><PERSON>tet
 */
@Composable
fun NotificationNavGraph(modifier: Modifier = Modifier, navController: NavHostController) {
    val context = LocalContext.current
    NavHost(
        modifier = modifier,
        navController = navController,
        startDestination = NotificationLanding
    ) {
        composableWithTransaction<NotificationLanding> {
            NotificationScreen(navController = navController, onBackPressed = {
                (context as FragmentActivity).finish()
            })
        }
        composableWithTransaction<MessageCenterDetails>(
            typeMap = MessageCenterDetailsArgument.typeMap
        ) { backStackEntry ->
            val arguments =
                backStackEntry.toRoute<MessageCenterDetails>().argument
            MessageCenterDetailsScreen(navController = navController, argument = arguments)
        }
        composableWithTransaction<OrderHistoryList>(
            typeMap = OrderHistoryListArgument.typeMap
        ) { backStackEntry ->
            val arguments =
                backStackEntry.toRoute<OrderHistoryList>().argument
            HistoryScreen(
                navController = navController,
                ignorePaddingValue = false,
                argument = arguments,
                isDetailsMode = true
            )
        }
    }
}
