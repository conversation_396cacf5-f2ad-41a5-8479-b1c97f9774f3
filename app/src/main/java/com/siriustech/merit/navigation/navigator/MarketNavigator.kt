package com.siriustech.merit.navigation.navigator

import android.content.Intent
import androidx.activity.compose.ManagedActivityResultLauncher
import androidx.activity.result.ActivityResult
import androidx.core.app.ActivityOptionsCompat
import androidx.fragment.app.FragmentActivity
import androidx.navigation.NavController
import com.sirius.useronboarding.screen.steplanding.SignUpLandingActivity
import com.siriustech.market.navigation.MarketNavigation
import com.siriustech.market.screen.refineasset.RefineAssetListActivity
import com.siriustech.market.screen.refineasset.RefineAssetListArguments
import com.siriustech.market.screen.searchinstrument.SearchMarketInstrumentActivity
import com.siriustech.market.screen.selectcategory.SelectMarketCategoryActivity
import com.siriustech.merit.app_common.navigation.MarketProfile
import com.siriustech.merit.app_common.navigation.argument.market.MarketProfileArgument
import com.siriustech.merit.app_common.screen.chat.ChatActivity
import com.siriustech.merit.app_common.screen.pdfviewer.PDFViewerActivity
import com.siriustech.merit.app_common.screen.pdfviewer.PDFViewerArguments
import com.siriustech.merit.shareactivity.NotificationActivity
import com.siriustech.settings.screen.profile.SettingsActivity
import com.siriustech.wealthplan.screen.createwealthplan.CreateWealthPlanActivity
import javax.inject.Inject

/**
 * Created by Hein Htet
 */
class MarketNavigator @Inject constructor() : MarketNavigation {
    override fun onNavigateToSelectMarketCategoryActivity(
        resultCallback: ManagedActivityResultLauncher<Intent, ActivityResult>,
        activity: FragmentActivity,
        selectedValue : String
    ) {
        val option = ActivityOptionsCompat.makeCustomAnimation(
            activity,
            com.siriustech.merit.app_common.R.anim.slide_in_right,
            com.siriustech.merit.app_common.R.anim.slide_out_right,
        )
        resultCallback.launch(
            Intent(
                activity,
                SelectMarketCategoryActivity::class.java
            ).apply {
                putExtra(SelectMarketCategoryActivity.EXTRA_SELECT_CATEGORY, selectedValue)
            }, option
        )
    }

    override fun onNavigateToInstrumentSearchActivity(activity: FragmentActivity) {
        val option = ActivityOptionsCompat.makeCustomAnimation(
            activity,
            com.siriustech.merit.app_common.R.anim.slide_in_right,
            com.siriustech.merit.app_common.R.anim.slide_out_right,
        )
        activity.startActivity(
            Intent(
                activity,
                SearchMarketInstrumentActivity::class.java,
            ), option.toBundle()
        )
    }

    override fun onNavigateToRefineAssetListActivity(
        activity: FragmentActivity,
        arguments: RefineAssetListArguments?,
        refineAssetListResultCallback: ManagedActivityResultLauncher<Intent, ActivityResult>,
    ) {
        val option = ActivityOptionsCompat.makeCustomAnimation(
            activity,
            com.siriustech.merit.app_common.R.anim.slide_in_right,
            com.siriustech.merit.app_common.R.anim.slide_out_right,
        )
        refineAssetListResultCallback.launch(
            Intent(
                activity,
                RefineAssetListActivity::class.java,
            ).apply {
                putExtra(RefineAssetListActivity.RESULT_EXTRA_REFINE_RESULT, arguments)
            }, option
        )
    }

    override fun onNavigateToMarketProfile(
        navController: NavController,
        args: MarketProfileArgument,
    ) {
        navController.navigate(MarketProfile(args))
    }

    override fun onNavigateToPDFViewerActivity(
        activity: FragmentActivity,
        arguments: PDFViewerArguments,
    ) {
        val option = ActivityOptionsCompat.makeCustomAnimation(
            activity,
            com.siriustech.merit.app_common.R.anim.slide_in_right,
            com.siriustech.merit.app_common.R.anim.slide_out_right,
        )
        activity.startActivity(
            Intent(
                activity,
                PDFViewerActivity::class.java,
            ).apply {
                putExtra(PDFViewerActivity.EXTRA_PDF_VIEWER_ARGUMENT, arguments)
            }, option.toBundle()
        )
    }

    override fun onNavigateToNotification(activity: FragmentActivity) {
        val option = ActivityOptionsCompat.makeCustomAnimation(
            activity,
            com.siriustech.merit.app_common.R.anim.slide_in_right,
            com.siriustech.merit.app_common.R.anim.slide_out_right,
        )
        activity.startActivity(
            Intent(
                activity,
                NotificationActivity::class.java,
            ), option.toBundle()
        )
    }

    override fun onNavigateToSettings(activity: FragmentActivity) {
        val option = ActivityOptionsCompat.makeCustomAnimation(
            activity,
            com.siriustech.merit.app_common.R.anim.slide_in_right,
            com.siriustech.merit.app_common.R.anim.slide_out_right,
        )
        activity.startActivity(
            Intent(
                activity,
                SettingsActivity::class.java,
            ), option.toBundle()
        )
    }

    override fun onNavigateToChatActivity(activity: FragmentActivity) {
        val option = ActivityOptionsCompat.makeCustomAnimation(
            activity,
            com.siriustech.merit.app_common.R.anim.slide_in_right,
            com.siriustech.merit.app_common.R.anim.slide_out_right,
        )
        activity.startActivity(
            Intent(
                activity,
                ChatActivity::class.java,
            ), option.toBundle()
        )
    }

    override fun onNavigateToCreateWealthPlanActivity(
        activity: FragmentActivity,
        createWealthPlanActivityResultCallback: ManagedActivityResultLauncher<Intent, ActivityResult>,
    ) {
        val option = ActivityOptionsCompat.makeCustomAnimation(
            activity,
            com.siriustech.merit.app_common.R.anim.slide_in_right,
            com.siriustech.merit.app_common.R.anim.slide_out_right,
        )
        createWealthPlanActivityResultCallback.launch(
            Intent(
                activity,
                CreateWealthPlanActivity::class.java,
            ), option
        )
    }

    override fun onNavigateToOnboardingSetup(activity: FragmentActivity) {
        activity.startActivity(
            Intent(
                activity,
                SignUpLandingActivity::class.java
            ).apply {
                putExtra(
                    SignUpLandingActivity.EXTRA_IS_FROM_LIMIT_FLOW,
                    true
                )
            }
        )
    }
}