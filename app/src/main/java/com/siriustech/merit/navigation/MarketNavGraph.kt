package com.siriustech.merit.navigation

import androidx.navigation.NavGraphBuilder
import androidx.navigation.NavHostController
import androidx.navigation.compose.composable
import androidx.navigation.navigation
import androidx.navigation.toRoute
import com.siriustech.market.MarketRouteName
import com.siriustech.market.screen.assetListLanding.MarketScreen
import com.siriustech.market.screen.marketprofile.MarketProfileScreen
import com.siriustech.market.screen.searchinstrument.SearchInstrumentScreen
import com.siriustech.merit.app_common.ext.composableWithTransaction
import com.siriustech.merit.app_common.navigation.Verification2FA
import com.siriustech.merit.app_common.navigation.argument.market.MarketProfileArgument

/**
 * Created by He<PERSON> Htet
 */

fun NavGraphBuilder.marketNavigation(
    navHostController: NavHostController,
) {
    navigation<DashboardScreen.Market>(startDestination = MarketRouteName.MarketAssetListing) {
        composable<MarketRouteName.MarketAssetListing> { MarketScreen(navController = navHostController) }
        composableWithTransaction<MarketRouteName.MarketInstrumentSearch> {
            SearchInstrumentScreen(
                navHostController
            )
        }
    }
}