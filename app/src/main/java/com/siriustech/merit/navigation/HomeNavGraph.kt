package com.siriustech.merit.navigation

import androidx.navigation.NavGraphBuilder
import androidx.navigation.NavHostController
import androidx.navigation.compose.composable
import androidx.navigation.navigation
import com.siriustech.home.HomeRouteName
import com.siriustech.home.screen.landing.HomeScreen
import com.siriustech.market.screen.searchinstrument.SearchInstrumentScreen

/**
 * Created by <PERSON><PERSON>
 */


fun NavGraphBuilder.homeNavigation(
    navHostController: NavHostController,
) {
    navigation<DashboardScreen.Home>(startDestination = HomeRouteName.HomeLanding) {
        composable<HomeRouteName.HomeLanding> { HomeScreen(navController = navHostController) }
    }
}