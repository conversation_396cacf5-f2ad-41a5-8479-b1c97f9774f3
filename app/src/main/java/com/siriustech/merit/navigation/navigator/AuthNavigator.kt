package com.siriustech.merit.navigation.navigator

import android.content.Intent
import androidx.activity.compose.ManagedActivityResultLauncher
import androidx.activity.result.ActivityResult
import androidx.core.app.ActivityOptionsCompat
import androidx.fragment.app.FragmentActivity
import androidx.navigation.NavController
import com.sirius.useronboarding.screen.steplanding.SignUpLandingActivity
import com.siriustech.merit.app_common.ext.navigateReplaceAll
import com.siriustech.merit.app_common.navigation.AgentSignUp
import com.siriustech.merit.app_common.navigation.AgentSignupLanding
import com.siriustech.merit.app_common.navigation.Dashboard
import com.siriustech.merit.app_common.navigation.PinLogin
import com.siriustech.merit.app_common.navigation.RegisterAgentSignupSuccess
import com.siriustech.merit.app_common.navigation.Setup2FA
import com.siriustech.merit.app_common.navigation.SetupPin
import com.siriustech.merit.app_common.navigation.Verification2FA
import com.siriustech.merit.app_common.navigation.argument.authentication.AgentSignupLandingArguments
import com.siriustech.merit.app_common.navigation.argument.authentication.Verification2FAArguments
import com.siriustech.merit.app_common.screen.accountclosure.AccountClosureActivity
import com.siriustech.merit.authentication.AuthenticationRouteName
import com.siriustech.merit.authentication.screen.navigation.AuthNavigation
import com.siriustech.merit.authentication.screen.usersignup.landing.SignupLandingArguments
import com.siriustech.merit.authentication.screen.usersignup.signup.SignupArguments
import javax.inject.Inject

/**
 * Created by Hein Htet
 */
class AuthNavigator @Inject constructor() : AuthNavigation {
    override fun onNavigateToVerification2FA(
        navController: NavController,
        args: Verification2FAArguments,
    ) {
        navController.navigate(Verification2FA(args))
    }

    override fun onNavigateToAgentSignupLanding(
        navController: NavController,
        arguments: AgentSignupLandingArguments,
    ) {
        navController.navigate(AgentSignupLanding(arguments))
    }

    override fun onNavigateToAgentSignup(
        navController: NavController,
        arguments: AgentSignupLandingArguments,
    ) {
        navController.navigate(AgentSignUp(arguments))
    }

    override fun onNavigateToDashboard(navController: NavController) {
        navController.navigateReplaceAll(Dashboard)
    }

    override fun onNavigateToSignupSuccess(navController: NavController) {
        navController.navigate(RegisterAgentSignupSuccess)
    }

    override fun onNavigateToSetup2FA(
        navController: NavController,
        args: Verification2FAArguments,
    ) {
        navController.navigate(Setup2FA(args = args))
    }

    override fun onNavigateToPinLogin(navController: NavController) {
        navController.navigate(PinLogin)
    }

    override fun onNavigateToSetupPin(navController: NavController) {
        navController.navigate(SetupPin)
    }

    override fun onNavigateToSignupLanding(
        navController: NavController,
        arguments: SignupLandingArguments,
    ) {
        navController.navigate(AuthenticationRouteName.SignupLanding(arguments))
    }

    override fun onNavigateToSignup(navController: NavController, arguments: SignupArguments) {
        navController.navigate(AuthenticationRouteName.Signup(arguments))
    }

    override fun onNavigateSelfVerificationCamera(navController: NavController) {
        navController.navigate(AuthenticationRouteName.SelfVerificationCamera)
    }

    override fun onNavigateToSignupLandingV1(activity: FragmentActivity) {
        val option = ActivityOptionsCompat.makeCustomAnimation(
            activity,
            com.siriustech.merit.app_common.R.anim.slide_in_right,
            com.siriustech.merit.app_common.R.anim.slide_out_right
        ).toBundle()
        activity.startActivity(Intent(activity, SignUpLandingActivity::class.java).apply {
            putExtra(SignUpLandingActivity.EXTRA_IS_FROM_ONBOARDING_FLOW, true)
        }, option)
    }

    override fun onNavigateToSignupLanding(activity: FragmentActivity) {
        val option = ActivityOptionsCompat.makeCustomAnimation(
            activity,
            com.siriustech.merit.app_common.R.anim.slide_in_right,
            com.siriustech.merit.app_common.R.anim.slide_out_right
        ).toBundle()
        activity.startActivity(Intent(activity, SignUpLandingActivity::class.java).apply {
            putExtra(SignUpLandingActivity.EXTRA_IS_FROM_ONBOARDING_FLOW, true)
        }, option)
    }

    // TODO reopen it later
    override fun onStartDocSignActivity(
        activity: FragmentActivity,
        url: String,
        resultCallback: ManagedActivityResultLauncher<Intent, ActivityResult>,
    ) {
//        Intent(activity, AppWebViewerActivity::class.java).apply {
//            putExtra("EXTRA_PDF_DISPLAY", AppWebViewerDisplay(
//                title =  activity.getString(R.string.key0163),
//                url = url,
//                showDownloadPDFButton = false
//            )
//            )
//        }.also {
//            resultCallback.launch(it, activity.getIntentOption())
//        }
    }

    override fun onNavigateToAccountClosure(activity: FragmentActivity) {
        val option = ActivityOptionsCompat.makeCustomAnimation(
            activity,
            com.siriustech.merit.app_common.R.anim.slide_in_right,
            com.siriustech.merit.app_common.R.anim.slide_out_right
        ).toBundle()
        activity.startActivity(Intent(activity, AccountClosureActivity::class.java).apply {
            this.flags = Intent.FLAG_ACTIVITY_CLEAR_TASK or Intent.FLAG_ACTIVITY_NEW_TASK
        }, option)
        activity.finish()
    }

    override fun onNavigateToContinueOnboardingSignupLanding(activity: FragmentActivity) {
        val option = ActivityOptionsCompat.makeCustomAnimation(
            activity,
            com.siriustech.merit.app_common.R.anim.slide_in_right,
            com.siriustech.merit.app_common.R.anim.slide_out_right
        ).toBundle()
        activity.startActivity(Intent(activity, SignUpLandingActivity::class.java).apply {
            this.flags = Intent.FLAG_ACTIVITY_CLEAR_TASK or Intent.FLAG_ACTIVITY_NEW_TASK
        }, option)
        activity.finish()
    }
}