package com.siriustech.merit.navigation.navigator

import android.content.Intent
import androidx.activity.compose.ManagedActivityResultLauncher
import androidx.activity.result.ActivityResult
import androidx.core.app.ActivityOptionsCompat
import androidx.fragment.app.FragmentActivity
import androidx.navigation.NavController
import com.siriustech.market.screen.searchinstrument.SearchMarketInstrumentActivity
import com.siriustech.merit.app_common.Constants
import com.siriustech.merit.app_common.data.display.MarketAssetDisplayData
import com.siriustech.merit.app_common.navigation.MarketProfile
import com.siriustech.merit.app_common.navigation.argument.market.MarketProfileArgument
import com.siriustech.merit.app_common.screen.chat.ChatActivity
import com.siriustech.merit.app_common.screen.emailverify.CommonEmailOtpVerificationActivity
import com.siriustech.merit.app_common.screen.emailverify.CommonOTPVerificationArguments
import com.siriustech.merit.navigation.DashboardScreen
import com.siriustech.merit.shareactivity.NotificationActivity
import com.siriustech.portfolio.PortfolioRouteName
import com.siriustech.portfolio.navigation.PortfolioNavigation
import com.siriustech.portfolio.screen.detailslist.PortfolioDetailsListingArguments
import com.siriustech.portfolio.screen.gainlosssettings.GainLossChartSettingsActivity
import com.siriustech.portfolio.screen.gainlosssettings.GainLossChartSettingsArgument
import com.siriustech.portfolio.screen.reportgenerate.ReportGenerateActivity
import com.siriustech.portfolio.screen.reportgenerate.landing.ReportGenerateLandingActivity
import com.siriustech.settings.screen.profile.SettingsActivity
import javax.inject.Inject

/**
 * Created by Hein Htet
 */
class PortfolioNavigator @Inject constructor() : PortfolioNavigation {
    override fun onNavigateToPortfolioDetailsListing(
        navController: NavController,
        args: PortfolioDetailsListingArguments,
    ) {
        navController.navigate(PortfolioRouteName.PortfolioDetailsListing(args))
    }

    override fun onNavigateToEmailOtpVerification(
        resultCallback: ManagedActivityResultLauncher<Intent, ActivityResult>,
        activity: FragmentActivity,
        args: CommonOTPVerificationArguments,
    ) {
        val option = ActivityOptionsCompat.makeCustomAnimation(
            activity,
            com.siriustech.merit.app_common.R.anim.slide_in_right,
            com.siriustech.merit.app_common.R.anim.slide_out_right,
        )
        resultCallback.launch(
            Intent(
                activity,
                CommonEmailOtpVerificationActivity::class.java
            ).apply {
                putExtra(Constants.KEY_INTENT_EXTRA_COMMON_EMAIL_OTP_ARGUMENT, args)
            }, option
        )
    }

    override fun onNavigateToInstrumentSearchActivity(activity: FragmentActivity) {
        val option = ActivityOptionsCompat.makeCustomAnimation(
            activity,
            com.siriustech.merit.app_common.R.anim.slide_in_right,
            com.siriustech.merit.app_common.R.anim.slide_out_right,
        )
        activity.startActivity(
            Intent(
                activity,
                SearchMarketInstrumentActivity::class.java,
            ), option.toBundle()
        )
    }

    override fun onNavigateToMarketTab(navController: NavController) {
        navController.navigate(DashboardScreen.Market)
    }

    override fun onNavigateToNotification(activity: FragmentActivity) {
        val option = ActivityOptionsCompat.makeCustomAnimation(
            activity,
            com.siriustech.merit.app_common.R.anim.slide_in_right,
            com.siriustech.merit.app_common.R.anim.slide_out_right,
        )
        activity.startActivity(
            Intent(
                activity,
                NotificationActivity::class.java,
            ), option.toBundle()
        )
    }

    override fun onNavigateToReportGenerateActivity(
        activity: FragmentActivity,
        resultCallback: ManagedActivityResultLauncher<Intent, ActivityResult>,
    ) {
        val option = ActivityOptionsCompat.makeCustomAnimation(
            activity,
            com.siriustech.merit.app_common.R.anim.slide_in_right,
            com.siriustech.merit.app_common.R.anim.slide_out_right,
        )
        resultCallback.launch(
            Intent(
                activity,
                ReportGenerateActivity::class.java,
            ), option
        )
    }

    override fun onNavigateToReportGenerateLandingActivity(
        activity: FragmentActivity,
        resultCallback: ManagedActivityResultLauncher<Intent, ActivityResult>,
    ) {
        val option = ActivityOptionsCompat.makeCustomAnimation(
            activity,
            com.siriustech.merit.app_common.R.anim.slide_in_right,
            com.siriustech.merit.app_common.R.anim.slide_out_right,
        )
        activity.startActivity(
            Intent(
                activity,
                ReportGenerateLandingActivity::class.java,
            ), option.toBundle()
        )
    }

    override fun onNavigateToSettings(activity: FragmentActivity) {
        val option = ActivityOptionsCompat.makeCustomAnimation(
            activity,
            com.siriustech.merit.app_common.R.anim.slide_in_right,
            com.siriustech.merit.app_common.R.anim.slide_out_right,
        )
        activity.startActivity(
            Intent(
                activity,
                SettingsActivity::class.java,
            ), option.toBundle()
        )
    }

    override fun onNavigateToChatActivity(activity: FragmentActivity) {
        val option = ActivityOptionsCompat.makeCustomAnimation(
            activity,
            com.siriustech.merit.app_common.R.anim.slide_in_right,
            com.siriustech.merit.app_common.R.anim.slide_out_right,
        )
        activity.startActivity(
            Intent(
                activity,
                ChatActivity::class.java,
            ), option.toBundle()
        )
    }

    override fun onNavigateToGainLossChartSettingsActivity(
        activity: FragmentActivity,
        argument: GainLossChartSettingsArgument,
        callback: ManagedActivityResultLauncher<Intent, ActivityResult>,
    ) {
        val option = ActivityOptionsCompat.makeCustomAnimation(
            activity,
            com.siriustech.merit.app_common.R.anim.slide_in_right,
            com.siriustech.merit.app_common.R.anim.slide_out_right,
        )
        callback.launch(
            Intent(
                activity,
                GainLossChartSettingsActivity::class.java,
            ).apply {
                putExtra(GainLossChartSettingsActivity.EXTRA_GAIN_LOSS_CHART_SETTINGS, argument)
            }, option
        )
    }

    override fun onNavigateToMarketProfileDetails(
        activity: FragmentActivity,
        navController: NavController,
        marketAssetDisplayData: MarketAssetDisplayData
    ) {
        navController.navigate(MarketProfile(MarketProfileArgument(data = marketAssetDisplayData)))
    }
}