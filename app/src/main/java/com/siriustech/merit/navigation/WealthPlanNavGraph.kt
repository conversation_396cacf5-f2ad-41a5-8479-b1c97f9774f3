package com.siriustech.merit.navigation

import androidx.navigation.NavGraphBuilder
import androidx.navigation.NavHostController
import androidx.navigation.compose.composable
import androidx.navigation.navigation
import com.siriustech.merit.app_common.ext.composableWithTransaction
import com.siriustech.wealthplan.WealthPlanRouteName
import com.siriustech.wealthplan.screen.wealthplandetails.WealthPlanDetailsArguments
import com.siriustech.wealthplan.screen.wealthplandetails.WealthPlanDetailsScreen
import com.siriustech.wealthplan.screen.wealthplanlist.WealthPlanListScreen
import com.siriustech.wealthplan.screen.wealthplanportfoliodetailslist.WealthPlanPortfolioDetailsListing
import com.siriustech.wealthplan.screen.wealthplanportfoliodetailslist.WealthPlanPortfolioDetailsListingArguments

/**
 * Created by Hein Htet
 */


fun NavGraphBuilder.wealthPlanNavigation(
    navHostController: NavHostController,
) {
    navigation<DashboardScreen.WealthPlan>(startDestination = WealthPlanRouteName.WealthPlanListing) {
        composable<WealthPlanRouteName.WealthPlanListing> { WealthPlanListScreen(navController = navHostController) }
        composableWithTransaction<WealthPlanRouteName.WealthPlanDetails>(
            typeMap = WealthPlanDetailsArguments.typeMap
        ) {
            WealthPlanDetailsScreen(navHostController)
        }
        composableWithTransaction<WealthPlanRouteName.WealthPlanPortfolioDetailsListing>(
            typeMap = WealthPlanPortfolioDetailsListingArguments.typeMap
        ) {
            WealthPlanPortfolioDetailsListing(navHostController)
        }
    }
}