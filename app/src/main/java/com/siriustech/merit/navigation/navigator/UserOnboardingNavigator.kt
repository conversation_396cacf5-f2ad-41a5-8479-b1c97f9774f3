package com.siriustech.merit.navigation.navigator

import android.app.Activity
import android.content.Intent
import androidx.activity.result.ActivityResultLauncher
import androidx.core.app.ActivityOptionsCompat
import androidx.fragment.app.FragmentActivity
import com.sirius.useronboarding.navigation.UserOnboardingNavigation
import com.sirius.useronboarding.screen.register.UserRegistrationActivity
import com.sirius.useronboarding.screen.register.base.FaceScanRecordActivity
import com.sirius.useronboarding.screen.register.step.riskassessment.RiskAssessmentProgressActivity
import com.sirius.useronboarding.screen.register.step.signdocument.SignDocumentActivity
import com.sirius.useronboarding.screen.register.success.SuccessInformationActivity
import com.sirius.useronboarding.screen.register.success.SuccessMessagePageDisplayModel
import com.sirius.useronboarding.screen.steplanding.SignUpLandingActivity
import com.siriustech.merit.app_common.navigation.RouteName
import com.siriustech.merit.main.MainActivity
import com.siriustech.merit.navigation.getIntentOption
import javax.inject.Inject

/**
 * Created by He<PERSON> Htet
 */
class UserOnboardingNavigator @Inject constructor() : UserOnboardingNavigation {
    override fun onStartUserRegistrationActivity(fragmentActivity: FragmentActivity, step: Int?) {
        Intent(fragmentActivity, UserRegistrationActivity::class.java).also {
            it.putExtra(UserRegistrationActivity.EXTRA_INIT_STEP, step)
            fragmentActivity.startActivity(it, fragmentActivity.getIntentOption().toBundle())
        }
    }

    override fun onNavigateToLoginActivity(fragmentActivity: FragmentActivity) {
        val intent = Intent(fragmentActivity, MainActivity::class.java).apply {
            flags = Intent.FLAG_ACTIVITY_CLEAR_TASK or Intent.FLAG_ACTIVITY_NEW_TASK
        }
        fragmentActivity.startActivity(intent)
    }

    override fun onNavigateSignUpLandingActivityToLoginActivity(fragmentActivity: FragmentActivity) {
        val intent = Intent(fragmentActivity, MainActivity::class.java).apply {
            flags = Intent.FLAG_ACTIVITY_CLEAR_TASK or Intent.FLAG_ACTIVITY_NEW_TASK
        }
        fragmentActivity.startActivity(intent)
    }

    override fun onNavigateToDashboardActivity(fragmentActivity: FragmentActivity) {
        val intent = Intent(fragmentActivity, MainActivity::class.java).apply {
            putExtra(MainActivity.EXTRA_ROUTE_TYPE, RouteName.DASHBOARD)
            putExtra(MainActivity.EXTRA_FULL_ACCESS, false)
            flags = Intent.FLAG_ACTIVITY_CLEAR_TASK or Intent.FLAG_ACTIVITY_NEW_TASK
        }
        fragmentActivity.startActivity(intent)
    }

    override fun onNavigateToLoginActivityWithEmail(
        fragmentActivity: FragmentActivity,
        email: String,
    ) {
        val intent = Intent(fragmentActivity, MainActivity::class.java).apply {
            flags = Intent.FLAG_ACTIVITY_CLEAR_TASK or Intent.FLAG_ACTIVITY_NEW_TASK
        }
        fragmentActivity.startActivity(intent)
    }

    override fun onNavigateToSignUpLandingActivity(fragmentActivity: FragmentActivity) {
        Intent(fragmentActivity, SignUpLandingActivity::class.java).also {
            it.putExtra(SignUpLandingActivity.EXTRA_IS_FROM_ONBOARDING_FLOW, true)
            fragmentActivity.startActivity(it, fragmentActivity.getIntentOption().toBundle())
        }
    }


        override fun onStartRiskAssessmentProgressActivity(
        activity: FragmentActivity,
        resultLauncher: ActivityResultLauncher<Intent>,
    ) {
        Intent(activity, RiskAssessmentProgressActivity::class.java).also {
            resultLauncher.launch(it, activity.getIntentOption())
        }
    }

    override fun onStartFaceScanRecordingActivity(
        fragmentActivity: FragmentActivity,
        callback: ActivityResultLauncher<Intent>,
    ) {
        Intent(fragmentActivity, FaceScanRecordActivity::class.java).also {
            callback.launch(it, fragmentActivity.getIntentOption())
        }
    }

    override fun onNavigateToSignAllDocumentActivity(
        activity: FragmentActivity,
        callback: ActivityResultLauncher<Intent>
    ) {
        Intent(activity, SignDocumentActivity::class.java).also {
            callback.launch(it, activity.getIntentOption())
        }
    }

        override fun onNavigateToSuccessInfoActivity(
            activity: Activity,
            item: SuccessMessagePageDisplayModel,
            result: ActivityResultLauncher<Intent>?,
    ) {
        val intent = Intent(activity, SuccessInformationActivity::class.java).apply {
            putExtra(SuccessInformationActivity.EXTRA_SUCCESS_MESSAGE, item)
        }
        val option = ActivityOptionsCompat.makeCustomAnimation(
            activity,
            com.siriustech.merit.app_common.R.anim.slide_in_right,
            com.siriustech.merit.app_common.R.anim.slide_out_right
        )
        if (result != null) {
            result.launch(intent, option)
        } else {
            activity.startActivity(intent, option.toBundle())
        }
    }
}