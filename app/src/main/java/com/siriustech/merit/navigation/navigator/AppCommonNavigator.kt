package com.siriustech.merit.navigation.navigator

import android.content.Intent
import androidx.core.app.ActivityOptionsCompat
import androidx.fragment.app.FragmentActivity
import com.siriustech.merit.app_common.navigation.AppCommonNavigation
import com.siriustech.merit.main.MainActivity
import javax.inject.Inject

/**
 * Created by <PERSON><PERSON>tet
 */
class AppCommonNavigator @Inject constructor() : AppCommonNavigation {
    override fun onNavigateToLoginActivity(activity: FragmentActivity) {
        val option = ActivityOptionsCompat.makeCustomAnimation(
            activity,
            com.siriustech.merit.app_common.R.anim.slide_in_right,
            com.siriustech.merit.app_common.R.anim.slide_out_right
        ).toBundle()
        activity.startActivity(Intent(activity, MainActivity::class.java).apply {
            this.flags = Intent.FLAG_ACTIVITY_CLEAR_TASK or Intent.FLAG_ACTIVITY_NEW_TASK
        }, option)
    }
}