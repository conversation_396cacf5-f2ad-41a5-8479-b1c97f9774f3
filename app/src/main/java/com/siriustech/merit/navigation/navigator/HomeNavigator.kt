package com.siriustech.merit.navigation.navigator

import android.content.Intent
import androidx.activity.compose.ManagedActivityResultLauncher
import androidx.activity.result.ActivityResult
import androidx.core.app.ActivityOptionsCompat
import androidx.fragment.app.FragmentActivity
import androidx.navigation.NavController
import com.sirius.useronboarding.screen.steplanding.SignUpLandingActivity
import com.siriustech.home.navigation.HomeNavigation
import com.siriustech.home.screen.refinehomepage.HomeAssetRefineActivity
import com.siriustech.home.screen.refinehomepage.HomePageRefineArgument
import com.siriustech.market.screen.searchinstrument.SearchMarketInstrumentActivity
import com.siriustech.merit.app_common.Constants.BROADCAST_ACTION_MENU_INDEX
import com.siriustech.merit.app_common.Constants.EXTRA_MENU_INDEX
import com.siriustech.merit.app_common.data.display.MarketAssetDisplayData
import com.siriustech.merit.app_common.navigation.MarketProfile
import com.siriustech.merit.app_common.navigation.argument.market.MarketProfileArgument
import com.siriustech.merit.app_common.screen.appwebview.AppWebViewActivity
import com.siriustech.merit.app_common.screen.appwebview.AppWebViewArgument
import com.siriustech.merit.app_common.screen.chat.ChatActivity
import com.siriustech.merit.navigation.DashboardScreen
import com.siriustech.merit.shareactivity.NotificationActivity
import com.siriustech.portfolio.screen.reportgenerate.ReportGenerateActivity
import com.siriustech.settings.screen.profile.SettingsActivity
import javax.inject.Inject

/**
 * Created by Hein Htet
 */
class HomeNavigator @Inject constructor() : HomeNavigation {
    override fun onNavigateToNotification(activity: FragmentActivity) {
        val option = ActivityOptionsCompat.makeCustomAnimation(
            activity,
            com.siriustech.merit.app_common.R.anim.slide_in_right,
            com.siriustech.merit.app_common.R.anim.slide_out_right,
        )
        activity.startActivity(
            Intent(
                activity,
                NotificationActivity::class.java,
            ), option.toBundle()
        )
    }

    override fun onNavigateToInstrumentSearchActivity(activity: FragmentActivity) {
        val option = ActivityOptionsCompat.makeCustomAnimation(
            activity,
            com.siriustech.merit.app_common.R.anim.slide_in_right,
            com.siriustech.merit.app_common.R.anim.slide_out_right,
        )
        activity.startActivity(
            Intent(
                activity,
                SearchMarketInstrumentActivity::class.java,
            ), option.toBundle()
        )
    }

    override fun onNavigateToMarketProfileDetails(
        activity: FragmentActivity,
        navController: NavController,
        marketAssetDisplayData: MarketAssetDisplayData,
    ) {
        navController.navigate(MarketProfile(MarketProfileArgument(data = marketAssetDisplayData)))
    }

    override fun onNavigateToMarketTab(activity: FragmentActivity, navController: NavController) {
        activity.sendBroadcast(Intent(BROADCAST_ACTION_MENU_INDEX).apply {
            putExtra(EXTRA_MENU_INDEX, 1)
        })
        navController.navigate(DashboardScreen.Market)
    }

    override fun onNavigateToSettings(activity: FragmentActivity) {
        val option = ActivityOptionsCompat.makeCustomAnimation(
            activity,
            com.siriustech.merit.app_common.R.anim.slide_in_right,
            com.siriustech.merit.app_common.R.anim.slide_out_right,
        )
        activity.startActivity(
            Intent(
                activity,
                SettingsActivity::class.java,
            ), option.toBundle()
        )
    }

    override fun onNavigateToChatActivity(activity: FragmentActivity) {
        val option = ActivityOptionsCompat.makeCustomAnimation(
            activity,
            com.siriustech.merit.app_common.R.anim.slide_in_right,
            com.siriustech.merit.app_common.R.anim.slide_out_right,
        )
        activity.startActivity(
            Intent(
                activity,
                ChatActivity::class.java,
            ), option.toBundle()
        )
    }

    override fun onNavigateToHomePageRefineActivity(
        activity: FragmentActivity,
        argument: HomePageRefineArgument,
        callback: ManagedActivityResultLauncher<Intent, ActivityResult>,
    ) {
        val option = ActivityOptionsCompat.makeCustomAnimation(
            activity,
            com.siriustech.merit.app_common.R.anim.slide_in_right,
            com.siriustech.merit.app_common.R.anim.slide_out_right,
        )
        callback.launch(
            Intent(
                activity,
                HomeAssetRefineActivity::class.java,
            ).apply {
                putExtra(HomeAssetRefineActivity.EXTRA_HOME_REFINE_DATA, argument)
            }, option
        )
    }

    override fun onNavigateToPortfolioTab(
        activity: FragmentActivity,
        navController: NavController,
    ) {
        activity.sendBroadcast(Intent(BROADCAST_ACTION_MENU_INDEX).apply {
            putExtra(EXTRA_MENU_INDEX, 3)
        })
        navController.navigate(DashboardScreen.Portfolio)
    }

    override fun onNavigateToReportStatementActivity(
        activity: FragmentActivity,
        callback: ManagedActivityResultLauncher<Intent, ActivityResult>,
    ) {
        val option = ActivityOptionsCompat.makeCustomAnimation(
            activity,
            com.siriustech.merit.app_common.R.anim.slide_in_right,
            com.siriustech.merit.app_common.R.anim.slide_out_right,
        )
        callback.launch(
            Intent(
                activity,
                ReportGenerateActivity::class.java,
            ), option
        )
    }

    override fun onNavigateAppWebViewActivity(
        activity: FragmentActivity,
        data: AppWebViewArgument,
    ) {
        val option = ActivityOptionsCompat.makeCustomAnimation(
            activity,
            com.siriustech.merit.app_common.R.anim.slide_in_right,
            com.siriustech.merit.app_common.R.anim.slide_out_right,
        )
        activity.startActivity(
            Intent(
                activity,
                AppWebViewActivity::class.java,
            ).apply {
                putExtra(AppWebViewActivity.EXTRA_WEB_VIEW_ARGUMENT, data)
            }, option.toBundle()
        )
    }

    override fun onNavigateToContinueSignUp(activity: FragmentActivity) {
        activity.startActivity(
            Intent(
                activity,
                SignUpLandingActivity::class.java
            ).apply {
                putExtra(
                    SignUpLandingActivity.EXTRA_IS_FROM_LIMIT_FLOW,
                    true
                )
            }
        )
    }
}