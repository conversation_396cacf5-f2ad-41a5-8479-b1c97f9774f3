package com.siriustech.merit.navigation

import androidx.compose.foundation.background
import androidx.compose.runtime.Composable
import androidx.compose.runtime.compositionLocalOf
import androidx.compose.ui.Modifier
import androidx.hilt.navigation.compose.hiltViewModel
import androidx.navigation.NavHostController
import androidx.navigation.compose.NavHost
import com.siriustech.merit.app_common.ext.composableWithTransaction
import com.siriustech.merit.app_common.navigation.Authentication
import com.siriustech.merit.app_common.navigation.Dashboard
import com.siriustech.merit.app_common.navigation.PinLogin
import com.siriustech.merit.app_common.navigation.RouteName
import com.siriustech.merit.app_common.screen.pinlogin.PinLoginScreen
import com.siriustech.merit.app_common.screen.pinlogin.PinLoginViewModel
import com.siriustech.merit.app_common.theme.LocalAppColor
import com.siriustech.merit.screen.DashboardScreen

/**
 * Created by <PERSON><PERSON>t
 */


@Composable
fun AppNavGraph(
    navHostController: NavHostController,
    route: String? = RouteName.LOGIN_ROUTE,
) {
    NavHost(
        modifier = Modifier.background(LocalAppColor.current.bgDefault),
        navController = navHostController,
        startDestination = if (route == RouteName.DASHBOARD) Dashboard else if (route == RouteName.PIN_LOGIN) PinLogin else Authentication
    ) {

        // auth and onboarding
        authNavigation(navHostController)
        // dashboard
        composableWithTransaction<Dashboard> {
            DashboardScreen(
                mainNavController = navHostController,
            )
        }

        composableWithTransaction<PinLogin> {
            PinLoginScreen(
                viewModel = hiltViewModel<PinLoginViewModel>(),
                navController = navHostController,
            )
        }
    }
}

val LocalNavController = compositionLocalOf<NavHostController> {
    error("No LocalNavController provided")
}
