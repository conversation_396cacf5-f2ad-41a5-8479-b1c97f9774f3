package com.siriustech.merit.navigation.navigator

import android.content.Intent
import androidx.activity.compose.ManagedActivityResultLauncher
import androidx.activity.result.ActivityResult
import androidx.core.app.ActivityOptionsCompat
import androidx.fragment.app.FragmentActivity
import androidx.navigation.NavController
import com.siriustech.history.component.HistoryItemDisplayData
import com.siriustech.history.navigation.HistoryNavigation
import com.siriustech.history.screen.categoryselection.HistoryCategorySelectionActivity
import com.siriustech.history.screen.refinehistory.RefineHistoryListActivity
import com.siriustech.market.screen.searchinstrument.SearchMarketInstrumentActivity
import com.siriustech.merit.app_common.data.display.HistoryFilterModel
import com.siriustech.merit.app_common.navigation.MarketProfile
import com.siriustech.merit.app_common.navigation.argument.market.MarketProfileArgument
import com.siriustech.merit.app_common.screen.chat.ChatActivity
import com.siriustech.merit.app_common.screen.pdfviewer.PDFViewerActivity
import com.siriustech.merit.app_common.screen.pdfviewer.PDFViewerArguments
import com.siriustech.merit.navigation.DashboardScreen
import com.siriustech.merit.shareactivity.HistoryDetailsActivity
import com.siriustech.merit.shareactivity.NotificationActivity
import com.siriustech.settings.screen.profile.SettingsActivity
import javax.inject.Inject

/**
 * Created by Hein Htet
 */
class HistoryNavigator @Inject constructor() : HistoryNavigation {
    override fun onNavigateToSelectHistoryCategoryActivity(
        resultCallback: ManagedActivityResultLauncher<Intent, ActivityResult>,
        activity: FragmentActivity,
    ) {
        val option = ActivityOptionsCompat.makeCustomAnimation(
            activity,
            com.siriustech.merit.app_common.R.anim.slide_in_right,
            com.siriustech.merit.app_common.R.anim.slide_out_right,
        )
        resultCallback.launch(
            Intent(
                activity,
                HistoryCategorySelectionActivity::class.java
            ), option
        )
    }

    override fun onNavigateToInstrumentSearchActivity(activity: FragmentActivity) {
        val option = ActivityOptionsCompat.makeCustomAnimation(
            activity,
            com.siriustech.merit.app_common.R.anim.slide_in_right,
            com.siriustech.merit.app_common.R.anim.slide_out_right,
        )
        activity.startActivity(
            Intent(
                activity,
                SearchMarketInstrumentActivity::class.java,
            ), option.toBundle()
        )
    }


    override fun onNavigateToMarketProfile(
        navController: NavController,
        args: MarketProfileArgument,
    ) {
        navController.navigate(MarketProfile(args))
    }

    override fun onNavigateToPDFViewerActivity(
        activity: FragmentActivity,
        arguments: PDFViewerArguments,
    ) {
        val option = ActivityOptionsCompat.makeCustomAnimation(
            activity,
            com.siriustech.merit.app_common.R.anim.slide_in_right,
            com.siriustech.merit.app_common.R.anim.slide_out_right,
        )
        activity.startActivity(
            Intent(
                activity,
                PDFViewerActivity::class.java,
            ).apply {
                putExtra(PDFViewerActivity.EXTRA_PDF_VIEWER_ARGUMENT, arguments)
            }, option.toBundle()
        )
    }

    override fun onNavigateToNotification(activity: FragmentActivity) {
        val option = ActivityOptionsCompat.makeCustomAnimation(
            activity,
            com.siriustech.merit.app_common.R.anim.slide_in_right,
            com.siriustech.merit.app_common.R.anim.slide_out_right,
        )
        activity.startActivity(
            Intent(
                activity,
                NotificationActivity::class.java,
            ), option.toBundle()
        )
    }

    override fun onNavigateToSettings(activity: FragmentActivity) {
        val option = ActivityOptionsCompat.makeCustomAnimation(
            activity,
            com.siriustech.merit.app_common.R.anim.slide_in_right,
            com.siriustech.merit.app_common.R.anim.slide_out_right,
        )
        activity.startActivity(
            Intent(
                activity,
                SettingsActivity::class.java,
            ), option.toBundle()
        )
    }

    override fun onNavigateToRefineHistoryListActivity(
        activity: FragmentActivity,
        arguments: HistoryFilterModel?,
        refineAssetListResultCallback: ManagedActivityResultLauncher<Intent, ActivityResult>,
    ) {
        val option = ActivityOptionsCompat.makeCustomAnimation(
            activity,
            com.siriustech.merit.app_common.R.anim.slide_in_right,
            com.siriustech.merit.app_common.R.anim.slide_out_right,
        )
        refineAssetListResultCallback.launch(
            Intent(
                activity,
                RefineHistoryListActivity::class.java,
            ).apply {
                putExtra(RefineHistoryListActivity.RESULT_EXTRA_REFINE_RESULT, arguments)
            }, option
        )
    }

    override fun onNavigateToHistoryDetails(
        activity: FragmentActivity,
        item: HistoryItemDisplayData,
        callback: ManagedActivityResultLauncher<Intent, ActivityResult>,
    ) {
        val option = ActivityOptionsCompat.makeCustomAnimation(
            activity,
            com.siriustech.merit.app_common.R.anim.slide_in_right,
            com.siriustech.merit.app_common.R.anim.slide_out_right,
        )
        callback.launch(
            Intent(
                activity,
                HistoryDetailsActivity::class.java,
            ).apply {
                putExtra(HistoryDetailsActivity.EXTRA_HISTORY_ITEM, item)
            }, option
        )
    }

    override fun onNavigateToPDFViewer(arguments: PDFViewerArguments, activity: FragmentActivity) {
        val option = ActivityOptionsCompat.makeCustomAnimation(
            activity,
            com.siriustech.merit.app_common.R.anim.slide_in_right,
            com.siriustech.merit.app_common.R.anim.slide_out_right,
        )
        activity.startActivity(
            Intent(
                activity,
                PDFViewerActivity::class.java,
            ).apply {
                putExtra(PDFViewerActivity.EXTRA_PDF_VIEWER_ARGUMENT, arguments)
            }, option.toBundle()
        )
    }

    override fun onNavigateToMarketTab(navController: NavController) {
        navController.navigate(DashboardScreen.Market)
    }

    override fun onNavigateToPortfolio(navController: NavController) {
        navController.navigate(DashboardScreen.Portfolio)
    }

    override fun onNavigateToChatActivity(activity: FragmentActivity) {
        val option = ActivityOptionsCompat.makeCustomAnimation(
            activity,
            com.siriustech.merit.app_common.R.anim.slide_in_right,
            com.siriustech.merit.app_common.R.anim.slide_out_right,
        )
        activity.startActivity(
            Intent(
                activity,
                ChatActivity::class.java,
            ), option.toBundle()
        )
    }
}