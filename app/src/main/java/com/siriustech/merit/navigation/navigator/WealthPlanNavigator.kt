package com.siriustech.merit.navigation.navigator

import android.content.Intent
import androidx.activity.compose.ManagedActivityResultLauncher
import androidx.activity.result.ActivityResult
import androidx.core.app.ActivityOptionsCompat
import androidx.fragment.app.FragmentActivity
import androidx.navigation.NavController
import com.siriustech.market.screen.searchinstrument.SearchMarketInstrumentActivity
import com.siriustech.merit.app_common.data.display.PortfolioItemDisplayData
import com.siriustech.merit.app_common.screen.chat.ChatActivity
import com.siriustech.merit.shareactivity.NotificationActivity
import com.siriustech.settings.screen.profile.SettingsActivity
import com.siriustech.wealthplan.WealthPlanRouteName
import com.siriustech.wealthplan.domain.display.WealthPlanDisplayData
import com.siriustech.wealthplan.navigation.WealthPlanNavigation
import com.siriustech.wealthplan.screen.createwealthplan.CreateWealthPlanActivity
import com.siriustech.wealthplan.screen.historicaldatatable.HistoricalReturnDetailsActivity
import com.siriustech.wealthplan.screen.historicaldatatable.HistoricalReturnDetailsActivity.Companion.EXTRA_WEALTH_PLAN_DATA
import com.siriustech.wealthplan.screen.wealthplandetails.WealthPlanDetailsArguments
import com.siriustech.wealthplan.screen.wealthplanportfoliodetailslist.WealthPlanPortfolioDetailsListingArguments
import javax.inject.Inject

/**
 * Created by Hein Htet
 */
class WealthPlanNavigator @Inject constructor() : WealthPlanNavigation {
    override fun onNavigateToInstrumentSearchActivity(activity: FragmentActivity) {
        val option = ActivityOptionsCompat.makeCustomAnimation(
            activity,
            com.siriustech.merit.app_common.R.anim.slide_in_right,
            com.siriustech.merit.app_common.R.anim.slide_out_right,
        )
        activity.startActivity(
            Intent(
                activity,
                SearchMarketInstrumentActivity::class.java,
            ), option.toBundle()
        )
    }

    override fun onNavigateToCreateWealthPlanActivity(
        activity: FragmentActivity,
        createWealthPlanActivityResultCallback: ManagedActivityResultLauncher<Intent, ActivityResult>,
    ) {
        val option = ActivityOptionsCompat.makeCustomAnimation(
            activity,
            com.siriustech.merit.app_common.R.anim.slide_in_right,
            com.siriustech.merit.app_common.R.anim.slide_out_right,
        )
        createWealthPlanActivityResultCallback.launch(
            Intent(
                activity,
                CreateWealthPlanActivity::class.java,
            ), option
        )
    }

    override fun onNavigateToEditWealthPlanActivity(
        activity: FragmentActivity,
        data: WealthPlanDisplayData,
        createWealthPlanActivityResultCallback: ManagedActivityResultLauncher<Intent, ActivityResult>,
    ) {
        val option = ActivityOptionsCompat.makeCustomAnimation(
            activity,
            com.siriustech.merit.app_common.R.anim.slide_in_right,
            com.siriustech.merit.app_common.R.anim.slide_out_right,
        )
        createWealthPlanActivityResultCallback.launch(
            Intent(
                activity,
                CreateWealthPlanActivity::class.java,
            ).apply {
                putExtra(CreateWealthPlanActivity.EXTRA_WEALTH_PLAN_DATA, data)
            }, option
        )
    }

    override fun onNavigateToInstrumentSearchWithResult(
        activity: FragmentActivity,
        refineAssetListResultCallback: ManagedActivityResultLauncher<Intent, ActivityResult>,
    ) {
        val option = ActivityOptionsCompat.makeCustomAnimation(
            activity,
            com.siriustech.merit.app_common.R.anim.slide_in_right,
            com.siriustech.merit.app_common.R.anim.slide_out_right,
        )
        refineAssetListResultCallback.launch(
            Intent(
                activity,
                SearchMarketInstrumentActivity::class.java,
            ).apply {
                putExtra(SearchMarketInstrumentActivity.EXTRA_NEED_RESULT_DATA, true)
            }, option
        )
    }

    override fun onNavigateToWealthPlanDetails(
        navController: NavController,
        arguments: WealthPlanDetailsArguments,
    ) {
        navController.navigate(WealthPlanRouteName.WealthPlanDetails(arguments))
    }

    override fun onNavigateToPortfolioDetails(
        navController: NavController,
        data: PortfolioItemDisplayData,
    ) {
        navController.navigate(
            WealthPlanRouteName.WealthPlanPortfolioDetailsListing(
                args = WealthPlanPortfolioDetailsListingArguments(
                    data
                )
            )
        )

    }

    override fun onNavigateToHistoryReturnDetailsActivity(
        activity: FragmentActivity,
        wealthPlanDisplayData: WealthPlanDisplayData
    ) {
        val option = ActivityOptionsCompat.makeCustomAnimation(
            activity,
            com.siriustech.merit.app_common.R.anim.slide_in_right,
            com.siriustech.merit.app_common.R.anim.slide_out_right,
        )
        activity.startActivity(
            Intent(
                activity,
                HistoricalReturnDetailsActivity::class.java,
            ).apply {
                    putExtra(EXTRA_WEALTH_PLAN_DATA,wealthPlanDisplayData)
            }, option.toBundle()
        )
    }

    override fun onNavigateToChat(activity: FragmentActivity) {
        val option = ActivityOptionsCompat.makeCustomAnimation(
            activity,
            com.siriustech.merit.app_common.R.anim.slide_in_right,
            com.siriustech.merit.app_common.R.anim.slide_out_right,
        )
        activity.startActivity(
            Intent(
                activity,
                ChatActivity::class.java,
            ), option.toBundle()
        )
    }

    override fun onNavigateToSettings(activity: FragmentActivity) {
        val option = ActivityOptionsCompat.makeCustomAnimation(
            activity,
            com.siriustech.merit.app_common.R.anim.slide_in_right,
            com.siriustech.merit.app_common.R.anim.slide_out_right,
        )
        activity.startActivity(
            Intent(
                activity,
                SettingsActivity::class.java,
            ), option.toBundle()
        )
    }

    override fun onNavigateToNotification(activity: FragmentActivity) {
        val option = ActivityOptionsCompat.makeCustomAnimation(
            activity,
            com.siriustech.merit.app_common.R.anim.slide_in_right,
            com.siriustech.merit.app_common.R.anim.slide_out_right,
        )
        activity.startActivity(
            Intent(
                activity,
                NotificationActivity::class.java,
            ), option.toBundle()
        )
    }
}