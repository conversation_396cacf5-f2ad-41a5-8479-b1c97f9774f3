package com.siriustech.merit.navigation

import androidx.navigation.NavController
import androidx.navigation.NavGraphBuilder
import androidx.navigation.NavHostController
import androidx.navigation.compose.composable
import androidx.navigation.navigation
import com.siriustech.merit.app_common.ext.composableWithTransaction
import com.siriustech.portfolio.PortfolioRouteName
import com.siriustech.portfolio.screen.detailslist.PortfolioDetailsListing
import com.siriustech.portfolio.screen.detailslist.PortfolioDetailsListingArguments
import com.siriustech.portfolio.screen.portfoliolanding.PortfolioScreen

/**
 * Created by Hein Htet
 */


fun NavGraphBuilder.portfolioNavigation(
    navHostController: NavHostController,
) {
    navigation<DashboardScreen.Portfolio>(startDestination = PortfolioRouteName.PortfolioListing) {
        composable<PortfolioRouteName.PortfolioListing> { PortfolioScreen(navController = navHostController) }
        composableWithTransaction<PortfolioRouteName.PortfolioDetailsListing>(
            typeMap = PortfolioDetailsListingArguments.typeMap
        ) {
            PortfolioDetailsListing(navHostController)
        }
    }
}