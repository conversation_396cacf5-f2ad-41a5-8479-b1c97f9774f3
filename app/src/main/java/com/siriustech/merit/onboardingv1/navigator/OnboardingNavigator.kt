//package com.siriustech.merit.onboardingv1.navigator
//
//import android.content.Intent
//import androidx.activity.result.ActivityResultLauncher
//import androidx.core.app.ActivityOptionsCompat
//import androidx.fragment.app.FragmentActivity
//import com.sirius.appui.screen.pdf.AppPDFViewerActivity
//import com.sirius.appui.screen.pdf.AppPDFViewerDisplay
//import com.sirius.appui.screen.success.SuccessInformationActivity
//import com.sirius.appui.screen.success.SuccessMessagePageDisplayModel
//import com.sirius.onboarding.navigation.OnboardingNavigation
//import com.sirius.onboarding.screen.signup.steps.onboardingvaiagent.OnboardingViaAgentActivity
//import com.sirius.onboarding.screen.signup.FaceScanRecordActivity
//import com.sirius.onboarding.screen.signup.UserRegistrationActivity
//import com.sirius.onboarding.screen.signup.steplanding.SignUpLandingActivity
//import com.sirius.onboarding.screen.signup.steps.riskassessment.RiskAssessmentProgressActivity
//import com.sirius.useronboarding.navigation.UserOnboardingNavigation
//import com.sirius.useronboarding.screen.register.UserRegistrationActivity
//import com.siriustech.merit.main.MainActivity
//import com.siriustech.merit.app_common.navigation.RouteName
//import com.siriustech.merit.navigation.getIntentOption
//import dagger.hilt.android.scopes.ActivityScoped
//import javax.inject.Inject
//
///**
// * Created by Hein Htet
// */
//@ActivityScoped
//class OnboardingNavigator @Inject constructor() : UserOnboardingNavigation {
//    override fun onStartUserRegistrationActivity(fragmentActivity: FragmentActivity, step: Int?) {
//        Intent(fragmentActivity, UserRegistrationActivity::class.java).also {
//            it.putExtra(UserRegistrationActivity.EXTRA_INIT_STEP, step)
////            it.flags = Intent.FLAG_ACTIVITY_CLEAR_TASK or Intent.FLAG_ACTIVITY_NEW_TASK
//            fragmentActivity.startActivity(it, fragmentActivity.getIntentOption().toBundle())
//        }
//    }
//
//    override fun onNavigateToOnboardingViaAgent(fragmentActivity: FragmentActivity) {
//        val intent = Intent(fragmentActivity, OnboardingViaAgentActivity::class.java)
//        fragmentActivity.startActivity(intent,fragmentActivity.getIntentOption().toBundle())
//    }
//
//    override fun onStartConsentFormPDFActivity(
//        fragmentActivity: FragmentActivity,
//        display: AppPDFViewerDisplay,
//        resultLauncher: ActivityResultLauncher<Intent>,
//    ) {
//        Intent(fragmentActivity, AppPDFViewerActivity::class.java).apply {
//            putExtra("EXTRA_PDF_DISPLAY", display)
//        }.also {
//            resultLauncher.launch(it, fragmentActivity.getIntentOption())
//        }
//    }
//
//    override fun onStartFaceScanRecordingActivity(
//        activity: FragmentActivity,
//        resultLauncher: ActivityResultLauncher<Intent>,
//    ) {
//        Intent(activity, FaceScanRecordActivity::class.java).also {
//            resultLauncher.launch(it, activity.getIntentOption())
//        }
//    }
//
//    override fun onStartRiskAssessmentProgressActivity(
//        activity: FragmentActivity,
//        resultLauncher: ActivityResultLauncher<Intent>,
//    ) {
//        Intent(activity, RiskAssessmentProgressActivity::class.java).also {
//            resultLauncher.launch(it, activity.getIntentOption())
//        }
//    }
//
//    override fun onNavigateToLoginActivity(fragmentActivity: FragmentActivity) {
//        val intent = Intent(fragmentActivity, MainActivity::class.java).apply {
//            flags = Intent.FLAG_ACTIVITY_CLEAR_TASK or Intent.FLAG_ACTIVITY_NEW_TASK
//        }
//        fragmentActivity.startActivity(intent)
//    }
//
//    override fun onNavigateSignUpLandingActivityToLoginActivity(fragmentActivity: FragmentActivity) {
//        val intent = Intent(fragmentActivity, MainActivity::class.java).apply {
//            flags = Intent.FLAG_ACTIVITY_CLEAR_TASK or Intent.FLAG_ACTIVITY_NEW_TASK
//        }
//        fragmentActivity.startActivity(intent)
//    }
//
//    override fun onNavigateToDashboardActivity(fragmentActivity: FragmentActivity) {
////        val intent = Intent(fragmentActivity, DashboardActivity::class.java).apply {
////            flags = Intent.FLAG_ACTIVITY_CLEAR_TASK or Intent.FLAG_ACTIVITY_NEW_TASK
////            putExtra(LoginActivity.EXTRA_IS_SESSION_TIMEOUT, true)
////        }
////        fragmentActivity.startActivity(intent)
//        val intent = Intent(fragmentActivity, MainActivity::class.java).apply {
//            putExtra(MainActivity.EXTRA_ROUTE_TYPE,RouteName.DASHBOARD)
//            putExtra(MainActivity.EXTRA_FULL_ACCESS,false)
//            flags = Intent.FLAG_ACTIVITY_CLEAR_TASK or Intent.FLAG_ACTIVITY_NEW_TASK
//        }
//        fragmentActivity.startActivity(intent)
//    }
//
//    override fun onNavigateToLoginActivityWithEmail(
//        fragmentActivity: FragmentActivity,
//        email: String,
//    ) {
//        val intent = Intent(fragmentActivity, MainActivity::class.java).apply {
//            flags = Intent.FLAG_ACTIVITY_CLEAR_TASK or Intent.FLAG_ACTIVITY_NEW_TASK
//        }
//        fragmentActivity.startActivity(intent)
//    }
//
//
//    override fun onNavigateToSignUpLandingActivity(fragmentActivity: FragmentActivity) {
//        Intent(fragmentActivity, SignUpLandingActivity::class.java).also {
//            it.putExtra(SignUpLandingActivity.EXTRA_IS_FROM_ONBOARDING_FLOW,true)
//            fragmentActivity.startActivity(it, fragmentActivity.getIntentOption().toBundle())
//        }
//    }
//
//    override fun onNavigateSuccessPinSetupInfo(
//        activity: FragmentActivity, item: SuccessMessagePageDisplayModel,
//        resultLauncher: ActivityResultLauncher<Intent>,
//    ) {
//        val intent = Intent(activity, SuccessInformationActivity::class.java).apply {
//            putExtra(SuccessInformationActivity.EXTRA_SUCCESS_MESSAGE, item)
//        }
//        val option = ActivityOptionsCompat.makeCustomAnimation(
//            activity,
//            com.sirius.appui.R.anim.slide_in_right,
//            com.sirius.appui.R.anim.slide_out_right
//        )
//        resultLauncher.launch(intent, option)
//    }
//}