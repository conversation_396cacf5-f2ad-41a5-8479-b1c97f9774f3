//package com.siriustech.merit.onboardingv1.navigator
//
//import android.app.Activity
//import android.content.Intent
//import androidx.activity.result.ActivityResultLauncher
//import androidx.core.app.ActivityOptionsCompat
//import androidx.fragment.app.FragmentActivity
//import com.sirius.appui.R
//import com.sirius.appui.navigation.CommonNavigationActions
//import com.sirius.appui.screen.auth2FALogin.Auth2FACodeRoute
//import com.sirius.appui.screen.auth2FALogin.Auth2FALoginActivity
//import com.sirius.appui.screen.pdf.AppPDFViewerActivity
//import com.sirius.appui.screen.pdf.AppPDFViewerDisplay
//import com.sirius.appui.screen.pinlogin.PinLoginActivity
//import com.sirius.appui.screen.pinlogin.PinLoginCodeRoute
//import com.sirius.appui.screen.reportgeneration.RequestDocumentSuccessActivity
//import com.sirius.appui.screen.reportgeneration.RequestReportActivity
//import com.sirius.appui.screen.success.SuccessInformationActivity
//import com.sirius.appui.screen.success.SuccessMessagePageDisplayModel
//import com.sirius.appui.screen.webview.AppWebViewerActivity
//import com.sirius.appui.screen.webview.AppWebViewerDisplay
//import com.sirius.onboarding.navigation.OnboardingNavigationActions
//import com.sirius.onboarding.screen.moredocument.RequestMoreDocumentActivity
//import com.sirius.onboarding.screen.opennontradeaccount.NonTradingAccountCreatedActivity
//import com.sirius.onboarding.screen.opennontradeaccount.NonTradingAccountCreatedActivity.Companion.EXTRA_LOGIN_SUCCESS
//import com.sirius.onboarding.screen.opentradeaccount.newaccountflow.OpenTradingNewAccountLandingActivity
//import com.sirius.onboarding.screen.opentradeaccount.retakesuittest.RetakeSuitTestLandingActivity
//import com.sirius.onboarding.screen.signup.signdocument.SignDocumentActivity
//import com.sirius.useronboarding.navigation.UserOnboardingNavigation
//import com.siriustech.merit.main.MainActivity
//import dagger.hilt.android.scopes.ActivityScoped
//import javax.inject.Inject
//
///**
// * Created by Hein Htet
// */
//@ActivityScoped
//class AppNavigator @Inject constructor() :
//    UserOnboardingNavigation, CommonNavigationActions {
//
//
//    override fun onPinLoginActivityToSimpleLoginActivity(
//        fragmentActivity: FragmentActivity,
//        invalidPin: Boolean,
//    ) {
//
//    }
//
//    override fun onNavigateToDashboardActivity(activity: FragmentActivity) {
////        val intent = Intent(activity, DashboardActivity::class.java).apply {
////            flags = Intent.FLAG_ACTIVITY_NEW_TASK or Intent.FLAG_ACTIVITY_CLEAR_TASK
////        }
////        activity.startActivity(intent)
//    }
//
//
//    override fun onStartOpenNonTradingAccountCreated(
//        fragmentActivity: FragmentActivity,
//        isLoginSuccess: Boolean,
//    ) {
//        val intent = Intent(fragmentActivity, NonTradingAccountCreatedActivity::class.java).apply {
//            putExtra(EXTRA_LOGIN_SUCCESS, isLoginSuccess)
//        }
//        fragmentActivity.startActivity(intent)
//        fragmentActivity.finish()
//    }
//
//    override fun onStartDashboardActivity(fragmentActivity: FragmentActivity) {
////        fragmentActivity.startActivity(
////            Intent(
////                fragmentActivity,
////                DashboardActivity::class.java
////            ).apply {
////                flags = Intent.FLAG_ACTIVITY_CLEAR_TASK or Intent.FLAG_ACTIVITY_NEW_TASK
////            })
////        fragmentActivity.finish()
//    }
//
//    override fun onStartSuccessOpenTradingAccount(
//        data: SuccessMessagePageDisplayModel,
//        fragmentActivity: FragmentActivity,
//        resultLauncher: ActivityResultLauncher<Intent>,
//    ) {
//        val intent = Intent(fragmentActivity, SuccessInformationActivity::class.java).apply {
//            putExtra(SuccessInformationActivity.EXTRA_SUCCESS_MESSAGE, data)
//
//        }
//        val option = ActivityOptionsCompat.makeCustomAnimation(
//            fragmentActivity,
//            R.anim.slide_in_right,
//            R.anim.slide_out_right
//        )
//        resultLauncher.launch(intent, option)
//    }
//
//    override fun onNavigateForgotPasswordSuccess(
//        activity: Activity,
//        item: SuccessMessagePageDisplayModel,
//    ) {
//        val intent = Intent(activity, SuccessInformationActivity::class.java).apply {
//            putExtra(SuccessInformationActivity.EXTRA_SUCCESS_MESSAGE, item)
//        }
//        val option = ActivityOptionsCompat.makeCustomAnimation(
//            activity,
//            R.anim.slide_in_right,
//            R.anim.slide_out_right
//        )
//        activity.startActivity(intent, option.toBundle())
//    }
//
//    override fun onNavigateToLogin(fragmentActivity: FragmentActivity) {
//        fragmentActivity.startActivity(Intent(fragmentActivity, MainActivity::class.java).apply {
//            flags = Intent.FLAG_ACTIVITY_CLEAR_TASK or Intent.FLAG_ACTIVITY_NEW_TASK
//        })
//        fragmentActivity.finish()
//    }
//
//
//    override fun onNavigateToSetupPinAuthenticationActivity(
//        activity: FragmentActivity,
//        resultLauncher: ActivityResultLauncher<Intent>?,
//        biometricOnly: Boolean,
//        isFromSettings: Boolean,
//    ) {
//
//    }
//
//
//    override fun onNavigatePinLoginActivity(
//        activity: FragmentActivity,
//        route: PinLoginCodeRoute,
//        resultLauncher: ActivityResultLauncher<Intent>?,
//    ) {
//        val intent = Intent(activity, PinLoginActivity::class.java).apply {
//            putExtra(PinLoginActivity.EXTRA_COMMON_PIN_INPUT_ROUTE, route)
//        }
//        val option = ActivityOptionsCompat.makeCustomAnimation(
//            activity,
//            R.anim.slide_in_right,
//            R.anim.slide_out_right
//        )
//        if (resultLauncher != null) {
//            resultLauncher.launch(intent, option)
//        } else {
//            activity.startActivity(intent, option.toBundle())
//            activity.finish()
//        }
//    }
//
//    override fun onNavigateTo2FALoginActivity(
//        fragmentActivity: FragmentActivity,
//        route: Auth2FACodeRoute,
//        result: ActivityResultLauncher<Intent>?,
//    ) {
//        val intent = Intent(fragmentActivity, Auth2FALoginActivity::class.java).apply {
//            putExtra(Auth2FALoginActivity.EXTRA_COMMON_2FA_ROUTE, route)
//        }
//        val option = ActivityOptionsCompat.makeCustomAnimation(
//            fragmentActivity,
//            R.anim.slide_in_right,
//            R.anim.slide_out_right
//        )
//        if (result != null) {
//            result.launch(intent, option)
//        } else {
//            fragmentActivity.startActivity(intent, option.toBundle())
//        }
//    }
//
//
//    override fun onNavigateToPinSetupActivity(fragmentActivity: FragmentActivity) {
//    }
//
//
//    override fun onCommonNavigateToForgotPinActivity(fragmentActivity: FragmentActivity) {
////        val intent = Intent(fragmentActivity, ChangeCredentialsActivity::class.java).apply {
////            putExtra(EXTRA_CHANGE_CREDENTIALS_TYPE, ChangeCredentialsType.FORGOT_PIN)
////        }
////        val option = ActivityOptionsCompat.makeCustomAnimation(
////            fragmentActivity,
////            R.anim.slide_in_right,
////            R.anim.slide_out_right
////        )
////        fragmentActivity.startActivity(intent, option.toBundle())
////    }
//    }
//
//    override fun onNavigateForgotPasswordToLogin(fragmentActivity: FragmentActivity) {
//        fragmentActivity.startActivity(Intent(fragmentActivity, MainActivity::class.java).apply {
//            flags = Intent.FLAG_ACTIVITY_CLEAR_TASK or Intent.FLAG_ACTIVITY_NEW_TASK
//        })
//        fragmentActivity.finish()
//    }
//
//    override fun onSubmitMoreDocument(activity: Activity) {
//        val intent = Intent(activity, RequestMoreDocumentActivity::class.java)
//        val option = ActivityOptionsCompat.makeCustomAnimation(
//            activity,
//            R.anim.slide_in_right,
//            R.anim.slide_out_right
//        )
//        activity.startActivity(intent, option.toBundle())
//    }
//
//    override fun onNavigateToOpenNewAccountLanding(fragmentActivity: FragmentActivity) {
//        val intent = Intent(fragmentActivity, OpenTradingNewAccountLandingActivity::class.java)
//        val option = ActivityOptionsCompat.makeCustomAnimation(
//            fragmentActivity,
//            R.anim.slide_in_right,
//            R.anim.slide_out_right
//        )
//        fragmentActivity.startActivity(intent, option.toBundle())
//    }
//
//    override fun onNavigateToSuitTestLandingActivity(fragmentActivity: FragmentActivity) {
//        val intent = Intent(fragmentActivity, RetakeSuitTestLandingActivity::class.java)
//        val option = ActivityOptionsCompat.makeCustomAnimation(
//            fragmentActivity,
//            R.anim.slide_in_right,
//            R.anim.slide_out_right
//        )
//        fragmentActivity.startActivity(intent, option.toBundle())
//    }
//
//    override fun onNavigateToRequestReport(activity: Activity) {
//        val intent = Intent(activity, RequestReportActivity::class.java)
//        val option = ActivityOptionsCompat.makeCustomAnimation(
//            activity,
//            R.anim.slide_in_right,
//            R.anim.slide_out_right
//        ).toBundle()
//        activity.startActivity(intent, option)
//    }
//
//    override fun onNavigateToSuccessInfoActivity(
//        activity: Activity,
//        item: SuccessMessagePageDisplayModel,
//        result: ActivityResultLauncher<Intent>?,
//    ) {
//        val intent = Intent(activity, SuccessInformationActivity::class.java).apply {
//            putExtra(SuccessInformationActivity.EXTRA_SUCCESS_MESSAGE, item)
//        }
//        val option = ActivityOptionsCompat.makeCustomAnimation(
//            activity,
//            R.anim.slide_in_right,
//            R.anim.slide_out_right
//        )
//        if (result != null) {
//            result.launch(intent, option)
//        } else {
//            activity.startActivity(intent, option.toBundle())
//        }
//    }
//
//
//    override fun onNavigateToDocumentRequestSuccessActivity(activity: Activity, url: String) {
//        val intent = Intent(activity, RequestDocumentSuccessActivity::class.java).apply {
//            putExtra("EXTRA_PDF_DISPLAY", url)
//        }
//        val option = ActivityOptionsCompat.makeCustomAnimation(
//            activity,
//            R.anim.slide_in_right,
//            R.anim.slide_out_right
//        )
//        activity.startActivity(intent, option.toBundle())
//    }
//
//    override fun onNavigateToOpenAppPdfViewer(activity: Activity, display: AppPDFViewerDisplay) {
//        val intent = Intent(activity, AppPDFViewerActivity::class.java).apply {
//        }
//        val option = ActivityOptionsCompat.makeCustomAnimation(
//            activity,
//            R.anim.slide_in_right,
//            R.anim.slide_out_right
//        )
//        activity.startActivity(intent, option.toBundle())
//    }
//
//
//    override fun onNavigateToAppWebViewerActivity(
//        activity: FragmentActivity,
//        display: AppWebViewerDisplay,
//        callback: ActivityResultLauncher<Intent>,
//    ) {
//        Intent(activity, AppWebViewerActivity::class.java).apply {
//            putExtra("EXTRA_PDF_DISPLAY", display)
//        }.also {
//            callback.launch(it, activity.getIntentOption())
//        }
//    }
//
//
//
//    override fun onNavigateToSignAllDocumentActivity(
//        activity: FragmentActivity,
//        callback: ActivityResultLauncher<Intent>,
//    ) {
//        Intent(activity, SignDocumentActivity::class.java).also {
//            callback.launch(it, activity.getIntentOption())
//        }
//    }
//
//    override fun onStartNavigateLogin(activity: FragmentActivity) {
//    }
//
//}
//
//fun FragmentActivity.getIntentOption(): ActivityOptionsCompat {
//    return ActivityOptionsCompat.makeCustomAnimation(
//        this,
//        com.sirius.appui.R.anim.slide_in_right,
//        com.sirius.appui.R.anim.slide_out_right
//    )
//}