package com.siriustech.merit.di

import android.content.Context
import com.core.network.base.ApiHeaderInterceptor
import com.core.network.base.RemoteOkHttpClient
import com.core.network.base.provideRetrofit
import com.core.network.config.HttpConfigurable
import com.siriustech.merit.app_common.data.CommonSharedPreferences
import com.siriustech.merit.app_common.network.AppHeader
import com.siriustech.merit.app_common.network.AppHttpConfiguration
import com.siriustech.merit.app_common.network.AuthInterceptor
import dagger.Module
import dagger.Provides
import dagger.hilt.InstallIn
import dagger.hilt.android.qualifiers.ApplicationContext
import dagger.hilt.components.SingletonComponent
import javax.inject.Named
import javax.inject.Singleton
import okhttp3.OkHttpClient
import retrofit2.Retrofit

/**
 * Created by <PERSON><PERSON>tet
 */

@Module
@InstallIn(SingletonComponent::class)
class NetworkModule {

    @Provides
    @Singleton
    fun provideApiHeaderInterceptor(
        @Named("IpAddress") ipAddress: String,
        @Named("DeviceID") deviceId: String,
        commonSharedPreferences: CommonSharedPreferences,
    ): ApiHeaderInterceptor {
        return AppHeader(ipAddress, deviceId, commonSharedPreferences)
    }

    @Provides
    @Singleton
    fun provideAuthInterceptor(@ApplicationContext context: Context,commonSharedPreferences: CommonSharedPreferences): AuthInterceptor {
        return AuthInterceptor(context,commonSharedPreferences)
    }

    @Provides
    @Singleton
    fun provideHttpConfigurable(
        apiHeaderInterceptor: ApiHeaderInterceptor,
        authInterceptor: AuthInterceptor,
        @Named("FLAVOR") flavor: String,
    ): HttpConfigurable {
        return AppHttpConfiguration(apiHeaderInterceptor, authInterceptor,flavor)
    }

    @Provides
    @Singleton
    fun provideOkHttpClient(
        @ApplicationContext context: Context,
        httpConfigurable: HttpConfigurable,
    ): OkHttpClient {
        return RemoteOkHttpClient(
            context = context, config = httpConfigurable
        ).build()
    }

    @Singleton
    @Provides
    fun provideNetworkModule(
        okHttpClient: OkHttpClient,
        @Named("BaseUrl") baseUrl: String,
    ): Retrofit {
        return provideRetrofit(okHttpClient, baseUrl)
    }
}