package com.siriustech.merit.di

import android.annotation.SuppressLint
import android.content.Context
import android.net.wifi.WifiManager
import android.provider.Settings
import android.text.format.Formatter
import com.core.localstorage.pref.fileName
import com.core.localstorage.pref.getSharedPrefs
import com.siriustech.merit.BuildConfig
import com.siriustech.merit.app_common.data.AppCache
import com.siriustech.merit.app_common.data.CommonSharedPreferences
import dagger.Module
import dagger.Provides
import dagger.hilt.InstallIn
import dagger.hilt.android.qualifiers.ApplicationContext
import dagger.hilt.components.SingletonComponent
import javax.inject.Named
import javax.inject.Singleton

/**
 * Created by He<PERSON>tet
 */
@Module
@InstallIn(SingletonComponent::class)
class AppModule {

    @Provides
    @Singleton
    @Named("BaseUrl")
    fun provideBaseUrl() = BuildConfig.BASE_URL

    @Provides
    @Singleton
    @Named("IpAddress")
    fun getIpAddress(
        @ApplicationContext context: Context,
    ): String {
        val wm = context.getSystemService(Context.WIFI_SERVICE) as WifiManager
        return Formatter.formatIpAddress(wm.connectionInfo.ipAddress)
    }

    @Provides
    @Singleton
    fun provideCommonSharedPreferences(@ApplicationContext context: Context): CommonSharedPreferences {
        return CommonSharedPreferences(context, context.getSharedPrefs(filename = fileName, encrypted = true))
    }

    @SuppressLint("HardwareIds")
    @Provides
    @Singleton
    @Named("DeviceID")
    fun provideDeviceId(@ApplicationContext context: Context): String {
        return Settings.Secure.getString(context.contentResolver,
            Settings.Secure.ANDROID_ID)
    }

    @Provides
    @Singleton
    fun provideAppCache(
        @Named("BaseUrl") url:String
    ) : AppCache = AppCache(url)


    @Provides
    @Singleton
    @Named("FLAVOR")
    fun provideFlavor() = BuildConfig.FLAVOR
}