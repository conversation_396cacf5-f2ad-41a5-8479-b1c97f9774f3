package com.siriustech.merit.di

import com.sirius.useronboarding.navigation.UserOnboardingNavigation
import com.siriustech.history.navigation.HistoryNavigation
import com.siriustech.home.navigation.HomeNavigation
import com.siriustech.market.navigation.MarketNavigation
import com.siriustech.merit.app_common.navigation.AppCommonNavigation
import com.siriustech.merit.authentication.screen.navigation.AuthNavigation
import com.siriustech.merit.navigation.navigator.AppCommonNavigator
import com.siriustech.merit.navigation.navigator.AuthNavigator
import com.siriustech.merit.navigation.navigator.CommonNavigation
import com.siriustech.merit.navigation.navigator.CommonNavigator
import com.siriustech.merit.navigation.navigator.HistoryNavigator
import com.siriustech.merit.navigation.navigator.HomeNavigator
import com.siriustech.merit.navigation.navigator.MarketNavigator
import com.siriustech.merit.navigation.navigator.PortfolioNavigator
import com.siriustech.merit.navigation.navigator.SettingsNavigator
import com.siriustech.merit.navigation.navigator.UserOnboardingNavigator
import com.siriustech.merit.navigation.navigator.WealthPlanNavigator
import com.siriustech.portfolio.navigation.PortfolioNavigation
import com.siriustech.settings.navigation.SettingsNavigation
import com.siriustech.wealthplan.navigation.WealthPlanNavigation
import dagger.Binds
import dagger.Module
import dagger.hilt.InstallIn
import dagger.hilt.components.SingletonComponent

/**
 * Created by Hein Htet
 */
// In app module
@Module
@InstallIn(SingletonComponent::class)
abstract class NavigationModule {



    @Binds
    abstract fun bindAuthNavigation(impl: AuthNavigator): AuthNavigation


    @Binds
    abstract fun bindPortfolioNavigation(impl: PortfolioNavigator): PortfolioNavigation

    @Binds
    abstract fun bindWealthPlan(impl: WealthPlanNavigator): WealthPlanNavigation


    @Binds
    abstract fun bindHomeNavigation(impl: HomeNavigator): HomeNavigation

    @Binds
    abstract fun bindCommonNavigation(impl: CommonNavigator): CommonNavigation


    @Binds
    abstract fun bindMarketNavigation(impl: MarketNavigator): MarketNavigation


    @Binds
    abstract fun bindHistoryNavigation(fooImpl: HistoryNavigator): HistoryNavigation

    @Binds
    abstract fun bindSettingsNavigation(fooImpl: SettingsNavigator): SettingsNavigation

    @Binds
    abstract fun bindUserOnboardingNavigation(impl: UserOnboardingNavigator): UserOnboardingNavigation

    @Binds
    abstract fun bindAppCommonNavigation(impl: AppCommonNavigator): AppCommonNavigation


}