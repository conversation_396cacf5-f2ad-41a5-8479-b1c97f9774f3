package com.siriustech.merit.shareactivity

import android.os.Build
import android.os.Bundle
import androidx.activity.compose.setContent
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.toArgb
import androidx.fragment.app.FragmentActivity
import androidx.navigation.compose.rememberNavController
import com.siriustech.core_ui_compose.ext.ChangeSystemBarsTheme
import com.siriustech.merit.app_common.R
import com.siriustech.merit.app_common.theme.AppScreen
import com.siriustech.merit.app_common.theme.AppViewModel
import com.siriustech.merit.navigation.HistoryDetailsNavGraph
import dagger.hilt.android.AndroidEntryPoint
import kotlinx.serialization.Serializable

/**
 * Created by <PERSON><PERSON>tet
 */
@AndroidEntryPoint
class HistoryDetailsActivity : FragmentActivity() {
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContent {
            ChangeSystemBarsTheme(lightTheme = true, Color.White.toArgb())
            val navController = rememberNavController()
            AppScreen(vm = AppViewModel()) {
                HistoryDetailsNavGraph(navController = navController)
            }
        }
    }

    override fun finish() {
        super.finish()
        onFinishWithAnimate()
    }

    private fun onFinishWithAnimate() {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.UPSIDE_DOWN_CAKE) {
            overrideActivityTransition(
                OVERRIDE_TRANSITION_CLOSE,
                R.anim.slide_in_left,
                R.anim.slide_out_left
            )
        } else {
            overridePendingTransition(
                R.anim.slide_in_left,
                R.anim.slide_out_left
            )
        }
    }

    companion object {
        val EXTRA_HISTORY_ITEM = "EXTRA_HISTORY_ITEM"
    }
}

object HistoryDetailsRouteName {
    @Serializable
    data object HistoryDetailsScreen
}

