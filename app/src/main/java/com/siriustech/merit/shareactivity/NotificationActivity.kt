package com.siriustech.merit.shareactivity

import android.os.Build
import android.os.Bundle
import androidx.activity.compose.setContent
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.toArgb
import androidx.fragment.app.FragmentActivity
import androidx.navigation.compose.rememberNavController
import com.siriustech.core_ui_compose.ext.ChangeSystemBarsTheme
import com.siriustech.merit.app_common.R
import com.siriustech.merit.navigation.NotificationNavGraph
import dagger.hilt.android.AndroidEntryPoint

/**
 * Created by <PERSON><PERSON> <PERSON><PERSON><PERSON>
 */
@AndroidEntryPoint
class NotificationActivity : FragmentActivity() {


    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContent {
            ChangeSystemBarsTheme(lightTheme = true, Color.White.toArgb())
            val navController = rememberNavController()
            NotificationNavGraph(navController = navController,)
        }
    }

    override fun finish() {
        super.finish()
        onFinishWithAnimate()
    }

    private fun onFinishWithAnimate() {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.UPSIDE_DOWN_CAKE) {
            overrideActivityTransition(
                OVERRIDE_TRANSITION_CLOSE,
                R.anim.slide_in_left,
                R.anim.slide_out_left
            )
        } else {
            overridePendingTransition(
                R.anim.slide_in_left,
                R.anim.slide_out_left
            )
        }
    }
}