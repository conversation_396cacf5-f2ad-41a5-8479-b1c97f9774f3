package com.siriustech.merit.screen

import com.siriustech.merit.app_common.data.display.UserBasicInfoDisplay
import com.siriustech.merit.app_common.theme.AppAction

/**
 * Created by <PERSON><PERSON> H<PERSON>t
 */
sealed interface DashboardAction : AppAction {
    data class OnUpdateUserData(val data : UserBasicInfoDisplay) : DashboardAction
    data object OnContinueToUserOnBoardingActivity : DashboardAction
    data object OnNavigateToAccountClosure : DashboardAction
}