package com.siriustech.merit.screen

import com.core.network.BuildConfig
import com.siriustech.merit.apilayer.service.authentication.updatejpush.UpdateJPushRegIDRequest
import com.siriustech.merit.apilayer.service.authentication.updatejpush.UpdateJPushRegIDUseCase
import com.siriustech.merit.apilayer.service.authentication.userinfo.GetUserInfoUseCase
import com.siriustech.merit.apilayer.service.user.userinfo.GetUserBasicInfoUseCase
import com.siriustech.merit.app_common.data.AppCache
import com.siriustech.merit.app_common.data.CommonSharedPreferences
import com.siriustech.merit.app_common.data.display.UserBasicInfoDisplay
import com.siriustech.merit.app_common.theme.AppViewModel
import com.siriustech.merit.app_common.typeenum.UserStatus
import dagger.hilt.android.lifecycle.HiltViewModel
import javax.inject.Inject
import javax.inject.Named
import kotlinx.coroutines.ExperimentalCoroutinesApi
import kotlinx.coroutines.flow.catch
import kotlinx.coroutines.flow.collectLatest
import kotlinx.coroutines.flow.flatMapLatest
import kotlinx.coroutines.flow.map
import kotlinx.coroutines.flow.onCompletion
import kotlinx.coroutines.flow.onStart
import kotlinx.coroutines.launch

/**
 * Created by Hein Htet
 */
@HiltViewModel
class DashboardViewModel @Inject constructor(
    @Named("DeviceID") private val deviceId: String,
    private val commonSharedPreferences: CommonSharedPreferences,
    private val updateJPushRegIDUseCase: UpdateJPushRegIDUseCase,
    private val appCache: AppCache,
    private val getUserBasicInfoUseCase: GetUserBasicInfoUseCase,
    private val getUserInfoUseCase: GetUserInfoUseCase,
) : AppViewModel() {

    override val inputs = DashboardInputs()
    override val outputs = DashboardOutputs()


    inner class DashboardInputs : BaseInputs() {
        fun onUpdateJPushRegID(id: String) = updateJPushToken(id)

        fun onGetUserInfo() = getUserBasicInfo()
    }

    inner class DashboardOutputs : BaseOutputs() {
    }


    private fun updateJPushToken(id: String) {
        if (commonSharedPreferences.sessionId.isNotEmpty()) {
            val request = UpdateJPushRegIDRequest(
                deviceId = deviceId,
                jpushRid = id
            )
            scope.launch {
                updateJPushRegIDUseCase(request)
                    .onStart { }
                    .onCompletion { }
                    .catch { }
                    .collectLatest { }
            }
        }
    }

    @OptIn(ExperimentalCoroutinesApi::class)
    private fun getUserBasicInfo() {
        if (commonSharedPreferences.sessionId.isNotEmpty()) {
            if (appCache.userBasicInfoDisplay != null) {
                onTriggerActions(
                    DashboardAction.OnUpdateUserData(
                        appCache.userBasicInfoDisplay ?: UserBasicInfoDisplay()
                    )
                )
            }
            scope.launch {
                getUserBasicInfoUseCase(param = Unit)
                    .onStart { inputs.emitLoading(true) }
                    .onCompletion { inputs.emitLoading(false) }
                    .catch { }
                    .map {
                        val info = UserBasicInfoDisplay(
                            name = it.fullName.orEmpty(),
                            email = it.email.orEmpty(),
                            mobile = it.mobile.orEmpty(),
                            language = it.language.orEmpty(),
                            profileImage = it.profilePicture.orEmpty()
                        )
                        info
                    }
                    .flatMapLatest { user ->
                        getUserInfoUseCase(param = Unit)
                            .map {
                                if (!appCache.hasFullAccess) {
                                    user.copy(userStatus = UserStatus.ONBOARDING)
                                } else {
                                    val status = UserStatus.fromParam(it.accountStatus.orEmpty())
                                    checkUserAccountStatus(status)
                                    if (BuildConfig.DEBUG) {
                                        user.copy(userStatus = UserStatus.NORMAL)
//                                        user.copy(userStatus = UserStatus.fromParam(it.accountStatus.orEmpty()))
                                    } else {
                                        user.copy(userStatus = UserStatus.fromParam(it.accountStatus.orEmpty()))
                                    }
                                }
                            }
                    }
                    .collectLatest {
                        appCache.updateUserBasicInfo(it)
                        onTriggerActions(
                            DashboardAction.OnUpdateUserData(
                                appCache.userBasicInfoDisplay ?: UserBasicInfoDisplay()
                            )
                        )
                    }
            }
        }
    }

    private fun checkUserAccountStatus(status: UserStatus) {
        when (status) {
            UserStatus.PENDING_CLOSURE -> onTriggerActions(DashboardAction.OnNavigateToAccountClosure)
            UserStatus.CLOSED -> onTriggerActions(DashboardAction.OnNavigateToAccountClosure)
            UserStatus.ONBOARDING -> onTriggerActions(DashboardAction.OnContinueToUserOnBoardingActivity)
            else -> {}
        }
    }
}