package com.siriustech.merit.screen

import android.content.BroadcastReceiver
import android.content.Context
import android.content.Intent
import android.content.IntentFilter
import androidx.compose.animation.AnimatedVisibility
import androidx.compose.animation.animateContentSize
import androidx.compose.animation.fadeIn
import androidx.compose.animation.fadeOut
import androidx.compose.animation.slideInVertically
import androidx.compose.animation.slideOutVertically
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.padding
import androidx.compose.material3.Scaffold
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableIntStateOf
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Modifier
import androidx.compose.ui.platform.LocalContext
import androidx.core.content.ContextCompat
import androidx.fragment.app.FragmentActivity
import androidx.hilt.navigation.compose.hiltViewModel
import androidx.lifecycle.Lifecycle
import androidx.lifecycle.compose.LifecycleEventEffect
import androidx.navigation.NavController
import androidx.navigation.compose.currentBackStackEntryAsState
import androidx.navigation.compose.rememberNavController
import cn.jpush.android.api.JPushInterface
import com.siriustech.core_ui_compose.base.SingleEventEffect
import com.siriustech.merit.app_common.Constants.BROADCAST_ACTION_MENU_INDEX
import com.siriustech.merit.app_common.Constants.EXTRA_MENU_INDEX
import com.siriustech.merit.app_common.theme.LocalAppColor
import com.siriustech.merit.app_common.typeenum.UserStatus
import com.siriustech.merit.authentication.screen.navigation.AuthNavigationEntryPoint
import com.siriustech.merit.navigation.DashboardBottomNavigation
import com.siriustech.merit.navigation.DashboardNavHost
import dagger.hilt.android.EntryPointAccessors
import timber.log.Timber

/**
 * Created by Hein Htet
 */
@Composable
fun DashboardScreen(
    viewModel: DashboardViewModel = hiltViewModel(),
    mainNavController: NavController,
) {
    val context = LocalContext.current

    var userStatus by remember {
        mutableStateOf(UserStatus.NORMAL)
    }


    val authNavigation = remember {
        EntryPointAccessors.fromApplication(context, AuthNavigationEntryPoint::class.java)
            .authNavigation()
    }


    LaunchedEffect(Unit) {
        viewModel.inputs.onUpdateJPushRegID(JPushInterface.getRegistrationID(context))
        viewModel.inputs.onGetUserInfo()
    }

    val initRoute = com.siriustech.merit.navigation.DashboardScreen.Home
//        if (userStatus.isLimitedAccess()) com.siriustech.merit.navigation.DashboardScreen.Home else com.siriustech.merit.navigation.DashboardScreen.Portfolio

    val bottomNavController = rememberNavController()
    var selectedIndex by remember {
        mutableIntStateOf(0)
    }

    LaunchedEffect(Unit) {
        bottomNavController.navigate(initRoute) {
            popUpTo(bottomNavController.graph.startDestinationId) { inclusive = true }
            launchSingleTop = true
        }
    }


    LifecycleEventEffect(Lifecycle.Event.ON_RESUME) {
        ContextCompat.registerReceiver(
            context,
            object : BroadcastReceiver() {
                override fun onReceive(p0: Context?, p1: Intent?) {
                    val action = p1?.getIntExtra(EXTRA_MENU_INDEX, -1) ?: -1
                    if (action != -1) {
                        Timber.d("RECEIVED $action")
                        selectedIndex = action
                    }
                }
            }, IntentFilter(BROADCAST_ACTION_MENU_INDEX),
            ContextCompat.RECEIVER_EXPORTED
        )
    }

    LifecycleEventEffect(Lifecycle.Event.ON_PAUSE) {

    }

    SingleEventEffect(viewModel.appAction) {
        when (it) {
            is DashboardAction.OnUpdateUserData -> {
                userStatus = it.data.userStatus
                Timber.d("USER_STATUS ${userStatus}")
//                selectedIndex = if (userStatus.value == UserStatus.NORMAL.value) 3 else 0
                selectedIndex = 0
            }
            is DashboardAction.OnContinueToUserOnBoardingActivity -> {
                authNavigation.onNavigateToContinueOnboardingSignupLanding(context as FragmentActivity)
            }

            is DashboardAction.OnNavigateToAccountClosure -> {
                authNavigation.onNavigateToAccountClosure(context as FragmentActivity)
            }
        }
    }

    Scaffold(
        containerColor = LocalAppColor.current.bgDefault,
        bottomBar = {
            val currentRoute = getCurrentRoute(bottomNavController)
            Timber.d("CURRENT_ROUTE $currentRoute")

//            if (currentRoute == null && bottomNavController.graph != null) {
//                bottomNavController.navigate(initRoute) {
//                    popUpTo(bottomNavController.graph.startDestinationId) { inclusive = true }
//                    launchSingleTop = true
//                }
//            }

            AnimatedVisibility(
                visible = !currentRoute.checkIfRouteNeedToHideBottomNav(),
                enter = slideInVertically(initialOffsetY = { it }) + fadeIn(),
                exit = slideOutVertically(targetOffsetY = { it }) + fadeOut(),
                modifier = Modifier.animateContentSize(),
            ) {
                DashboardBottomNavigation(
                    selectedIndex,
                    bottomNavController,
                    userStatus,
                    onItemSelectedIndex = {
                        selectedIndex = it
                    })
            }
        }
    ) { innerPadding ->
        DashboardNavHost(
            modifier = Modifier
                .background(LocalAppColor.current.bgDefault)
                .padding(innerPadding),
            navController = bottomNavController,
            startDestination = initRoute,
            onSelected = {
                selectedIndex = it
            }
        )
    }
}

fun String?.checkIfRouteNeedToHideBottomNav(): Boolean {
    val routePath = this.orEmpty().substringAfterLast(".")
    val hideList = listOf("NotificationListing")
    return hideList.contains(routePath)
}

@Composable
fun getCurrentRoute(navController: NavController): String? {
    val navBackStackEntry by navController.currentBackStackEntryAsState()
    return navBackStackEntry?.destination?.route
}