package com.siriustech.merit.authentication.screen.usersignup

import androidx.compose.runtime.Composable
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.AnnotatedString
import com.siriustech.merit.app_common.ext.AttributeStringData
import com.siriustech.merit.app_common.ext.buildAttrString
import com.siriustech.merit.app_common.ext.colorTxtParagraph
import com.siriustech.merit.app_common.ext.colorTxtTitle
import com.siriustech.merit.app_common.theme.LocalTypography
import com.siriustech.merit.app_common.R as AppCommonR


/**
 * Created by Hein Htet
 */
enum class SignupStep(val step: Int) {
    IDENTIFICATION(0),
    PERSONAL_INFORMATION(1),
    RISK_ASSESSMENT(2),
    DECLARATION(3),
    DOCUMENT_UPLOAD(4);

    @Composable
    fun signupToolbarTitle(): AnnotatedString {
        return when(this){
            IDENTIFICATION -> buildAttrString(arrayListOf(
                AttributeStringData(text = stringResource(id = AppCommonR.string.key0003), textStyle = LocalTypography.current.text14.semiBold.colorTxtTitle()),
                AttributeStringData(text = stringResource(id = AppCommonR.string.key0008), textStyle = LocalTypography.current.text14.regular.colorTxtParagraph()),
            ))
            PERSONAL_INFORMATION -> buildAttrString(arrayListOf(
                AttributeStringData(text = stringResource(id = AppCommonR.string.key0004), textStyle = LocalTypography.current.text14.semiBold.colorTxtTitle()),
                AttributeStringData(text = stringResource(id = AppCommonR.string.key0009), textStyle = LocalTypography.current.text14.regular.colorTxtParagraph()),
            ))
            RISK_ASSESSMENT -> buildAttrString(arrayListOf(
                AttributeStringData(text = stringResource(id = AppCommonR.string.key0005), textStyle = LocalTypography.current.text14.semiBold.colorTxtTitle()),
                AttributeStringData(text = stringResource(id = AppCommonR.string.key0010), textStyle = LocalTypography.current.text14.regular.colorTxtParagraph()),
            ))
            DECLARATION -> buildAttrString(arrayListOf(
                AttributeStringData(text = stringResource(id = AppCommonR.string._key0114), textStyle = LocalTypography.current.text14.semiBold.colorTxtTitle()),
                AttributeStringData(text = stringResource(id = AppCommonR.string._key0111), textStyle = LocalTypography.current.text14.regular.colorTxtParagraph()),
            ))
            DOCUMENT_UPLOAD -> buildAttrString(arrayListOf(
                AttributeStringData(text = stringResource(id = AppCommonR.string._key0115), textStyle = LocalTypography.current.text14.semiBold.colorTxtTitle()),
                AttributeStringData(text = stringResource(id = AppCommonR.string._key0112), textStyle = LocalTypography.current.text14.regular.colorTxtParagraph()),
            ))
        }
    }


    companion object {

        const val TOTAL_STEP = 5
        fun fromParam(value: Int): SignupStep {
            return when (value) {
                0 -> IDENTIFICATION
                1 -> PERSONAL_INFORMATION
                2 -> RISK_ASSESSMENT
                3 -> DECLARATION
                4 -> DOCUMENT_UPLOAD
                else -> IDENTIFICATION
            }
        }
    }
}