package com.siriustech.merit.authentication.screen.forgotpassword.component

import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.collectAsState
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import com.siriustech.core_ui_compose.base.SingleEventEffect
import com.siriustech.merit.app_common.R
import com.siriustech.merit.app_common.component.alert.BannerAlertProperties
import com.siriustech.merit.app_common.component.otp.OTPVerification
import com.siriustech.merit.app_common.component.otp.OTPVerificationProperties
import com.siriustech.merit.app_common.component.otp.VerificationEvent
import com.siriustech.merit.app_common.ext.AttributeStringData
import com.siriustech.merit.app_common.ext.buildAttrString
import com.siriustech.merit.app_common.ext.colorTxtLabel
import com.siriustech.merit.app_common.ext.colorTxtParagraph
import com.siriustech.merit.app_common.ext.displayEmailAddress
import com.siriustech.merit.app_common.theme.LocalTypography
import com.siriustech.merit.app_common.typeenum.Auth2FAType
import com.siriustech.merit.authentication.screen.forgotpassword.ForgotPasswordViewModel

/**
 * Created by Hein Htet
 */

@Composable
fun ForgotPasswordVerifyEmailOTPScreen(viewModel: ForgotPasswordViewModel) {

    val context = LocalContext.current
    val authType = viewModel.outputs.authType.collectAsState()
    val userAuthValue = viewModel.outputs.email.collectAsState()
    val otpCode = viewModel.outputs.otpCode.collectAsState()
    val otpResendCount = viewModel.outputs.otpResendCount.collectAsState()
    val refNumber = viewModel.outputs.refNumber.collectAsState()
    val otpExpireTime = viewModel.outputs.otpExpiredTime.collectAsState()

    LaunchedEffect(Unit) {
        viewModel.inputs.onRequestOTP()
    }

    fun showOtpSentAlert() {
        viewModel.emitBannerAlert(
            BannerAlertProperties(
                title = context.getString(R.string.key0111),
                description = context.getString(R.string.key0814),
            ),
        )
    }

    SingleEventEffect(viewModel.outputs.verificationEvent) { sideEffect ->
        when (sideEffect) {
            is VerificationEvent.OnOTPSubmit -> {
                viewModel.inputs.onSubmitVerificationCode(sideEffect.code)
            }

            is VerificationEvent.OnResendOTPCode -> {
                viewModel.inputs.onResendOTPCode()
            }

            is VerificationEvent.OnOTPCodeChanged -> {
                viewModel.inputs.updateOtpCode(sideEffect.code)
            }

            is VerificationEvent.OnOTPSent -> {
                showOtpSentAlert()
            }

            is VerificationEvent.OnConfigurePin -> {
            }

            is VerificationEvent.VerificationSuccess -> {
            }

            else -> {}
        }
    }

    OTPVerification(
        otpResendCount = otpResendCount.value,
        otpExpireDuration = otpExpireTime.value,
        defaultOtpCode = otpCode,
        referenceNumber = refNumber.value,
        onEventChanged = { event -> viewModel.inputs.onOTPVerificationEventChanged(event) },
        properties = OTPVerificationProperties(
            title = stringResource(id = if (authType.value == Auth2FAType.PHONE) R.string.key0198 else R.string.key0816),
            description = buildAttrString(
                arrayListOf(
                    AttributeStringData(
                        text = context.getString(if (authType.value == Auth2FAType.PHONE) R.string.key0032 else R.string.key0033),
                        textStyle = LocalTypography.current.text14.light.colorTxtParagraph()
                    ),
                    AttributeStringData(
                        text = userAuthValue.value.displayEmailAddress(),
                        textStyle = LocalTypography.current.text14.medium.colorTxtLabel()
                    ),
                ),
            ),
            iconPainter = painterResource(id = if (authType.value == Auth2FAType.PHONE) R.drawable.ic_phone_otp_verification else R.drawable.ic_email_otp_verification),
            leftButtonText = stringResource(id = R.string.key0046),
            rightButtonText = stringResource(id = R.string.key0045)
        )
    )
}