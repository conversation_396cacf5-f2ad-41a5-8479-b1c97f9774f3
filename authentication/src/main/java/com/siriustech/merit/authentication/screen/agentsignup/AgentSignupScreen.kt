package com.siriustech.merit.authentication.screen.agentsignup

import android.app.Activity
import androidx.activity.compose.BackHandler
import androidx.activity.compose.rememberLauncherForActivityResult
import androidx.activity.result.contract.ActivityResultContracts
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.pager.HorizontalPager
import androidx.compose.foundation.pager.rememberPagerState
import androidx.compose.runtime.Composable
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.runtime.setValue
import androidx.compose.ui.Modifier
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.AnnotatedString
import androidx.compose.ui.tooling.preview.Preview
import androidx.fragment.app.FragmentActivity
import androidx.lifecycle.viewmodel.compose.viewModel
import androidx.navigation.NavController
import androidx.navigation.compose.rememberNavController
import com.siriustech.core_ui_compose.base.SingleEventEffect
import com.siriustech.merit.app_common.R
import com.siriustech.merit.app_common.component.header.CommonToolbarWithBackAndResetMenu
import com.siriustech.merit.app_common.component.header.CommonToolbarWithBackMenu
import com.siriustech.merit.app_common.component.indicator.HorizontalPagerIndicator
import com.siriustech.merit.app_common.component.modalbts.CommonModelBottomSheet
import com.siriustech.merit.app_common.component.modalbts.CommonModelBottomSheetInfoProperties
import com.siriustech.merit.app_common.component.modalbts.CommonModelBottomSheetProperties
import com.siriustech.merit.app_common.ext.colorTxtCaution
import com.siriustech.merit.app_common.ext.getToolbarStepLabel
import com.siriustech.merit.app_common.navigation.argument.authentication.AgentSignupLandingArguments
import com.siriustech.merit.app_common.theme.AppScreen
import com.siriustech.merit.app_common.theme.LocalDimens
import com.siriustech.merit.app_common.theme.LocalTypography
import com.siriustech.merit.app_common.typeenum.AgentCreatePasswordStep
import com.siriustech.merit.authentication.screen.agentsignup.component.AgentCreatePasswordPage
import com.siriustech.merit.authentication.screen.confirmdocumentinformation.ConfirmDocInformationScreen
import com.siriustech.merit.authentication.screen.navigation.AuthNavigationEntryPoint
import dagger.hilt.android.EntryPointAccessors
import kotlinx.coroutines.launch
import com.siriustech.merit.app_common.R as AppCommonR

/**
 * Created by Hein Htet
 */

@Composable
fun AgentSignupScreen(
    navController: NavController,
    viewModel: AgentSignupViewModel = viewModel(),
    arguments: AgentSignupLandingArguments,
) {

    val pagerState = rememberPagerState(pageCount = { AgentCreatePasswordStep.TOTAL_STEP })
    val coroutineScope = rememberCoroutineScope()
    val resetCount = viewModel.outputs.resetCount.collectAsState()
    var showPasswordReset by remember {
        mutableStateOf(false)
    }

    val activity = LocalContext.current as FragmentActivity
    val authNavigation = remember {
        EntryPointAccessors.fromApplication(activity, AuthNavigationEntryPoint::class.java)
            .authNavigation()
    }

    fun onHandleBack() {
        when {
            pagerState.currentPage > 0 -> {
                coroutineScope.launch { pagerState.animateScrollToPage(pagerState.currentPage - 1) }
            }

            else -> {
                navController.popBackStack()
            }
        }
    }


    fun onNavigateNextPage() {
        coroutineScope.launch {
            pagerState.animateScrollToPage(pagerState.currentPage + 1)
        }
    }

    val docSignActivityResultCallback =
        rememberLauncherForActivityResult(ActivityResultContracts.StartActivityForResult()) { result ->
            if (result.resultCode == Activity.RESULT_OK) {
                println("Consent Form OK")
                authNavigation.onNavigateToSignupSuccess(navController)
            }
        }

    SingleEventEffect(sideEffectFlow = viewModel.outputs.agentSignupEvent) {
        when (it) {
            AgentSignupEvent.OnCreateNewPasswordSuccess -> {
                onNavigateNextPage()
            }

            is AgentSignupEvent.OnSignDocumentRequested -> {
                authNavigation.onStartDocSignActivity(
                    activity,
                    it.url,
                    docSignActivityResultCallback
                )
            }

            else -> {}
        }
    }

    BackHandler(enabled = true, onBack = {
        onHandleBack()
    })

    AppScreen(vm = viewModel, toolbar = {
        if (pagerState.currentPage == AgentCreatePasswordStep.CREATE_PASSWORD.step) {
            CommonToolbarWithBackAndResetMenu(annotatedTitleString = getCurrentStepToolbarTitle(
                page = pagerState.currentPage
            ), onBackClicked = {
                onHandleBack()
            }, onResetButtonClicked = {
                showPasswordReset = true
            })
        } else {
            CommonToolbarWithBackMenu(annotatedTitleString = getCurrentStepToolbarTitle(
                page = pagerState.currentPage
            ), onBackPressed = {
                onHandleBack()
            })
        }
    }) {
        Column(modifier = Modifier.fillMaxSize()) {
            HorizontalPagerIndicator(
                pagerState = pagerState,
                modifier = Modifier.padding(top = LocalDimens.current.dimen32)
            )
            Spacer(modifier = Modifier.height(LocalDimens.current.dimen24))
            HorizontalPager(
                userScrollEnabled = false,
                state = pagerState,
                modifier = Modifier
                    .fillMaxSize()
                    .padding(horizontal = (LocalDimens.current.dimen12))
            ) { page ->
                val step = AgentCreatePasswordStep.fromParam(page)
                if (step == AgentCreatePasswordStep.CREATE_PASSWORD) {
                    AgentCreatePasswordPage(
                        resetCount.value,
                        arguments,
                        onPasswordChange = {
                            viewModel.inputs.updatePassword(it)
                        },
                        onRepeatPasswordChange = {
                            viewModel.inputs.updateRepeatPassword(it)
                        },
                        onClickedNext = {
                            viewModel.inputs.onCreateNewPassword()
                        },
                    )
                } else {
                    ConfirmDocInformationScreen(navController = navController, onClickNext = {
                        viewModel.inputs.onRequestConfirmDocument()
                    })
                }
            }
        }
        if (showPasswordReset) {
            CommonModelBottomSheet(
                onDismissed = {
                    showPasswordReset = false
                }, properties = CommonModelBottomSheetProperties(
                    prefixTitle = stringResource(id = AppCommonR.string.key0102),
                    title = stringResource(id = AppCommonR.string.key0311),
                    description = stringResource(id = AppCommonR.string.key0312),
                    buttonText = stringResource(id = AppCommonR.string.key0260),
                    contentInfo = CommonModelBottomSheetInfoProperties(
                        icon = painterResource(id = AppCommonR.drawable.ic_caution),
                        content = stringResource(id = AppCommonR.string.key0313),
                        contentTextStyle = LocalTypography.current.text14.regular.colorTxtCaution()
                    ),
                    iconTitle = painterResource(id = AppCommonR.drawable.ic_reset),
                ),
                onButtonClicked = {
                    showPasswordReset = false
                    viewModel.inputs.onReset()
                }
            )
        }
    }
}

@Composable
fun getCurrentStepToolbarTitle(page: Int): AnnotatedString {
    return when (AgentCreatePasswordStep.fromParam(page)) {
        AgentCreatePasswordStep.CREATE_PASSWORD -> getToolbarStepLabel(
            stepNo = stringResource(id = R.string.key0003),
            stepLabel = stringResource(id = R.string.key0920)
        )

        AgentCreatePasswordStep.CONFIRM_DOCUMENT -> getToolbarStepLabel(
            stepNo = stringResource(id = R.string.key0004),
            stepLabel = stringResource(id = R.string.key0139)
        )
    }
}


@Preview(showBackground = true, showSystemUi = true)
@Composable
fun PreviewAgentSignup() {
    AgentSignupScreen(
        navController = rememberNavController(),
        viewModel = viewModel(),
        arguments = AgentSignupLandingArguments("", "", "")
    )
}
