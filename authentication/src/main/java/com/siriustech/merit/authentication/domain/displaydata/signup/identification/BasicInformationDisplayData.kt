package com.siriustech.merit.authentication.domain.displaydata.signup.identification

import com.siriustech.merit.app_common.utils.Validation.isValidEmail

/**
 * Created by <PERSON><PERSON><PERSON>t
 */

data class BasicInformationDisplayData(
    var chineseName: String = "",
    var englishName: String = "",
    var referenceNumber: String = "",
    var phoneNumber: String = "",
    var email: String = "",
    var password: String = "",
    var confirmPassword: String = "",


    ) {
    fun isValidate(): Boolean {
        return chineseName.isNotEmpty()
                && englishName.isNotEmpty() &&
                phoneNumber.isNotEmpty() && email.isNotEmpty() && password.isNotEmpty() && confirmPassword.isNotEmpty()
                && password == confirmPassword && email.isValidEmail()

    }
}