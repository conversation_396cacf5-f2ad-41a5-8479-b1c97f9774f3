package com.siriustech.merit.authentication.screen.navigation

import android.content.Intent
import androidx.activity.compose.ManagedActivityResultLauncher
import androidx.activity.result.ActivityResult
import androidx.fragment.app.FragmentActivity
import androidx.navigation.NavController
import com.siriustech.merit.app_common.navigation.argument.authentication.AgentSignupLandingArguments
import com.siriustech.merit.app_common.navigation.argument.authentication.Verification2FAArguments
import com.siriustech.merit.authentication.screen.usersignup.landing.SignupLandingArguments
import com.siriustech.merit.authentication.screen.usersignup.signup.SignupArguments
import dagger.hilt.EntryPoint
import dagger.hilt.InstallIn
import dagger.hilt.components.SingletonComponent

/**
 * Created by He<PERSON> Htet
 */
interface AuthNavigation {
    fun onNavigateToVerification2FA(navController: NavController, args: Verification2FAArguments)
    fun onNavigateToAgentSignupLanding(
        navController: NavController,
        arguments: AgentSignupLandingArguments,
    )

    fun onNavigateToAgentSignup(
        navController: NavController,
        arguments: AgentSignupLandingArguments,
    )

    fun onNavigateToSignupSuccess(navController: NavController)
    fun onNavigateToDashboard(navController: NavController)
    fun onNavigateToSetup2FA(navController: NavController, args: Verification2FAArguments)
    fun onNavigateToPinLogin(navController: NavController)
    fun onNavigateToSetupPin(navController: NavController)
    fun onNavigateToSignupLanding(navController: NavController, arguments: SignupLandingArguments)
    fun onNavigateToSignup(navController: NavController, arguments: SignupArguments)
    fun onNavigateSelfVerificationCamera(navController: NavController)
    fun onNavigateToSignupLanding(activity: FragmentActivity)
    fun onNavigateToSignupLandingV1(activity: FragmentActivity)
    fun onStartDocSignActivity(
        activity: FragmentActivity,
        url:String,
        resultCallback: ManagedActivityResultLauncher<Intent, ActivityResult>,
    )
    fun onNavigateToAccountClosure(activity: FragmentActivity)
    fun onNavigateToContinueOnboardingSignupLanding(activity: FragmentActivity)

}


@EntryPoint
@InstallIn(SingletonComponent::class)
interface AuthNavigationEntryPoint {
    fun authNavigation(): AuthNavigation
}