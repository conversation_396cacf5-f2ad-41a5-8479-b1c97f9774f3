package com.siriustech.merit.authentication.screen.usersignup.signup.step.identification.page.identity

import android.Manifest
import android.util.Log
import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxHeight
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.ModalBottomSheet
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.tooling.preview.Preview
import androidx.fragment.app.FragmentActivity
import androidx.hilt.navigation.compose.hiltViewModel
import androidx.lifecycle.viewmodel.compose.viewModel
import androidx.navigation.NavBackStackEntry
import androidx.navigation.NavController
import androidx.navigation.compose.rememberNavController
import com.siriustech.core_ui_compose.base.SingleEventEffect
import com.siriustech.merit.app_common.Constants
import com.siriustech.merit.app_common.Constants.NAV_RETURN_RESULT_LIVENESS_CHECK_PASSED
import com.siriustech.merit.app_common.component.button.ButtonProperties
import com.siriustech.merit.app_common.component.button.SecondaryButton
import com.siriustech.merit.app_common.component.modalbts.CommonModelBottomSheet
import com.siriustech.merit.app_common.component.modalbts.CommonModelBottomSheetInfoProperties
import com.siriustech.merit.app_common.component.modalbts.CommonModelBottomSheetProperties
import com.siriustech.merit.app_common.component.modalbts.ModelListBottomSheet
import com.siriustech.merit.app_common.component.modalbts.ModelListBottomSheetProperties
import com.siriustech.merit.app_common.component.text.NumberBoxLabel
import com.siriustech.merit.app_common.component.textfield.InputBox
import com.siriustech.merit.app_common.component.textfield.InputBoxType
import com.siriustech.merit.app_common.component.textfield.InputProperties
import com.siriustech.merit.app_common.ext.colorTxtCaution
import com.siriustech.merit.app_common.screen.image.TakeImageScreen
import com.siriustech.merit.app_common.theme.AppScreen
import com.siriustech.merit.app_common.theme.LocalAppColor
import com.siriustech.merit.app_common.theme.LocalDimens
import com.siriustech.merit.app_common.theme.LocalTypography
import com.siriustech.merit.authentication.screen.navigation.AuthNavigationEntryPoint
import com.siriustech.merit.authentication.screen.usersignup.component.PageTitle
import com.siriustech.merit.authentication.screen.usersignup.component.PageTitleProperties
import com.siriustech.merit.authentication.screen.usersignup.signup.step.identification.page.selfverification.SelfVerificationModal
import dagger.hilt.android.EntryPointAccessors
import java.io.File
import timber.log.Timber
import com.siriustech.merit.app_common.R as AppCommonR

/**
 * Created by Hein Htet
 */

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun IdentityPage(
    navController: NavController,
    viewModel: IdentityViewModel = hiltViewModel(),
    onNextStep: (result: String) -> Unit = {},
) {

    var showCountryModal by remember { mutableStateOf(false) }
    var showSelfVerificationModal by remember { mutableStateOf(false) }
    val activity = LocalContext.current as FragmentActivity
    val authNavigation = remember {
        EntryPointAccessors.fromApplication(activity, AuthNavigationEntryPoint::class.java)
            .authNavigation()
    }
    var livenessResult by remember {
        mutableStateOf("")
    }

    // Listen for the value once after navigating back
    val savedStateHandle = navController.currentBackStackEntry?.savedStateHandle
    LaunchedEffect(navController.currentBackStackEntry) {
        savedStateHandle?.getLiveData<String>(NAV_RETURN_RESULT_LIVENESS_CHECK_PASSED)?.observeForever { result ->
            if (result.isNotEmpty()) {
                onNextStep(result)
                Timber.d("NAV_RETURN_RESULT_LIVENESS_CHECK_PASSED: $result")
            }
            savedStateHandle.remove<String>(NAV_RETURN_RESULT_LIVENESS_CHECK_PASSED) // Ensures it's consumed only once
        }
    }

    SingleEventEffect(sideEffectFlow = viewModel.outputs.identityEvent) {
        when (it) {
            is IdentityEvent.ShowRegionModalList -> {
                showCountryModal = true
            }

            is IdentityEvent.ShowSelfVerificationModal -> {
                showSelfVerificationModal = true
            }

            else -> {

            }
        }
    }
    val regionList = viewModel.outputs.regionList.collectAsState()
    val selectedRegion = viewModel.outputs.selectedRegion.collectAsState()
//    val enableNextStepButton = viewModel.outputs.isEnabledNextStep.collectAsState()
    val enableNextStepButton = remember {
        mutableStateOf(true)
    }

    AppScreen(
        vm = viewModel
    ) {
        Column(modifier = Modifier.padding(horizontal = LocalDimens.current.dimen12)) {
            PageTitle(
                properties = PageTitleProperties(
                    title = stringResource(id = AppCommonR.string._key0139),
                    currentStep = stringResource(id = AppCommonR.string._key0122),
                    stringResource(id = AppCommonR.string._key0122),
                    description = stringResource(id = AppCommonR.string._key0140),
                    showSeparator = true
                )
            )
            InputBox(
                inputBoxModifier = Modifier
                    .clickable {
                        println("OPEN_DROPDOWN")
                        viewModel.inputs.onGetCountryList()
                    },
                value = selectedRegion.value?.title.orEmpty(),
                properties = InputProperties(
                    editable = false,
                    defaultValue = selectedRegion.value?.title.orEmpty(),
                    showClearButton = false,
                    showRequiredStar = true,
                    inputBoxType = InputBoxType.PICKER,
                    onPickerIconClick = { showCountryModal = true },
                    title = stringResource(id = AppCommonR.string._key0141),
                    placeholder = stringResource(id = AppCommonR.string._key0142)
                )
            )
            IdentityInputInformation(viewModel = viewModel)
            Spacer(modifier = Modifier.weight(1f))
            SecondaryButton(
                onClicked = {
                    viewModel.inputs.onTriggerEvent(IdentityEvent.ShowSelfVerificationModal)
                },
                properties = ButtonProperties(
                    text = stringResource(id = AppCommonR.string._key0146),
                    enabled = enableNextStepButton.value
                )
            )
        }
        if (showCountryModal) {
            ModelListBottomSheet(
                modifier = Modifier
                    .fillMaxHeight(0.8f),
                onDismissed = {
                    showCountryModal = false
                }, properties = ModelListBottomSheetProperties(
                    items = regionList.value,
                    searchPlaceholder = stringResource(id = AppCommonR.string._key0144),
                    prefixTitle = stringResource(id = AppCommonR.string.key0102),
                    title = stringResource(id = AppCommonR.string._key0143),
                ),
                onItemClicked = {
                    viewModel.inputs.updateSelectedRegion(it)
                }
            )
        }

        if (showSelfVerificationModal) {
            SelfVerificationModal(onDismissed = {
                showSelfVerificationModal = false
            }, onConfirm = {
                authNavigation.onNavigateSelfVerificationCamera(navController = navController)
            })
        }
    }
}


@Composable
fun IdentityInputInformation(viewModel: IdentityViewModel) {
    var selectedIdentityIdType by remember {
        mutableStateOf("")
    }
    val displayInformation = viewModel.outputs.displayIdentityInformation
    val context = LocalContext.current as FragmentActivity
    TakeImageScreen(onTakeImageResult = { uri, filePath ->
        viewModel.uploadIdentityImage(selectedIdentityIdType, filePath, context)
    }) { cameraPermissionLauncher ->
        LazyColumn {
            items(displayInformation) { item ->
                InputBox(
                    value = item.identityFileName,
                    modifier = Modifier.padding(top = LocalDimens.current.dimen8),
                    properties = InputProperties(
                        showRequiredStar = true,
                        showCheckMark = true,
                        onPickerIconClick = {
                            selectedIdentityIdType = item.id
                            val permission = Manifest.permission.CAMERA
                            cameraPermissionLauncher.launch(permission)
                        },
                        pickerTypeIcon = painterResource(id = AppCommonR.drawable.ic_camera),
                        inputBoxType = InputBoxType.INPUT,
                        title = item.title,
                        onValueChange = {},
                        placeholder = stringResource(id = AppCommonR.string._key0145, item.title)
                    )
                )
            }
        }
    }
}

@Preview(showBackground = true, showSystemUi = true)
@Composable
fun PreviewIdentity() {
    IdentityPage(
        viewModel = viewModel(),
        navController = rememberNavController()
    )
}