package com.siriustech.merit.authentication.screen.usersignup.signup

import com.siriustech.merit.authentication.screen.usersignup.SignupStep
import com.siriustech.merit.authentication.screen.usersignup.signup.step.identification.IdentificationStep

/**
 * Created by <PERSON><PERSON><PERSON><PERSON>
 */
sealed interface SignupEvent {
    data object OnDismissScreen : SignupEvent
    data class OnNavigateToStep(val step: SignupStep) : SignupEvent
}