package com.siriustech.merit.authentication

import com.siriustech.merit.authentication.screen.usersignup.landing.SignupLandingArguments
import com.siriustech.merit.authentication.screen.usersignup.signup.SignupArguments
import kotlinx.serialization.Serializable

/**
 * Created by <PERSON><PERSON>
 */
@Serializable
object AuthenticationRouteName {
    @Serializable
    data class SignupLanding(val args: SignupLandingArguments)
    @Serializable
    data class Signup(val args: SignupArguments)

    @Serializable
    data object SelfVerificationCamera
}