package com.siriustech.merit.authentication.screen.usersignup.signup

import androidx.lifecycle.SavedStateHandle
import com.siriustech.merit.app_common.Constants
import com.siriustech.merit.app_common.theme.AppViewModel
import com.siriustech.merit.authentication.screen.usersignup.SignupStep
import com.siriustech.merit.authentication.screen.usersignup.signup.step.identification.IdentificationStep
import com.siriustech.merit.authentication.screen.usersignup.signup.step.personalifnromation.PersonalInformationStep
import dagger.hilt.android.lifecycle.HiltViewModel
import javax.inject.Inject
import kotlinx.coroutines.channels.Channel
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.SharingStarted
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.combine
import kotlinx.coroutines.flow.receiveAsFlow
import kotlinx.coroutines.flow.stateIn
import kotlinx.coroutines.launch

/**
 * Created by <PERSON><PERSON>tet
 */
@HiltViewModel
class SignupViewModel @Inject constructor(
    savedStateHandle: SavedStateHandle,
) : AppViewModel() {

    private val _signupStep = MutableStateFlow(SignupStep.TOTAL_STEP)
    private val _currentStep = MutableStateFlow(SignupStep.IDENTIFICATION)
    private val _signupEvent = Channel<SignupEvent>()
    private val _currentIdentificationStep = MutableStateFlow(IdentificationStep.BASIC_INFORMATION)
    private val _currentPersonalInformationStep =
        MutableStateFlow(PersonalInformationStep.PERSONAL_DETAILS)

    init {
        val args = SignupArguments.from(savedStateHandle).args
        val resumeStepIndex =
            args.checkOnboardingDisplayData.steps.indexOfFirst { it.status == Constants.STATUS_DONE }
        if (resumeStepIndex >= 0) {
            _currentStep.value = SignupStep.fromParam(resumeStepIndex)
        }
    }

    inner class SignupInputs : BaseInputs() {

        fun onNextSignupStep() {
            val nextStep = _currentStep.value.step + 1
            _currentStep.value = SignupStep.fromParam(nextStep)
            scope.launch {
                _signupEvent.send(SignupEvent.OnNavigateToStep(SignupStep.fromParam(nextStep)))
            }
        }

        fun onPreviousSignupStep() {
            var previousStep = 0
            if (_currentStep.value.step > 0) {
                previousStep = _currentStep.value.step - 1
            }
            scope.launch {
                _signupEvent.send(SignupEvent.OnNavigateToStep(SignupStep.fromParam(previousStep)))
            }
        }

        fun onUpdateIdentificationStep(step: IdentificationStep) {
            _currentIdentificationStep.value = step
        }

        fun onUpdatePersonalInformationStep(step: PersonalInformationStep) {
            _currentPersonalInformationStep.value = step
        }

        fun onHandleBackPressed() = handleBackPressed()

    }


    inner class SignupOutputs : BaseOutputs() {

        val signupStep: StateFlow<Int>
            get() = _signupStep

        val currentStep: StateFlow<SignupStep>
            get() = _currentStep

        val currentIdentificationStep: StateFlow<IdentificationStep>
            get() = _currentIdentificationStep

        val currentPersonalInformationStep: StateFlow<PersonalInformationStep>
            get() = _currentPersonalInformationStep

        val signupEvent: Flow<SignupEvent>
            get() = _signupEvent.receiveAsFlow()

        val showResetButton: Flow<Boolean>
            get() = combine(
                _currentIdentificationStep,
                _currentPersonalInformationStep
            ) { identificationStep, personalInformationStep ->
                when (_currentStep.value) {
                    SignupStep.IDENTIFICATION -> identificationStep.showResetButton
                    SignupStep.PERSONAL_INFORMATION -> personalInformationStep.showResetButton
                    else -> false
                }
            }.stateIn(
                scope,
                initialValue = true,
                started = SharingStarted.WhileSubscribed(5000)
            )
    }

    override val inputs = SignupInputs()
    override val outputs = SignupOutputs()


    private fun handleBackPressed() {
        val step = _currentStep.value
        when (step) {
            SignupStep.IDENTIFICATION -> {
                checkIdentificationStep()
            }

            SignupStep.PERSONAL_INFORMATION -> {
                checkPersonalInformationStep()
            }

            else -> {}
        }
    }

    private fun checkIdentificationStep() {
        scope.launch {
            val currentIdentificationStep =
                IdentificationStep.fromParam(_currentIdentificationStep.value.step)

            when (currentIdentificationStep) {
                IdentificationStep.BASIC_INFORMATION -> {
                    _signupEvent.send(SignupEvent.OnDismissScreen)
                }

                IdentificationStep.EMAIL_VERIFICATION -> {
                    _currentIdentificationStep.value = IdentificationStep.BASIC_INFORMATION
                }

                IdentificationStep.EMAIL_VERIFICATION_SUCCESS -> {
                    _currentIdentificationStep.value = IdentificationStep.EMAIL_VERIFICATION
                }

                IdentificationStep.IDENTITY_VERIFICATION -> {
                    _currentIdentificationStep.value = IdentificationStep.EMAIL_VERIFICATION_SUCCESS
                }

                IdentificationStep.LIVENESS_RESULT -> {
                    _currentIdentificationStep.value = IdentificationStep.IDENTITY_VERIFICATION
                }
            }
        }
    }

    private fun checkPersonalInformationStep() {
        scope.launch {
            val previousStep = _currentStep.value.step - 1
            _currentStep.value = SignupStep.fromParam(previousStep)
            _signupEvent.send(
                SignupEvent.OnNavigateToStep(
                    step = SignupStep.fromParam(
                        previousStep
                    )
                )
            )
        }
    }
}