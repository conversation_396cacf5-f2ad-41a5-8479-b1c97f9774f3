package com.siriustech.merit.authentication.screen.usersignup.signup.step.identification.page.identity

import androidx.compose.runtime.mutableStateListOf
import androidx.compose.runtime.snapshots.SnapshotStateList
import androidx.fragment.app.FragmentActivity
import com.core.network.base.getError
import com.siriustech.merit.apilayer.service.authentication.common.eumeration.DropDownEnumeration
import com.siriustech.merit.apilayer.service.authentication.common.eumeration.EnumerationRequest
import com.siriustech.merit.apilayer.service.authentication.common.eumeration.GetEnumerationUseCase
import com.siriustech.merit.apilayer.service.authentication.common.ocr.CheckOCRUseCase
import com.siriustech.merit.apilayer.service.authentication.common.ocr.OCRRequest
import com.siriustech.merit.apilayer.service.authentication.common.uploadfile.UploadFileUseCase
import com.siriustech.merit.app_common.Constants
import com.siriustech.merit.app_common.Constants.REGION_CHINA
import com.siriustech.merit.app_common.Constants.REGION_HK
import com.siriustech.merit.app_common.component.modalbts.ModalListDataContent
import com.siriustech.merit.app_common.data.AppCache
import com.siriustech.merit.app_common.data.display.IdentityDisplayModel
import com.siriustech.merit.app_common.mapper.EnumerationMapper.toModalDisplayList
import com.siriustech.merit.app_common.theme.AppViewModel
import com.siriustech.merit.app_common.utils.ErrorCode.mapToErrorDisplay
import com.siriustech.merit.app_common.utils.FileUtils.fileToRequestBody
import com.siriustech.merit.authentication.domain.cache.CacheOnboardingUserData
import dagger.hilt.android.lifecycle.HiltViewModel
import id.zelory.compressor.Compressor
import id.zelory.compressor.constraint.size
import java.io.File
import javax.inject.Inject
import kotlinx.coroutines.channels.Channel
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.catch
import kotlinx.coroutines.flow.collectLatest
import kotlinx.coroutines.flow.map
import kotlinx.coroutines.flow.onCompletion
import kotlinx.coroutines.flow.onStart
import kotlinx.coroutines.flow.receiveAsFlow
import kotlinx.coroutines.launch
import okhttp3.MediaType.Companion.toMediaTypeOrNull

/**
 * Created by Hein Htet
 */
@HiltViewModel
class IdentityViewModel @Inject constructor(
    private val getEnumerationUseCase: GetEnumerationUseCase,
    private val cacheOnboardingUserData: CacheOnboardingUserData,
    private val uploadFileUseCase: UploadFileUseCase,
    private val appCache: AppCache,
    private val checkOCRUseCase: CheckOCRUseCase,
) : AppViewModel() {
    private var _regionList = MutableStateFlow<List<ModalListDataContent>>(emptyList())
    private val _identityEvent = Channel<IdentityEvent>(capacity = Channel.BUFFERED)
    private val _selectedRegion = MutableStateFlow<ModalListDataContent?>(null)
    private val _displayIdentityInformation = mutableStateListOf<IdentityDisplayModel>()
    private val _isEnableNextStep = MutableStateFlow(false)

    inner class IdentityInputs : BaseInputs() {
        fun onTriggerEvent(event: IdentityEvent) {
            scope.launch {
                _identityEvent.send(event)
            }
        }

        fun onGetCountryList() = getCountyList()
        fun updateSelectedRegion(item: ModalListDataContent) {
            _regionList.value = _regionList.value.map {
                it.isSelected = it.id == item.id
                it
            }
            _selectedRegion.value = item
            cacheOnboardingUserData.updateRegion(item)
            onUpdateCardInformation()
        }
    }


    inner class IdentityOutputs : BaseOutputs() {
        val regionList: StateFlow<List<ModalListDataContent>>
            get() = _regionList

        val identityEvent: Flow<IdentityEvent>
            get() = _identityEvent.receiveAsFlow()

        val selectedRegion: StateFlow<ModalListDataContent?>
            get() = _selectedRegion

        val displayIdentityInformation: SnapshotStateList<IdentityDisplayModel>
            get() = _displayIdentityInformation

        val isEnabledNextStep: StateFlow<Boolean>
            get() = _isEnableNextStep

    }

    override val inputs = IdentityInputs()
    override val outputs = IdentityOutputs()

    private fun getCountyList() {
        scope.launch {
            if (_regionList.value.isNotEmpty()) {
                _identityEvent.send(IdentityEvent.ShowRegionModalList)
                return@launch
            }
            getEnumerationUseCase(
                param = EnumerationRequest(
                    codeList = listOf(
                        DropDownEnumeration.REGION,
                        DropDownEnumeration.IDENTITY_REGION_HK,
                        DropDownEnumeration.BROKER,
                        DropDownEnumeration.IDENTITY_REGION_OTHERS,
                        DropDownEnumeration.IDENTITY_REGION_CHINA,
                        DropDownEnumeration.BANK_ACCOUNT_TYPE,
                        DropDownEnumeration.PRODUCT_CATEGORY,
                        DropDownEnumeration.ASSET_CLASS,
                        DropDownEnumeration.INDUSTRY,
                        DropDownEnumeration.EDUCATION,
                        DropDownEnumeration.STATEMENT_DELIVERY_METHOD,
                        DropDownEnumeration.CURRENCY

                    ).map { it.name }
                )
            )
                .onStart { inputs.emitLoading(true) }
                .onCompletion { inputs.emitLoading(false) }
                .catch { emitError(it.getError().mapToErrorDisplay()) }
                .map {
                    val modalDisplayContent = it.toModalDisplayList()
                    appCache.updateEnumerationItems(modalDisplayContent)
                    modalDisplayContent
                }
                .collectLatest {
                    val items = appCache.getEnumerationItemsByCode(DropDownEnumeration.REGION.name)
                    _regionList.value = items
                    _identityEvent.send(IdentityEvent.ShowRegionModalList)
                }
        }
    }

    private fun onUpdateCardInformation() {
        _selectedRegion.value?.id.let {
            val displayType = when (it) {
                REGION_HK -> {
                    DropDownEnumeration.IDENTITY_REGION_HK
                }

                REGION_CHINA -> {
                    DropDownEnumeration.IDENTITY_REGION_CHINA

                }

                else -> {
                    DropDownEnumeration.IDENTITY_REGION_OTHERS
                }
            }
            _displayIdentityInformation.clear()
            _isEnableNextStep.value = false
            _displayIdentityInformation.addAll(
                appCache.getEnumerationItemsByCode(displayType.name).map {
                    IdentityDisplayModel(
                        title = it.title,
                        value = it.value,
                        id = it.id
                    )
                })
        }
    }

    fun uploadIdentityImage(id: String, filePath: String, context: FragmentActivity) {
        scope.launch {
            val compressedFile = Compressor.compress(
                context,
                File(filePath),
                scope.coroutineContext
            ) {
                this.size(Constants.MAX_FILE_SIZE)
            }
            val requestBody = fileToRequestBody(
                compressedFile,
                "application/octet-stream".toMediaTypeOrNull()
            )
            uploadFileUseCase(param = requestBody)
                .onStart { inputs.emitLoading(true) }
                .onCompletion { inputs.emitLoading(false) }
                .catch { emitError(it.getError().mapToErrorDisplay()) }
                .collectLatest {
                    val index = _displayIdentityInformation.indexOfFirst { it.id == id }
                    val item = _displayIdentityInformation[index]
                    _displayIdentityInformation[index] = item.copy(identityFilePath = filePath)
                    checkOCR(id, it.dosKey)
                }
        }
    }

    private fun checkOCR(id: String, docKey: String) {
        scope.launch {
            checkOCRUseCase(param = OCRRequest(idType = id, idDosKey = docKey))
                .onStart { inputs.emitLoading(true) }
                .onCompletion { inputs.emitLoading(false) }
                .catch { emitError(it.getError().mapToErrorDisplay()) }
                .collectLatest {
                    val index = _displayIdentityInformation.indexOfFirst { it.id == id }
                    val item = _displayIdentityInformation[index]
                    _displayIdentityInformation[index] =
                        item.copy(identityFileName = it.idNumber, identityDocKey = docKey)
                    cacheOnboardingUserData.onUpdateIdentityData(_displayIdentityInformation.toList())
                    _isEnableNextStep.value =
                        _displayIdentityInformation.none { it.identityFileName.isEmpty() }
                }
        }
    }

}