package com.siriustech.merit.authentication.screen.usersignup.landing

import androidx.compose.foundation.Image
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.ColumnScope
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.verticalScroll
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.remember
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.tooling.preview.Preview
import androidx.fragment.app.FragmentActivity
import androidx.navigation.NavController
import androidx.navigation.compose.rememberNavController
import com.siriustech.merit.app_common.component.button.ButtonProperties
import com.siriustech.merit.app_common.component.button.PrimaryButton
import com.siriustech.merit.app_common.component.button.SecondaryBorderButton
import com.siriustech.merit.app_common.ext.colorTxtParagraph
import com.siriustech.merit.app_common.ext.colorTxtTitle
import com.siriustech.merit.app_common.theme.AppScreen
import com.siriustech.merit.app_common.theme.AppViewModel
import com.siriustech.merit.app_common.theme.LocalDimens
import com.siriustech.merit.app_common.theme.LocalTypography
import com.siriustech.merit.app_common.R as AppCommonR
import androidx.lifecycle.viewmodel.compose.viewModel
import com.siriustech.merit.app_common.Constants
import com.siriustech.merit.authentication.domain.displaydata.checkonboarding.CheckOnboardingDisplayData
import com.siriustech.merit.authentication.screen.navigation.AuthNavigation
import com.siriustech.merit.authentication.screen.navigation.AuthNavigationEntryPoint
import com.siriustech.merit.authentication.screen.usersignup.component.SignUpStepItem
import com.siriustech.merit.authentication.screen.usersignup.signup.SignupArguments
import dagger.hilt.android.EntryPointAccessors

/**
 * Created by Hein Htet
 */

@Composable
fun SignUpLandingScreen(
    navController: NavController,
    viewModel: SignupLandingViewModel = viewModel(),
) {
    LaunchedEffect(key1 = Unit) {
        viewModel.inputs.onGetUserInformation()
    }
    val activity = LocalContext.current as FragmentActivity
    val authNavigation = remember {
        EntryPointAccessors.fromApplication(activity, AuthNavigationEntryPoint::class.java)
            .authNavigation()
    }

    val onboardingStepDisplay = viewModel.outputs.onboardingStepDisplayData.collectAsState()
    val isResume = viewModel.outputs.isResumeOnboardingStep.collectAsState()

    AppScreen(vm = AppViewModel()) {
        Column(
            modifier = Modifier
                .fillMaxSize()
                .padding(horizontal = LocalDimens.current.dimen12)
                .verticalScroll(rememberScrollState()),
        ) {
            SignupHeading()
            Spacer(modifier = Modifier.height(LocalDimens.current.dimen8))
            SignupStep(onboardingStepDisplay.value)
            SignupLandingActions(
                navController,
                authNavigation,
                checkOnboardingDisplayData = onboardingStepDisplay.value,
                isResumeStep = isResume.value
            )
        }
    }
}

@Composable
fun ColumnScope.SignupHeading() {
    Spacer(modifier = Modifier.height(LocalDimens.current.dimen24))
    Image(
        painter = painterResource(id = AppCommonR.drawable.ic_signup_account),
        contentDescription = "SignUp Logo Resource",
        modifier = Modifier.align(Alignment.CenterHorizontally)
    )
    Spacer(modifier = Modifier.height(LocalDimens.current.dimen32))
    Text(
        modifier = Modifier.fillMaxWidth(),
        text = stringResource(id = AppCommonR.string._key0107),
        style = LocalTypography.current.text18.semiBold.colorTxtTitle(),
        textAlign = TextAlign.Center
    )
    Spacer(modifier = Modifier.height(LocalDimens.current.dimen16))
    Text(
        text = stringResource(id = AppCommonR.string._key0108),
        style = LocalTypography.current.text14.light.colorTxtParagraph(),
    )
    Spacer(modifier = Modifier.height(LocalDimens.current.dimen8))
    Text(
        text = stringResource(id = AppCommonR.string._key0109),
        style = LocalTypography.current.text14.medium.colorTxtTitle(),
        textAlign = TextAlign.Start
    )
    Spacer(modifier = Modifier.height(LocalDimens.current.dimen8))
    Text(
        text = stringResource(id = AppCommonR.string._key0110),
        style = LocalTypography.current.text14.light.colorTxtParagraph(),
    )
    Spacer(modifier = Modifier.height(LocalDimens.current.dimen12))
    Image(
        painter = painterResource(id = AppCommonR.drawable.ic_separatoer),
        contentDescription = "Separator Image Resource"
    )
}

@Composable
fun SignupStep(onboardingStepDisplay: CheckOnboardingDisplayData) {
    SignUpStepItem(
        modifier = Modifier,
        isFinish = onboardingStepDisplay.steps.findLast { it.code == Constants.IDENTIFICATION_STEP }?.status == Constants.STATUS_DONE,
        prefix = stringResource(id = AppCommonR.string.key0003),
        description = stringResource(id = AppCommonR.string.key0008)
    )
    SignUpStepItem(
        modifier = Modifier,
        isFinish = onboardingStepDisplay.steps.findLast { it.code == Constants.PERSONAL_INFO_STEP }?.status == Constants.STATUS_DONE,
        prefix = stringResource(id = AppCommonR.string.key0004),
        description = stringResource(id = AppCommonR.string.key0009)
    )
    SignUpStepItem(
        modifier = Modifier,
        isFinish = onboardingStepDisplay.steps.findLast { it.code == Constants.RISK_ASSESSMENT_STEP }?.status == Constants.STATUS_DONE,
        prefix = stringResource(id = AppCommonR.string.key0005),
        description = stringResource(id = AppCommonR.string.key0010)
    )
    SignUpStepItem(
        modifier = Modifier,
        isFinish = onboardingStepDisplay.steps.findLast { it.code == Constants.DECLARATION_STEP }?.status == Constants.STATUS_DONE,
        prefix = stringResource(id = AppCommonR.string._key0114),
        description = stringResource(id = AppCommonR.string._key0111)
    )
    SignUpStepItem(
        modifier = Modifier,
        isFinish = onboardingStepDisplay.steps.findLast { it.code == Constants.DOCUMENT_UPLOAD_STEP }?.status == Constants.STATUS_DONE,
        prefix = stringResource(id = AppCommonR.string._key0115),
        description = stringResource(id = AppCommonR.string._key0112)
    )
    SignUpStepItem(
        modifier = Modifier,
        isFinish = onboardingStepDisplay.steps.findLast { it.code == Constants.CONFIRM_STEP }?.status == Constants.STATUS_DONE,
        prefix = stringResource(id = AppCommonR.string._key0116),
        description = stringResource(id = AppCommonR.string._key0113)
    )
}

@Composable
fun SignupLandingActions(
    navController: NavController,
    authNavigation: AuthNavigation, checkOnboardingDisplayData: CheckOnboardingDisplayData,
    isResumeStep : Boolean = false
) {
    Row(
        horizontalArrangement = Arrangement.SpaceBetween,
        modifier = Modifier
            .fillMaxWidth()
            .padding(top = LocalDimens.current.dimen24)
    ) {
        SecondaryBorderButton(
            modifier = Modifier
                .weight(0.5f), properties = ButtonProperties(
                text = stringResource(
                    id = AppCommonR.string.key0015,
                )
            ),
            onClicked = {
                navController.popBackStack()
            }
        )
        Spacer(modifier = Modifier.width(LocalDimens.current.dimen16))
        PrimaryButton(
            modifier = Modifier.weight(0.5f), properties = ButtonProperties(
                text = stringResource(
                    id = if(!isResumeStep) AppCommonR.string.key0215 else AppCommonR.string._key0117
                )
            ),
            onClicked = {
                authNavigation.onNavigateToSignup(
                    navController,
                    SignupArguments(checkOnboardingDisplayData)
                )
            }
        )
    }
}

@Preview(showBackground = true, showSystemUi = true)
@Composable
fun PreviewSignUpLandingScreen() {
    val activity = LocalContext.current as FragmentActivity
    val authNavigation = remember {
        EntryPointAccessors.fromApplication(activity, AuthNavigationEntryPoint::class.java)
            .authNavigation()
    }
    Column {
        SignupHeading()
        SignupStep(onboardingStepDisplay = CheckOnboardingDisplayData())
        SignupLandingActions(
            navController = rememberNavController(), authNavigation = authNavigation,
            CheckOnboardingDisplayData()
        )
    }
}