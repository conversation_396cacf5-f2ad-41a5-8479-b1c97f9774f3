package com.siriustech.merit.authentication.component

import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.padding
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.tooling.preview.Preview
import com.siriustech.merit.app_common.component.separator.SeparatorLine
import com.siriustech.merit.app_common.component.textfield.InputBox
import com.siriustech.merit.app_common.component.textfield.InputProperties
import com.siriustech.merit.app_common.ext.colorTxtParagraph
import com.siriustech.merit.app_common.ext.colorTxtTitle
import com.siriustech.merit.app_common.theme.LocalDimens
import com.siriustech.merit.app_common.theme.LocalTypography

/**
 * Created by Hein Htet
 */

@Composable
fun ConfigCredentialInput(
    modifier: Modifier = Modifier,
    properties: ConfigCredentialInputProperties = ConfigCredentialInputProperties(),
) {
    Column(modifier) {
        Text(
            text = properties.title,
            style = LocalTypography.current.text14.medium.colorTxtTitle()
        )
        Text(
            modifier = Modifier.padding(top = LocalDimens.current.dimen16),
            text = properties.description,
            style = LocalTypography.current.text14.light.colorTxtParagraph()
        )
        SeparatorLine(modifier = Modifier.padding(vertical = LocalDimens.current.dimen16))
        InputBox(
            onTextChange = properties.onInputTextChanged,
            properties = InputProperties(
                title = properties.inputTitle,
                placeholder = properties.inputPlaceholder,
                titleLabelTextStyle = LocalTypography.current.text14.medium.colorTxtTitle()
            )
        )
    }
}

data class ConfigCredentialInputProperties(
    val title: String = "",
    val description: String = "",
    val inputTitle: String = "",
    val inputPlaceholder: String = "",
    val onInputTextChanged: (value: String) -> Unit = {},
)


@Preview(showBackground = true, showSystemUi = true)
@Composable
fun PreviewConfigCredentialInput() {
    ConfigCredentialInput(properties = ConfigCredentialInputProperties(
        title = "Recovering your PIN",
        description = "We will send a verification code to your email address that you registered for Merit App.",
        inputTitle = "Email",
        inputPlaceholder = "Enter your email"
    ))
}