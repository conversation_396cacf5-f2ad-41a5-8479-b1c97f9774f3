package com.siriustech.merit.authentication.screen.usersignup.signup

import androidx.activity.compose.BackHandler
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.pager.HorizontalPager
import androidx.compose.foundation.pager.rememberPagerState
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.ui.Modifier
import androidx.compose.ui.tooling.preview.Preview
import androidx.hilt.navigation.compose.hiltViewModel
import androidx.lifecycle.viewmodel.compose.viewModel
import androidx.navigation.NavController
import androidx.navigation.compose.rememberNavController
import com.siriustech.core_ui_compose.base.SingleEventEffect
import com.siriustech.merit.app_common.component.header.CommonToolbarWithBackAndResetMenu
import com.siriustech.merit.app_common.component.header.CommonToolbarWithBackMenu
import com.siriustech.merit.app_common.component.indicator.HorizontalPagerIndicator
import com.siriustech.merit.app_common.theme.AppScreen
import com.siriustech.merit.app_common.theme.LocalDimens
import com.siriustech.merit.authentication.screen.usersignup.SignupStep
import com.siriustech.merit.authentication.screen.usersignup.signup.step.identification.IdentificationStepScreen
import kotlinx.coroutines.launch
import timber.log.Timber

/**
 * Created by Hein Htet
 */
@Composable
fun SignupScreen(
    navController: NavController,
    viewModel: SignupViewModel = hiltViewModel(),
) {
    val coroutineScope = rememberCoroutineScope()
    val identificationStep = viewModel.outputs.currentIdentificationStep.collectAsState()
    val currentSignupStep = viewModel.outputs.currentStep.collectAsState()
    val pagerState = rememberPagerState(
        pageCount = { SignupStep.TOTAL_STEP },
        initialPage = currentSignupStep.value.step
    )

    fun onNavigateToPage(index: Int) {
        coroutineScope.launch {
            pagerState.animateScrollToPage(index)
        }
    }

    fun onHandleBack() {
        viewModel.inputs.onHandleBackPressed()
    }

    BackHandler(enabled = true, onBack = {
        onHandleBack()
    })

    SingleEventEffect(sideEffectFlow = viewModel.outputs.signupEvent) {
        when (it) {
            is SignupEvent.OnDismissScreen -> {
                navController.popBackStack()
            }

            is SignupEvent.OnNavigateToStep -> {
                onNavigateToPage(it.step.step)
            }

            else -> {}
        }
    }
    val showResetMenu = viewModel.outputs.showResetButton.collectAsState(initial = true)
    AppScreen(vm = viewModel, toolbar = {
        Timber.d("SignUpScreen ${showResetMenu.value}")
        if (showResetMenu.value) {
            CommonToolbarWithBackAndResetMenu(
                annotatedTitleString = SignupStep.fromParam(pagerState.currentPage)
                    .signupToolbarTitle()
            )
        } else {
            CommonToolbarWithBackMenu(
                annotatedTitleString = SignupStep.fromParam(pagerState.currentPage)
                    .signupToolbarTitle()
            )
        }
    }) {
        Column(modifier = Modifier) {
            HorizontalPagerIndicator(
                pagerState = pagerState,
                modifier = Modifier.padding(top = LocalDimens.current.dimen32)
            )
            HorizontalPager(
                userScrollEnabled = false,
                state = pagerState,
                modifier = Modifier
                    .fillMaxSize()
                    .padding(top = LocalDimens.current.dimen16)
            ) {
                val step = SignupStep.fromParam(pagerState.currentPage)
                when (step) {
                    SignupStep.IDENTIFICATION -> IdentificationStepScreen(
                        navController = navController,
                        onUpdateCurrentStep = {
                            viewModel.inputs.onUpdateIdentificationStep(
                                it
                            )
                        },
                        identificationStep = identificationStep.value,
                        onSignupNextStep = {
                            viewModel.inputs.onNextSignupStep()
                        }
                    )

                    SignupStep.PERSONAL_INFORMATION -> Text(text = "PERSONAL_INFORMATION")
                    SignupStep.DECLARATION -> {}
                    SignupStep.RISK_ASSESSMENT -> {}
                    SignupStep.DOCUMENT_UPLOAD -> {}
                }
            }
        }
    }
}


@Preview(showBackground = true, showSystemUi = true)
@Composable
fun PreviewSignupScreen() {
    SignupScreen(rememberNavController(), viewModel())
}