package com.siriustech.merit.authentication.screen.usersignup.signup.step.identification

import com.siriustech.merit.apilayer.service.authentication.common.uploadfile.UploadFileUseCase
import com.siriustech.merit.app_common.theme.AppViewModel
import dagger.hilt.android.lifecycle.HiltViewModel
import javax.inject.Inject
import kotlinx.coroutines.channels.Channel
import kotlinx.coroutines.delay
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.flow.receiveAsFlow
import kotlinx.coroutines.launch

/**
 * Created by He<PERSON>tet
 */
@HiltViewModel
class IdentificationViewModel @Inject constructor(

) : AppViewModel() {

    private val _identificationStep = MutableStateFlow(IdentificationStep.TOTAL_STEP)
    private val _currentStep = MutableStateFlow(IdentificationStep.BASIC_INFORMATION.step)
    private val _identificationEvent = Channel<IdentificationEvent>()


    init {
        scope.launch {
//            delay(2000)
//            _currentStep.value = IdentificationStep.IDENTITY_VERIFICATION.step
//            _identificationEvent.send(IdentificationEvent.OnNavigateToStep(_currentStep.value))
        }
    }

    inner class IdentificationInputs : BaseInputs() {


        private var _enableToNavigationAction =
            true // avoid called multiple time due to widget rendering

        fun navigateToNextStep() {
            scope.launch {
                if (_enableToNavigationAction) {
                    _enableToNavigationAction = false
                    _currentStep.value += 1
                    _identificationEvent.send(IdentificationEvent.OnNavigateToStep(_currentStep.value))
                    delay(1000)
                    _enableToNavigationAction = true
                }
            }
        }

        fun onUpdateCurrentStep(step: IdentificationStep) {
            _currentStep.value = step.step
        }

        fun onNavigateToStep(step: IdentificationStep) {

        }
    }

    inner class IdentificationOutputs : BaseOutputs() {
        val currentStep: StateFlow<Int>
            get() = _currentStep

        val identificationEvent: Flow<IdentificationEvent>
            get() = _identificationEvent.receiveAsFlow()
    }


    override val inputs = IdentificationInputs()
    override val outputs = IdentificationOutputs()
}