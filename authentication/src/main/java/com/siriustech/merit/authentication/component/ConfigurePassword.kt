package com.siriustech.merit.authentication.component

import android.util.Log
import androidx.compose.foundation.Image
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.text.KeyboardActions
import androidx.compose.foundation.text.KeyboardOptions
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateMapOf
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.runtime.snapshots.SnapshotStateMap
import androidx.compose.ui.Modifier
import androidx.compose.ui.focus.FocusDirection
import androidx.compose.ui.platform.LocalFocusManager
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.text.input.ImeAction
import androidx.compose.ui.tooling.preview.Preview
import com.siriustech.merit.app_common.Constants
import com.siriustech.merit.app_common.R
import com.siriustech.merit.app_common.component.textfield.InputBox
import com.siriustech.merit.app_common.component.textfield.InputProperties
import com.siriustech.merit.app_common.ext.colorTxtInactive
import com.siriustech.merit.app_common.ext.colorTxtTitle
import com.siriustech.merit.app_common.theme.LocalDimens
import com.siriustech.merit.app_common.theme.LocalTypography
import com.siriustech.merit.app_common.utils.Validation.hasAtLeastOneLowercase
import com.siriustech.merit.app_common.utils.Validation.hasAtLeastOneNumber
import com.siriustech.merit.app_common.utils.Validation.hasAtLeastOneSpecialChar
import com.siriustech.merit.app_common.utils.Validation.hasAtLeastOneUppercase

/**
 * Created by Hein Htet
 */

@Composable
fun ConfigurePassword(
    modifier: Modifier = Modifier,
    defaultPassword: String = "",
    defaultConfirmPassword: String = "",
    onResetCount: Int = 0,
    properties: ConfigurePasswordProperties = ConfigurePasswordProperties(),
    onPasswordChanged: (value: String) -> Unit = {},
    onConfirmPasswordChanged: (value: String) -> Unit = {},
    onValidate: (validPassword: Boolean) -> Unit = {},
) {
    val defaultRulesMap = mutableStateMapOf(
        Constants.AT_LEAST_8_CHARACTERS to false,
        Constants.AT_LEAST_1_UPPERCASE to false,
        Constants.AT_LEAST_1_LOWERCASE to false,
        Constants.AT_LEAST_1_NUMBER to false,
        Constants.AT_LEAST_1_SPECIAL_CHAR to false,
    )

    var rules = remember {
        defaultRulesMap
    }

    var password by remember {
        mutableStateOf(defaultPassword)
    }
    var confirmPassword by remember {
        mutableStateOf(defaultConfirmPassword)
    }

    val focusManager = LocalFocusManager.current

    LaunchedEffect(onResetCount) {
        password = ""
        confirmPassword = ""
        rules.clear()
        rules = defaultRulesMap
    }


    LaunchedEffect(key1 = password, key2 = confirmPassword) {
        val valid = rules.filter { !it.value }
            .isEmpty() && password == confirmPassword && password.isNotEmpty() && confirmPassword.isNotEmpty()
        onValidate(valid)
    }

    Column(
        modifier = Modifier
            .fillMaxWidth()
            .then(modifier)
    ) {
        InputBox(
            value = password,
            onTextChange = {
                password = it
                onPasswordChanged(it)
            },
            properties = InputProperties(
                titleLabelTextStyle = properties.passwordTitleStyle
                    ?: LocalTypography.current.text14.regular.colorTxtTitle(),
                title = properties.passwordTitle,
                placeholder = properties.placeholderPassword,
                isPasswordMode = true,
                showRequiredStar = properties.showRequiredStar,
                keyboardOptions = KeyboardOptions(imeAction = ImeAction.Next),
                keyboardActions = KeyboardActions(
                    onNext = { focusManager.moveFocus(FocusDirection.Down) }
                )

            )
        )
        Spacer(modifier = Modifier.height(LocalDimens.current.dimen8))
        PasswordMatching(password, rules)
        Spacer(modifier = Modifier.height(LocalDimens.current.dimen8))
        InputBox(
            value = confirmPassword,
            onTextChange = {
                confirmPassword = it
                onConfirmPasswordChanged(it)
            },
            properties = InputProperties(
                titleLabelTextStyle = properties.confirmPasswordTitleStyle
                    ?: LocalTypography.current.text14.regular.colorTxtTitle(),
                title = properties.confirmPasswordTitle,
                placeholder = properties.placeholderConfirmPassword,
                isPasswordMode = true,
                showRequiredStar = true,
                keyboardOptions = KeyboardOptions(imeAction = ImeAction.Done),
            )
        )
        Spacer(modifier = Modifier.height(LocalDimens.current.dimen8))
        PasswordRuleItem(
            isMatch = password.isNotEmpty() && confirmPassword.isNotEmpty() && password == confirmPassword,
            title = stringResource(id = R.string.key0028)
        )
    }
}

@Composable
fun PasswordMatching(
    password: String = "",
    rules: SnapshotStateMap<String, Boolean> = mutableStateMapOf(),
) {


    LaunchedEffect(password) {
        rules[Constants.AT_LEAST_8_CHARACTERS] = password.length >= 8
        rules[Constants.AT_LEAST_1_UPPERCASE] = password.hasAtLeastOneUppercase()
        rules[Constants.AT_LEAST_1_LOWERCASE] = password.hasAtLeastOneLowercase()
        rules[Constants.AT_LEAST_1_NUMBER] = password.hasAtLeastOneNumber()
        rules[Constants.AT_LEAST_1_SPECIAL_CHAR] = password.hasAtLeastOneSpecialChar()
        Log.d("PasswordMatching", "password changed: $password")
    }

    Column(modifier = Modifier.fillMaxWidth()) {
        PasswordRuleItem(
            isMatch = rules[Constants.AT_LEAST_8_CHARACTERS] == true,
            title = stringResource(id = R.string.key0022)
        )
        PasswordRuleItem(
            isMatch = rules[Constants.AT_LEAST_1_UPPERCASE] == true,
            title = stringResource(id = R.string.key0023)
        )
        PasswordRuleItem(
            isMatch = rules[Constants.AT_LEAST_1_LOWERCASE] == true,
            title = stringResource(id = R.string.key0024)
        )
        PasswordRuleItem(
            isMatch = rules[Constants.AT_LEAST_1_NUMBER] == true,
            title = stringResource(id = R.string.key0025)
        )
        PasswordRuleItem(
            isMatch = rules[Constants.AT_LEAST_1_SPECIAL_CHAR] == true,
            title = stringResource(id = R.string.key0026)
        )
    }
}

@Composable
fun PasswordRuleItem(isMatch: Boolean, title: String) {
    Row {
        Image(
            painter = painterResource(id = if (isMatch) R.drawable.ic_match else R.drawable.ic_unmatch),
            contentDescription = "Un match image resource"
        )
        Spacer(modifier = Modifier.width(LocalDimens.current.dimen8))
        Text(text = title, style = LocalTypography.current.text12.light.colorTxtInactive())
    }
}


data class ConfigurePasswordProperties(
    val passwordTitle: String = "",
    val placeholderPassword: String = "",
    val placeholderConfirmPassword: String = "",
    val confirmPasswordTitle: String = "",
    val passwordTitleStyle: TextStyle? = null,
    val confirmPasswordTitleStyle: TextStyle? = null,
    val showRequiredStar: Boolean = false,
)


@Preview(showBackground = true, showSystemUi = true, locale = "zh")
@Composable
fun PreviewConfigurePassword() {
    ConfigurePassword(
        modifier = Modifier.padding(LocalDimens.current.dimen12),
        properties = ConfigurePasswordProperties(
            passwordTitle = "Password",
            placeholderPassword = "Enter new password",
            confirmPasswordTitle = "Repeat Password",
            placeholderConfirmPassword = "Enter the new password",
            showRequiredStar = true
        )
    )
}

