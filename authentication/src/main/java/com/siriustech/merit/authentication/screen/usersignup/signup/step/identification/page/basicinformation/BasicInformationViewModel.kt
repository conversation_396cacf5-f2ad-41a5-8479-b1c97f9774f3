package com.siriustech.merit.authentication.screen.usersignup.signup.step.identification.page.basicinformation

import com.siriustech.merit.apilayer.service.authentication.register.RegisterUseCase
import com.siriustech.merit.app_common.theme.AppViewModel
import com.siriustech.merit.authentication.domain.cache.CacheOnboardingUserData
import com.siriustech.merit.authentication.domain.displaydata.signup.identification.BasicInformationDisplayData
import dagger.hilt.android.lifecycle.HiltViewModel
import javax.inject.Inject
import kotlinx.coroutines.channels.Channel
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.SharingStarted
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.map
import kotlinx.coroutines.flow.receiveAsFlow
import kotlinx.coroutines.flow.stateIn
import kotlinx.coroutines.launch

/**
 * Created by <PERSON><PERSON><PERSON>
 */
@HiltViewModel
class BasicInformationViewModel @Inject constructor(
    private val cacheOnboardingUserData: CacheOnboardingUserData,
) : AppViewModel() {

    private val _basicInformationDisplayData =
        MutableStateFlow(cacheOnboardingUserData.basicInformationDisplayData)

    private val _basicInformationEvent = Channel<BasicInformationEvent>()

    inner class BasicInformationInputs : BaseInputs() {

        fun updateChineseName(value: String) {
            _basicInformationDisplayData.value.copy(chineseName = value)
                .updateBasicInformationData()
        }

        fun updateEnglishName(value: String) {
            _basicInformationDisplayData.value.copy(englishName = value)
                .updateBasicInformationData()
        }

        fun updateReferenceNumber(value: String) {
            _basicInformationDisplayData.value.copy(referenceNumber = value)
                .updateBasicInformationData()
        }

        fun updatePhoneNumber(value: String) {
            _basicInformationDisplayData.value.copy(phoneNumber = value)
                .updateBasicInformationData()
        }

        fun updateEmailAddress(value: String) {
            _basicInformationDisplayData.value.copy(email = value).updateBasicInformationData()
        }

        fun updatePassword(value: String) {
            _basicInformationDisplayData.value.copy(password = value).updateBasicInformationData()
        }

        fun updateRepeatPassword(value: String) {
            _basicInformationDisplayData.value.copy(confirmPassword = value)
                .updateBasicInformationData()
        }

        fun onUserRegister() {
           scope.launch {
               _basicInformationEvent.send(BasicInformationEvent.OnEmailVerification)
           }
        }
    }

    inner class BasicInformationOutputs : BaseOutputs() {

        val basicInformationEvent: Flow<BasicInformationEvent>
            get() = _basicInformationEvent.receiveAsFlow()

        val basicInformationDisplayData: StateFlow<BasicInformationDisplayData>
            get() = _basicInformationDisplayData

        val nextButtonEnabled = _basicInformationDisplayData
            .map { it.isValidate() }
            .stateIn(
                scope,
                initialValue = false,
                started = SharingStarted.WhileSubscribed(5000)
            )

    }


    override val inputs: BasicInformationInputs = BasicInformationInputs()
    override val outputs: BasicInformationOutputs = BasicInformationOutputs()


    private fun BasicInformationDisplayData.updateBasicInformationData() {
        cacheOnboardingUserData.onUpdateBasicInformation(this)
        _basicInformationDisplayData.value = this
    }

}