package com.siriustech.merit.authentication.screen.usersignup.component

import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.tooling.preview.Preview
import com.siriustech.merit.app_common.R
import com.siriustech.merit.app_common.component.separator.SeparatorLine
import com.siriustech.merit.app_common.ext.AttributeStringData
import com.siriustech.merit.app_common.ext.buildAttrString
import com.siriustech.merit.app_common.ext.colorTxtInactive
import com.siriustech.merit.app_common.ext.colorTxtParagraph
import com.siriustech.merit.app_common.ext.colorTxtTitle
import com.siriustech.merit.app_common.theme.LocalDimens
import com.siriustech.merit.app_common.theme.LocalTypography

/**
 * Created by <PERSON><PERSON>
 */

@Composable
fun PageTitle(properties: PageTitleProperties = PageTitleProperties()) {
    Text(
        text = buildAttrString(
            attributedString = listOf(
                AttributeStringData(
                    text = properties.title,
                    textStyle = LocalTypography.current.text14.medium.colorTxtTitle(),
                ),
                AttributeStringData(
                    text = " (${
                        properties.currentStep.plus("/")
                            .plus(properties.totalStep)
                    })",
                    textStyle = LocalTypography.current.text14.light.colorTxtInactive(),
                ),
            )
        ), modifier = Modifier.fillMaxWidth(),
        textAlign = TextAlign.Center,
        style = LocalTypography.current.text14.medium.colorTxtTitle()
    )
    if (properties.description.isNotEmpty()) {
        Text(
            modifier = Modifier.padding(top = LocalDimens.current.dimen16),
            text = properties.description,
            style = LocalTypography.current.text14.light.colorTxtParagraph()
        )
    }
    if (properties.showSeparator) {
        SeparatorLine(Modifier.padding(vertical = LocalDimens.current.dimen16))
    }
}

@Preview(showBackground = true, showSystemUi = true)
@Composable
fun PreviewPageTitle() {
    PageTitle(
        properties = PageTitleProperties(
            title = "Basic Information",
            currentStep = "1",
            totalStep = "3",
            showSeparator = true
        )
    )
}

data class PageTitleProperties(
    val title: String = "",
    val currentStep: String = "",
    val totalStep: String = "",
    val description: String = "",
    val showSeparator: Boolean = false,
)