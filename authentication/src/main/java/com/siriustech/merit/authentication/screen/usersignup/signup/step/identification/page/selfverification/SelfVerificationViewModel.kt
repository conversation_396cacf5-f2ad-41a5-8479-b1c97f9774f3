package com.siriustech.merit.authentication.screen.usersignup.signup.step.identification.page.selfverification

import android.annotation.SuppressLint
import android.os.CountDownTimer
import com.core.network.base.getError
import com.siriustech.core_ui_compose.model.ErrorDisplay
import com.siriustech.merit.apilayer.service.authentication.common.uploadfile.UploadFileUseCase
import com.siriustech.merit.apilayer.service.authentication.liveness.LivenessCheckResquest
import com.siriustech.merit.apilayer.service.authentication.liveness.LivenessCheckUseCase
import com.siriustech.merit.app_common.Constants
import com.siriustech.merit.app_common.theme.AppViewModel
import com.siriustech.merit.app_common.utils.ErrorCode.mapToErrorDisplay
import com.siriustech.merit.app_common.utils.FileUtils
import com.siriustech.merit.authentication.domain.cache.CacheOnboardingUserData
import dagger.hilt.android.lifecycle.HiltViewModel
import java.io.File
import javax.inject.Inject
import kotlinx.coroutines.ExperimentalCoroutinesApi
import kotlinx.coroutines.channels.Channel
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.catch
import kotlinx.coroutines.flow.collectLatest
import kotlinx.coroutines.flow.flatMapLatest
import kotlinx.coroutines.flow.map
import kotlinx.coroutines.flow.onCompletion
import kotlinx.coroutines.flow.onStart
import kotlinx.coroutines.flow.receiveAsFlow
import kotlinx.coroutines.launch
import okhttp3.MediaType.Companion.toMediaTypeOrNull

/**
 * Created by Hein Htet
 */
@HiltViewModel
class SelfVerificationViewModel @Inject constructor(
    private val uploadFileUseCase: UploadFileUseCase,
    private val livenessCheckUseCase: LivenessCheckUseCase,
    private val cacheOnboardingUserData: CacheOnboardingUserData,
) : AppViewModel() {

    private val _event = Channel<SelfVerificationEvent>()
    private val _displayTime = MutableStateFlow("10:00")


    inner class SelfVerificationInputs : BaseInputs() {

        fun onStartRecording() {
            startTimer()
        }

        fun onLivenessCheck(filePath: String) {
            livenessCheck(filePath)
        }
    }

    inner class SelfVerificationOutputs : BaseOutputs() {
        val event: Flow<SelfVerificationEvent>
            get() = _event.receiveAsFlow()

        val time: StateFlow<String>
            get() = _displayTime

    }

    override val inputs: SelfVerificationInputs = SelfVerificationInputs()
    override val outputs: SelfVerificationOutputs = SelfVerificationOutputs()


    @OptIn(ExperimentalCoroutinesApi::class)
    private fun livenessCheck(filePath: String) {
        val requestBody = FileUtils.fileToRequestBody(
            File(filePath),
            "application/octet-stream".toMediaTypeOrNull()
        )
        scope.launch {
            uploadFileUseCase(param = requestBody)
                .onStart { inputs.emitLoading(true) }
                .onCompletion { inputs.emitLoading(false) }
                .flatMapLatest { docRes ->
                    val identityInformation = cacheOnboardingUserData.identityDisplayData
                    val imageDocKey = identityInformation.firstOrNull()?.identityDocKey.orEmpty()
                    val idType = identityInformation.firstOrNull()?.id.orEmpty()
                    val idNumber = identityInformation.firstOrNull()?.identityFileName.orEmpty()
                    livenessCheckUseCase(
                        param = LivenessCheckResquest(
                            imageDosKey = imageDocKey,
                            idType = idType,
                            idCode = idNumber,
                            videoDosKey = docRes.dosKey
                        )
                    )
                }
                .catch { emitError(it.getError().mapToErrorDisplay()) }
                .collectLatest { res ->
                    cacheOnboardingUserData.updateLivenessCheckResult(if(res.pass) Constants.STATUS_PASSED else Constants.STATUS_FAILED)
                    if (res.pass) {
                        _event.send(SelfVerificationEvent.LivenessCheckPassed)
                    } else {
                        _event.send(SelfVerificationEvent.LivenessCheckFailed)
                        inputs.emitError(
                            ErrorDisplay(
                                code = "101",
                                message = "Failed to check Liveness documents"
                            )
                        )
                    }
                }
        }
    }

    // Function to start the countdown timer
    fun startTimer() {
        val totalTime = 10 * 1000L // 10 seconds in milliseconds
        val timer = object : CountDownTimer(totalTime, 10) {

            @SuppressLint("DefaultLocale")
            override fun onTick(millisUntilFinished: Long) {
                val seconds = millisUntilFinished / 1000
                val milliseconds = (millisUntilFinished % 1000) / 10
                val time = String.format("%02d:%02d", seconds, milliseconds)
                _displayTime.value = time
            }

            override fun onFinish() {
                _displayTime.value = "00:00"
                scope.launch {
                    _event.send(SelfVerificationEvent.RecordingCompleted)
                }
            }
        }
        timer.start()
    }
}