package com.siriustech.merit.authentication.screen.agentsignup

import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxHeight
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.remember
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.tooling.preview.Preview
import androidx.fragment.app.FragmentActivity
import androidx.lifecycle.viewmodel.compose.viewModel
import androidx.navigation.NavController
import androidx.navigation.compose.rememberNavController
import com.siriustech.merit.app_common.component.button.ButtonProperties
import com.siriustech.merit.app_common.component.button.PrimaryButton
import com.siriustech.merit.app_common.component.button.SecondaryBorderButton
import com.siriustech.merit.app_common.ext.AttributeStringData
import com.siriustech.merit.app_common.ext.buildAttrString
import com.siriustech.merit.app_common.ext.colorTxtParagraph
import com.siriustech.merit.app_common.ext.colorTxtTitle
import com.siriustech.merit.app_common.navigation.argument.authentication.AgentSignupLandingArguments
import com.siriustech.merit.app_common.theme.AppScreen
import com.siriustech.merit.app_common.theme.LocalAppColor
import com.siriustech.merit.app_common.theme.LocalDimens
import com.siriustech.merit.app_common.theme.LocalTypography
import com.siriustech.merit.authentication.screen.navigation.AuthNavigationEntryPoint
import dagger.hilt.android.EntryPointAccessors
import com.siriustech.merit.app_common.R as AppCommonR

/**
 * Created by Hein Htet
 */

@Composable
fun AgentSignupLandingScreen(
    viewModel: AgentSignupViewModel = viewModel(),
    navController: NavController,
    arguments: AgentSignupLandingArguments
) {
    val activity = LocalContext.current as FragmentActivity

    val authNavigation = remember {
        EntryPointAccessors.fromApplication(activity, AuthNavigationEntryPoint::class.java)
            .authNavigation()
    }


    AppScreen(vm = viewModel) {
        Column(
            modifier = Modifier
                .fillMaxSize()
                .padding(horizontal = LocalDimens.current.dimen16),
            horizontalAlignment = Alignment.CenterHorizontally
        ) {
            Spacer(modifier = Modifier.fillMaxHeight(0.15f))
            Image(
                painter = painterResource(id = AppCommonR.drawable.ic_agent_landing),
                contentDescription = ""
            )
            Spacer(modifier = Modifier.height(LocalDimens.current.dimen32))
            Text(
                text = stringResource(id = AppCommonR.string.key0361),
                style = LocalTypography.current.text18.semiBold.colorTxtTitle(),
                modifier = Modifier
            )
            Spacer(modifier = Modifier.height(LocalDimens.current.dimen16))
            Text(
                text = stringResource(id = AppCommonR.string.key0372),
                style = LocalTypography.current.text14.light.colorTxtParagraph(),
                modifier = Modifier
                    .fillMaxWidth(),
                textAlign = TextAlign.Start
            )
            Spacer(modifier = Modifier.height(LocalDimens.current.dimen16))
            Image(
                painter = painterResource(id = AppCommonR.drawable.ic_separatoer),
                contentDescription = "Separator"
            )

            AgentStepItem(
                modifier = Modifier,
                prefix = stringResource(id = AppCommonR.string.key0003),
                description = stringResource(
                    id = AppCommonR.string.key0008
                )
            )
            Spacer(modifier = Modifier.height(LocalDimens.current.dimen8))
            AgentStepItem(
                modifier = Modifier,
                prefix = stringResource(id = AppCommonR.string.key0004),
                description = stringResource(id = AppCommonR.string.key0373)
            )
            Spacer(modifier = Modifier.height(LocalDimens.current.dimen24))
            PrimaryButton(
                onClicked = {
                    // on start agent signup
                    authNavigation.onNavigateToAgentSignup(navController,arguments)
                },
                properties = ButtonProperties(text = stringResource(id = AppCommonR.string.key0215))
            )
            Spacer(modifier = Modifier.height(LocalDimens.current.dimen16))
            SecondaryBorderButton(
                onClicked = {
                    navController.popBackStack()
                },
                properties = ButtonProperties(text = stringResource(id = AppCommonR.string.key0015))
            )
        }
    }
}

@Composable
fun AgentStepItem(modifier: Modifier, prefix: String, description: String) {
    Box(
        modifier = Modifier
            .fillMaxWidth()
            .height(LocalDimens.current.dimen44)
            .background(LocalAppColor.current.bgTone)
            .clip(RoundedCornerShape(LocalDimens.current.dimen4))
            .padding(horizontal = LocalDimens.current.dimen12)
            .then(modifier),
        contentAlignment = Alignment.Center
    ) {
        Text(
            modifier = Modifier.fillMaxWidth(),
            textAlign = TextAlign.Start,
            text = buildAttrString(
                listOf(
                    AttributeStringData(
                        text = prefix,
                        textStyle = LocalTypography.current.text14.semiBold.colorTxtTitle(),
                    ),
                    AttributeStringData(
                        text = " $description",
                        textStyle = LocalTypography.current.text14.light.colorTxtParagraph(),
                    )
                )
            )
        )
    }
}

@Preview
@Composable
fun PreviewAgentStepItem(){
    AgentStepItem(modifier = Modifier, prefix = "Step 01 " , description = "Identification" )
}

@Preview(showBackground = true, showSystemUi = true)
@Composable
fun PreviewAgentSignupLandingScreen() {
    AgentSignupLandingScreen(
        viewModel = viewModel(),
        navController = rememberNavController(),
        arguments = AgentSignupLandingArguments("","","")
    )
}