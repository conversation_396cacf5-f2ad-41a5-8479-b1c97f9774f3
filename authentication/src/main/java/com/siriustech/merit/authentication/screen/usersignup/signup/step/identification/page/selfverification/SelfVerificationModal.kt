package com.siriustech.merit.authentication.screen.usersignup.signup.step.identification.page.selfverification

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import com.siriustech.merit.app_common.component.modalbts.CommonModelBottomSheet
import com.siriustech.merit.app_common.component.modalbts.CommonModelBottomSheetInfoProperties
import com.siriustech.merit.app_common.component.modalbts.CommonModelBottomSheetProperties
import com.siriustech.merit.app_common.component.text.NumberBoxLabel
import com.siriustech.merit.app_common.ext.colorTxtCaution
import com.siriustech.merit.app_common.theme.LocalAppColor
import com.siriustech.merit.app_common.theme.LocalDimens
import com.siriustech.merit.app_common.theme.LocalTypography
import com.siriustech.merit.app_common.R as AppCommonR

@Composable
fun SelfVerificationModal(onDismissed: () -> Unit, onConfirm: () -> Unit = {}) {
    CommonModelBottomSheet(customContent = {
        Box(
            modifier = Modifier
                .clip(RoundedCornerShape(LocalDimens.current.dimen4))
                .background(LocalAppColor.current.bgTone)
        ) {
            Column(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(LocalDimens.current.dimen8)
            ) {
                NumberBoxLabel(
                    modifier = Modifier.padding(bottom = LocalDimens.current.dimen4),
                    number = "1",
                    description = stringResource(id = AppCommonR.string._key0153)
                )
                NumberBoxLabel(
                    Modifier.padding(bottom = LocalDimens.current.dimen4),
                    number = "2",
                    description = stringResource(id = AppCommonR.string._key0154)
                )
                NumberBoxLabel(
                    Modifier.padding(bottom = LocalDimens.current.dimen4),
                    number = "3",
                    description = stringResource(id = AppCommonR.string._key0155)
                )
                NumberBoxLabel(
                    Modifier.padding(bottom = LocalDimens.current.dimen4),
                    number = "4",
                    description = stringResource(id = AppCommonR.string._key0156)
                )
            }
        }
    },
        properties = CommonModelBottomSheetProperties(
            prefixTitle = stringResource(id = AppCommonR.string._key0151),
            title = stringResource(id = AppCommonR.string._key0147),
            buttonText = stringResource(id = AppCommonR.string._key0150),
            description = stringResource(id = AppCommonR.string._key0148),
            showCloseButton = true,
            contentInfo = CommonModelBottomSheetInfoProperties(
                icon = painterResource(id = AppCommonR.drawable.ic_caution),
                content = stringResource(id = AppCommonR.string._key0149),
                modifier = Modifier
                    .fillMaxWidth()
                    .clip(RoundedCornerShape(LocalDimens.current.dimen4))
                    .background(LocalAppColor.current.bgCaution)
                    .padding(
                        horizontal = LocalDimens.current.dimen8,
                        vertical = LocalDimens.current.dimen2
                    ),
                contentTextStyle = LocalTypography.current.text14.light.colorTxtCaution()
            ),
        ),
        onButtonClicked = {
            onDismissed()
            onConfirm()
        },
        onDismissed = {
            onDismissed()
        })
}