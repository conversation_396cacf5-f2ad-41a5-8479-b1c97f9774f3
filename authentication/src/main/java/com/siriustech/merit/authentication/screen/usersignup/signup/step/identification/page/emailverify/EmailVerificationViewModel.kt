package com.siriustech.merit.authentication.screen.usersignup.signup.step.identification.page.emailverify

import androidx.lifecycle.viewModelScope
import com.core.network.base.getError
import com.siriustech.merit.apilayer.service.authentication.common.GetOTPRequest
import com.siriustech.merit.apilayer.service.authentication.common.GetOTPUseCase
import com.siriustech.merit.apilayer.service.authentication.login.LoginUseCase
import com.siriustech.merit.apilayer.service.authentication.login.LoginUserRequest
import com.siriustech.merit.apilayer.service.authentication.login.LoginUserResponse
import com.siriustech.merit.apilayer.service.authentication.register.RegisterRequest
import com.siriustech.merit.apilayer.service.authentication.register.RegisterUseCase
import com.siriustech.merit.app_common.component.otp.VerificationEvent
import com.siriustech.merit.app_common.data.CommonSharedPreferences
import com.siriustech.merit.app_common.theme.AppViewModel
import com.siriustech.merit.app_common.typeenum.BizType
import com.siriustech.merit.app_common.typeenum.OtpType
import com.siriustech.merit.app_common.utils.ErrorCode.mapToErrorDisplay
import com.siriustech.merit.authentication.domain.cache.CacheOnboardingUserData
import com.siriustech.merit.authentication.domain.displaydata.signup.identification.BasicInformationDisplayData
import dagger.hilt.android.lifecycle.HiltViewModel
import java.util.concurrent.TimeUnit
import javax.inject.Inject
import javax.inject.Named
import kotlinx.coroutines.channels.Channel
import kotlinx.coroutines.delay
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.catch
import kotlinx.coroutines.flow.collectLatest
import kotlinx.coroutines.flow.onCompletion
import kotlinx.coroutines.flow.onStart
import kotlinx.coroutines.flow.receiveAsFlow
import kotlinx.coroutines.launch

/**
 * Created by Hein Htet
 */
@HiltViewModel
class EmailVerificationViewModel @Inject constructor(
    private val getOTPUseCase: GetOTPUseCase,
    private val cacheOnboardingUserData: CacheOnboardingUserData,
    private val registerUseCase: RegisterUseCase,
    private val loginUseCase: LoginUseCase,
    private val commonSharedPreferences: CommonSharedPreferences,
    @Named("DeviceID") private val deviceId: String,
) : AppViewModel() {
    private val _otpCode = MutableStateFlow("")
    private val _verificationEvent = Channel<VerificationEvent>(capacity = Channel.BUFFERED)
    private val _otpExpiredTime = MutableStateFlow(0L)
    private val _otpResendCount = MutableStateFlow(0)
    private val _refNumber = MutableStateFlow("-")

    private val _basicInformationDisplayData: BasicInformationDisplayData
        get() = cacheOnboardingUserData.basicInformationDisplayData

    init {
        scope.launch {
            delay(1000)
            onRequestOTPApiCall()
        }
    }

    inner class EmailVerificationInputs : BaseInputs() {

        fun updateOtpCode(code: String) {
            _otpCode.value = code
        }

        fun onOTPVerificationEventChanged(event: VerificationEvent) {
            viewModelScope.launch {
                _verificationEvent.send(event)
            }
        }

        fun onResendOTPCode() {
            onRequestOTPApiCall()
        }

        fun onSubmitVerificationCode(code: String) {
            _otpCode.value = code
            register()
        }
    }

    inner class EmailVerificationOutputs : BaseOutputs() {
        val otpCode: StateFlow<String>
            get() = _otpCode

        val verificationEvent: Flow<VerificationEvent>
            get() = _verificationEvent.receiveAsFlow()

        val otpExpiredTime: StateFlow<Long>
            get() = _otpExpiredTime

        val refNumber: StateFlow<String>
            get() = _refNumber

        val otpResendCount: StateFlow<Int>
            get() = _otpResendCount

        val userEmail: String
            get() = cacheOnboardingUserData.basicInformationDisplayData.email
    }

    private fun onRequestOTPApiCall() {
        scope.launch {
            getOTPUseCase(
                param = GetOTPRequest(
                    bizType = BizType.CUSTOMER_REGISTRATION.type,
                    otpAddress = cacheOnboardingUserData.basicInformationDisplayData.email,
                    otpType = OtpType.EMAIL.value
                )
            )
                .onStart { inputs.emitLoading(true) }
                .catch { emitError(it.getError().mapToErrorDisplay()) }
                .onCompletion { inputs.emitLoading(false) }
                .collectLatest {
                    _otpResendCount.value += 1
                    _refNumber.value = it.refCode.orEmpty()
                    _otpExpiredTime.value = TimeUnit.SECONDS.toMillis(it.expireSeconds ?: 0)
                    _verificationEvent.send(VerificationEvent.OnOTPSent)
                }
        }
    }

    override val inputs = EmailVerificationInputs()
    override val outputs = EmailVerificationOutputs()

    private fun register() {
        scope.launch {
            val request = RegisterRequest(
                chineseName = _basicInformationDisplayData.chineseName,
                englishName = _basicInformationDisplayData.englishName,
                refCode = _refNumber.value,
                phoneNumber = _basicInformationDisplayData.phoneNumber,
                email = _basicInformationDisplayData.email,
                password = _basicInformationDisplayData.password,
                otpType = OtpType.PHONE.value,
                otpValue = _otpCode.value,
                otpBizType = BizType.CUSTOMER_REGISTRATION.type,
                referenceNumber = _basicInformationDisplayData.referenceNumber,
                mobileRegion = ""
            )
            registerUseCase(param = request)
                .onStart { inputs.emitLoading(true) }
                .onCompletion { inputs.emitLoading(false) }
                .catch { emitError(it.getError().mapToErrorDisplay()) }
                .collectLatest {
                    cacheOnboardingUserData.updateRegisteredCustomerId(it.customerId)
                    loginUser()
                }
        }
    }

    // after register success needed to login API to get sessionId
    private fun loginUser() {
        scope.launch {
            val request = LoginUserRequest(
                username = _basicInformationDisplayData.email,
                password = _basicInformationDisplayData.password,
                deviceId = deviceId,
                otpType = OtpType.EMAIL.value
            )
            loginUseCase(param = request)
                .onStart { inputs.emitLoading(true) }
                .onCompletion { inputs.emitLoading(false) }
                .catch { emitError(it.getError().mapToErrorDisplay()) }
                .collectLatest {
                    cacheUserInfo(it)
                    _verificationEvent.send(VerificationEvent.VerificationSuccess)
                }
        }
    }

    private fun cacheUserInfo(loginResponse: LoginUserResponse) {
        commonSharedPreferences.setUserEmail(loginResponse.email.orEmpty())
        commonSharedPreferences.setUserLoginPassword(_basicInformationDisplayData.password)
        commonSharedPreferences.setSessionID(loginResponse.sessionId.orEmpty())
    }
}