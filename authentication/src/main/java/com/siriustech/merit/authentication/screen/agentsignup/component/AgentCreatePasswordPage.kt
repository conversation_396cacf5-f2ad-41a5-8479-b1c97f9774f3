package com.siriustech.merit.authentication.screen.agentsignup.component

import androidx.compose.foundation.Image
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.ColumnScope
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Modifier
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.tooling.preview.Preview
import com.siriustech.merit.app_common.component.button.ButtonProperties
import com.siriustech.merit.app_common.component.button.SecondaryButton
import com.siriustech.merit.app_common.ext.colorTxtParagraph
import com.siriustech.merit.app_common.ext.colorTxtTitle
import com.siriustech.merit.app_common.navigation.argument.authentication.AgentSignupLandingArguments
import com.siriustech.merit.app_common.theme.LocalDimens
import com.siriustech.merit.app_common.theme.LocalTypography
import com.siriustech.merit.authentication.component.ConfigurePassword
import com.siriustech.merit.authentication.component.ConfigurePasswordProperties
import com.siriustech.merit.app_common.R as AppThemeR

/**
 * Created by Hein Htet
 */
@Composable
fun AgentCreatePasswordPage(
    onResetCount: Int,
    arguments: AgentSignupLandingArguments,
    onPasswordChange: (String) -> Unit,
    onRepeatPasswordChange: (String) -> Unit,
    onClickedNext: () -> Unit = {},
) {
    var password by remember {
        mutableStateOf("")
    }

    var repeatPassword by remember {
        mutableStateOf("")
    }

    var validButton by remember {
        mutableStateOf(false)
    }

    LaunchedEffect(onResetCount) {
        password = ""
        repeatPassword = ""
        validButton = false
    }

    Column(
        modifier = Modifier.fillMaxSize()
    ) {
        Text(
            text = stringResource(id = AppThemeR.string.key0921),
            style = LocalTypography.current.text14.light.colorTxtParagraph()
        )
        Spacer(modifier = Modifier.height(LocalDimens.current.dimen16))
        Image(
            painter = painterResource(id = AppThemeR.drawable.ic_separatoer),
            contentDescription = "Separator Line Resource"
        )
        Spacer(modifier = Modifier.height(LocalDimens.current.dimen16))
        UserInformation(arguments.email, arguments.phone)
        Spacer(modifier = Modifier.height(LocalDimens.current.dimen16))
        ConfigurePassword(
            onResetCount = onResetCount,
            defaultPassword = password,
            defaultConfirmPassword = repeatPassword,
            onPasswordChanged = {
                password = it
                onPasswordChange(it)
            }, onConfirmPasswordChanged = {
                repeatPassword = it
                onRepeatPasswordChange(it)
            },
            properties = ConfigurePasswordProperties(
                passwordTitle = stringResource(id = AppThemeR.string.key0021),
                placeholderPassword = stringResource(id = AppThemeR.string.key0817),
                confirmPasswordTitle = stringResource(id = AppThemeR.string.key0027),
                placeholderConfirmPassword = stringResource(id = AppThemeR.string.key0258)
            ),
            onValidate = {
                validButton = it
            }
        )
        Spacer(modifier = Modifier.weight(1f))
        SecondaryButton(
            properties = ButtonProperties(
                enabled = validButton,
                text = stringResource(id = AppThemeR.string.key0029)
            ),
            onClicked = onClickedNext
        )
    }
}

@Composable
fun ColumnScope.UserInformation(email: String, phone: String) {
    Text(
        text = stringResource(id = AppThemeR.string.key0922),
        style = LocalTypography.current.text12.semiBold.colorTxtTitle()
    )
    Spacer(modifier = Modifier.height(LocalDimens.current.dimen8))
    Column {
        Row(modifier = Modifier.fillMaxWidth(), horizontalArrangement = Arrangement.SpaceBetween) {
            Text(
                text = stringResource(id = AppThemeR.string.key0018),
                style = LocalTypography.current.text14.light.colorTxtParagraph(),
            )
            Text(
                text = phone,
                style = LocalTypography.current.text14.regular.colorTxtTitle(),
            )
        }

            Row(modifier = Modifier.fillMaxWidth(), horizontalArrangement = Arrangement.SpaceBetween) {
            Text(
                text = stringResource(id = AppThemeR.string.key0020),
                style = LocalTypography.current.text14.light.colorTxtParagraph(),
            )
            Text(
                text = email,
                style = LocalTypography.current.text14.regular.colorTxtTitle(),
            )
        }
    }
}

@Preview(showBackground = true, showSystemUi = true)
@Composable
fun PreviewAgentCreatePassword() {
    AgentCreatePasswordPage(
        onResetCount = 0,
        arguments = AgentSignupLandingArguments("<EMAIL>", "09123456789",""),
        onPasswordChange = {},
        onRepeatPasswordChange = {})
}
