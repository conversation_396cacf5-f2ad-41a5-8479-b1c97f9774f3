package com.siriustech.merit.authentication.screen.agentsignup

import androidx.lifecycle.SavedStateHandle
import androidx.lifecycle.viewModelScope
import com.core.network.base.getError
import com.siriustech.merit.apilayer.service.authentication.auth.ConfigurePinPassRequest
import com.siriustech.merit.apilayer.service.authentication.auth.ConfigurePinPassUseCase
import com.siriustech.merit.apilayer.service.authentication.signdocument.SignDocumentRequest
import com.siriustech.merit.apilayer.service.authentication.signdocument.SignDocumentResponse
import com.siriustech.merit.apilayer.service.authentication.signdocument.SignDocumentUseCase
import com.siriustech.merit.app_common.navigation.argument.authentication.AgentSignupLandingArguments
import com.siriustech.merit.app_common.theme.AppViewModel
import com.siriustech.merit.app_common.utils.ErrorCode.mapToErrorDisplay
import dagger.hilt.android.lifecycle.HiltViewModel
import javax.inject.Inject
import javax.inject.Named
import kotlinx.coroutines.channels.Channel
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.catch
import kotlinx.coroutines.flow.collectLatest
import kotlinx.coroutines.flow.onCompletion
import kotlinx.coroutines.flow.onStart
import kotlinx.coroutines.flow.receiveAsFlow
import kotlinx.coroutines.launch

/**
 * Created by Hein Htet
 */
@HiltViewModel
class AgentSignupViewModel @Inject constructor(
    private val savedStateHandle: SavedStateHandle,
    private val configurePinPassUseCase: ConfigurePinPassUseCase,
    private val signDocumentUseCase: SignDocumentUseCase,
    @Named("DeviceID") private val deviceID: String,
) : AppViewModel() {
    private val _currentStep = MutableStateFlow(0)
    private val _email = MutableStateFlow("")
    private val _phone = MutableStateFlow("")
    private val _password = MutableStateFlow("")
    private val _confirmPassword = MutableStateFlow("")
    private val _agentSignupEvent = Channel<AgentSignupEvent>(capacity = Channel.BUFFERED)
    private val _onResetCount = MutableStateFlow(0)
    private val _loginPassword = MutableStateFlow("")

    inner class AgentSignupInputs : BaseInputs() {
        init {
            val args = AgentSignupLandingArguments.from(savedStateHandle = savedStateHandle).args
            _email.value = args.email
            _phone.value = args.phone
            _loginPassword.value = args.password
        }

        fun onCreateNewPassword() {
            createPassword()
        }

        fun updateCurrentStep(step: Int) {
            _currentStep.value = step
        }

        fun onReset() {
            _onResetCount.value += 1
        }

        fun updatePassword(value: String) {
            _password.value = value
        }

        fun updateRepeatPassword(value: String) {
            _confirmPassword.value = value
        }

        fun onTriggerEvent(event: AgentSignupEvent) {
            scope.launch {
                _agentSignupEvent.send(event)
            }
        }

        fun onRequestConfirmDocument() = requestConfirmDocument()
    }

    inner class AgentSignupOutputs : BaseOutputs() {

        val email: StateFlow<String>
            get() = _email

        val phone: StateFlow<String>
            get() = _phone


        val currentStep: StateFlow<Int>
            get() = _currentStep

        val agentSignupEvent: Flow<AgentSignupEvent>
            get() = _agentSignupEvent.receiveAsFlow()

        val password: StateFlow<String>
            get() = _password

        val confirmPassword: StateFlow<String>
            get() = _confirmPassword

        val resetCount: StateFlow<Int>
            get() = _onResetCount


    }

    private fun createPassword() {
        scope.launch {
            val request = ConfigurePinPassRequest(
                password = _loginPassword.value,
                newPassword =_password.value,
                deviceId = deviceID
            )
            configurePinPassUseCase(param = request)
                .onStart { inputs.emitLoading(true) }
                .onCompletion { inputs.emitLoading(false) }
                .catch { emitError(it.getError().mapToErrorDisplay()) }
                .collectLatest {
                    inputs.onTriggerEvent(AgentSignupEvent.OnCreateNewPasswordSuccess)
                }
        }
    }

    private fun requestConfirmDocument() {
        viewModelScope.launch {
            signDocumentUseCase(param = SignDocumentRequest())
                .onStart { inputs.emitLoading(true) }
                .onCompletion { inputs.emitLoading(false) }
                .catch { emitError(it.getError().mapToErrorDisplay()) }
                .collectLatest {
                    inputs.onTriggerEvent(AgentSignupEvent.OnSignDocumentRequested(it.docuSignUrl))
                }
        }
    }

    override val inputs = AgentSignupInputs()
    override val outputs = AgentSignupOutputs()


}