package com.siriustech.merit.authentication.screen.usersignup.landing

import androidx.lifecycle.SavedStateHandle
import androidx.navigation.toRoute
import com.siriustech.merit.app_common.ext.serializableType
import com.siriustech.merit.authentication.AuthenticationRouteName
import com.siriustech.merit.authentication.domain.displaydata.checkonboarding.CheckOnboardingDisplayData
import kotlin.reflect.typeOf
import kotlinx.serialization.Serializable

/**
 * Created by <PERSON><PERSON>
 */
@Serializable
data class SignupLandingArguments(
    val onboardingDisplayData: CheckOnboardingDisplayData = CheckOnboardingDisplayData()

){
    companion object {
        val typeMap =
            mapOf(typeOf<SignupLandingArguments>() to serializableType<SignupLandingArguments>())

        fun from(savedStateHandle: SavedStateHandle) =
            savedStateHandle.toRoute<AuthenticationRouteName.SignupLanding>(typeMap)
    }
}