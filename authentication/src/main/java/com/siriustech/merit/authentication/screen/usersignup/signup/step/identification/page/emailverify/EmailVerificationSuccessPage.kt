package com.siriustech.merit.authentication.screen.usersignup.signup.step.identification.page.emailverify

import androidx.compose.foundation.layout.Column
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.tooling.preview.Preview
import com.siriustech.merit.app_common.R
import com.siriustech.merit.app_common.ext.colorTxtParagraph
import com.siriustech.merit.app_common.screen.CommonSuccessScreen
import com.siriustech.merit.app_common.screen.CommonSuccessScreenProperties
import com.siriustech.merit.app_common.theme.LocalTypography
import com.siriustech.merit.authentication.screen.usersignup.component.PageTitle
import com.siriustech.merit.authentication.screen.usersignup.component.PageTitleProperties
import com.siriustech.merit.app_common.R as AppCommonR

/**
 * Created by <PERSON><PERSON>
 */

@Composable
fun EmailVerificationSuccessPage(onNextPage: () -> Unit = {}) {
    Column {
        PageTitle(
            properties = PageTitleProperties(
                title = stringResource(id = R.string._key0136),
                currentStep = "2",
                totalStep = "3"
            )
        )
        CommonSuccessScreen(
            properties = CommonSuccessScreenProperties(
                disableBackPressed = true,
                title = stringResource(id = AppCommonR.string._key0137),
                iconPainter = painterResource(id = AppCommonR.drawable.ic_green_success),
                secondaryButtonText = stringResource(id = AppCommonR.string.key0029).plus(" ")
                    .plus(stringResource(id = AppCommonR.string._key0122)).plus("/").plus(
                        stringResource(id = AppCommonR.string._key0122)
                    ),

                ),
            onSecondaryButtonClick = onNextPage
        ) {
            Column(horizontalAlignment = Alignment.Start) {
                Text(
                    text = stringResource(id = R.string._key0138),
                    style = LocalTypography.current.text14.light.colorTxtParagraph()
                )
            }
        }
    }
}

@Preview(showBackground = true, showSystemUi = true)
@Composable
fun PreviewEmailVerificationSuccessPage() {
    EmailVerificationSuccessPage()
}