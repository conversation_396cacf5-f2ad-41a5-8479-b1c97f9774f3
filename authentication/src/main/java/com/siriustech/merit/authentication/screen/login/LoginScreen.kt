package com.siriustech.merit.authentication.screen.login

import androidx.compose.foundation.Image
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.text.KeyboardActions
import androidx.compose.foundation.text.KeyboardOptions
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.focus.FocusDirection
import androidx.compose.ui.graphics.ColorFilter
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.platform.LocalFocusManager
import androidx.compose.ui.platform.LocalSoftwareKeyboardController
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.input.ImeAction
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.fragment.app.FragmentActivity
import androidx.hilt.navigation.compose.hiltViewModel
import androidx.navigation.NavBackStackEntry
import androidx.navigation.NavController
import androidx.navigation.compose.rememberNavController
import com.siriustech.core_ui_compose.base.SingleEventEffect
import com.siriustech.merit.app_common.Constants
import com.siriustech.merit.app_common.Constants.NAV_RETURN_RESULT_PIN_LOGOUT
import com.siriustech.merit.app_common.component.button.ButtonProperties
import com.siriustech.merit.app_common.component.button.PrimaryButton
import com.siriustech.merit.app_common.component.button.SecondaryButton
import com.siriustech.merit.app_common.component.container.PaddingStart
import com.siriustech.merit.app_common.component.modalbts.ChangeLanguageModal
import com.siriustech.merit.app_common.component.text.ErrorInfo
import com.siriustech.merit.app_common.component.textfield.InputBox
import com.siriustech.merit.app_common.component.textfield.InputProperties
import com.siriustech.merit.app_common.component.toggle.ToggleOnOff
import com.siriustech.merit.app_common.ext.colorTxtInactive
import com.siriustech.merit.app_common.ext.colorTxtLabel
import com.siriustech.merit.app_common.ext.colorTxtParagraph
import com.siriustech.merit.app_common.ext.colorTxtTitle
import com.siriustech.merit.app_common.ext.displayAppVersioning
import com.siriustech.merit.app_common.ext.noRippleClickable
import com.siriustech.merit.app_common.ext.underline
import com.siriustech.merit.app_common.navigation.ForgotPassword
import com.siriustech.merit.app_common.theme.AppScreen
import com.siriustech.merit.app_common.theme.LocalAppColor
import com.siriustech.merit.app_common.theme.LocalDimens
import com.siriustech.merit.app_common.theme.LocalTypography
import com.siriustech.merit.authentication.screen.navigation.AuthNavigationEntryPoint
import com.siriustech.merit.authentication.screen.usersignup.landing.SignupLandingArguments
import dagger.hilt.android.EntryPointAccessors
import timber.log.Timber
import com.siriustech.merit.app_common.R as AppCommonR

/**
 * Created by Hein Htet
 */

@Composable
fun LoginScreen(
    backStackEntry: NavBackStackEntry? = null,
    viewModel: LoginViewModel = hiltViewModel(),
    navController: NavController,
) {

    val loginErrorMessage = viewModel.outputs.loginErrorMessage.collectAsState()
    val isLoginError = viewModel.outputs.isLoginError.collectAsState()
    val userEmail = viewModel.outputs.userEmail.collectAsState()
    val userPassword = viewModel.outputs.password.collectAsState()
    val activity = LocalContext.current as FragmentActivity
    val currentLanguage = viewModel.outputs.currentLanguage.collectAsState()

    var showLanguageModal by remember {
        mutableStateOf(false)
    }

    val authNavigation = remember {
        EntryPointAccessors.fromApplication(activity, AuthNavigationEntryPoint::class.java)
            .authNavigation()
    }

    if (backStackEntry != null) {
        // retrieve from previous screen
        val popReturnResult by backStackEntry.savedStateHandle.getStateFlow(
            Constants.NAV_RETURN_RESULT_KEY,
            ""
        ).collectAsState()

        // listen result
        LaunchedEffect(popReturnResult) {
            Timber.d("LoginScreen", "popReturnResult: $popReturnResult")
            if (popReturnResult == NAV_RETURN_RESULT_PIN_LOGOUT) {
                viewModel.inputs.clearPin()
            }
        }
    }


    SingleEventEffect(sideEffectFlow = viewModel.outputs.loginEventChannel) {
        when (it) {
            LoginEvent.LoginSuccess -> {
                authNavigation.onNavigateToDashboard(navController)
            }

            is LoginEvent.NavigateToSignUp -> {
                authNavigation.onNavigateToSignupLandingV1(activity)
            }

            LoginEvent.NavigateToPinLogin -> {
                authNavigation.onNavigateToPinLogin(navController)
            }

            is LoginEvent.NavigateTo2FAVerification -> {
                authNavigation.onNavigateToVerification2FA(navController, it.args)
            }

            is LoginEvent.NavigateToPinSetup -> {
                authNavigation.onNavigateToSetupPin(navController)
            }

            is LoginEvent.NavigateTo2FASetup -> {
                authNavigation.onNavigateToSetup2FA(navController, it.args)
            }

            is LoginEvent.NavigateToAgentSignupLanding -> {
                authNavigation.onNavigateToAgentSignupLanding(navController, it.args)
            }

            is LoginEvent.NavigateToForgotPassword -> {
                navController.navigate(ForgotPassword)
            }

            is LoginEvent.OnToggleLanguageModal -> {
                showLanguageModal = it.show
            }

            else -> {}
        }
    }
    val context = LocalContext.current
    val focusManager = LocalFocusManager.current
    AppScreen(vm = viewModel) {
        Box(
            modifier = Modifier
                .fillMaxSize()
        ) {
            Column(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(
                        top = 80.dp,
                        start = LocalDimens.current.dimen12,
                        end = LocalDimens.current.dimen12
                    ),
            ) {
                Box(
                    modifier = Modifier
                        .width(230.dp)
                        .height(60.dp)
                        .align(Alignment.CenterHorizontally)
                ) {
                    Image(
                        painter = painterResource(id = AppCommonR.drawable.ic_login_merit_logo),
                        contentDescription = "Merit Logo Resource"
                    )
                }
                Spacer(modifier = Modifier.height(LocalDimens.current.dimen60))
                ErrorInfo(errorMessage = loginErrorMessage.value)
                Spacer(modifier = Modifier.height(LocalDimens.current.dimen16))
                InputBox(
                    value = userEmail.value,
                    onTextChange = { viewModel.inputs.updateEmail(it) },
                    properties = InputProperties(
                        defaultValue = userEmail.value,
                        title = stringResource(id = AppCommonR.string.key0244),
                        placeholder = stringResource(id = AppCommonR.string.key0245),
                        isError = isLoginError.value,
                        keyboardOptions = KeyboardOptions(imeAction = ImeAction.Next),
                        keyboardActions = KeyboardActions(
                            onNext = { focusManager.moveFocus(FocusDirection.Down) }
                        )
                    )
                )
                Spacer(modifier = Modifier.height(LocalDimens.current.dimen16))
                InputBox(
                    value = userPassword.value,
                    onTextChange = { viewModel.inputs.updatePassword(it) },
                    properties = InputProperties(
                        defaultValue = userPassword.value,
                        title = stringResource(id = AppCommonR.string.key0021),
                        placeholder = stringResource(id = AppCommonR.string.key0727),
                        isPasswordMode = true,
                        isError = isLoginError.value,
                        keyboardOptions = KeyboardOptions(imeAction = ImeAction.Done),
                        keyboardActions = KeyboardActions(
                            onNext = {
                                viewModel.inputs.onLogin()
                            }
                        )
                    )
                )

                Spacer(modifier = Modifier.height(LocalDimens.current.dimen28))
                ActionButtons(viewModel)
                Text(
                    modifier = Modifier
                        .align(Alignment.CenterHorizontally)
                        .noRippleClickable {
                            viewModel.inputs.onTriggerEvent(LoginEvent.NavigateToForgotPassword)
                        }
                        .padding(horizontal = LocalDimens.current.dimen4),
                    text = stringResource(id = AppCommonR.string.key0759),
                    style = LocalTypography.current.text14.regular.colorTxtInactive().underline(),
                    textAlign = TextAlign.Center
                )
                Box(
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(
                            top = LocalDimens.current.dimen24,
                            end = LocalDimens.current.dimen12
                        ), contentAlignment = Alignment.Center
                ) {
                    Box(modifier = Modifier
                        .padding(LocalDimens.current.dimen8)
                        .noRippleClickable {
                            viewModel.inputs.onTriggerEvent(
                                LoginEvent.OnToggleLanguageModal(
                                    show = true
                                )
                            )

                        }) {
                        Row(verticalAlignment = Alignment.CenterVertically) {
                            Image(
                                painter = painterResource(id = AppCommonR.drawable.ic_language_selection),
                                contentDescription = "Language Image Resource",
                                colorFilter = ColorFilter.tint(LocalAppColor.current.txtLabel)
                            )
                            PaddingStart(value = LocalDimens.current.dimen8)
                            Text(
                                text = currentLanguage.value.getDisplayLanguage(context),
                                style = LocalTypography.current.text14.medium.colorTxtLabel()
                                    .underline()
                            )
                        }
                    }
                }
                Spacer(modifier = Modifier.weight(1f))
                Text(
                    modifier = Modifier.fillMaxWidth(),
                    textAlign = TextAlign.Center,
                    text = context.displayAppVersioning(),
                    style = LocalTypography.current.text12.light.colorTxtTitle(),
                )

            }
        }
        ChangeLanguageModal(currentSelectedLanguageType = currentLanguage.value,
            showModal = showLanguageModal, onDismissed = {
                viewModel.inputs.onTriggerEvent(LoginEvent.OnToggleLanguageModal(false))
            }, onSelectedLanguage = {
                viewModel.inputs.onChangeLocale(it)
                activity.recreate()
            })
    }
}

@Composable
fun ActionButtons(viewModel: LoginViewModel) {
    val isRememberMe = viewModel.outputs.isRememberMe.collectAsState()
    val isLoginEnabled = viewModel.outputs.loginEnabled.collectAsState()
    val localSoftwareKeyboardController = LocalSoftwareKeyboardController.current
    Column {
        ToggleOnOff(
            modifier = Modifier.fillMaxWidth(),
            defaultValue = isRememberMe.value,
            description = stringResource(id = AppCommonR.string.key0728),
            onToggleChanged = {
                viewModel.inputs.updateRememberMe(it)
            })
        Spacer(modifier = Modifier.height(LocalDimens.current.dimen38))
        PrimaryButton(
            properties = ButtonProperties(
                text = stringResource(id = AppCommonR.string.key0248),
                enabled = isLoginEnabled.value,
            ), onClicked = {
                localSoftwareKeyboardController?.hide()
                viewModel.inputs.onLogin()
            })
        Spacer(modifier = Modifier.height(LocalDimens.current.dimen20))
        Row(
            modifier = Modifier.fillMaxWidth(),
            verticalAlignment = Alignment.CenterVertically,
            horizontalArrangement = Arrangement.Center
        ) {
            Image(
                painter = painterResource(id = AppCommonR.drawable.ic_new_user_line),
                contentDescription = "Line"
            )
            Text(
                modifier = Modifier.padding(horizontal = LocalDimens.current.dimen4),
                text = stringResource(id = com.siriustech.merit.app_common.R.string.key0729),
                style = LocalTypography.current.text14.regular.colorTxtParagraph()
            )
            Image(
                painter = painterResource(id = AppCommonR.drawable.ic_new_user_line),
                contentDescription = "Line"
            )
        }
        Spacer(modifier = Modifier.height(LocalDimens.current.dimen20))
        SecondaryButton(
            properties = ButtonProperties(text = stringResource(id = AppCommonR.string.key0730)),
            onClicked = {
//                viewModel.inputs.onTriggerEvent(LoginEvent.LoginSuccess)
//                return@SecondaryButton
                viewModel.inputs.clearSID()
                viewModel.inputs.onTriggerEvent(LoginEvent.NavigateToSignUp(args = SignupLandingArguments()))
            })
        Spacer(modifier = Modifier.height(LocalDimens.current.dimen20))


    }
}

@Preview(showBackground = true)
@Composable
fun PreviewLoginScreen() {
    LoginScreen(backStackEntry = null, navController = rememberNavController())
}