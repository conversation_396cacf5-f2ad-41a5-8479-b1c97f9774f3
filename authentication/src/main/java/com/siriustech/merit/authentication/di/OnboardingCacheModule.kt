package com.siriustech.merit.authentication.di

import com.siriustech.merit.authentication.domain.cache.CacheOnboardingUserData
import dagger.Module
import dagger.Provides
import dagger.hilt.InstallIn
import dagger.hilt.components.SingletonComponent
import javax.inject.Singleton

/**
 * Created by <PERSON><PERSON>t
 */
@Module
@InstallIn(SingletonComponent::class)
class OnboardingCacheModule {

    @Provides
    @Singleton
    fun provideCacheOnboardingUserData(): CacheOnboardingUserData = CacheOnboardingUserData()
}