package com.siriustech.merit.authentication.screen.login

import androidx.lifecycle.viewModelScope
import com.core.network.base.getError
import com.siriustech.merit.apilayer.service.authentication.checkonboardingstep.CheckOnboardingStepRequest
import com.siriustech.merit.apilayer.service.authentication.checkonboardingstep.CheckOnboardingStepUseCase
import com.siriustech.merit.apilayer.service.authentication.login.LoginUseCase
import com.siriustech.merit.apilayer.service.authentication.login.LoginUserRequest
import com.siriustech.merit.apilayer.service.authentication.login.LoginUserResponse
import com.siriustech.merit.apilayer.service.authentication.userinfo.GetUserInfoUseCase
import com.siriustech.merit.app_common.data.CommonSharedPreferences
import com.siriustech.merit.app_common.navigation.argument.authentication.AgentSignupLandingArguments
import com.siriustech.merit.app_common.navigation.argument.authentication.Verification2FAArguments
import com.siriustech.merit.app_common.theme.AppViewModel
import com.siriustech.merit.app_common.typeenum.Auth2FAType
import com.siriustech.merit.app_common.typeenum.BizType
import com.siriustech.merit.app_common.typeenum.LanguageType
import com.siriustech.merit.app_common.utils.ErrorCode.mapToErrorDisplay
import com.siriustech.merit.app_common.utils.Validation.isValidEmail
import com.siriustech.merit.authentication.domain.displaydata.checkonboarding.CheckOnboardingDisplayData
import com.siriustech.merit.authentication.domain.mapper.checkonboarding.toDisplayData
import com.siriustech.merit.authentication.screen.usersignup.landing.SignupLandingArguments
import dagger.hilt.android.lifecycle.HiltViewModel
import javax.inject.Inject
import javax.inject.Named
import kotlinx.coroutines.channels.Channel
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.SharingStarted
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.catch
import kotlinx.coroutines.flow.collectLatest
import kotlinx.coroutines.flow.combine
import kotlinx.coroutines.flow.map
import kotlinx.coroutines.flow.onCompletion
import kotlinx.coroutines.flow.onStart
import kotlinx.coroutines.flow.receiveAsFlow
import kotlinx.coroutines.flow.stateIn
import kotlinx.coroutines.launch

/**
 * Created by Hein Htet
 */
@HiltViewModel
class LoginViewModel @Inject constructor(
    private val loginUseCase: LoginUseCase,
    private val checkOnboardingStepUseCase: CheckOnboardingStepUseCase,
    private val getUserInfoUseCase: GetUserInfoUseCase,
    private val commonSharedPreferences: CommonSharedPreferences,
    @Named("DeviceID") private val deviceID: String,
) : AppViewModel() {
    private val _email = MutableStateFlow("")
    private val _password = MutableStateFlow("")
    private val _loginError = MutableStateFlow("")
    private val _isLoginError = MutableStateFlow(false)
    private val _isRememberMe = MutableStateFlow(commonSharedPreferences.isRememberMe)
    private val _loginEventChannel = Channel<LoginEvent>(capacity = Channel.BUFFERED)
    private val _loginResponse = MutableStateFlow(LoginUserResponse())
    private val _checkOnboardingStepDisplayData = MutableStateFlow(CheckOnboardingDisplayData())
    private val _currentLanguage =
        MutableStateFlow(LanguageType.fromParams(commonSharedPreferences.appLocale))


    inner class LoginInputs : BaseInputs() {
        init {
            if (commonSharedPreferences.loginPinCode.isNotEmpty() && commonSharedPreferences.alreadyPinSetup) {
                onTriggerEvent(LoginEvent.NavigateToPinLogin)
            }
            if (commonSharedPreferences.isRememberMe && commonSharedPreferences.userEmail.isNotEmpty()) {
                _email.value = commonSharedPreferences.userEmail
            }
        }

        fun updateEmail(email: String) {
            _email.value = email
            _isLoginError.value = false
        }

        fun updatePassword(password: String) {
            _password.value = password
            _isLoginError.value = false
        }

        fun onLogin() = onDoLogin()
        fun updateRememberMe(value: Boolean) {
            _isRememberMe.value = value
        }

        fun onTriggerEvent(event: LoginEvent) {
            viewModelScope.launch {
                _loginEventChannel.send(event)
            }
        }

        // if back from pin login screen need to clear the pin code from locally
        fun clearPin() {
            commonSharedPreferences.setUserLoginPin("")
        }

        fun clearSID() {
            commonSharedPreferences.setSessionID("") // TODO re-open later
        }

        fun onChangeLocale(languageType: LanguageType) {
            _currentLanguage.value = languageType
            emitLanguageChanged(languageType)
        }
    }

    inner class LoginOutputs : BaseOutputs() {
        val loginEnabled: StateFlow<Boolean> =
            combine(_email, _password) { emailValue, passwordValue ->
                emailValue.isNotEmpty() && passwordValue.isNotEmpty() && emailValue.isValidEmail()
            }.stateIn(
                viewModelScope,
                initialValue = false,
                started = SharingStarted.WhileSubscribed(5000)
            )

        val loginErrorMessage: StateFlow<String>
            get() = _loginError

        val isLoginError: StateFlow<Boolean>
            get() = _isLoginError

        val isRememberMe: StateFlow<Boolean>
            get() = _isRememberMe

        val loginEventChannel: Flow<LoginEvent>
            get() = _loginEventChannel.receiveAsFlow()

        val userEmail: StateFlow<String>
            get() = _email

        val password: StateFlow<String>
            get() = _password

        val currentLanguage: StateFlow<LanguageType>
            get() = _currentLanguage

    }

    override val inputs = LoginInputs()
    override val outputs = LoginOutputs()


    private fun onDoLogin() {
        if (!outputs.loginEnabled.value) return
        commonSharedPreferences.setSessionID("")
        if (commonSharedPreferences.userEmail != _email.value) {
            commonSharedPreferences.setPinSetUp(false)
            commonSharedPreferences.setBiometricSetUp(false)
            commonSharedPreferences.setUserLoginPin("")
        }
        val loginRequest = LoginUserRequest(
            username = _email.value.trim(),
            password = _password.value.trim(),
            deviceId = deviceID,
        )
        viewModelScope.launch {
            loginUseCase(param = loginRequest)
                .onStart {
                    _loginError.value = ""
                    inputs.emitLoading(true)
                }
                .onCompletion {
                    inputs.emitLoading(false)
                }
                .catch {
                    _loginError.value = it.getError().errorResponse.message
                    _isLoginError.value = true
                }
                .collectLatest {
                    onHandleLoginApiSuccess(it)
                }
        }
    }

    private fun onHandleLoginApiSuccess(response: LoginUserResponse) {
        _loginResponse.value = response
        commonSharedPreferences.setUserEmail(_email.value)
        commonSharedPreferences.setUserLoginPassword(_password.value)
        commonSharedPreferences.setRememberMe(_isRememberMe.value)
        commonSharedPreferences.setSessionID(response.sessionId.orEmpty())
        val isMockOnboardingFlow = true

        when {
            response.boOnboarding == true -> {
                // register via agent flow
                // call user info API to get the user information first
                getUserInformation(
                    event = LoginEvent.NavigateToAgentSignupLanding(
                        args = AgentSignupLandingArguments(
                            "",
                            "",
                            ""
                        )
                    )
                )
            }
            // check register and onboarding flow finished
//            !response.sessionId.isNullOrEmpty() && response.configuredPin == false -> {
            !response.sessionId.isNullOrEmpty() -> {
                onCheckOnboardingStep()
            }
            else -> {
                onHandleLoginRouteFlow()
            }
        }
    }

    private fun onCheckOnboardingStep() {
        scope.launch {
            checkOnboardingStepUseCase(param = CheckOnboardingStepRequest())
                .onStart { inputs.emitLoading(true) }
                .onCompletion { inputs.emitLoading(false) }
                .catch { emitError(it.getError().mapToErrorDisplay()) }
                .map { it.toDisplayData() }
                .collectLatest {
                    onCheckOnboardingApiSuccess(it)
                }
        }
    }

    private fun onCheckOnboardingApiSuccess(data: CheckOnboardingDisplayData) {
        _checkOnboardingStepDisplayData.value = data
        if (data.isCompleted) {
            onHandleLoginRouteFlow()
        } else {
            onHandleOnboardingStepNavigation()
        }
    }

    private fun onHandleLoginRouteFlow() {
        val response = _loginResponse.value
        when {
            // session id from BE and need to setup 2fa
            response.configured2Fa == false -> {
                inputs.onTriggerEvent(
                    LoginEvent.NavigateTo2FASetup(
                        Verification2FAArguments(
                            authType = Auth2FAType.EMAIL,
                            token = response.token,
                            email = _email.value,
                            userBizType = BizType.CONFIG_2FA
                        )
                    )
                )
            }
            // not return session id yet from BE and need to login 2FA
            response.require2Fa == true -> {
                inputs.onTriggerEvent(
                    LoginEvent.NavigateTo2FAVerification(
                        Verification2FAArguments(
                            authType = Auth2FAType.EMAIL,
                            token = response.token,
                            email = _email.value,
                            userBizType = BizType.LOGIN_2FA
                        )
                    )
                )
            }
            // has session in BE
            response.configuredPin == false || commonSharedPreferences.loginPinCode.isEmpty() || !commonSharedPreferences.alreadyPinSetup -> {
                inputs.onTriggerEvent(
                    LoginEvent.NavigateToPinSetup(
                        email = _email.value,
                        password = _password.value
                    )
                )
            }
            // all setup already and navigate to dashboard screen
            else -> {
                inputs.onTriggerEvent(LoginEvent.LoginSuccess)
            }
        }
    }

    private fun onHandleOnboardingStepNavigation() {
//        inputs.onTriggerEvent(
//            LoginEvent.NavigateToPinSetup(
//                email = _email.value,
//                password = _password.value
//            )
//        )
//        return
        val step = _checkOnboardingStepDisplayData.value
        inputs.onTriggerEvent(event = LoginEvent.NavigateToSignUp(args = SignupLandingArguments(step)))
    }

    private fun getUserInformation(event: LoginEvent) {
        scope.launch {
            getUserInfoUseCase()
                .onStart { inputs.emitLoading(true) }
                .onCompletion { inputs.emitLoading(false) }
                .catch { emitError(it.getError().mapToErrorDisplay()) }
                .collectLatest {
                    if (event is LoginEvent.NavigateToAgentSignupLanding) {
                        inputs.onTriggerEvent(
                            LoginEvent.NavigateToAgentSignupLanding(
                                args = AgentSignupLandingArguments(
                                    email = _email.value,
                                    phone = it.basic?.base?.phoneNumber.orEmpty(),
                                    password = _password.value
                                )
                            )
                        )
                    }
                }
        }
    }


    override val TAG: String
        get() = "LoginViewModel"
}