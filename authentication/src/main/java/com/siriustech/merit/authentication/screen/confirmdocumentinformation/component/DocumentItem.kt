package com.siriustech.merit.authentication.screen.confirmdocumentinformation.component

import androidx.compose.foundation.Image
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.padding
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.text.AnnotatedString
import androidx.compose.ui.tooling.preview.Preview
import com.siriustech.merit.app_common.ext.colorTxtTitle
import com.siriustech.merit.app_common.ext.noRippleClickable
import com.siriustech.merit.app_common.theme.LocalDimens
import com.siriustech.merit.app_common.theme.LocalTypography
import com.siriustech.merit.app_common.R as AppCommonR

/**
 * Created by <PERSON><PERSON><PERSON><PERSON>
 */

@Composable
fun DocumentItem(
    enabled: <PERSON><PERSON>an,
    annotatedString: AnnotatedString,
    onClicked: () -> Unit = {},
) {
    Row(
        verticalAlignment = Alignment.CenterVertically,
        modifier = Modifier
            .padding(
                vertical = LocalDimens.current.dimen16,
            )
            .noRippleClickable { onClicked() }

    ) {
        Image(
            painter = painterResource(
                id = if (enabled) AppCommonR.drawable.ic_match else AppCommonR.drawable.ic_disable_check,
            ),
            contentDescription = "Match Icon Resource"
        )
        Spacer(modifier = Modifier.padding(end = LocalDimens.current.dimen8))
        Text(
            text = annotatedString,
            modifier = Modifier.weight(1f),
            style = LocalTypography.current.text14.medium.colorTxtTitle()
        )
        Image(
            painter = painterResource(id = AppCommonR.drawable.ic_right_arrow),
            contentDescription = "Match Icon Resource"
        )
    }
}

@Preview(showBackground = true, showSystemUi = true)
@Composable
fun PreviewDocumentItem() {
    DocumentItem(
        enabled = false,
        annotatedString = AnnotatedString(text = "Hello"),
        onClicked = {},
    )
}