package com.siriustech.merit.authentication.domain.cache

import com.siriustech.merit.apilayer.service.authentication.common.eumeration.EnumerationList
import com.siriustech.merit.apilayer.service.authentication.common.eumeration.EnumerationResponse
import com.siriustech.merit.app_common.Constants
import com.siriustech.merit.app_common.component.modalbts.ModalListDataContent
import com.siriustech.merit.app_common.data.display.IdentityDisplayModel
import com.siriustech.merit.authentication.domain.displaydata.signup.identification.BasicInformationDisplayData
import javax.inject.Inject

/**
 * Created by Hein Htet
 */

class CacheOnboardingUserData @Inject constructor() {


    private var _registerCustomerId: Int? = null // saved in after basic information page and verify email
    private var _region: ModalListDataContent = ModalListDataContent() // saved in basic information page
    private var _basicInformationDisplayData = BasicInformationDisplayData() // saved in basic information
    private val _identityDisplayData = ArrayList<IdentityDisplayModel>() // saved in identityPage
    private var _livenessCheckResult = Constants.STATUS_FAILED // saved in SelfVerificationViewModel after check liveness result

    fun updateLivenessCheckResult(status: String) {
        _livenessCheckResult = status
    }

    fun onUpdateBasicInformation(data: BasicInformationDisplayData) {
        _basicInformationDisplayData = data
    }

    fun onUpdateIdentityData(data: List<IdentityDisplayModel>) {
        _identityDisplayData.clear()
        _identityDisplayData.addAll(data)
    }

    fun updateRegisteredCustomerId(id: Int?) {
        _registerCustomerId = id
    }

    fun updateRegion(item: ModalListDataContent) {
        _region = item
    }

    val basicInformationDisplayData: BasicInformationDisplayData
        get() = _basicInformationDisplayData

    val registeredCustomerId: Int?
        get() = _registerCustomerId

    val identityDisplayData: ArrayList<IdentityDisplayModel>
        get() = _identityDisplayData

    val livenessCheckResult: String
        get() = _livenessCheckResult
}