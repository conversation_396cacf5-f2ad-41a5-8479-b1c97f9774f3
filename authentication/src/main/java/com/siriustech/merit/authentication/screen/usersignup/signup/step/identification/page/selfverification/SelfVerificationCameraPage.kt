package com.siriustech.merit.authentication.screen.usersignup.signup.step.identification.page.selfverification

import android.Manifest
import android.net.Uri
import android.util.Size
import androidx.activity.compose.rememberLauncherForActivityResult
import androidx.activity.result.contract.ActivityResultContracts
import androidx.camera.core.CameraInfo
import androidx.camera.core.CameraSelector
import androidx.camera.core.TorchState
import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.BoxWithConstraints
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.CompositionLocalProvider
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.viewinterop.AndroidView
import androidx.hilt.navigation.compose.hiltViewModel
import androidx.navigation.NavController
import androidx.navigation.compose.rememberNavController
import com.siriustech.core_ui_compose.base.SingleEventEffect
import com.siriustech.merit.app_common.Constants.NAV_RETURN_RESULT_LIVENESS_CHECK_PASSED
import com.siriustech.merit.app_common.Constants.STATUS_FAILED
import com.siriustech.merit.app_common.Constants.STATUS_PASSED
import com.siriustech.merit.app_common.component.button.ButtonProperties
import com.siriustech.merit.app_common.component.button.SecondaryButton
import com.siriustech.merit.app_common.component.container.BlinkingAnimation
import com.siriustech.merit.app_common.component.video.LocalVideoCaptureManager
import com.siriustech.merit.app_common.component.video.PreviewState
import com.siriustech.merit.app_common.component.video.VideoCaptureManager
import com.siriustech.merit.app_common.ext.colorTxtCaution
import com.siriustech.merit.app_common.ext.colorTxtInverted
import com.siriustech.merit.app_common.ext.noRippleClickable
import com.siriustech.merit.app_common.ext.popBackWithResult
import com.siriustech.merit.app_common.theme.AppScreen
import com.siriustech.merit.app_common.theme.LocalAppColor
import com.siriustech.merit.app_common.theme.LocalDimens
import com.siriustech.merit.app_common.theme.LocalTypography
import com.siriustech.merit.app_common.utils.FileUtils.createTempVideoFile
import com.siriustech.merit.app_common.utils.FileUtils.getRealPathFromURI
import kotlinx.coroutines.launch
import com.siriustech.merit.app_common.R as AppCommonR

/**
 * Created by Hein Htet
 */

@Composable
fun SelfVerificationCameraPage(
    navController: NavController,
    viewModel: SelfVerificationViewModel = hiltViewModel(),
) {

    var recordUri by remember {
        mutableStateOf<Uri?>(null)
    }

    var isCameraPreviewInitialized by remember {
        mutableStateOf(false)
    }

    val time = viewModel.outputs.time.collectAsState()

    var isRecording by remember {
        mutableStateOf(false)
    }

    var isHasCameraAccess by remember {
        mutableStateOf(false)
    }


    val cameraPermissionResult = rememberLauncherForActivityResult(
        contract = ActivityResultContracts.RequestPermission()
    ) { isGranted ->
        if (isGranted) {
            isHasCameraAccess = true
        }
    }

    LaunchedEffect(Unit) {
        cameraPermissionResult.launch(
            Manifest.permission.CAMERA,
        )
    }

    val context = LocalContext.current
    val lifecycleOwner = androidx.lifecycle.compose.LocalLifecycleOwner.current

    val listener = remember(viewModel) {
        object : VideoCaptureManager.Listener {
            override fun onInitialised(cameraLensInfo: HashMap<Int, CameraInfo>) {
                isCameraPreviewInitialized = true
            }

            override fun recordingPaused() {
            }

            override fun onProgress(progress: Int) {
            }

            override fun recordingCompleted(outputUri: Uri) {
                recordUri?.let {
                    val recordedFile = context.getRealPathFromURI(it).orEmpty()
                    viewModel.inputs.onLivenessCheck(recordedFile)
                }

            }

            override fun onError(throwable: Throwable?) {
                navController.popBackStack()
            }
        }
    }

    val captureManager = remember(viewModel) {
        VideoCaptureManager.Builder(context)
            .registerLifecycleOwner(lifecycleOwner)
            .create()
            .apply { this.listener = listener }
    }


    SingleEventEffect(sideEffectFlow = viewModel.outputs.event) {
        when (it) {
            SelfVerificationEvent.RecordingCompleted -> {
                captureManager.stopRecording()
            }

            SelfVerificationEvent.LivenessCheckFailed -> {
                navController.popBackWithResult(
                    NAV_RETURN_RESULT_LIVENESS_CHECK_PASSED,
                    STATUS_FAILED
                )
            }

            SelfVerificationEvent.LivenessCheckPassed -> {
                navController.popBackWithResult(
                    NAV_RETURN_RESULT_LIVENESS_CHECK_PASSED,
                    STATUS_PASSED
                )
            }

            else -> {}
        }
    }

    val scope = rememberCoroutineScope()

    fun startRecord() {
        context.createTempVideoFile(onFileCreated = { filePath, uri ->
            recordUri = uri
            scope.launch {
                captureManager.startRecording(filePath)
                viewModel.inputs.onStartRecording()
            }
        })

    }

    AppScreen(vm = viewModel) {
        Box(
            modifier = Modifier
                .fillMaxSize()
        ) {
            if (isHasCameraAccess) {
                CompositionLocalProvider(LocalVideoCaptureManager provides captureManager) {
                    CameraPreview(
                        torchState = TorchState.OFF,
                        lens = CameraSelector.LENS_FACING_FRONT
                    )
                }
            }
            Column(
                modifier = Modifier.fillMaxSize(),
                horizontalAlignment = Alignment.CenterHorizontally,
                verticalArrangement = Arrangement.Bottom
            ) {
                SecondaryButton(onClicked = {
                    navController.popBackWithResult(
                        NAV_RETURN_RESULT_LIVENESS_CHECK_PASSED,
                        STATUS_PASSED
                    )
                }, properties = ButtonProperties(text = "NEXT STEP"))
                if (isRecording) {
                    BlinkingAnimation {
                        Box(
                            modifier = Modifier
                                .clip(RoundedCornerShape(LocalDimens.current.dimen4))
                                .background(LocalAppColor.current.bgCaution)
                                .padding(
                                    horizontal = LocalDimens.current.dimen12,
                                    vertical = LocalDimens.current.dimen4,
                                )
                        ) {
                            Text(
                                text = stringResource(id = AppCommonR.string._key0152),
                                modifier = Modifier,
                                style = LocalTypography.current.text16.medium.colorTxtCaution()
                            )
                        }
                    }
                    Spacer(modifier = Modifier.height(LocalDimens.current.dimen16))
                }
                if (!isRecording) {
                    Image(
                        modifier = Modifier
                            .noRippleClickable {
                                if (isCameraPreviewInitialized) {
                                    isRecording = true
                                    startRecord()
                                }
                            }
                            .padding(vertical = LocalDimens.current.dimen16),
                        painter = painterResource(id = AppCommonR.drawable.ic_play_button),
                        contentDescription = "Play button Resource"
                    )
                }
                Text(
                    text = time.value,
                    modifier = Modifier
                        .padding(bottom = LocalDimens.current.dimen24)
                        .noRippleClickable {
                            captureManager.stopRecording()
                        },
                    style = LocalTypography.current.text24.bold.colorTxtInverted()
                )
            }
            Image(
                modifier = Modifier
                    .fillMaxSize()
                    .padding(top = LocalDimens.current.dimen60),
                painter = painterResource(id = AppCommonR.drawable.ic_camera_frame),
                contentDescription = "Camera Frame Resource"
            )
        }
    }
}

@Composable
private fun CameraPreview(lens: Int, @TorchState.State torchState: Int) {
    val captureManager = LocalVideoCaptureManager.current
    BoxWithConstraints {
        AndroidView(
            factory = {
                captureManager.showPreview(
                    PreviewState(
                        cameraLens = lens,
                        torchState = torchState,
                        size = Size(this.minWidth.value.toInt(), this.maxHeight.value.toInt())
                    )
                )
            },
            modifier = Modifier.fillMaxSize(),
            update = {
                captureManager.updatePreview(
                    PreviewState(cameraLens = lens, torchState = torchState),
                    it
                )
            }
        )
    }
}

@Preview(showBackground = true, showSystemUi = true)
@Composable
fun PreviewSelfVerificationCameraPage() {
    SelfVerificationCameraPage(rememberNavController())
}