package com.siriustech.merit.authentication.screen.login

import com.siriustech.merit.app_common.navigation.argument.authentication.AgentSignupLandingArguments
import com.siriustech.merit.app_common.navigation.argument.authentication.Verification2FAArguments
import com.siriustech.merit.authentication.screen.usersignup.landing.SignupLandingArguments

/**
 * Created by <PERSON><PERSON> Htet
 */
sealed interface LoginEvent {
    data object LoginSuccess : LoginEvent
    data class NavigateToSignUp(val args: SignupLandingArguments) : LoginEvent
    data object NavigateToPinLogin : LoginEvent
    data class NavigateToPinSetup(val email: String, val password: String) : LoginEvent
    data class NavigateTo2FAVerification(val args: Verification2FAArguments) : LoginEvent
    data class NavigateTo2FASetup(val args: Verification2FAArguments) : LoginEvent
    data class NavigateToAgentSignupLanding(val args: AgentSignupLandingArguments) : LoginEvent
    data object NavigateToForgotPassword : LoginEvent
    data class OnToggleLanguageModal(val show: Boolean) : LoginEvent
}