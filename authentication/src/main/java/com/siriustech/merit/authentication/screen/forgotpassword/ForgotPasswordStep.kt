package com.siriustech.merit.authentication.screen.forgotpassword

enum class ForgotPasswordStep(val step: Int) {
    INPUT_EMAIL(0),
    VERIFY_EMAIL_OTP(1),
    CREATE_PASSWORD(2);

    companion object {

        const val TOTAL_STEP = 4

        fun fromParam(value: Int): ForgotPasswordStep {
            return when (value) {
                0 -> INPUT_EMAIL
                1 -> VERIFY_EMAIL_OTP
                2 -> CREATE_PASSWORD
                else -> INPUT_EMAIL
            }
        }
    }
}
