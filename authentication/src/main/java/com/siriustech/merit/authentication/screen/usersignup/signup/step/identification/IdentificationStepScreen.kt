package com.siriustech.merit.authentication.screen.usersignup.signup.step.identification

import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.pager.HorizontalPager
import androidx.compose.foundation.pager.rememberPagerState
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.runtime.setValue
import androidx.compose.ui.Modifier
import androidx.fragment.app.FragmentActivity
import androidx.lifecycle.viewmodel.compose.viewModel
import androidx.navigation.NavController
import com.siriustech.core_ui_compose.base.SingleEventEffect
import com.siriustech.core_ui_compose.component.CommonToolbar
import com.siriustech.core_ui_compose.component.ToolbarProperties
import com.siriustech.merit.app_common.R
import com.siriustech.merit.app_common.component.header.CommonToolbarWithBackAndResetMenu
import com.siriustech.merit.app_common.component.header.CommonToolbarWithBackMenu
import com.siriustech.merit.app_common.ext.colorTxtTitle
import com.siriustech.merit.app_common.theme.AppScreen
import com.siriustech.merit.app_common.theme.AppViewModel
import com.siriustech.merit.app_common.theme.LocalAppColor
import com.siriustech.merit.app_common.theme.LocalDimens
import com.siriustech.merit.app_common.theme.LocalTypography
import com.siriustech.merit.authentication.screen.navigation.AuthNavigationEntryPoint
import com.siriustech.merit.authentication.screen.usersignup.SignupStep
import com.siriustech.merit.authentication.screen.usersignup.signup.step.identification.page.basicinformation.BasicInformationPage
import com.siriustech.merit.authentication.screen.usersignup.signup.step.identification.page.emailverify.EmailVerificationPage
import com.siriustech.merit.authentication.screen.usersignup.signup.step.identification.page.emailverify.EmailVerificationSuccessPage
import com.siriustech.merit.authentication.screen.usersignup.signup.step.identification.page.identifyresult.IdentifyResultPage
import com.siriustech.merit.authentication.screen.usersignup.signup.step.identification.page.identity.IdentityPage
import dagger.hilt.android.EntryPointAccessors
import kotlinx.coroutines.launch
import timber.log.Timber


/**
 * Created by Hein Htet
 */

@Composable
fun IdentificationStepScreen(
    navController: NavController,
    viewModel: IdentificationViewModel = viewModel(),
    onUpdateCurrentStep: (step: IdentificationStep) -> Unit = {},
    identificationStep: IdentificationStep = IdentificationStep.BASIC_INFORMATION,
    onSignupNextStep: () -> Unit = {},
) {

    val activity = navController.context as FragmentActivity
    val authNavigation = remember {
        EntryPointAccessors.fromApplication(activity, AuthNavigationEntryPoint::class.java)
            .authNavigation()
    }
    val coroutineScope = rememberCoroutineScope()
    val pagerState = rememberPagerState(
        pageCount = { IdentificationStep.TOTAL_STEP },
        initialPage = identificationStep.step
    )
    var currentStep by remember {
        mutableStateOf(identificationStep)
    }

    fun onNavigateToPage(index: Int) {
        coroutineScope.launch {
            pagerState.animateScrollToPage(index)
        }
    }

    LaunchedEffect(identificationStep) {
        Timber.d("IdentificationStepScreen: $identificationStep")
        currentStep = identificationStep
        onNavigateToPage(identificationStep.step)
        viewModel.inputs.onUpdateCurrentStep(identificationStep)
    }


    SingleEventEffect(sideEffectFlow = viewModel.outputs.identificationEvent) {
        when (it) {
            is IdentificationEvent.OnNavigateToStep -> {
                onUpdateCurrentStep(IdentificationStep.fromParam(it.step))
                onNavigateToPage(it.step)
            }

            else -> {}
        }
    }
    AppScreen(vm = viewModel) {
        HorizontalPager(
            userScrollEnabled = false,
            state = pagerState,
            modifier = Modifier
                .fillMaxSize()
                .padding(top = LocalDimens.current.dimen16)
        ) {
            val step = IdentificationStep.fromParam(pagerState.currentPage)
            when (step) {
                IdentificationStep.BASIC_INFORMATION -> BasicInformationPage(onNextStep = { viewModel.inputs.navigateToNextStep() })
                IdentificationStep.EMAIL_VERIFICATION -> EmailVerificationPage(onNextStep = { viewModel.inputs.navigateToNextStep() })
                IdentificationStep.EMAIL_VERIFICATION_SUCCESS -> EmailVerificationSuccessPage(
                    onNextPage = { viewModel.inputs.navigateToNextStep() })

                IdentificationStep.IDENTITY_VERIFICATION -> IdentityPage(
                    navController = navController,
                    onNextStep = { viewModel.inputs.navigateToNextStep() })

                IdentificationStep.LIVENESS_RESULT -> IdentifyResultPage(onSignupNextStep = {
                    onSignupNextStep() // signup nextstep
                }, onRetryLiveness = {
                    onUpdateCurrentStep(IdentificationStep.IDENTITY_VERIFICATION)
                }, onEnterApp = {
                    authNavigation.onNavigateToDashboard(navController)
                })
            }
        }
    }
}
