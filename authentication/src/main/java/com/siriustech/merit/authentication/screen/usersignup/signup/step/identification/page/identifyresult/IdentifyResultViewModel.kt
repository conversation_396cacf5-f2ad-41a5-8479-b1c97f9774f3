package com.siriustech.merit.authentication.screen.usersignup.signup.step.identification.page.identifyresult

import com.siriustech.merit.app_common.theme.AppViewModel
import com.siriustech.merit.authentication.domain.cache.CacheOnboardingUserData
import dagger.hilt.android.lifecycle.HiltViewModel
import javax.inject.Inject
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow

/**
 * Created by <PERSON><PERSON>
 */
@HiltViewModel
class IdentifyResultViewModel @Inject constructor(
    private val cacheOnboardingUserData: CacheOnboardingUserData,
) : AppViewModel() {

    private val _livenessCheckResultStatus = MutableStateFlow<String>("")

    init {
        _livenessCheckResultStatus.value = cacheOnboardingUserData.livenessCheckResult
    }

    inner class IdentifyResultInputs : BaseInputs()
    inner class IdentifyResultOutputs : BaseOutputs() {

        val livenessCheckResultStatus: StateFlow<String>
            get() = _livenessCheckResultStatus
    }


    override val inputs = IdentifyResultInputs()
    override val outputs = IdentifyResultOutputs()

}