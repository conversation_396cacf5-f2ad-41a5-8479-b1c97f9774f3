package com.siriustech.merit.authentication.screen.usersignup.signup.step.identification

import com.siriustech.merit.authentication.screen.usersignup.SignupStep
import com.siriustech.merit.authentication.screen.usersignup.signup.StepProperties

/**
 * Created by <PERSON><PERSON> Htet
 */


enum class IdentificationStep(val step: Int) : StepProperties {

    BASIC_INFORMATION(0) {
        override val showResetButton: <PERSON>olean
            get() = true
    },
    EMAIL_VERIFICATION(1) {
        override val showResetButton: Boolean
            get() = false
    },
    EMAIL_VERIFICATION_SUCCESS(2) {
        override val showResetButton: <PERSON><PERSON><PERSON>
            get() = false
    },
    IDENTITY_VERIFICATION(3) {
        override val showResetButton: Boolean
            get() = true
    },// after need to present the liveness scan screen

    // LIVE FACE SCAN DETECT
    LIVENESS_RESULT(4) {
        override val showResetButton: <PERSON>ole<PERSON>
            get() = false
    };


    companion object {
        const val TOTAL_STEP = 5
        fun fromParam(value: Int): IdentificationStep {
            return when (value) {
                0 -> BASIC_INFORMATION
                1 -> EMAIL_VERIFICATION
                2 -> EMAIL_VERIFICATION_SUCCESS
                3 -> IDENTITY_VERIFICATION
                4 -> LIVENESS_RESULT
                else -> BASIC_INFORMATION
            }
        }
    }
}