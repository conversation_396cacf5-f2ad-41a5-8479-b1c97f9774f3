package com.siriustech.merit.authentication.screen.confirmdocumentinformation

import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.verticalScroll
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.remember
import androidx.compose.ui.Modifier
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.tooling.preview.Preview
import androidx.fragment.app.FragmentActivity
import androidx.navigation.NavController
import androidx.navigation.compose.rememberNavController
import com.siriustech.merit.app_common.component.button.ButtonProperties
import com.siriustech.merit.app_common.component.button.SecondaryButton
import com.siriustech.merit.app_common.ext.AttributeStringData
import com.siriustech.merit.app_common.ext.buildAttrString
import com.siriustech.merit.app_common.ext.colorTxtInfo
import com.siriustech.merit.app_common.ext.colorTxtParagraph
import com.siriustech.merit.app_common.ext.colorTxtTitle
import com.siriustech.merit.app_common.theme.AppScreen
import com.siriustech.merit.app_common.theme.AppViewModel
import com.siriustech.merit.app_common.theme.LocalAppColor
import com.siriustech.merit.app_common.theme.LocalDimens
import com.siriustech.merit.app_common.theme.LocalTypography
import com.siriustech.merit.authentication.screen.confirmdocumentinformation.component.DocumentItem
import com.siriustech.merit.authentication.screen.navigation.AuthNavigationEntryPoint
import dagger.hilt.android.EntryPointAccessors
import com.siriustech.merit.app_common.R as AppCommonR

/**
 * Created by Hein Htet
 */

@Composable
fun ConfirmDocInformationScreen(
    modifier: Modifier = Modifier,
    navController: NavController,
    onClickNext : () -> Unit = {}
) {

    val activity = LocalContext.current as FragmentActivity
    val authNavigation = remember {
        EntryPointAccessors.fromApplication(activity, AuthNavigationEntryPoint::class.java)
            .authNavigation()
    }

    AppScreen(vm = AppViewModel(), ignorePaddingValue = true) {
        Column(
            modifier = Modifier
                .fillMaxSize()
                .then(modifier)
        ) {
            Column(
                modifier = Modifier
                    .weight(1f)
                    .verticalScroll(rememberScrollState())
                    .then(modifier)
            ) {
                Text(
                    text = stringResource(id = AppCommonR.string.key0174),
                    style = LocalTypography.current.text14.semiBold.colorTxtTitle(),
                )
                DocumentItem(
                    enabled = false,
                    annotatedString = buildAttrString(
                        attributedString = listOf(
                            AttributeStringData(
                                text = stringResource(
                                    id = AppCommonR.string.key0009
                                ),
                                textStyle = LocalTypography.current.text14.medium.colorTxtTitle()
                            )
                        )
                    ),
                    onClicked = {}
                )
                DocumentItem(
                    enabled = false,
                    annotatedString = buildAttrString(
                        attributedString = listOf(
                            AttributeStringData(
                                text = stringResource(
                                    id = AppCommonR.string.key0010
                                ),
                                textStyle = LocalTypography.current.text14.medium.colorTxtTitle()
                            )
                        )
                    ),
                    onClicked = {}
                )
                DocumentItem(
                    enabled = false,
                    annotatedString = buildAttrString(
                        attributedString = listOf(
                            AttributeStringData(
                                text = stringResource(
                                    id = AppCommonR.string.key0175
                                ),
                                textStyle = LocalTypography.current.text14.medium.colorTxtTitle()
                            ),
                            AttributeStringData(
                                text = " ".plus(
                                    stringResource(
                                        id = AppCommonR.string.key0176
                                    )
                                ),
                                textStyle = LocalTypography.current.text14.regular.colorTxtParagraph()
                            )
                        )
                    ),
                    onClicked = {}
                )
                DocumentItem(
                    enabled = false,
                    annotatedString = buildAttrString(
                        attributedString = listOf(
                            AttributeStringData(
                                text = stringResource(
                                    id = AppCommonR.string.key0177
                                ),
                                textStyle = LocalTypography.current.text14.medium.colorTxtTitle()
                            )
                        )
                    ),
                    onClicked = {}
                )
                DocumentItem(
                    enabled = false,
                    annotatedString = buildAttrString(
                        attributedString = listOf(
                            AttributeStringData(
                                text = stringResource(
                                    id = AppCommonR.string.key0178
                                ),
                                textStyle = LocalTypography.current.text14.medium.colorTxtTitle()
                            )
                        )
                    ),
                    onClicked = {}
                )
                DocumentItem(
                    enabled = false,
                    annotatedString = buildAttrString(
                        attributedString = listOf(
                            AttributeStringData(
                                text = stringResource(
                                    id = AppCommonR.string.key0179
                                ),
                                textStyle = LocalTypography.current.text14.medium.colorTxtTitle()
                            )
                        )
                    ),
                    onClicked = {}
                )
                DocumentItem(
                    enabled = false,
                    annotatedString = buildAttrString(
                        attributedString = listOf(
                            AttributeStringData(
                                text = stringResource(
                                    id = AppCommonR.string.key0180
                                ),
                                textStyle = LocalTypography.current.text14.medium.colorTxtTitle()
                            )
                        )
                    ),
                    onClicked = {}
                )
                Spacer(modifier = Modifier.height(LocalDimens.current.dimen16))
                Image(
                    painter = painterResource(id = AppCommonR.drawable.ic_separatoer),
                    contentDescription = "Image Separator Resource"
                )
                Spacer(modifier = Modifier.height(LocalDimens.current.dimen16))
                Text(
                    text = stringResource(id = AppCommonR.string.key0181),
                    style = LocalTypography.current.text14.semiBold.colorTxtTitle(),
                )
                DocumentItem(
                    enabled = false,
                    annotatedString = buildAttrString(
                        attributedString = listOf(
                            AttributeStringData(
                                text = stringResource(
                                    id = AppCommonR.string.key0182
                                ),
                                textStyle = LocalTypography.current.text14.medium.colorTxtTitle()
                            )
                        )
                    ),
                    onClicked = {}
                )
                DocumentItem(
                    enabled = false,
                    annotatedString = buildAttrString(
                        attributedString = listOf(
                            AttributeStringData(
                                text = stringResource(
                                    id = AppCommonR.string.key0183
                                ),
                                textStyle = LocalTypography.current.text14.medium.colorTxtTitle()
                            )
                        )
                    ),
                    onClicked = {}
                )
                Spacer(modifier = Modifier.height(LocalDimens.current.dimen16))
                Image(
                    painter = painterResource(id = AppCommonR.drawable.ic_separatoer),
                    contentDescription = "Image Separator Resource"
                )
                Spacer(modifier = Modifier.height(LocalDimens.current.dimen16))
                Box(
                    modifier = Modifier
                        .background(LocalAppColor.current.bgInfo)
                        .padding(LocalDimens.current.dimen12)
                ) {
                    Text(
                        modifier = Modifier.padding(LocalDimens.current.dimen12),
                        style = LocalTypography.current.text14.light.colorTxtInfo(),
                        text = stringResource(id = AppCommonR.string.key0184) + " " + stringResource(id = AppCommonR.string.key0185)
                    )
                }
            }
            Spacer(modifier = Modifier.height(LocalDimens.current.dimen16))
            SecondaryButton(
                properties = ButtonProperties(text = stringResource(id = AppCommonR.string.key0163)),
                onClicked = {
                    onClickNext()
                })
        }
    }
}

@Preview(showBackground = true, showSystemUi = true)
@Composable
fun PreviewConfirmDocInformation() {
    ConfirmDocInformationScreen(
        navController = rememberNavController(),
        modifier = Modifier.background(LocalAppColor.current.bgDefault)
    )
}