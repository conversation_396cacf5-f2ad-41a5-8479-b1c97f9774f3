package com.siriustech.merit.authentication.screen.usersignup.signup.step.identification.page.identifyresult

import androidx.compose.foundation.layout.Column
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.collectAsState
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.hilt.navigation.compose.hiltViewModel
import com.siriustech.merit.app_common.Constants
import com.siriustech.merit.app_common.ext.colorTxtParagraph
import com.siriustech.merit.app_common.screen.CommonSuccessScreen
import com.siriustech.merit.app_common.screen.CommonSuccessScreenProperties
import com.siriustech.merit.app_common.theme.AppScreen
import com.siriustech.merit.app_common.theme.LocalTypography
import com.siriustech.merit.app_common.R as AppCommonR

/**
 * Created by He<PERSON> <PERSON>tet
 */

@Composable
fun IdentifyResultPage(
    onEnterApp: () -> Unit = {},
    onSignupNextStep: () -> Unit = {},
    onRetryLiveness: () -> Unit = {},
    viewModel: IdentifyResultViewModel = hiltViewModel(),
) {
    val status = viewModel.outputs.livenessCheckResultStatus.collectAsState()
    val isPassed = status.value == Constants.STATUS_PASSED

    AppScreen(vm = viewModel) {
        CommonSuccessScreen(
            properties = CommonSuccessScreenProperties(
                disableBackPressed = false,
                iconPainter = painterResource(id = if (isPassed) AppCommonR.drawable.ic_liveness_check_success else AppCommonR.drawable.ic_liveness_check_failed),
                title = stringResource(id = if (isPassed) AppCommonR.string._key0157 else AppCommonR.string._key0158),
                secondaryButtonText = stringResource(id = if (isPassed) AppCommonR.string._key0160 else AppCommonR.string._key0163),
                accentButtonText = if (isPassed) stringResource(id = AppCommonR.string._key0161) else null,
            ),
            onSecondaryButtonClick = {
                if (isPassed) {
                    onSignupNextStep()
                } else {
                    onRetryLiveness()
                }
            },
            onAccentButtonClick = { onEnterApp() }
        ) {
            Column {
                Text(
                    text = stringResource(id = if (isPassed) AppCommonR.string._key0159 else AppCommonR.string._key0162),
                    style = LocalTypography.current.text14.light.colorTxtParagraph()
                )
            }
        }
    }
}