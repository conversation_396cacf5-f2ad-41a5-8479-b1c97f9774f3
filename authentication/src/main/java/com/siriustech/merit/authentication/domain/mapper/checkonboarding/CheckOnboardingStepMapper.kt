package com.siriustech.merit.authentication.domain.mapper.checkonboarding

import com.siriustech.merit.apilayer.service.authentication.checkonboardingstep.CheckOnboardingStep
import com.siriustech.merit.apilayer.service.authentication.checkonboardingstep.CheckOnboardingStepResponse
import com.siriustech.merit.app_common.Constants
import com.siriustech.merit.authentication.domain.displaydata.checkonboarding.CheckOnboardingDisplayData

/**
 * Created by <PERSON><PERSON><PERSON>t
 */
fun CheckOnboardingStepResponse.toDisplayData(): CheckOnboardingDisplayData {
    val data = this
    val identification =
        data.list.findLast { it.code == Constants.IDENTIFICATION_STEP }?.status ?: ""
    val personalInfo =
        data.list.findLast { it.code == Constants.PERSONAL_INFO_STEP }?.status ?: ""
    val riskAssessment =
        data.list.findLast { it.code == Constants.RISK_ASSESSMENT_STEP }?.status ?: ""
    val declaration = data.list.findLast { it.code == Constants.DECLARATION_STEP }?.status ?: ""
    val uploadDocument =
        data.list.findLast { it.code == Constants.DOCUMENT_UPLOAD_STEP }?.status ?: ""
    val confirmationStep =
        data.list.findLast { it.code == Constants.CONFIRM_STEP }?.status ?: ""
    val steps = listOf(
        CheckOnboardingStep(code = Constants.IDENTIFICATION_STEP, status = identification),
        CheckOnboardingStep(code = Constants.PERSONAL_INFO_STEP, status = personalInfo),
        CheckOnboardingStep(code = Constants.RISK_ASSESSMENT_STEP, status = riskAssessment),
        CheckOnboardingStep(code = Constants.DECLARATION_STEP, status = declaration),
        CheckOnboardingStep(code = Constants.DOCUMENT_UPLOAD_STEP, status = uploadDocument),
        CheckOnboardingStep(code = Constants.CONFIRM_STEP, status = confirmationStep),
    )
    val model = CheckOnboardingDisplayData(
        steps = steps,
        isCompleted = steps.all { it.status == Constants.STATUS_DONE }
    )
    return model
}
