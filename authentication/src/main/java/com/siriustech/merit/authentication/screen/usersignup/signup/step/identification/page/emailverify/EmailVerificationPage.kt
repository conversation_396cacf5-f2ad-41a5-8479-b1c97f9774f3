package com.siriustech.merit.authentication.screen.usersignup.signup.step.identification.page.emailverify

import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.height
import androidx.compose.runtime.Composable
import androidx.compose.runtime.collectAsState
import androidx.compose.ui.Modifier
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.tooling.preview.Preview
import androidx.hilt.navigation.compose.hiltViewModel
import com.siriustech.core_ui_compose.base.SingleEventEffect
import com.siriustech.merit.app_common.R
import com.siriustech.merit.app_common.component.alert.BannerAlertProperties
import com.siriustech.merit.app_common.component.otp.OTPVerification
import com.siriustech.merit.app_common.component.otp.OTPVerificationProperties
import com.siriustech.merit.app_common.component.otp.VerificationEvent
import com.siriustech.merit.app_common.ext.AttributeStringData
import com.siriustech.merit.app_common.ext.buildAttrString
import com.siriustech.merit.app_common.ext.colorTxtLabel
import com.siriustech.merit.app_common.ext.colorTxtParagraph
import com.siriustech.merit.app_common.theme.AppScreen
import com.siriustech.merit.app_common.theme.LocalDimens
import com.siriustech.merit.app_common.theme.LocalTypography
import com.siriustech.merit.authentication.screen.usersignup.component.PageTitle
import com.siriustech.merit.authentication.screen.usersignup.component.PageTitleProperties

/**
 * Created by Hein Htet
 */

@Composable
fun EmailVerificationPage(
    viewModel: EmailVerificationViewModel = hiltViewModel(),
    onNextStep : () -> Unit = {}
) {
    val context = LocalContext.current
    val otpCode = viewModel.outputs.otpCode.collectAsState()
    val otpResendCount = viewModel.outputs.otpResendCount.collectAsState()
    val refNumber = viewModel.outputs.refNumber.collectAsState()
    val otpExpireTime = viewModel.outputs.otpExpiredTime.collectAsState()


    fun showOtpSentAlert() {
        viewModel.emitBannerAlert(
            BannerAlertProperties(
                title = context.getString(R.string.key0111),
                description = context.getString(R.string.key0814),
            ),
        )
    }

    SingleEventEffect(viewModel.outputs.verificationEvent) { sideEffect ->
        when (sideEffect) {
            is VerificationEvent.OnOTPSubmit -> {
                onNextStep()
//                viewModel.inputs.onSubmitVerificationCode(sideEffect.code)
            }

            is VerificationEvent.OnResendOTPCode -> {
                viewModel.inputs.onResendOTPCode()
            }

            is VerificationEvent.OnOTPCodeChanged -> {
                viewModel.inputs.updateOtpCode(sideEffect.code)
            }

            is VerificationEvent.OnOTPSent -> {
                showOtpSentAlert()
            }

            is VerificationEvent.VerificationSuccess -> {
                onNextStep()
            }

            else -> {}
        }
    }
    AppScreen(vm = viewModel) {
        Column {
            PageTitle(properties = PageTitleProperties(
                title = stringResource(id = R.string._key0136),
                currentStep = stringResource(id = R.string._key0121),
                stringResource(id = R.string._key0122)
            ))
            Spacer(modifier = Modifier.height(LocalDimens.current.dimen34))
            OTPVerification(
                otpResendCount = otpResendCount.value,
                otpExpireDuration = otpExpireTime.value,
                defaultOtpCode = otpCode,
                referenceNumber = refNumber.value,
                onEventChanged = { event ->
                    viewModel.inputs.onOTPVerificationEventChanged(event)
                },
                properties = OTPVerificationProperties(
                    needFocus = false,
                    title = stringResource(id = R.string.key0816),
                    description = buildAttrString(
                        arrayListOf(
                            AttributeStringData(
                                text = context.getString(R.string.key0033),
                                textStyle = LocalTypography.current.text14.light.colorTxtParagraph()
                            ),
                            AttributeStringData(
                                text = viewModel.outputs.userEmail,
                                textStyle = LocalTypography.current.text14.medium.colorTxtLabel()
                            ),
                        ),
                    ),
                    iconPainter = painterResource(id = R.drawable.ic_email_otp_verification),
                    leftButtonText = stringResource(id = R.string.key0046),
                    rightButtonText = stringResource(id = R.string.key0045)
                )
            )
        }
    }
}

@Preview(showBackground = true, showSystemUi = true)
@Composable
fun PreviewEmailVerificationPage() {
    EmailVerificationPage()
}