package com.siriustech.merit.authentication.screen.usersignup.signup.step.personalifnromation

import com.siriustech.merit.authentication.screen.usersignup.signup.StepProperties

/**
 * Created by <PERSON><PERSON> <PERSON>tet
 */
enum class PersonalInformationStep(val step: Int) : StepProperties {
    PERSONAL_DETAILS(0) {
        override val showResetButton: Boolean
            get() = true
    },
    EMPLOYMENT_DETAILS(1) {
        override val showResetButton: <PERSON><PERSON><PERSON>
            get() = true
    },
    SETTLEMENT_ACCOUNT_DETAILS(2) {
        override val showResetButton: Boolean
            get() = true
    };

    companion object {
        const val TOTAL_STEP = 3
        fun fromParam(value: Int): PersonalInformationStep {
            return when (value) {
                0 -> PERSONAL_DETAILS
                1 -> EMPLOYMENT_DETAILS
                2 -> SETTLEMENT_ACCOUNT_DETAILS
                else -> PERSONAL_DETAILS
            }
        }
    }
}
