package com.siriustech.merit.authentication.screen.forgotpassword

import com.siriustech.merit.app_common.theme.AppAction

/**
 * Created by <PERSON><PERSON><PERSON>
 */
sealed interface ForgotPasswordAction : AppAction {
    data class OnEmailTextChanged(val value : String) : ForgotPasswordAction
    data object OnSubmittedEmail : ForgotPasswordAction
    data object OnOTPVerified : ForgotPasswordAction
    data object OnComparePassword : ForgotPasswordAction
    data object OnPasswordUpdated : ForgotPasswordAction
}