package com.siriustech.merit.authentication.screen.forgotpassword

import androidx.activity.compose.BackHandler
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.pager.HorizontalPager
import androidx.compose.foundation.pager.rememberPagerState
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.runtime.snapshotFlow
import androidx.compose.ui.Modifier
import androidx.compose.ui.res.stringResource
import androidx.hilt.navigation.compose.hiltViewModel
import androidx.navigation.NavController
import com.siriustech.core_ui_compose.base.SingleEventEffect
import com.siriustech.core_ui_compose.component.CommonToolbar
import com.siriustech.merit.app_common.R
import com.siriustech.merit.app_common.component.header.defaultToolbarProperties
import com.siriustech.merit.app_common.component.indicator.HorizontalPagerIndicator
import com.siriustech.merit.app_common.ext.navigateReplaceAll
import com.siriustech.merit.app_common.navigation.ForgotPasswordUpdatedSuccess
import com.siriustech.merit.app_common.navigation.ForgotPinUpdatedSuccess
import com.siriustech.merit.app_common.theme.AppScreen
import com.siriustech.merit.app_common.theme.LocalDimens
import com.siriustech.merit.authentication.screen.forgotpassword.component.ForgotCreatePasswordPage
import com.siriustech.merit.authentication.screen.forgotpassword.component.ForgotPasswordVerifyEmailInputScreen
import com.siriustech.merit.authentication.screen.forgotpassword.component.ForgotPasswordVerifyEmailOTPScreen
import kotlinx.coroutines.launch

/**
 * Created by Hein Htet
 */
@Composable
fun ForgotPasswordScreen(
    navController: NavController,
    viewModel: ForgotPasswordViewModel = hiltViewModel(),
) {

    val pagerState = rememberPagerState(pageCount = { ForgotPasswordStep.TOTAL_STEP })
    val coroutineScope = rememberCoroutineScope()

    fun onNavigateToStep(step: ForgotPasswordStep) {
        coroutineScope.launch {
            pagerState.animateScrollToPage(step.step)
        }
    }

    fun onHandleBack() {
        when {
            pagerState.currentPage != 0 -> {
                coroutineScope.launch { pagerState.animateScrollToPage(pagerState.currentPage - 1) }
            }

            else -> {
                navController.popBackStack()
            }
        }
    }


    BackHandler(enabled = true, onBack = {
        onHandleBack()
    })

    val createPassword = viewModel.outputs.createPassword.collectAsState()
    val confirmPassword = viewModel.outputs.confirmPassword.collectAsState()
    val onPasswordResetCount = viewModel.outputs.onPasswordResetCount.collectAsState()

    SingleEventEffect(sideEffectFlow = viewModel.appAction) {
        when (it) {
            is ForgotPasswordAction.OnSubmittedEmail -> {
                onNavigateToStep(ForgotPasswordStep.VERIFY_EMAIL_OTP)
            }

            is ForgotPasswordAction.OnOTPVerified -> {
                onNavigateToStep(ForgotPasswordStep.CREATE_PASSWORD)
            }

            is ForgotPasswordAction.OnPasswordUpdated -> {
                navController.navigateReplaceAll(ForgotPasswordUpdatedSuccess)
            }
        }
    }

    LaunchedEffect(pagerState) {
        snapshotFlow { pagerState.currentPage }.collect { page ->
            viewModel.inputs.updateCurrentStep(page)
        }
    }

    AppScreen(vm = viewModel) {
        Column(modifier = Modifier.fillMaxSize()) {
            ForgotPasswordToolbar(onResetPin = {
                viewModel.inputs.onReset()
            }, onBackPressed = {
                onHandleBack()
            })
            HorizontalPagerIndicator(
                pagerState = pagerState,
                modifier = Modifier.padding(top = LocalDimens.current.dimen32)
            )
            HorizontalPager(
                userScrollEnabled = false,
                state = pagerState,
                modifier = Modifier.fillMaxSize()
            ) { page ->
                val step = ForgotPasswordStep.fromParam(page)
                when (step) {
                    ForgotPasswordStep.INPUT_EMAIL -> ForgotPasswordVerifyEmailInputScreen(viewModel)
                    ForgotPasswordStep.VERIFY_EMAIL_OTP -> ForgotPasswordVerifyEmailOTPScreen(
                        viewModel
                    )

                    ForgotPasswordStep.CREATE_PASSWORD -> Box(
                        modifier = Modifier
                            .padding(
                                horizontal = LocalDimens.current.dimen12,
                                vertical = LocalDimens.current.dimen16,
                            )
                    ) {
                        ForgotCreatePasswordPage(
                            onPasswordChange = {
                                viewModel.inputs.onCreatePasswordChange(it)
                            },
                            onRepeatPasswordChange = {
                                viewModel.inputs.onConfirmPasswordChange(it)
                            },
                            onResetCount = onPasswordResetCount.value,
                            onClickedNext = {
                                viewModel.inputs.onComparePassword()
                            }
                        )
                    }
                }
            }
        }
    }

}

@Composable
fun ForgotPasswordToolbar(
    onBackPressed: () -> Unit = {},
    onResetPin: () -> Unit = {},
) {
    CommonToolbar(
        onLeftActionClicked = onBackPressed,
        onRightActionClicked = onResetPin,
        properties = defaultToolbarProperties().copy(
            title = stringResource(id = R.string.key0253),
            leftActionResId = R.drawable.ic_back_arrow
        )
    )
}
