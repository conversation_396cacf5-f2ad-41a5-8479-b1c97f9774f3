package com.siriustech.merit.authentication.screen.usersignup.landing

import android.util.Log
import androidx.lifecycle.SavedStateHandle
import androidx.lifecycle.viewModelScope
import com.core.network.base.getError
import com.siriustech.merit.apilayer.service.authentication.userinfo.GetUserInfoUseCase
import com.siriustech.merit.apilayer.service.authentication.userinfo.UserInfoResponse
import com.siriustech.merit.app_common.theme.AppViewModel
import com.siriustech.merit.app_common.utils.ErrorCode.mapToErrorDisplay
import com.siriustech.merit.app_common.utils.Validation.isValidEmail
import com.siriustech.merit.authentication.domain.cache.CacheOnboardingUserData
import com.siriustech.merit.authentication.domain.displaydata.checkonboarding.CheckOnboardingDisplayData
import dagger.hilt.android.lifecycle.HiltViewModel
import java.lang.Thread.State
import javax.inject.Inject
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.SharingStarted
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.catch
import kotlinx.coroutines.flow.collectLatest
import kotlinx.coroutines.flow.combine
import kotlinx.coroutines.flow.map
import kotlinx.coroutines.flow.onCompletion
import kotlinx.coroutines.flow.onStart
import kotlinx.coroutines.flow.stateIn
import kotlinx.coroutines.launch

/**
 * Created by Hein Htet
 */
@HiltViewModel
class SignupLandingViewModel @Inject constructor(
    private val savedStateHandle: SavedStateHandle,
    private val getUserInfoUseCase: GetUserInfoUseCase,
    private val cacheOnboardingUserData: CacheOnboardingUserData
) : AppViewModel() {
    private var _userInfoResponse = MutableStateFlow<UserInfoResponse?>(null)
    private val _onboardingStepDisplayData = MutableStateFlow(CheckOnboardingDisplayData())

    init {
        val args = SignupLandingArguments.from(savedStateHandle = savedStateHandle).args
        Log.d("SignupViewModel", "args: $args")
        _onboardingStepDisplayData.value = args.onboardingDisplayData
    }

    override val inputs = SignupInputs()
    override val outputs = SignupOutput()

    inner class SignupInputs : BaseInputs() {
        fun onGetUserInformation() = getUserInformation()
    }

    inner class SignupOutput : BaseOutputs() {
        val userInfoResponse: StateFlow<UserInfoResponse?>
            get() = _userInfoResponse

        val onboardingStepDisplayData: StateFlow<CheckOnboardingDisplayData>
            get() = _onboardingStepDisplayData

        val isResumeOnboardingStep: StateFlow<Boolean> =
            _onboardingStepDisplayData.map { it.isCompleted }.stateIn(
                viewModelScope,
                initialValue = false,
                started = SharingStarted.WhileSubscribed(5000)
            )
    }


    private fun getUserInformation() {
        scope.launch {
            getUserInfoUseCase()
                .onStart { inputs.emitLoading(true) }
                .onCompletion { inputs.emitLoading(false) }
                .catch { emitError(it.getError().mapToErrorDisplay()) }
                .collectLatest {
                    cacheUserInformation(it)
                    _userInfoResponse.value = it
                }
        }
    }

    private fun cacheUserInformation(response: UserInfoResponse) {
        response.let {
            cacheOnboardingUserData.onUpdateBasicInformation(data = cacheOnboardingUserData.basicInformationDisplayData.copy(
                chineseName = it.basic?.chineseName.orEmpty(),
                englishName = it.basic?.englishName.orEmpty(),
                phoneNumber = it.basic?.base?.phoneNumber.orEmpty(),
                email = it.basic?.base?.email.orEmpty(),
                referenceNumber = it.referenceNumber.orEmpty(),
            ))
        }
    }
}