package com.siriustech.merit.authentication.screen.usersignup.signup.step.identification.page.basicinformation

import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.text.KeyboardActions
import androidx.compose.foundation.text.KeyboardOptions
import androidx.compose.foundation.verticalScroll
import androidx.compose.runtime.Composable
import androidx.compose.runtime.collectAsState
import androidx.compose.ui.Modifier
import androidx.compose.ui.focus.FocusDirection
import androidx.compose.ui.platform.LocalFocusManager
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.input.ImeAction
import androidx.compose.ui.text.input.KeyboardType
import androidx.compose.ui.tooling.preview.Preview
import androidx.hilt.navigation.compose.hiltViewModel
import com.siriustech.core_ui_compose.base.SingleEventEffect
import com.siriustech.merit.app_common.component.button.ButtonProperties
import com.siriustech.merit.app_common.component.button.SecondaryButton
import com.siriustech.merit.app_common.component.textfield.InputBox
import com.siriustech.merit.app_common.component.textfield.InputProperties
import com.siriustech.merit.app_common.ext.colorTxtLabel
import com.siriustech.merit.app_common.theme.AppScreen
import com.siriustech.merit.app_common.theme.AppViewModel
import com.siriustech.merit.app_common.theme.LocalDimens
import com.siriustech.merit.app_common.theme.LocalTypography
import com.siriustech.merit.authentication.component.ConfigurePassword
import com.siriustech.merit.authentication.component.ConfigurePasswordProperties
import com.siriustech.merit.authentication.screen.usersignup.component.PageTitle
import com.siriustech.merit.authentication.screen.usersignup.component.PageTitleProperties
import com.siriustech.merit.app_common.R as AppCommonR

/**
 * Created by Hein Htet
 */

@Composable
fun BasicInformationPage(
    viewModel: BasicInformationViewModel = hiltViewModel(),
    onNextStep: () -> Unit = {},
) {
    AppScreen(vm = AppViewModel()) {
        BasicInformationContent(viewModel, onNextStep)
    }
}

@Composable
fun BasicInformationContent(viewModel: BasicInformationViewModel, onNextStep: () -> Unit) {

    val isValidToNextStep = viewModel.outputs.nextButtonEnabled.collectAsState()
    SingleEventEffect(sideEffectFlow = viewModel.outputs.basicInformationEvent) {
        when (it) {
            is BasicInformationEvent.OnEmailVerification -> onNextStep()
            else -> {
            }
        }
    }

    Column(
        modifier = Modifier
            .padding(horizontal = LocalDimens.current.dimen12)
    ) {
        Column(
            modifier = Modifier
                .weight(1f)
                .verticalScroll(rememberScrollState())
                .padding(bottom = LocalDimens.current.dimen16)
        ) {
            PageTitle(
                properties = PageTitleProperties(
                    title = stringResource(id = AppCommonR.string._key0118),
                    currentStep = stringResource(id = AppCommonR.string._key0120),
                    totalStep = stringResource(id = AppCommonR.string._key0122),
                    description = stringResource(id = AppCommonR.string._key0119),
                    showSeparator = true,
                )
            )
            InformationInputs(viewModel)
        }
        Spacer(modifier = Modifier.height(LocalDimens.current.dimen8))
        SecondaryButton(
            properties = ButtonProperties(
                text = stringResource(id = AppCommonR.string._key0135),
//                enabled = isValidToNextStep.value,
                enabled = true
            ),
            onClicked = {
                viewModel.inputs.onUserRegister()
            })
        Spacer(modifier = Modifier.height(LocalDimens.current.dimen8))
    }
}

@Composable
fun InformationInputs(viewModel: BasicInformationViewModel) {
    val focusManager = LocalFocusManager.current
    val information = viewModel.outputs.basicInformationDisplayData.collectAsState()
    InputBox(
        value = information.value.chineseName,
        properties = InputProperties(
            title = stringResource(id = AppCommonR.string._key0126),
            placeholder = stringResource(
                id = AppCommonR.string._key0127
            ), showRequiredStar = true,
            keyboardOptions = KeyboardOptions(imeAction = ImeAction.Next),
            keyboardActions = KeyboardActions(
                onNext = { focusManager.moveFocus(FocusDirection.Down) }
            )
        ),
        onTextChange = {
            viewModel.inputs.updateChineseName(it)
        }
    )
    InputBox(
        value = information.value.englishName,
        modifier = Modifier.padding(top = LocalDimens.current.dimen12),
        properties = InputProperties(
            title = stringResource(id = AppCommonR.string._key0128),
            placeholder = stringResource(
                id = AppCommonR.string._key0129
            ),
            showRequiredStar = true,
            keyboardOptions = KeyboardOptions(imeAction = ImeAction.Next),
            keyboardActions = KeyboardActions(
                onNext = { focusManager.moveFocus(FocusDirection.Down) }
            ),
        ),
        onTextChange = {
            viewModel.inputs.updateEnglishName(it)
        }
    )
    InputBox(
        value = information.value.referenceNumber,
        modifier = Modifier.padding(top = LocalDimens.current.dimen12),
        properties = InputProperties(
            title = stringResource(id = AppCommonR.string._key0130),
            placeholder = stringResource(
                id = AppCommonR.string._key0131
            ), infoTitleIcon = painterResource(id = AppCommonR.drawable.ic_info),
            onInfoIconClick = {},
            keyboardOptions = KeyboardOptions(imeAction = ImeAction.Next),
            keyboardActions = KeyboardActions(
                onNext = { focusManager.moveFocus(FocusDirection.Down) }
            )
        ),
        onTextChange = {
            viewModel.inputs.updateReferenceNumber(it)
        }
    )
    InputBox(
        value = information.value.phoneNumber,
        modifier = Modifier.padding(top = LocalDimens.current.dimen12),
        properties = InputProperties(
            title = stringResource(id = AppCommonR.string.key0018),
            placeholder = stringResource(
                id = AppCommonR.string._key0132
            ), showRequiredStar = true,
            keyboardOptions = KeyboardOptions(
                imeAction = ImeAction.Next,
                keyboardType = KeyboardType.Phone
            ),
            keyboardActions = KeyboardActions(
                onNext = { focusManager.moveFocus(FocusDirection.Down) }
            )
        ),
        onTextChange = {
            viewModel.inputs.updatePhoneNumber(it)
        }
    )
    InputBox(
        value = information.value.email,
        modifier = Modifier.padding(top = LocalDimens.current.dimen12),
        properties = InputProperties(
            title = stringResource(id = AppCommonR.string.key0020),
            placeholder = stringResource(
                id = AppCommonR.string._key0132
            ), showRequiredStar = true,
            keyboardOptions = KeyboardOptions(imeAction = ImeAction.Next),
            keyboardActions = KeyboardActions(
                onNext = { focusManager.moveFocus(FocusDirection.Down) }
            )
        ),
        onTextChange = {
            viewModel.inputs.updateEmailAddress(it)
        }
    )
    ConfigurePassword(
        defaultPassword = information.value.password,
        defaultConfirmPassword = information.value.confirmPassword,
        modifier = Modifier.padding(top = LocalDimens.current.dimen12),
        onPasswordChanged = {
            viewModel.inputs.updatePassword(it)
        },
        onConfirmPasswordChanged = {
            viewModel.inputs.updateRepeatPassword(it)
        },
        properties = ConfigurePasswordProperties(
            passwordTitle = stringResource(id = AppCommonR.string.key0021),
            confirmPasswordTitle = stringResource(
                id = AppCommonR.string.key0027,
            ),
            placeholderPassword = stringResource(id = AppCommonR.string._key0133),
            placeholderConfirmPassword = stringResource(id = AppCommonR.string._key0134),
            confirmPasswordTitleStyle = LocalTypography.current.text14.medium.colorTxtLabel(),
            passwordTitleStyle = LocalTypography.current.text14.medium.colorTxtLabel(),
        )
    )
}

@Preview(showBackground = true, showSystemUi = true)
@Composable
fun PreviewBasicInformationContent() {
    Column(modifier = Modifier.fillMaxSize()) {
        BasicInformationContent(viewModel = hiltViewModel(), onNextStep = {})
    }
}