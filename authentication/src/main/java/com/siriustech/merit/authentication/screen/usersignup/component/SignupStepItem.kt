package com.siriustech.merit.authentication.screen.usersignup.component

import androidx.compose.foundation.Image
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.tooling.preview.Preview
import com.siriustech.merit.app_common.ext.AttributeStringData
import com.siriustech.merit.app_common.ext.buildAttrString
import com.siriustech.merit.app_common.ext.colorTxtParagraph
import com.siriustech.merit.app_common.ext.colorTxtTitle
import com.siriustech.merit.app_common.theme.LocalDimens
import com.siriustech.merit.app_common.theme.LocalTypography
import com.siriustech.merit.app_common.R as AppCommonR

/**
 * Created by Hein Htet
 */
@Composable
fun SignUpStepItem(
    modifier: Modifier,
    isFinish: Boolean = false,
    prefix: String, description: String,
) {
    Box(
        modifier = Modifier
            .fillMaxWidth()
            .height(LocalDimens.current.dimen44)
            .clip(RoundedCornerShape(LocalDimens.current.dimen4))
            .then(modifier),
        contentAlignment = Alignment.Center
    ) {
        Row(verticalAlignment = Alignment.CenterVertically) {
            Image(
                painter = painterResource(id = if (!isFinish) AppCommonR.drawable.ic_disable_check else AppCommonR.drawable.ic_match),
                contentDescription = "Check Image Resource"
            )
            Spacer(modifier = Modifier.width(LocalDimens.current.dimen4))
            Text(
                modifier = Modifier.fillMaxWidth(),
                textAlign = TextAlign.Start,
                text = buildAttrString(
                    listOf(
                        AttributeStringData(
                            text = prefix,
                            textStyle = LocalTypography.current.text14.semiBold.colorTxtTitle(),
                        ),
                        AttributeStringData(
                            text = " $description",
                            textStyle = LocalTypography.current.text14.light.colorTxtParagraph(),
                        )
                    )
                )
            )
        }
    }
}

@Preview
@Composable
fun PreviewSignUpStepItem() {
    SignUpStepItem(Modifier, isFinish = false, "Step 01", "Identification")
}
