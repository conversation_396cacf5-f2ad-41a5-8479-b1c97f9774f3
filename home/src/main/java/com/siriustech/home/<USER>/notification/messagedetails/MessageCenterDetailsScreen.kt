package com.siriustech.home.screen.notification.messagedetails

import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.padding
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.tooling.preview.Preview
import androidx.navigation.NavController
import androidx.navigation.compose.rememberNavController
import com.siriustech.merit.app_common.component.common.NotificationItemDisplayData
import com.siriustech.merit.app_common.component.header.CommonToolbarWithBackMenu
import com.siriustech.merit.app_common.ext.colorTxtParagraph
import com.siriustech.merit.app_common.navigation.argument.notification.MessageCenterDetailsArgument
import com.siriustech.merit.app_common.theme.AppScreen
import com.siriustech.merit.app_common.theme.AppViewModel
import com.siriustech.merit.app_common.theme.LocalDimens
import com.siriustech.merit.app_common.theme.LocalTypography

/**
 * Created by <PERSON><PERSON>
 */

@Composable
fun MessageCenterDetailsScreen(
    navController: NavController,
    argument: MessageCenterDetailsArgument,
) {
    AppScreen(vm = AppViewModel()) {
        Column(modifier = Modifier.fillMaxSize()) {
            CommonToolbarWithBackMenu(
                onBackPressed = { navController.popBackStack() },
                title = argument.notificationItemDisplayData.title,
            )
            Text(
                modifier = Modifier.padding(LocalDimens.current.dimen12),
                text = argument.notificationItemDisplayData.description,
                style = LocalTypography.current.text14.light.colorTxtParagraph()
            )
        }
    }
}

@Preview(showBackground = true, showSystemUi = true)
@Composable
fun PreviewMessageCenterDetailsScreen() {
    MessageCenterDetailsScreen(
        rememberNavController(),
        MessageCenterDetailsArgument(
            notificationItemDisplayData = NotificationItemDisplayData(
                title = "Title",
                description = "Description"
            )
        )
    )
}