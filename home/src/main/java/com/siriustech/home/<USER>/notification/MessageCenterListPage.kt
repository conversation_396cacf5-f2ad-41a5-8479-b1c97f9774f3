package com.siriustech.home.screen.notification

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.verticalScroll
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.style.TextAlign
import com.siriustech.merit.app_common.R
import com.siriustech.merit.app_common.component.common.MessageCenterItem
import com.siriustech.merit.app_common.component.common.NotificationItem
import com.siriustech.merit.app_common.component.common.NotificationItemDisplayData
import com.siriustech.merit.app_common.component.container.PaddingTop
import com.siriustech.merit.app_common.ext.colorTxtInactive
import com.siriustech.merit.app_common.theme.LocalAppColor
import com.siriustech.merit.app_common.theme.LocalDimens
import com.siriustech.merit.app_common.theme.LocalTypography

/**
 * Created by Hein Htet
 */

@Composable
fun MessageCenterListPage(
    items: List<NotificationItemDisplayData>,
    viewModel: NotificationViewModel,
) {
    Box(modifier = Modifier.fillMaxSize(), contentAlignment = Alignment.Center) {
        if (items.isNotEmpty()) {
            Column(
                modifier = Modifier
                    .fillMaxSize()
                    .padding(horizontal = LocalDimens.current.dimen12)
                    .verticalScroll(rememberScrollState())
            ) {
                items.forEachIndexed { index, notificationItemDisplayData ->
                    MessageCenterItem(
                        onClicked = {
                            viewModel.onTriggerActions(NotificationActions.MarkAsReadItem(item = notificationItemDisplayData))
                            viewModel.onTriggerActions(NotificationActions.OnNavigateToMessageCenterDetails(data = notificationItemDisplayData))
                        },
                        data = notificationItemDisplayData,
                        modifier = Modifier
                            .background(if (notificationItemDisplayData.isRead) LocalAppColor.current.bgDefault else LocalAppColor.current.bgAccent)
                            .clip(RoundedCornerShape(LocalDimens.current.dimen2))
                    )
                    PaddingTop(value = LocalDimens.current.dimen8)
                }
            }
        }
        if (items.isEmpty()) {
            Text(
                text = stringResource(id = R.string.key0652),
                textAlign = TextAlign.Center,
                style = LocalTypography.current.text14.light.colorTxtInactive()
            )
        }
    }
}