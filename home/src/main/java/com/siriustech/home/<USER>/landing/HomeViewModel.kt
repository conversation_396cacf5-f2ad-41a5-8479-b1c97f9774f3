package com.siriustech.home.screen.landing

import androidx.compose.runtime.mutableStateListOf
import androidx.compose.runtime.snapshots.SnapshotStateList
import androidx.lifecycle.viewModelScope
import com.core.network.base.getError
import com.core.util.toAmount
import com.siriustech.core_ui_compose.model.ErrorDisplay
import com.siriustech.home.domain.HomeMapper.mapToBannerDisplayList
import com.siriustech.home.domain.HomeMapper.mapToHomeWalletSummaryDisplayData
import com.siriustech.home.domain.NotificationMapper.mapToNotificationDisplay
import com.siriustech.home.domain.display.BannerDisplayModel
import com.siriustech.home.domain.display.HomeWalletSummaryDisplayData
import com.siriustech.home.screen.refinehomepage.HomePageRefineArgument
import com.siriustech.merit.apilayer.service.authentication.common.eumeration.EnumerationRequest
import com.siriustech.merit.apilayer.service.authentication.common.eumeration.GetEnumerationUseCase
import com.siriustech.merit.apilayer.service.authentication.userinfo.GetUserInfoUseCase
import com.siriustech.merit.apilayer.service.home.banner.GetBannerListUseCase
import com.siriustech.merit.apilayer.service.home.notification.NotificationRequest
import com.siriustech.merit.apilayer.service.home.notification.NotificationUseCase
import com.siriustech.merit.apilayer.service.home.watchlist.MarketWatchListRequest
import com.siriustech.merit.apilayer.service.home.watchlist.MarketWatchListUseCase
import com.siriustech.merit.apilayer.service.market.exchangecurrency.MarketCurrencyRequest
import com.siriustech.merit.apilayer.service.market.exchangecurrency.MarketExchangeCurrencyUseCase
import com.siriustech.merit.apilayer.service.market.exchangecurrency.defaultMarketExchangeCurrencyRequestList
import com.siriustech.merit.apilayer.service.user.userinfo.GetUserBasicInfoUseCase
import com.siriustech.merit.apilayer.service.wallet.summary.WalletSummaryRequest
import com.siriustech.merit.apilayer.service.wallet.summary.WalletSummaryUseCase
import com.siriustech.merit.app_common.component.common.NotificationItemDisplayData
import com.siriustech.merit.app_common.component.common.NotificationTabType
import com.siriustech.merit.app_common.component.marquee.StockExchangeMarqueeData
import com.siriustech.merit.app_common.data.AppCache
import com.siriustech.merit.app_common.data.CommonSharedPreferences
import com.siriustech.merit.app_common.data.display.AssetClassModel
import com.siriustech.merit.app_common.data.display.MarketAssetDisplayData
import com.siriustech.merit.app_common.data.display.UserBasicInfoDisplay
import com.siriustech.merit.app_common.mapper.MarketExchangeCurrencyMapper.mapToStockExchangeDisplayData
import com.siriustech.merit.app_common.theme.AppAction
import com.siriustech.merit.app_common.theme.AppViewModel
import com.siriustech.merit.app_common.typeenum.DropDownEnumeration
import com.siriustech.merit.app_common.typeenum.HomeMarketListType
import com.siriustech.merit.app_common.typeenum.HomeShortcutMenuAction
import com.siriustech.merit.app_common.typeenum.MarketCategoryType
import com.siriustech.merit.app_common.typeenum.PortfolioAssetType
import com.siriustech.merit.app_common.typeenum.PortfolioListDisplayType
import com.siriustech.merit.app_common.typeenum.RiskLevel
import com.siriustech.merit.app_common.typeenum.UserStatus
import com.siriustech.merit.app_common.utils.ErrorCode.mapToErrorDisplay
import dagger.hilt.android.lifecycle.HiltViewModel
import javax.inject.Inject
import javax.inject.Named
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.SharingStarted
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.catch
import kotlinx.coroutines.flow.collectLatest
import kotlinx.coroutines.flow.flatMapLatest
import kotlinx.coroutines.flow.map
import kotlinx.coroutines.flow.onCompletion
import kotlinx.coroutines.flow.onStart
import kotlinx.coroutines.flow.stateIn
import kotlinx.coroutines.launch

/**
 * Created by Hein Htet
 */
@HiltViewModel
class HomeViewModel @Inject constructor(
    private val marketExchangeCurrencyUseCase: MarketExchangeCurrencyUseCase,
    private val walletSummaryUseCase: WalletSummaryUseCase,
    private val getUserInfoUseCase: GetUserInfoUseCase,
    private val getUserBasicInfoUseCase: GetUserBasicInfoUseCase,
    private val watchListUseCase: MarketWatchListUseCase,
    private val notificationUseCase: NotificationUseCase,
    private val bannerListUseCase: GetBannerListUseCase,
    private val getEnumerationUseCase: GetEnumerationUseCase,
    private val commonSharedPreferences: CommonSharedPreferences,
    private val appCache: AppCache,
    @Named("BaseUrl") private val baseUrl: String,
) : AppViewModel() {

    private val _displayMarketListType = MutableStateFlow(appCache.homeMarketListType)
    private val _displayStockExchangeItems = mutableStateListOf<StockExchangeMarqueeData>()
    private val _displayHomeWalletSummaryData = MutableStateFlow(HomeWalletSummaryDisplayData())
    private val _displayWatchListData =
        MutableStateFlow<MutableMap<String, List<MarketAssetDisplayData>>>(mutableMapOf())
    private val _notifications = mutableStateListOf<NotificationItemDisplayData>()
    private val _userBasicInfoDisplay =
        MutableStateFlow(UserBasicInfoDisplay())

    private val _availableMarketWatchListAllocation = MutableStateFlow(
        listOf(
            PortfolioAssetType.CASH_EQUIVALENT,
            PortfolioAssetType.FIX_INCOME,
            PortfolioAssetType.MULTI_ASSET,
            PortfolioAssetType.STRUCTURED_PRODUCTS,
            PortfolioAssetType.PRIVATE_CAPITAL,
            PortfolioAssetType.EQUITY,
            PortfolioAssetType.COMMODITIES,
            PortfolioAssetType.VIRTUAL_ASSET,
        )
    )

    private val _selectedWatchListIndex =
        MutableStateFlow(_availableMarketWatchListAllocation.value.indexOf(appCache.homePageSelectedAllocationClass))

    private val _banners = mutableStateListOf<BannerDisplayModel>()

    init {
//        getMarketExchangeCurrency()
        getWalletSummaryData()
        getUserInfo()
        getHomeMarketData(_selectedWatchListIndex.value)
        getNotification()
        getUserBasicInfo()
        getBannerList()
    }

    inner class HomeScreenInputs : BaseInputs() {
        fun onGetWalletSummary() = getWalletSummaryData()

        fun onMarketListTypeChange(value: HomeMarketListType) {
            _displayMarketListType.value = value
            getHomeMarketData(_selectedWatchListIndex.value)
        }

        fun applyRefine(argument: HomePageRefineArgument) {
            appCache.updateHomeMarketListType(argument.sortType)
            appCache.updateHomePageSelectedAllocationClass(argument.allocationAssetType)
            _displayMarketListType.value = argument.sortType
            val index =
                _availableMarketWatchListAllocation.value.indexOf(argument.allocationAssetType)
            _selectedWatchListIndex.value = index
            getHomeMarketData(index)
        }

        fun updateMenuActionCache(menu: HomeShortcutMenuAction) {
            appCache.updateMenuCache(menu)
        }

        fun updateFavoriteMenuCacheAction(title: String) {
            appCache.updateSelectedMarketAllocationClass(MarketCategoryType.FAVORITE.value, title)
        }
    }

    inner class HomeScreenOutputs : BaseOutputs() {
        val displayStockExchangeItems: SnapshotStateList<StockExchangeMarqueeData>
            get() = _displayStockExchangeItems

        val displayHomeWalletSummaryData: StateFlow<HomeWalletSummaryDisplayData>
            get() = _displayHomeWalletSummaryData

        val displayMarketListType: StateFlow<HomeMarketListType>
            get() = _displayMarketListType

        val availableMarketWatchListAllocation: StateFlow<List<PortfolioAssetType>>
            get() = _availableMarketWatchListAllocation

        val displayWatchListData: StateFlow<MutableMap<String, List<MarketAssetDisplayData>>>
            get() = _displayWatchListData

        val notifications: SnapshotStateList<NotificationItemDisplayData>
            get() = _notifications

        val sessionId: String
            get() = commonSharedPreferences.sessionId

        val displayUserBasicInfoDisplay: StateFlow<UserBasicInfoDisplay>
            get() = _userBasicInfoDisplay


        val selectedAllocationClass = _selectedWatchListIndex.map {
            availableMarketWatchListAllocation.value[it]
        }.stateIn(
            viewModelScope,
            initialValue = availableMarketWatchListAllocation.value.first(),
            started = SharingStarted.WhileSubscribed(5000)
        )

        val selectedWatchListIndex: StateFlow<Int>
            get() = _selectedWatchListIndex

        val banners: SnapshotStateList<BannerDisplayModel>
            get() = _banners

        val userStatus : UserStatus
            get() = appCache.userBasicInfoDisplay?.userStatus ?: UserStatus.PENDING_REVIEW


    }

    override fun onTriggerActions(action: AppAction) {
        when (action) {
            is HomeActions.OnMarketListTypeChanged -> {
                _displayMarketListType.value = action.type
                getHomeMarketData(_selectedWatchListIndex.value)

            }

            is HomeActions.OnMarketWatchListPageChanged -> {
                _selectedWatchListIndex.value = action.page
                getHomeMarketData(action.page)
            }

            else -> super.onTriggerActions(action)
        }
    }

    override val inputs = HomeScreenInputs()
    override val outputs = HomeScreenOutputs()

    private fun getMarketExchangeCurrency() {
        scope.launch {
            marketExchangeCurrencyUseCase(
                param = MarketCurrencyRequest(
                    currencyList = defaultMarketExchangeCurrencyRequestList
                )
            )
                .onStart { emitHorizontalLoading(true) }
                .onCompletion { emitHorizontalLoading(false) }
                .catch { }
                .collectLatest {
                    _displayStockExchangeItems.clear()
                    _displayStockExchangeItems.addAll(it.mapToStockExchangeDisplayData())
                }
        }
    }

    private fun getWalletSummaryData() {
        scope.launch {
            getEnumerationUseCase(param = EnumerationRequest(codeList = listOf(DropDownEnumeration.ASSET_CLASS.name)))
                .flatMapLatest { enumValue ->
                    val allocationClass = enumValue.list.firstOrNull()?.list.orEmpty()
                    val displayData = allocationClass.map {
                        AssetClassModel(
                            id = it.value,
                            title = it.desc,
                        )
                    }
                    appCache.updateAssetAllocationClass(displayData)
                    walletSummaryUseCase(param = WalletSummaryRequest(summaryBy = PortfolioListDisplayType.ALLOCATION_CLASS.type))
                }
                .onStart { emitHorizontalLoading(true) }
                .onCompletion { emitHorizontalLoading(false) }
                .catch { emitError(it.getError().mapToErrorDisplay()) }
                .collectLatest {
                    _displayHomeWalletSummaryData.value =
                        it.mapToHomeWalletSummaryDisplayData(_displayHomeWalletSummaryData.value,appCache)
                }
        }
    }

    private fun getUserInfo() {
        scope.launch {
            getUserInfoUseCase(param = Unit)
                .onStart { emitHorizontalLoading(true) }
                .onCompletion { emitHorizontalLoading(false) }
                .catch { emitError(it.getError().mapToErrorDisplay()) }
                .collectLatest {
                    _displayHomeWalletSummaryData.value = _displayHomeWalletSummaryData.value.copy(
                        userName = it.basic?.englishName ?: "-"
                    )
                }
        }
    }

    private fun getUserBasicInfo() {
        scope.launch {
            getUserBasicInfoUseCase(param = Unit)
                .onStart { emitHorizontalLoading(true) }
                .onCompletion { emitHorizontalLoading(false) }
                .catch {  }
                .collectLatest {
                    val info = UserBasicInfoDisplay(
                        name = it.fullName.orEmpty(),
                        email = it.email.orEmpty(),
                        mobile = it.mobile.orEmpty(),
                        language = it.language.orEmpty(),
                        profileImage = it.profilePicture.orEmpty(),
                        userStatus = outputs.userStatus
                    )
                    appCache.updateUserBasicInfo(info)
                    _userBasicInfoDisplay.value = info
                }
        }
    }

    private fun getHomeMarketData(index: Int) {
        scope.launch {
            watchListUseCase(
                param = MarketWatchListRequest(
//                    allocationClass = _availableMarketWatchListAllocation.value[index].assetName,
                    topType = _displayMarketListType.value.value
                )
            )
                .onStart { emitHorizontalLoading(true) }
                .onCompletion { emitHorizontalLoading(false) }
                .catch {}
                .collectLatest {
                    val currentMap = _displayWatchListData.value
                    val assetType = _availableMarketWatchListAllocation.value[index].assetName
                    currentMap[assetType] = it.assetList.orEmpty().map {
                        MarketAssetDisplayData(
                            id = (it.instrumentId ?: -1).toString(),
                            symbol = it.symbol.orEmpty(),
                            unrealizedGl = it.valueChange.orEmpty(),
                            unrealizedGlRate = it.valueChangeRate.orEmpty(),
                            currency = it.currency.orEmpty(),
                            riskLevel = RiskLevel.fromParam(it.riskLevel.orEmpty()),
                            exchange = it.exchange.orEmpty(),
                            name = it.name.orEmpty(),
                            venue = it.exchange.orEmpty(),
                            logo = it.logo.orEmpty(),
                            marketValue = it.value?.toAmount(4) ?: "-"
                        )
                    }
                    _displayWatchListData.value = currentMap
                }
        }
    }


    private fun getNotification() {
        scope.launch {
            notificationUseCase(
                param = NotificationRequest(
                    messageType = NotificationTabType.NOTIFICATIONS.value
                )
            )
                .onStart { emitHorizontalLoading(true) }
                .onCompletion { emitHorizontalLoading(false) }
                .catch {}
                .collectLatest {
                    _notifications.clear()
                    _notifications.addAll(it.mapToNotificationDisplay(homePage = true))
                }
        }
    }

    private fun getBannerList() {
        scope.launch {
            bannerListUseCase(param = Unit)
                .onStart { }
                .onCompletion { }
                .catch {}
                .collectLatest {
                    _banners.addAll(it.mapToBannerDisplayList(baseUrl))
                }
        }
    }

    fun testSessionTimeOut() {
        commonSharedPreferences.mockSessionTimeOut = true
        getNotification()
    }
}