package com.siriustech.home.modal

import androidx.compose.foundation.Image
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.ModalBottomSheet
import androidx.compose.material3.Text
import androidx.compose.material3.rememberModalBottomSheetState
import androidx.compose.runtime.Composable
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.tooling.preview.Preview
import com.siriustech.merit.app_common.R
import com.siriustech.merit.app_common.component.common.NotificationItemDisplayData
import com.siriustech.merit.app_common.component.container.IconLabelAction
import com.siriustech.merit.app_common.component.container.IconLabelActionProperties
import com.siriustech.merit.app_common.component.container.PaddingStart
import com.siriustech.merit.app_common.component.container.PaddingTop
import com.siriustech.merit.app_common.component.separator.SeparatorLine
import com.siriustech.merit.app_common.ext.colorTxtInactive
import com.siriustech.merit.app_common.ext.colorTxtParagraph
import com.siriustech.merit.app_common.ext.colorTxtTitle
import com.siriustech.merit.app_common.theme.LocalAppColor
import com.siriustech.merit.app_common.theme.LocalDimens
import com.siriustech.merit.app_common.theme.LocalTypography
import com.siriustech.merit.app_common.typeenum.NotificationBusinessType
import com.siriustech.merit.app_common.typeenum.NotificationTagType
import kotlinx.coroutines.launch

/**
 * Created by Hein Htet
 */
@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun ManageNotificationModal(
    data: NotificationItemDisplayData,
    onDismissed: () -> Unit = {},
    onDeleteNotification: (data: NotificationItemDisplayData) -> Unit,
    onNavigateToOrder: (data: NotificationItemDisplayData) -> Unit,
    onNavigateToWithdrawal: (data: NotificationItemDisplayData) -> Unit,
    onNavigateToDeposit: (data: NotificationItemDisplayData) -> Unit,
) {
    val sheetState = rememberModalBottomSheetState(skipPartiallyExpanded = true)
    val scope = rememberCoroutineScope()

    fun onDismiss() {
        scope.launch {
            sheetState.hide()
            onDismissed()
        }
    }

    ModalBottomSheet(
        dragHandle = {},
        shape = RoundedCornerShape(LocalDimens.current.dimen4),
        containerColor = LocalAppColor.current.bgDefault,
        onDismissRequest = {
            onDismiss()
        }, sheetState = sheetState,
        modifier = Modifier
    ) {
        ManageNotificationModalContent(
            onDeleteNotification = {
                onDismiss()
                onDeleteNotification(data)
            },
            onNavigateToWithdrawal = {
                onDismiss()
                onNavigateToWithdrawal(data)
            },
            onNavigateToDeposit = {
                onDismiss()
                onNavigateToDeposit(data)
            },
            onNavigateToOrder = {
                onDismiss()
                onNavigateToOrder(data)
            },
            onDismissed = { onDismiss() },
            data = data
        )
    }

}

@Composable
fun ManageNotificationModalContent(
    onNavigateToWithdrawal: () -> Unit = {},
    onNavigateToOrder: () -> Unit = {},
    onNavigateToDeposit: () -> Unit = {},
    onDeleteNotification: () -> Unit = {},
    onDismissed: () -> Unit = {},
    data: NotificationItemDisplayData = NotificationItemDisplayData(),
) {
    var actionTitle = ""
    val actionTrigger: (() -> Unit?)?
    when (data.type) {
        NotificationBusinessType.ORDER -> {
            actionTitle = stringResource(id = R.string.key0674)
            actionTrigger = onNavigateToOrder
        }

        NotificationBusinessType.WITHDRAW -> {
            actionTitle = stringResource(id = R.string.key0675)
            actionTrigger = onNavigateToWithdrawal
        }

        NotificationBusinessType.DEPOSIT -> {
            actionTitle = stringResource(id = R.string.key0673)
            actionTrigger = onNavigateToDeposit
        }

        else -> {
            actionTitle = ""
            actionTrigger = null
        }
    }
    Column(
        Modifier.padding(
            horizontal = LocalDimens.current.dimen12,
            vertical = LocalDimens.current.dimen16,
        ),
    ) {
        ManageNotificationModalHeader(onDismissed = {
            onDismissed()
        })
        PaddingTop(value = LocalDimens.current.dimen12)
        NotificationItemTitle(data)
        PaddingTop(value = LocalDimens.current.dimen12)
        Text(
            text = data.description,
            style = LocalTypography.current.text14.light.colorTxtParagraph()
        )
        SeparatorLine(modifier = Modifier.padding(vertical = LocalDimens.current.dimen12))
        IconLabelAction(
            onClick = {
                actionTrigger?.invoke()
            }, properties = IconLabelActionProperties(
                title = actionTitle,
                leftIconResId = R.drawable.ic_report_file_black,
                rightActionResId = R.drawable.ic_right_arrow
            )
        )
        IconLabelAction(
            onClick = { onDeleteNotification() }, properties = IconLabelActionProperties(
                title = stringResource(id = R.string.key0672),
                leftIconResId = R.drawable.ic_trash_bin_black,
            )
        )
    }
}


@Composable
fun ManageNotificationModalHeader(onDismissed: () -> Unit = {}) {
    Row(
        modifier = Modifier.fillMaxWidth(),
        verticalAlignment = Alignment.CenterVertically
    ) {
        Image(
            painter = painterResource(id = R.drawable.ic_three_dots),
            contentDescription = "Reset Image Resource"
        )
        Spacer(modifier = Modifier.width(LocalDimens.current.dimen4))
        Text(
            text = "Manage Notification",
            modifier = Modifier.weight(1f),
            style = LocalTypography.current.text14.regular.colorTxtTitle()
        )
        Image(
            modifier = Modifier.clickable {
                onDismissed()
            },
            painter = painterResource(id = R.drawable.ic_action_close),
            contentDescription = "CLOSE ACTION"
        )
    }
}

@Composable
fun NotificationItemTitle(data: NotificationItemDisplayData) {
    var status = ""
    val color: Color
    when (data.tag) {
        NotificationTagType.APPROVED -> {
            status = stringResource(id = R.string.key0687)
            color = LocalAppColor.current.txtPositive
        }

        NotificationTagType.REJECTED -> {
            status = stringResource(id = R.string.key0688)
            color = LocalAppColor.current.txtNegative
        }

        else -> {
            status = "-"
            color = LocalAppColor.current.txtParagraph
        }
    }

    Column {
        Row(verticalAlignment = Alignment.CenterVertically) {
            when (data.type) {
                NotificationBusinessType.DEPOSIT -> {
                    status = "${stringResource(id = R.string.key0350)} $status"
                    Image(
                        modifier = Modifier.size(LocalDimens.current.dimen24),
                        painter = painterResource(id = R.drawable.ic_status_deposit),
                        contentDescription = "Deposit Image Resource"
                    )
                }

                NotificationBusinessType.WITHDRAW -> {
                    status = "${stringResource(id = R.string.key0955)} $status"
                    Image(
                        modifier = Modifier.size(LocalDimens.current.dimen24),
                        painter = painterResource(id = R.drawable.ic_status_withdraw),
                        contentScale = ContentScale.Crop,
                        contentDescription = "Deposit Image Resource"
                    )
                }

                else -> {
                    status = "${stringResource(id = R.string.key0720)} $status"
                    Image(
                        modifier = Modifier.size(LocalDimens.current.dimen24),
                        contentScale = ContentScale.Crop,
                        painter = painterResource(id = R.drawable.ic_product_category_placeholder),
                        contentDescription = "Deposit Image Resource"
                    )
                }
            }
            PaddingStart(value = LocalDimens.current.dimen8)
            Text(text = data.title, style = LocalTypography.current.text14.regular.colorTxtTitle())
            Spacer(modifier = Modifier.weight(1f))
            Row(verticalAlignment = Alignment.CenterVertically) {
                Image(
                    painter = painterResource(id = R.drawable.ic_chart_time_frame),
                    contentDescription = "Clock Image Resource"
                )
                Text(
                    text = data.displayDateTime,
                    style = LocalTypography.current.text14.regular.colorTxtInactive()
                )
            }
        }
        PaddingTop(value = LocalDimens.current.dimen12)
        Text(text = status, style = LocalTypography.current.text14.regular.copy(color))
    }
}

@Preview(showBackground = true)
@Composable
fun PreviewManageNotificationModal() {
    ManageNotificationModalContent(
        onDeleteNotification = {},
        onNavigateToWithdrawal = {},
        onNavigateToOrder = {},
        onNavigateToDeposit = {},
        data = NotificationItemDisplayData(
            type = NotificationBusinessType.ORDER,
            logo = "",
            title = "Asset",
            tag = NotificationTagType.APPROVED,
            description = "This is description",
            displayDateTime = ""
        )
    )
}