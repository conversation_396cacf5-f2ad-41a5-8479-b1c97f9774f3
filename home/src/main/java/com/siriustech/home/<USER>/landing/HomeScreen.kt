package com.siriustech.home.screen.landing

import android.app.Activity
import androidx.activity.compose.rememberLauncherForActivityResult
import androidx.activity.result.contract.ActivityResultContracts
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.pager.rememberPagerState
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.verticalScroll
import androidx.compose.runtime.Composable
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.tooling.preview.Preview
import androidx.fragment.app.FragmentActivity
import androidx.hilt.navigation.compose.hiltViewModel
import androidx.navigation.NavController
import androidx.navigation.compose.rememberNavController
import com.siriustech.core_ui_compose.base.SingleEventEffect
import com.siriustech.home.navigation.HomeNavigationEntryPoint
import com.siriustech.home.screen.landing.component.HomeActionShortcuts
import com.siriustech.home.screen.landing.component.HomeBanner
import com.siriustech.home.screen.landing.component.HomeMarketListBox
import com.siriustech.home.screen.landing.component.HomeWalletSummary
import com.siriustech.home.screen.refinehomepage.HomeAssetRefineActivity
import com.siriustech.home.screen.refinehomepage.HomePageRefineArgument
import com.siriustech.merit.app_common.R
import com.siriustech.merit.app_common.component.alert.BannerAlertProperties
import com.siriustech.merit.app_common.component.header.DashboardToolbar
import com.siriustech.merit.app_common.component.header.DashboardToolbarProperties
import com.siriustech.merit.app_common.component.loading.HorizontalProgressBar
import com.siriustech.merit.app_common.component.modalbts.ExchangeCurrencyModalBottomSheet
import com.siriustech.merit.app_common.component.modalbts.LimitedAccessBottomSheet
import com.siriustech.merit.app_common.component.separator.SeparatorLine
import com.siriustech.merit.app_common.navigation.InstrumentSearch
import com.siriustech.merit.app_common.screen.appwebview.AppWebViewArgument
import com.siriustech.merit.app_common.theme.AppScreen
import com.siriustech.merit.app_common.theme.LocalDimens
import com.siriustech.merit.app_common.typeenum.HomeShortcutMenuAction
import com.siriustech.merit.app_common.typeenum.UserStatus
import dagger.hilt.android.EntryPointAccessors

/**
 * Created by Hein Htet
 */
@Composable
fun HomeScreen(
    navController: NavController,
    viewModel: HomeViewModel = hiltViewModel(),
) {

    var showCurrencyExchangeModal by remember {
        mutableStateOf(false)
    }

    val activity = LocalContext.current as FragmentActivity
    val navigation = remember {
        EntryPointAccessors.fromApplication(activity, HomeNavigationEntryPoint::class.java)
            .homeNavigation()
    }
    val exchangeMarqueeData = viewModel.outputs.displayStockExchangeItems
    val showLoading = viewModel.showHorizontalLoading.collectAsState()
    val displayHomeWalletSummaryData =
        viewModel.outputs.displayHomeWalletSummaryData.collectAsState()
    val displayMarketListType = viewModel.outputs.displayMarketListType.collectAsState()
    val availableMarketWatchListAllocation =
        viewModel.outputs.availableMarketWatchListAllocation.collectAsState()
    val displayWatchListData = viewModel.outputs.displayWatchListData.collectAsState()
    val selectedAllocationClass = viewModel.outputs.selectedAllocationClass.collectAsState()
    val notifications = viewModel.outputs.notifications
    val selectedAllocationIndex = viewModel.outputs.selectedWatchListIndex.collectAsState()
    val pagerState =
        rememberPagerState(
            pageCount = { availableMarketWatchListAllocation.value.size },
            initialPage = selectedAllocationIndex.value
        )

    val userProfile = viewModel.outputs.displayUserBasicInfoDisplay.collectAsState()

    val banners = viewModel.outputs.banners

    val context = LocalContext.current

    var showLimitedAccessModal by remember {
        mutableStateOf(false)
    }

    val refineCallback =
        rememberLauncherForActivityResult(contract = ActivityResultContracts.StartActivityForResult()) { result ->
            if (result.resultCode == Activity.RESULT_OK) {
                val argument =
                    result.data?.getSerializableExtra(HomeAssetRefineActivity.EXTRA_HOME_REFINE_DATA) as? HomePageRefineArgument?
                argument?.let {
                    viewModel.inputs.applyRefine(it)
                }
            }
        }

    val reportGenerationCallback =
        rememberLauncherForActivityResult(contract = ActivityResultContracts.StartActivityForResult()) { result ->
            if (result.resultCode == Activity.RESULT_OK) {
                viewModel.emitBannerAlert(
                    BannerAlertProperties(
                        title = activity.getString(R.string.key0676),
                        description = activity.getString(R.string.key0676)
                    )
                )
            }
        }

    SingleEventEffect(sideEffectFlow = viewModel.appAction) {
        when (it) {
            is HomeActions.OnToggleCurrencyPairModal -> {
                showCurrencyExchangeModal = it.show
            }

            is HomeActions.OnMarketWatchListItemClicked -> {
                navigation.onNavigateToMarketProfileDetails(activity, navController, it.item)
            }

            is HomeActions.OnNavigateToMarketTab -> {
                navigation.onNavigateToMarketTab(activity, navController)
            }

            is HomeActions.OnNavigateToNotificationList -> {
//                navController.navigate(NotificationListing)
                navigation.onNavigateToNotification(activity)
            }

            is HomeActions.OnNavigateToSettings -> {
                navigation.onNavigateToSettings(activity)
            }

            is HomeActions.OnNavigateToChat -> {
                navigation.onNavigateToChatActivity(activity)
            }

            is HomeActions.OnNavigateToRefineActivity -> {
                navigation.onNavigateToHomePageRefineActivity(
                    activity, HomePageRefineArgument(
                        sortType = viewModel.outputs.displayMarketListType.value,
                        allocationAssetType = selectedAllocationClass.value,
                    ), refineCallback
                )
            }

            is HomeActions.OnShortcutMenuItemSelected -> {
                when (it.menuAction) {
                    HomeShortcutMenuAction.DEPOSIT, HomeShortcutMenuAction.WITHDRAW -> {
                        if (viewModel.outputs.userStatus != UserStatus.NORMAL) {
                            showLimitedAccessModal = true
                        } else {
                            viewModel.inputs.updateMenuActionCache(it.menuAction)
                            navigation.onNavigateToPortfolioTab(activity, navController)
                        }
                    }

                    HomeShortcutMenuAction.FAVORITES -> {
                        viewModel.inputs.updateFavoriteMenuCacheAction(
                            context.getString(R.string.key0443)
                        )
                        navigation.onNavigateToMarketTab(activity, navController)
                    }

                    HomeShortcutMenuAction.STATEMENT -> {
                        if (viewModel.outputs.userStatus != UserStatus.NORMAL) {
                            showLimitedAccessModal = true
                        } else {
                            navigation.onNavigateToReportStatementActivity(
                                activity,
                                reportGenerationCallback
                            )
                        }
                    }

                    else -> {

                    }
                }
            }
        }
    }

    AppScreen(vm = viewModel, ignorePaddingValue = true) {
        Column {
            DashboardToolbar(
                properties = DashboardToolbarProperties(
                    showBackArrow = false,
                ),
                onSearchInstrument = {
                    navController.navigate(InstrumentSearch)
//                    navigation.onNavigateToInstrumentSearchActivity(activity)
                },
                onNotificationClicked = { viewModel.onTriggerActions(HomeActions.OnNavigateToNotificationList) },
                onSettingsClicked = { viewModel.onTriggerActions(HomeActions.OnNavigateToSettings) },
                onChatIconClicked = { viewModel.onTriggerActions(HomeActions.OnNavigateToChat) }
            )
            Box(modifier = Modifier.fillMaxWidth()) {
                HorizontalProgressBar(visible = showLoading.value)
            }
            Column(
                modifier = Modifier
                    .fillMaxSize()
                    .verticalScroll(rememberScrollState())
            ) {
                HomeBanner(
                    modifier = Modifier
                        .padding(LocalDimens.current.dimen12)
                        .clip(RoundedCornerShape(LocalDimens.current.dimen4)),
                    bannerItems = banners,
                    sessionId = viewModel.outputs.sessionId,
                    onItemClick = {
                        if (it.targetUrl.isNotEmpty()) {
                            navigation.onNavigateAppWebViewActivity(
                                activity, AppWebViewArgument(
                                    url = it.targetUrl, title = it.title
                                )
                            )
                        }
                    }
                )
                HomeActionShortcuts(
                    modifier = Modifier.padding(top = LocalDimens.current.dimen12),
                    onMenuSelected = { menu ->
                        viewModel.onTriggerActions(HomeActions.OnShortcutMenuItemSelected(menu))
                    })

                SeparatorLine(modifier = Modifier.padding(top = LocalDimens.current.dimen12))

                HomeWalletSummary(
                    modifier = Modifier.padding(
                        start = LocalDimens.current.dimen12,
                        end = LocalDimens.current.dimen12,
                        bottom = LocalDimens.current.dimen8
                    ),
                    displayData = displayHomeWalletSummaryData.value
                )
                SeparatorLine(modifier = Modifier.padding(vertical = LocalDimens.current.dimen12))

                HomeMarketListBox(
                    pagerState = pagerState,
                    displayWatchListData = displayWatchListData.value,
                    availableMarketWatchListAllocation = availableMarketWatchListAllocation.value,
                    modifier = Modifier.padding(horizontal = LocalDimens.current.dimen12),
                    selectedType = displayMarketListType.value,
                    selectedIndex = selectedAllocationIndex.value,
                    onTriggerAction = { viewModel.onTriggerActions(it) })
            }
        }
    }

    if (showCurrencyExchangeModal) {
        ExchangeCurrencyModalBottomSheet(items = exchangeMarqueeData, onDismissed = {
            viewModel.onTriggerActions(HomeActions.OnToggleCurrencyPairModal(show = false))
        })
    }
    if (showLimitedAccessModal) {
        LimitedAccessBottomSheet(
            userStatus = userProfile.value.userStatus,
            onDismissed = {
                showLimitedAccessModal = false
            }, onResumeOnboardingStep = {
                navigation.onNavigateToNotification(activity)
            }
        )
    }
}

@Preview(showBackground = true, showSystemUi = true)
@Composable
fun PreviewHomeScree() {
    HomeScreen(rememberNavController())
}