package com.siriustech.home.screen.landing.component

import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.snapshots.SnapshotStateList
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import com.siriustech.merit.app_common.component.common.NotificationItem
import com.siriustech.merit.app_common.component.common.NotificationItemDisplayData
import com.siriustech.merit.app_common.component.container.PaddingTop
import com.siriustech.merit.app_common.component.separator.SeparatorLine
import com.siriustech.merit.app_common.ext.colorTxtLabel
import com.siriustech.merit.app_common.ext.noRippleClickable
import com.siriustech.merit.app_common.ext.underline
import com.siriustech.merit.app_common.theme.LocalAppColor
import com.siriustech.merit.app_common.theme.LocalDimens
import com.siriustech.merit.app_common.theme.LocalTypography
import com.siriustech.merit.app_common.R as AppCommonR

/**
 * Created by Hein Htet
 */
@Composable
fun HomeNotificationList(
    notifications: SnapshotStateList<NotificationItemDisplayData> = SnapshotStateList(),
    onClicked: () -> Unit = {}, onSeeMoreClicked: () -> Unit = {},
) {
    if (notifications.isNotEmpty()) {
        Column(modifier = Modifier.padding(LocalDimens.current.dimen12)) {
            notifications.forEachIndexed { index, notificationItemDisplayData ->
                NotificationItem(
                    onClicked = onClicked,
                    data = notificationItemDisplayData,
                    modifier = Modifier
                        .background(LocalAppColor.current.bgAccent)
                        .clip(RoundedCornerShape(LocalDimens.current.dimen2))
                )
                PaddingTop(value = LocalDimens.current.dimen8)
            }
            Row(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(vertical = LocalDimens.current.dimen12)
                    .noRippleClickable {
                        onSeeMoreClicked()
                    },
                horizontalArrangement = Arrangement.Center,
                verticalAlignment = Alignment.CenterVertically,
            ) {
                Text(
                    text = stringResource(id = AppCommonR.string.key0640),
                    style = LocalTypography.current.text14.medium.colorTxtLabel().underline(),
                )
                Image(
                    painter = painterResource(id = com.siriustech.merit.app_common.R.drawable.ic_right_arrow),
                    contentDescription = "Right Image Resource"
                )
            }
            SeparatorLine(modifier = Modifier.padding(vertical = LocalDimens.current.dimen12))
        }
    }
}
