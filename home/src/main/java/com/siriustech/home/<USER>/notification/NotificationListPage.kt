package com.siriustech.home.screen.notification

import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.verticalScroll
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.ColorFilter
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import com.siriustech.merit.app_common.component.common.NotificationItem
import com.siriustech.merit.app_common.component.common.NotificationItemDisplayData
import com.siriustech.merit.app_common.component.container.PaddingBottom
import com.siriustech.merit.app_common.component.container.PaddingStart
import com.siriustech.merit.app_common.component.container.PaddingTop
import com.siriustech.merit.app_common.component.separator.SeparatorLine
import com.siriustech.merit.app_common.ext.colorTxtCaution
import com.siriustech.merit.app_common.ext.colorTxtInactive
import com.siriustech.merit.app_common.ext.colorTxtTitle
import com.siriustech.merit.app_common.theme.LocalAppColor
import com.siriustech.merit.app_common.theme.LocalDimens
import com.siriustech.merit.app_common.theme.LocalTypography
import com.siriustech.merit.app_common.R as AppCommonR

/**
 * Created by Hein Htet
 */
@Composable
fun NotificationListPage(
    notifications: List<NotificationItemDisplayData>,
    viewModel: NotificationViewModel,
) {
    Box(modifier = Modifier.fillMaxSize(), contentAlignment = Alignment.Center) {
        if (notifications.isNotEmpty()) {
            Column(
                modifier = Modifier
                    .fillMaxSize()
                    .padding(horizontal = LocalDimens.current.dimen12)
                    .verticalScroll(rememberScrollState())
            ) {
                NotificationDeleteInfo()
                notifications.forEachIndexed { index, notificationItemDisplayData ->
                    if (notificationItemDisplayData.isHeader) {
                        Text(
                            text = notificationItemDisplayData.headerDate,
                            style = LocalTypography.current.text14.semiBold.colorTxtTitle()
                        )
                    } else {
                        NotificationItem(
                            onClicked = {
                                viewModel.onTriggerActions(NotificationActions.MarkAsReadItem(item = notificationItemDisplayData))
                            },
                            data = notificationItemDisplayData,
                            modifier = Modifier
                                .background(if (notificationItemDisplayData.isRead) LocalAppColor.current.bgDefault else LocalAppColor.current.bgAccent)
                                .clip(RoundedCornerShape(LocalDimens.current.dimen2)),
                            onMoreInfoClicked = {
                                viewModel.onTriggerActions(
                                    NotificationActions.ToggleManageNotificationModal(
                                        show = true,
                                        item = notificationItemDisplayData
                                    )
                                )
                            }
                        )
                    }
                    PaddingTop(value = LocalDimens.current.dimen8)
                }
            }
        }
        if (notifications.isEmpty()) {
            Text(
                text = stringResource(id = AppCommonR.string.key0647),
                style = LocalTypography.current.text14.light.colorTxtInactive()
            )
        }
    }
}


@Composable
fun NotificationDeleteInfo() {
    Row(
        modifier = Modifier
            .fillMaxWidth()
            .padding(horizontal = LocalDimens.current.dimen12),
        verticalAlignment = Alignment.CenterVertically
    ) {
        Image(
            painter = painterResource(id = AppCommonR.drawable.ic_error_info),
            contentDescription = "Info Image Resource",
            colorFilter = ColorFilter.tint(LocalAppColor.current.txtCaution)
        )
        PaddingStart(value = LocalDimens.current.dimen4)
        Text(
            text = stringResource(id = AppCommonR.string.key0646),
            style = LocalTypography.current.text14.light.colorTxtCaution()
        )
    }
    SeparatorLine(modifier = Modifier.padding(vertical = LocalDimens.current.dimen12))
}