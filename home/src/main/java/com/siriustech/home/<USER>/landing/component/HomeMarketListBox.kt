package com.siriustech.home.screen.landing.component

import androidx.compose.foundation.Image
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.pager.HorizontalPager
import androidx.compose.foundation.pager.PagerState
import androidx.compose.foundation.pager.rememberPagerState
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.verticalScroll
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.runtime.snapshotFlow
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.ColorFilter
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import com.siriustech.home.screen.landing.HomeActions
import com.siriustech.merit.app_common.component.common.portfolio.EmptyItem
import com.siriustech.merit.app_common.component.container.PaddingStart
import com.siriustech.merit.app_common.component.container.PaddingTop
import com.siriustech.merit.app_common.component.indicator.HorizontalPagerIndicator
import com.siriustech.merit.app_common.data.display.MarketAssetDisplayData
import com.siriustech.merit.app_common.ext.colorTxtParagraph
import com.siriustech.merit.app_common.ext.noRippleClickable
import com.siriustech.merit.app_common.theme.LocalDimens
import com.siriustech.merit.app_common.theme.LocalTypography
import com.siriustech.merit.app_common.typeenum.HomeMarketListType
import com.siriustech.merit.app_common.typeenum.PortfolioAssetType
import kotlinx.coroutines.launch
import timber.log.Timber
import com.siriustech.merit.app_common.R as AppCommonR

/**
 * Created by Hein Htet
 */

@Composable
fun HomeMarketListBox(
    displayWatchListData: Map<String, List<MarketAssetDisplayData>>,
    availableMarketWatchListAllocation: List<PortfolioAssetType>,
    selectedType: HomeMarketListType,
    modifier: Modifier = Modifier,
    pagerState: PagerState,
    selectedIndex: Int = 0,
    scrollEnabled: Boolean = false,
    onTriggerAction: (action: HomeActions) -> Unit = {},
) {
    val coroutineScope = rememberCoroutineScope()
    LaunchedEffect(pagerState) {
        snapshotFlow { pagerState.currentPage }.collect { page ->
            Timber.d("PAGE_CHANGED $page")
            // call api to get data
            onTriggerAction(HomeActions.OnMarketWatchListPageChanged(page))
        }
    }

    fun onNavigateToStep(step: Int) {
        coroutineScope.launch {
            pagerState.animateScrollToPage(step)
        }
    }

    LaunchedEffect(selectedIndex) {
        onNavigateToStep(selectedIndex)
    }

    Column(
        modifier = Modifier
            .fillMaxWidth()
            .then(modifier)
    ) {
        HomeMarketListHeaderActions(
            selectedType,
            availableMarketWatchListAllocation[selectedIndex].displayName,
            onTriggerAction
        )
        PaddingTop(value = LocalDimens.current.dimen8)
        Box(modifier = Modifier.height(300.dp)) {
            HorizontalPager(
                state = pagerState,
                modifier = Modifier.fillMaxSize(),
                userScrollEnabled = scrollEnabled,
            ) {
                val items =
                    displayWatchListData[availableMarketWatchListAllocation[it].assetName].orEmpty()
                        .take(5)
                if (items.isEmpty()) {
                    Box(modifier = Modifier.fillMaxSize(), contentAlignment = Alignment.Center) {
                        EmptyItem(onNavigateToMarket = {
                            onTriggerAction(HomeActions.OnNavigateToMarketTab)
                        })
                    }
                } else {
                    Column(modifier = Modifier.fillMaxSize()
                        .verticalScroll(rememberScrollState())) {
//                        Text(
//                            text = availableMarketWatchListAllocation[it].displayName,
//                            style = LocalTypography.current.text14.light.colorTxtParagraph()
//                        )
                        items.forEach { data ->
                            MarketWatchListAssetItem(
                                data = data,
                                onItemClick = {
                                    onTriggerAction(
                                        HomeActions.OnMarketWatchListItemClicked(
                                            it
                                        )
                                    )
                                },
                            )
                        }
                    }
                }
            }
        }
    }
    if(scrollEnabled) {
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .padding(vertical = LocalDimens.current.dimen4),
            verticalAlignment = Alignment.CenterVertically,
        ) {
            Box(
                modifier = Modifier
                    .size(LocalDimens.current.dimen44)
                    .noRippleClickable {
                        onNavigateToStep(pagerState.currentPage - 1)
                    },
                contentAlignment = Alignment.Center
            ) {
                Image(
                    colorFilter = ColorFilter.tint(color = if (pagerState.currentPage == 0) Color.Gray else Color.Black),
                    painter = painterResource(id = AppCommonR.drawable.ic_arrow_left),
                    contentDescription = "Left Arrow Image Resource"
                )
            }
            Box(modifier = Modifier.weight(1f)) {
                HorizontalPagerIndicator(
                    selectedCurrentIndex = true,
                    pagerState = pagerState,
                    modifier = Modifier.padding(top = LocalDimens.current.dimen8),
                )
            }
            Box(
                modifier = Modifier
                    .size(LocalDimens.current.dimen44)
                    .noRippleClickable {
                        onNavigateToStep(pagerState.currentPage + 1)
                    },
                contentAlignment = Alignment.Center
            ) {
                Image(
                    colorFilter = ColorFilter.tint(color = if (pagerState.currentPage == availableMarketWatchListAllocation.size - 1) Color.Gray else Color.Black),
                    painter = painterResource(id = AppCommonR.drawable.ic_arrow_right),
                    contentDescription = "Right Arrow Image Resource"
                )
            }
        }
    }
}

@Composable
fun HomeMarketListHeaderActions(
    selectedType: HomeMarketListType,
    selectedAllocationClassName: String,
    onTriggerAction: (action: HomeActions) -> Unit = {},
) {
    val context = LocalContext.current
    Row(
        modifier = Modifier.fillMaxWidth(),
        verticalAlignment = Alignment.CenterVertically,
    ) {
        Text(
//            text = stringResource(id = AppCommonR.string.key0639).plus(" - "),
            text = stringResource(id = AppCommonR.string.key0639),
            style = LocalTypography.current.text14.light.colorTxtParagraph()
        )
//        Text(
//            text = selectedAllocationClassName,
//            style = LocalTypography.current.text14.medium.colorTxtParagraph()
//        )
        PaddingStart(value = LocalDimens.current.dimen4)
        selectedType.DisplayBadge()
        Spacer(modifier = Modifier.weight(1f))
        Image(
            modifier = Modifier.noRippleClickable {
                onTriggerAction(HomeActions.OnNavigateToRefineActivity)
            },
            painter = painterResource(id = AppCommonR.drawable.ic_filter_black),
            contentDescription = "Filter Image Resource"
        )
//        Row(
//            verticalAlignment = Alignment.CenterVertically,
//            horizontalArrangement = Arrangement.End,
//        ) {
//            Text(
//                selectedType.displayValue(context),
//                style = LocalTypography.current.text12.medium.copy(
//                    color = selectedType.getColor()
//                )
//            )
//            PaddingEnd(value = LocalDimens.current.dimen8)
//            ToggleOnOff(
//                onToggleChanged = {
//                    onTriggerAction(HomeActions.OnMarketListTypeChanged(if (it) HomeMarketListType.GAINER else HomeMarketListType.LOSER))
//                },
//                defaultValue = selectedType == HomeMarketListType.GAINER,
//                onImage = painterResource(id = AppCommonR.drawable.ic_toggle_up),
//                offImage = painterResource(id = AppCommonR.drawable.ic_toggle_down),
//            )
//        }
    }
}

@Preview(showBackground = true, showSystemUi = true)
@Composable
fun PreviewHomeMarketListBox() {
    HomeMarketListBox(
        availableMarketWatchListAllocation = listOf(
            PortfolioAssetType.CASH_EQUIVALENT,
            PortfolioAssetType.EQUITY,
            PortfolioAssetType.PRIVATE_EQUITY,
        ),
        modifier = Modifier.padding(LocalDimens.current.dimen12),
        selectedType = HomeMarketListType.GAINER,
        displayWatchListData = mapOf(),
        pagerState = rememberPagerState(pageCount = { 5 })
    )
}