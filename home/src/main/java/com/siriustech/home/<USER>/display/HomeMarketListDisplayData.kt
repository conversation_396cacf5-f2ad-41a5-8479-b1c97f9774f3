package com.siriustech.home.domain.display

import com.siriustech.merit.app_common.data.display.MarketAssetDisplayData
import com.siriustech.merit.app_common.typeenum.PortfolioAssetType

/**
 * Created by <PERSON><PERSON><PERSON><PERSON>
 */

data class MarketListDisplayData(
    val summaryName: PortfolioAssetType,
    val rawSummaryName: String,
    val assets: List<MarketAssetDisplayData> = emptyList(),
)