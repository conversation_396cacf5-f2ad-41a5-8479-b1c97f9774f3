package com.siriustech.home.screen.notification

import android.annotation.SuppressLint
import android.content.Context
import androidx.compose.runtime.mutableStateListOf
import androidx.compose.runtime.snapshots.SnapshotStateList
import com.core.network.base.getError
import com.siriustech.home.domain.NotificationMapper.mapToNotificationDisplay
import com.siriustech.home.domain.groupNotificationsByDate
import com.siriustech.merit.apilayer.service.home.deletenotification.DeleteNotificationRequest
import com.siriustech.merit.apilayer.service.home.deletenotification.DeleteNotificationUseCase
import com.siriustech.merit.apilayer.service.home.modifynotification.ModifyNotificationRequest
import com.siriustech.merit.apilayer.service.home.modifynotification.ModifyNotificationUseCase
import com.siriustech.merit.apilayer.service.home.notification.NotificationListResponse
import com.siriustech.merit.apilayer.service.home.notification.NotificationRequest
import com.siriustech.merit.apilayer.service.home.notification.NotificationUseCase
import com.siriustech.merit.app_common.component.common.MarketProfileTabModel
import com.siriustech.merit.app_common.component.common.NotificationItemDisplayData
import com.siriustech.merit.app_common.component.common.NotificationTabType
import com.siriustech.merit.app_common.theme.AppAction
import com.siriustech.merit.app_common.theme.AppViewModel
import com.siriustech.merit.app_common.utils.ErrorCode.mapToErrorDisplay
import dagger.hilt.android.lifecycle.HiltViewModel
import javax.inject.Inject
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.catch
import kotlinx.coroutines.flow.collectLatest
import kotlinx.coroutines.flow.onCompletion
import kotlinx.coroutines.flow.onStart
import kotlinx.coroutines.launch
import timber.log.Timber

/**
 * Created by Hein Htet
 */
@HiltViewModel
class NotificationViewModel @Inject constructor(
    private val notificationUseCase: NotificationUseCase,
    private val modifyNotificationUseCase: ModifyNotificationUseCase,
    private val deleteNotificationUseCase: DeleteNotificationUseCase,
) : AppViewModel() {
    private val _currentSelectedTab = MutableStateFlow<MarketProfileTabModel?>(null)
    private val _showLoading = MutableStateFlow(false)
    private val _notifications = mutableStateListOf<NotificationItemDisplayData>()
    private val _messages = mutableStateListOf<NotificationItemDisplayData>()
    private val _fetchCount = MutableStateFlow(0)
    private val _haveNewNotification = MutableStateFlow(false)
    @SuppressLint("StaticFieldLeak")
    private var _context : Context? = null

    init {
        getNotification(NotificationTabType.NOTIFICATIONS)
        getNotification(NotificationTabType.MESSAGE_CENTER)
    }

    inner class NotificationInputs : BaseInputs() {

        fun initContext(context: Context){
            _context = context
        }

        fun onUpdateCurrentTab(modal: MarketProfileTabModel) {
            _currentSelectedTab.value = modal
            getNotification()
        }

        fun onClearData() {
            deleteNotification() // delete All
        }

        fun markAllRead() {
            updateReadNotification(isAll = true)
        }

        fun markAsRead(item: NotificationItemDisplayData) {
            updateReadNotification(ids = listOf(item))
        }
    }

    inner class NotificationOutputs : BaseOutputs() {

        val currentSelectedTab: StateFlow<MarketProfileTabModel?>
            get() = _currentSelectedTab

        val showLoading: MutableStateFlow<Boolean>
            get() = _showLoading

        val notifications: SnapshotStateList<NotificationItemDisplayData>
            get() = _notifications

        val messages: SnapshotStateList<NotificationItemDisplayData>
            get() = _messages

        val fetchCount: StateFlow<Int>
            get() = _fetchCount

        val haveNewNotification: StateFlow<Boolean>
            get() = _haveNewNotification

    }

    override val inputs = NotificationInputs()
    override val outputs = NotificationOutputs()


    override fun onTriggerActions(action: AppAction) {
        when (action) {
            NotificationActions.MarkAllRead -> inputs.markAllRead()
            is NotificationActions.MarkAsReadItem -> inputs.markAsRead(action.item)
            is NotificationActions.OnDeleteNotification -> deleteNotification(action.data.id)
            else -> super.onTriggerActions(action)
        }
    }

    private fun getNotification(type: NotificationTabType? = null) {
        val fetchType = type?.value ?: _currentSelectedTab.value?.id
        scope.launch {
            notificationUseCase(
                param = NotificationRequest(
                    limit = 0,
                    messageType = _currentSelectedTab.value?.id ?: type?.value
                    ?: NotificationTabType.NOTIFICATIONS.value
                )
            )
                .onStart { _showLoading.value = true }
                .onCompletion { _showLoading.value = false }
                .catch {}
                .collectLatest {
                    Timber.d("FETCH_COUNT ${_fetchCount.value}")
                    val current = fetchType ?: NotificationTabType.NOTIFICATIONS.value
                    mapDisplayNotificationItem(current, it)
                }
            checkNewNotificationOrMessage()
        }
    }

    private fun mapDisplayNotificationItem(type: String, response: NotificationListResponse) {
        _fetchCount.value += 1
        val displayData = response.mapToNotificationDisplay(homePage = false)
            .map {
                it.showMoreInfoIcon = true
                it
            }
        if (type == NotificationTabType.NOTIFICATIONS.value) {
            _notifications.clear()
            _notifications.addAll(groupNotificationsByDate(displayData,_context))
            _haveNewNotification.value = _notifications.any { !it.isRead }
        } else if (type == NotificationTabType.MESSAGE_CENTER.value) {
            _messages.clear()
            _messages.addAll(displayData)
        }
    }

    private fun checkNewNotificationOrMessage() {
        val current = _currentSelectedTab.value?.id ?: NotificationTabType.NOTIFICATIONS.value
        if (current == NotificationTabType.NOTIFICATIONS.value) {
            _haveNewNotification.value = _notifications.any { !it.isRead }
        } else if (current == NotificationTabType.MESSAGE_CENTER.value) {
            _haveNewNotification.value = _messages.any { !it.isRead }
        }
    }

    private fun updateReadNotification(
        isAll: Boolean = false,
        ids: List<NotificationItemDisplayData> = emptyList(),
    ) {
        val request = ModifyNotificationRequest(
            idList = ids.map { it.id },
            messageType = _currentSelectedTab.value?.id ?: NotificationTabType.NOTIFICATIONS.value,
            isReadAll = isAll
        )
        scope.launch {
            modifyNotificationUseCase(
                param = request
            )
                .onStart { _showLoading.value = true }
                .onCompletion { _showLoading.value = false }
                .catch {}
                .collectLatest {
                    val current =
                        _currentSelectedTab.value?.id ?: NotificationTabType.NOTIFICATIONS.value
                    if (isAll) {
                        if (current == NotificationTabType.NOTIFICATIONS.value) {
                            _notifications.forEachIndexed { index, _ ->
                                _notifications[index] = _notifications[index].copy(isRead = true)
                            }
                        } else {
                            _messages.forEachIndexed { index, _ ->
                                _messages[index] = _messages[index].copy(isRead = true)
                            }
                        }
                    } else {
                        if (current == NotificationTabType.NOTIFICATIONS.value) {
                            val index =
                                _notifications.indexOfFirst { it.id == ids.firstOrNull()?.id }
                            _notifications[index] = _notifications[index].copy(
                                isRead = true
                            )
                        } else {
                            val index = _messages.indexOfFirst { it.id == ids.firstOrNull()?.id }
                            _messages[index] = _messages[index].copy(
                                isRead = true
                            )
                        }
                    }
                    checkNewNotificationOrMessage()
                    _fetchCount.value += 1
                }
        }
    }

    private fun deleteNotification(id: Int? = null) {
        val isDeleteAll = id == null
        val idList = if (_currentSelectedTab.value?.id == NotificationTabType.NOTIFICATIONS.value) {
            _notifications.map { it.id }
        } else {
            _messages.map { it.id }
        }


        val request = DeleteNotificationRequest(
            idList = if (id != null) listOf(id) else idList
        )
        scope.launch {
            deleteNotificationUseCase(param = request)
                .onStart { _showLoading.value = true }
                .onCompletion { _showLoading.value = false }
                .catch { emitError(it.getError().mapToErrorDisplay()) }
                .collectLatest {
                    val currentSelected = _currentSelectedTab.value?.id ?: NotificationTabType.NOTIFICATIONS.value
                    if (isDeleteAll) {
                        if (currentSelected == NotificationTabType.NOTIFICATIONS.value) {
                            _notifications.clear()
                        } else {
                            _messages.clear()
                        }
                        onTriggerActions(NotificationActions.AllNotificationCleared)
                    } else {
                        if (currentSelected == NotificationTabType.NOTIFICATIONS.value) {
                            val index = _notifications.indexOfFirst { it.id == id }
                            _notifications.removeAt(index)
                        } else {
                            val index = _messages.indexOfFirst { it.id == id }
                            _messages.removeAt(index)
                        }
                    }
                }
        }
    }


    companion object {
        const val PAGE_NOTIFICATION_PAGE = 0
        const val PAGE_MESSAGE_CENTER_PAGE = 1
    }

}