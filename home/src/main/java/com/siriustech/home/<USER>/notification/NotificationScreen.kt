package com.siriustech.home.screen.notification

import androidx.compose.animation.AnimatedVisibility
import androidx.compose.animation.core.tween
import androidx.compose.animation.fadeOut
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.pager.HorizontalPager
import androidx.compose.foundation.pager.rememberPagerState
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateListOf
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.runtime.setValue
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.tooling.preview.Preview
import androidx.fragment.app.FragmentActivity
import androidx.hilt.navigation.compose.hiltViewModel
import androidx.navigation.NavController
import androidx.navigation.compose.rememberNavController
import com.siriustech.core_ui_compose.base.SingleEventEffect
import com.siriustech.core_ui_compose.component.CommonToolbar
import com.siriustech.core_ui_compose.component.ToolbarProperties
import com.siriustech.home.modal.ManageNotificationModal
import com.siriustech.home.screen.notification.NotificationViewModel.Companion.PAGE_MESSAGE_CENTER_PAGE
import com.siriustech.home.screen.notification.NotificationViewModel.Companion.PAGE_NOTIFICATION_PAGE
import com.siriustech.merit.app_common.R
import com.siriustech.merit.app_common.component.alert.BannerAlertProperties
import com.siriustech.merit.app_common.component.button.AccentButton
import com.siriustech.merit.app_common.component.button.ButtonProperties
import com.siriustech.merit.app_common.component.button.SecondaryButton
import com.siriustech.merit.app_common.component.common.MarketProfileTabContent
import com.siriustech.merit.app_common.component.common.MarketProfileTabModel
import com.siriustech.merit.app_common.component.common.NotificationItemDisplayData
import com.siriustech.merit.app_common.component.common.NotificationTabType
import com.siriustech.merit.app_common.component.common.onGetNotificationMenuTabs
import com.siriustech.merit.app_common.component.container.PaddingBottom
import com.siriustech.merit.app_common.component.container.PaddingTop
import com.siriustech.merit.app_common.component.loading.HorizontalProgressBar
import com.siriustech.merit.app_common.component.modalbts.CommonModelBottomSheet
import com.siriustech.merit.app_common.component.modalbts.CommonModelBottomSheetInfoProperties
import com.siriustech.merit.app_common.component.modalbts.CommonModelBottomSheetProperties
import com.siriustech.merit.app_common.data.display.HistoryFilterModel
import com.siriustech.merit.app_common.ext.AttributeStringData
import com.siriustech.merit.app_common.ext.buildAttrString
import com.siriustech.merit.app_common.ext.colorTxtCaution
import com.siriustech.merit.app_common.ext.colorTxtTitle
import com.siriustech.merit.app_common.navigation.MessageCenterDetails
import com.siriustech.merit.app_common.navigation.OrderHistoryList
import com.siriustech.merit.app_common.navigation.argument.history.OrderHistoryListArgument
import com.siriustech.merit.app_common.navigation.argument.notification.MessageCenterDetailsArgument
import com.siriustech.merit.app_common.theme.AppScreen
import com.siriustech.merit.app_common.theme.LocalDimens
import com.siriustech.merit.app_common.theme.LocalTypography
import com.siriustech.merit.app_common.typeenum.HistoryFilterType
import kotlinx.coroutines.launch
import timber.log.Timber
import com.siriustech.merit.app_common.R as AppCommonR

/**
 * Created by Hein Htet
 */

@Composable
fun NotificationScreen(
    navController: NavController,
    ignorePadding: Boolean = false,
    onBackPressed: () -> Unit = {},
    viewModel: NotificationViewModel = hiltViewModel(),
) {
    val activity = LocalContext.current as FragmentActivity


    val showLoading = viewModel.outputs.showLoading.collectAsState()
    val notifications = viewModel.outputs.notifications
    val messages = viewModel.outputs.messages
    val currentMenuTab = viewModel.outputs.currentSelectedTab.collectAsState()
    val fetchCount = viewModel.outputs.fetchCount.collectAsState()
    val haveNewNotification = viewModel.outputs.haveNewNotification.collectAsState()
    val coroutineScope = rememberCoroutineScope()
    val pagerState = rememberPagerState(pageCount = { 2 })
    var showDeleteAllConfirmationModal by remember {
        mutableStateOf(false)
    }

    val tabs = remember {
        mutableStateListOf(*onGetNotificationMenuTabs(activity).toTypedArray())
    }

    var showManageNotificationModal by remember {
        mutableStateOf(false)
    }

    var manageNotificationModalItem by remember {
        mutableStateOf<NotificationItemDisplayData?>(null)
    }

    LaunchedEffect(fetchCount.value) {
        tabs.clear()
        tabs.addAll(onGetNotificationMenuTabs(activity).map {
            if (it.id == NotificationTabType.NOTIFICATIONS.value) {
                it.rightSuffixImage =
                    if (notifications.any { !it.isRead }) AppCommonR.drawable.ic_red_dot_circle else null
            } else if (it.id == NotificationTabType.MESSAGE_CENTER.value) {
                it.rightSuffixImage =
                    if (messages.any { !it.isRead }) AppCommonR.drawable.ic_red_dot_circle else null
            }
            it.isSelected =
                (currentMenuTab.value?.id ?: NotificationTabType.NOTIFICATIONS.value) == it.id
            it
        })
        Timber.d("TAB_$tabs")
    }

    fun onNavigateToPage(index: Int) {
        coroutineScope.launch {
            pagerState.animateScrollToPage(index)
        }
    }

    SingleEventEffect(sideEffectFlow = viewModel.appAction) {
        when (it) {
            is NotificationActions.OnNavigateToTab -> {
                onNavigateToPage(tabs.indexOfFirst { tab -> tab.id == it.data.id })
            }

            is NotificationActions.ToggleDeleteAllConfirmationModal -> {
                showDeleteAllConfirmationModal = it.show
            }

            is NotificationActions.AllNotificationCleared -> {
                viewModel.emitBannerAlert(
                    BannerAlertProperties(
                        title = activity.getString(R.string.key0650),
                        description = activity.getString(R.string.key0651),
                    ),
                )
            }

            is NotificationActions.ToggleManageNotificationModal -> {
                manageNotificationModalItem = it.item
                showManageNotificationModal = it.show
            }

            is NotificationActions.OnNavigateToMessageCenterDetails -> {
                navController.navigate(
                    MessageCenterDetails(MessageCenterDetailsArgument(it.data)),
                )
            }
        }
    }



    AppScreen(vm = viewModel, ignorePaddingValue = ignorePadding) {
        Column(modifier = Modifier.fillMaxSize()) {
            CommonToolbar(
                onRightActionClicked = {
                    viewModel.onTriggerActions(
                        NotificationActions.ToggleDeleteAllConfirmationModal(
                            show = true
                        )
                    )
                },
                onLeftActionClicked = { onBackPressed() },
                properties = ToolbarProperties(
                    leftActionResId = AppCommonR.drawable.ic_back_arrow,
                    rightActionResId = AppCommonR.drawable.ic_trash_bin_react,
                    annotatedTitleString = buildAttrString(
                        attributedString = listOf(
                            AttributeStringData(
                                text = stringResource(id = AppCommonR.string.key0641),
                                textStyle = LocalTypography.current.text14.semiBold.colorTxtTitle()
                            )
                        )
                    ),
                    iconTitleModifier = Modifier.size(LocalDimens.current.dimen14),
                    iconTitle = painterResource(id = AppCommonR.drawable.ic_notification_bell)
                )
            )
            Box(modifier = Modifier) {
                HorizontalProgressBar(visible = showLoading.value)
                NotificationTabs(
                    modifier = Modifier.padding(
                        horizontal = LocalDimens.current.dimen12,
                        vertical = LocalDimens.current.dimen12
                    ),
                    tabs = tabs,
                    onTabSelected = {
                        val index = tabs.indexOfFirst { tab -> tab.id == it.id }
                        tabs.forEachIndexed { i, marketProfileTabModel ->
                            tabs[i] = marketProfileTabModel.copy(isSelected = i == index)
                        }
                        viewModel.inputs.onUpdateCurrentTab(it)
                        viewModel.onTriggerActions(NotificationActions.OnNavigateToTab(it))
                    })
            }
            HorizontalPager(
                state = pagerState,
                userScrollEnabled = false,
                modifier = Modifier.weight(1f)
            ) {
                when (it) {
                    PAGE_NOTIFICATION_PAGE -> {
                        NotificationListPage(notifications, viewModel)
                    }

                    PAGE_MESSAGE_CENTER_PAGE -> MessageCenterListPage(messages, viewModel)
                }
            }
            AnimatedVisibility(
//                visible = haveNewNotification.value, enter = fadeIn(initialAlpha = 0.4f),
                visible = true,
                exit = fadeOut(animationSpec = tween(durationMillis = 250)),
            ) {
                Column(
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(horizontal = LocalDimens.current.dimen12)
                ) {
                    Box(
                        modifier = Modifier
                            .fillMaxWidth()
                            .height(LocalDimens.current.dimen1)
                            .background(Color.Gray.copy(alpha = 0.1f))
                    )
                    PaddingTop(value = LocalDimens.current.dimen12)
                    if (haveNewNotification.value) {
                        SecondaryButton(
                            properties = ButtonProperties(text = stringResource(id = AppCommonR.string.key0645)),
                            onClicked = {
                                viewModel.onTriggerActions(NotificationActions.MarkAllRead)
                            },
                        )
                    } else {
                        AccentButton(
                            properties = ButtonProperties(
                                text = stringResource(id = AppCommonR.string.key0645),
                                enabled = false
                            ),
                            onClicked = {
                            },
                        )
                    }
                    PaddingBottom(value = LocalDimens.current.dimen12)
                }
            }
        }

        if (showDeleteAllConfirmationModal) {
            CommonModelBottomSheet(
                onDismissed = {
                    viewModel.onTriggerActions(
                        NotificationActions.ToggleDeleteAllConfirmationModal(
                            false
                        )
                    )
                }, properties = CommonModelBottomSheetProperties(
                    prefixTitle = stringResource(id = AppCommonR.string.key0102),
                    title = stringResource(id = AppCommonR.string.key0648),
                    description = stringResource(id = AppCommonR.string.key0649),
                    buttonText = stringResource(id = AppCommonR.string.key0260),
                    contentInfo = CommonModelBottomSheetInfoProperties(
                        icon = painterResource(id = AppCommonR.drawable.ic_caution),
                        content = stringResource(id = AppCommonR.string.key0313),
                        contentTextStyle = LocalTypography.current.text14.regular.colorTxtCaution()
                    ),
                    iconTitle = painterResource(id = AppCommonR.drawable.ic_reset),
                ),
                onButtonClicked = {
                    viewModel.inputs.onClearData()
                }
            )
        }

        if (showManageNotificationModal && manageNotificationModalItem != null) {
            ManageNotificationModal(
                data = manageNotificationModalItem!!,
                onDismissed = {
                    viewModel.onTriggerActions(
                        NotificationActions.ToggleManageNotificationModal(
                            show = false,
                            null
                        )
                    )
                },
                onDeleteNotification = {
                    viewModel.onTriggerActions(NotificationActions.OnDeleteNotification(it))
                },
                onNavigateToDeposit = {
                    navController.navigate(
                        OrderHistoryList(
                            argument = OrderHistoryListArgument(
                                historyFilterType = HistoryFilterType.DEPOSIT_HISTORY,
                                historyFilterModel = HistoryFilterModel()
                            )
                        )
                    )
                },
                onNavigateToOrder = {
                    navController.navigate(
                        OrderHistoryList(
                            argument = OrderHistoryListArgument(
                                historyFilterType = HistoryFilterType.TRADE_HISTORY,
                                historyFilterModel = HistoryFilterModel()
                            )
                        )
                    )
                },
                onNavigateToWithdrawal = {
                    navController.navigate(
                        OrderHistoryList(
                            argument = OrderHistoryListArgument(
                                historyFilterType = HistoryFilterType.WITHDRAWAL_HISTORY,
                                historyFilterModel = HistoryFilterModel()
                            )
                        )
                    )
                })
        }
    }
}

@Composable
fun NotificationTabs(
    modifier: Modifier = Modifier,
    tabs: List<MarketProfileTabModel> = emptyList(),
    onTabSelected: (MarketProfileTabModel) -> Unit,
) {
    Row(modifier = Modifier.then(modifier)) {
        tabs.forEach {
            MarketProfileTabContent(item = it, onTabSelected = { selectTab ->
                val index = tabs.indexOf(selectTab)
                if (tabs[index].isSelected) return@MarketProfileTabContent
                onTabSelected(it)
            })
        }
    }
}

@Preview(showBackground = true, showSystemUi = true)
@Composable
fun PreviewNotificationScreen() {
    NotificationScreen(rememberNavController())
}
