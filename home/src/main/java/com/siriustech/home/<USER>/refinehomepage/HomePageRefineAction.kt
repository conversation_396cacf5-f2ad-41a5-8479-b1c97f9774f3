package com.siriustech.home.screen.refinehomepage

import com.siriustech.merit.app_common.theme.AppAction
import com.siriustech.merit.app_common.typeenum.HomeMarketListType
import com.siriustech.merit.app_common.typeenum.PortfolioAssetType

/**
 * Created by <PERSON><PERSON>t
 */
sealed interface HomePageRefineAction : AppAction {
    data class OnSelectSortType(val type: HomeMarketListType) : HomePageRefineAction
    data class OnSelectAllocationClassType(val type: PortfolioAssetType) : HomePageRefineAction
    data object OnCancelRefine : HomePageRefineAction
    data object OnApplyRefine : HomePageRefineAction
}