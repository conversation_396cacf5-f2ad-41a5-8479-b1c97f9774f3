package com.siriustech.home.screen.refinehomepage

import com.siriustech.merit.app_common.typeenum.HomeMarketListType
import com.siriustech.merit.app_common.typeenum.PortfolioAssetType
import kotlinx.serialization.Serializable

/**
 * Created by <PERSON><PERSON>
 */
@Serializable
data class HomePageRefineArgument(
    val sortType: HomeMarketListType = HomeMarketListType.GAINER,
    val allocationAssetType: PortfolioAssetType = PortfolioAssetType.CASH_EQUIVALENT,
) : java.io.Serializable