package com.siriustech.home.screen.refinehomepage

import com.siriustech.merit.app_common.theme.AppAction
import com.siriustech.merit.app_common.theme.AppViewModel
import com.siriustech.merit.app_common.typeenum.HomeMarketListType
import com.siriustech.merit.app_common.typeenum.PortfolioAssetType
import dagger.hilt.android.lifecycle.HiltViewModel
import javax.inject.Inject
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow

/**
 * Created by <PERSON><PERSON><PERSON><PERSON>
 */
@HiltViewModel
class HomePageRefineViewModel @Inject constructor(

) : AppViewModel() {
    private val _selectedSortingType =
        MutableStateFlow<HomeMarketListType>(HomeMarketListType.GAINER)
    private val _selectedAllocationClassType =
        MutableStateFlow<PortfolioAssetType>(PortfolioAssetType.CASH_EQUIVALENT)

    private val _availableMarketWatchListAllocation = MutableStateFlow(
        listOf(
            PortfolioAssetType.CASH_EQUIVALENT,
            PortfolioAssetType.FIX_INCOME,
            PortfolioAssetType.MULTI_ASSET,
            PortfolioAssetType.STRUCTURED_PRODUCTS,
            PortfolioAssetType.PRIVATE_CAPITAL,
            PortfolioAssetType.EQUITY,
            PortfolioAssetType.COMMODITIES,
            PortfolioAssetType.VIRTUAL_ASSET,
        )
    )


    private var _argument = HomePageRefineArgument()

    override val inputs = HomePageRefineInputs()
    override val outputs = HomePageRefineOutputs()


    inner class HomePageRefineInputs : BaseInputs() {
        fun init(argument: HomePageRefineArgument) {
            _selectedSortingType.value = argument.sortType
            _selectedAllocationClassType.value = argument.allocationAssetType
            _argument = argument
        }

        fun onUpdateSortType(sortType: HomeMarketListType) {
            _selectedSortingType.value = sortType
            _argument = _argument.copy(
                sortType = sortType
            )
        }

        fun onUpdateAllocationClassType(allocationClassType: PortfolioAssetType) {
            _selectedAllocationClassType.value = allocationClassType
            _argument = _argument.copy(
                allocationAssetType = allocationClassType
            )
        }

        fun onReset() {
            _argument = HomePageRefineArgument()
            _selectedSortingType.value = _argument.sortType
            _selectedAllocationClassType.value = _argument.allocationAssetType
        }
    }

    override fun onTriggerActions(action: AppAction) {
        when (action) {
            is HomePageRefineAction.OnSelectSortType -> {
                inputs.onUpdateSortType(action.type)
            }

            is HomePageRefineAction.OnSelectAllocationClassType -> {
                inputs.onUpdateAllocationClassType(action.type)
            }

            else -> super.onTriggerActions(action)
        }
    }

    inner class HomePageRefineOutputs : BaseOutputs() {
        val selectedSortingType: StateFlow<HomeMarketListType>
            get() = _selectedSortingType

        val selectedAllocationClassType: StateFlow<PortfolioAssetType>
            get() = _selectedAllocationClassType

        val availableMarketWatchListAllocation: StateFlow<List<PortfolioAssetType>>
            get() = _availableMarketWatchListAllocation

        val argument: HomePageRefineArgument
            get() = _argument
    }


}