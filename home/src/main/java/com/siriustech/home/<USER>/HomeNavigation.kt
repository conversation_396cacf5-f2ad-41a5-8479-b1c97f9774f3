package com.siriustech.home.navigation

import android.content.Intent
import androidx.activity.compose.ManagedActivityResultLauncher
import androidx.activity.result.ActivityResult
import androidx.fragment.app.FragmentActivity
import androidx.navigation.NavController
import com.siriustech.home.screen.refinehomepage.HomePageRefineArgument
import com.siriustech.merit.app_common.data.display.MarketAssetDisplayData
import com.siriustech.merit.app_common.screen.appwebview.AppWebViewArgument
import dagger.hilt.EntryPoint
import dagger.hilt.InstallIn
import dagger.hilt.components.SingletonComponent

/**
 * Created by He<PERSON> Htet
 */
interface HomeNavigation {
    fun onNavigateToNotification(activity: FragmentActivity)
    fun onNavigateToInstrumentSearchActivity(activity: FragmentActivity)
    fun onNavigateToMarketProfileDetails(activity: FragmentActivity,navController: NavController,marketAssetDisplayData: MarketAssetDisplayData)
    fun onNavigateToMarketTab(activity: FragmentActivity,navController: NavController)
    fun onNavigateToSettings(activity: FragmentActivity)
    fun onNavigateToChatActivity(activity: FragmentActivity)
    fun onNavigateToHomePageRefineActivity(activity: FragmentActivity,argument: HomePageRefineArgument,callback : ManagedActivityResultLauncher<Intent,ActivityResult>)
    fun onNavigateToPortfolioTab(activity: FragmentActivity,navController: NavController)
    fun onNavigateToReportStatementActivity(activity: FragmentActivity,callback: ManagedActivityResultLauncher<Intent, ActivityResult>)
    fun onNavigateAppWebViewActivity(activity: FragmentActivity,data : AppWebViewArgument)
    fun onNavigateToContinueSignUp(activity: FragmentActivity)
}


@EntryPoint
@InstallIn(SingletonComponent::class)
interface HomeNavigationEntryPoint {
    fun homeNavigation(): HomeNavigation
}