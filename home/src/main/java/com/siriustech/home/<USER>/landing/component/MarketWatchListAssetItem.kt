package com.siriustech.home.screen.landing.component

import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.layout.widthIn
import androidx.compose.foundation.layout.wrapContentWidth
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import coil3.compose.AsyncImage
import coil3.request.ImageRequest
import coil3.request.crossfade
import com.siriustech.merit.app_common.R
import com.siriustech.merit.app_common.component.common.MarketPriceChange
import com.siriustech.merit.app_common.component.common.MarketPriceChangeDisplayData
import com.siriustech.merit.app_common.component.text.BadgeText
import com.siriustech.merit.app_common.data.display.MarketAssetDisplayData
import com.siriustech.merit.app_common.ext.colorTxtParagraph
import com.siriustech.merit.app_common.ext.colorTxtTitle
import com.siriustech.merit.app_common.ext.noRippleClickable
import com.siriustech.merit.app_common.theme.LocalAppColor
import com.siriustech.merit.app_common.theme.LocalDimens
import com.siriustech.merit.app_common.theme.LocalTypography
import com.siriustech.merit.app_common.typeenum.RiskLevel
import timber.log.Timber

/**
 * Created by Hein Htet
 */
@Composable
fun MarketWatchListAssetItem(
    modifier: Modifier = Modifier,
    onItemClick: (data: MarketAssetDisplayData) -> Unit = {},
    toggleFavorite: (data: MarketAssetDisplayData) -> Unit = {},
    data: MarketAssetDisplayData = MarketAssetDisplayData(),
) {
    Row(
        modifier = modifier
            .fillMaxWidth()
            .padding(vertical = LocalDimens.current.dimen6)
            .noRippleClickable { onItemClick(data) },
        horizontalArrangement = Arrangement.SpaceBetween,
        verticalAlignment = Alignment.CenterVertically
    ) {
        Row(verticalAlignment = Alignment.CenterVertically, modifier = Modifier.weight(1f)) {
            AsyncImage(
                modifier = Modifier
                    .size(LocalDimens.current.dimen32)
                    .clip(RoundedCornerShape(50)),
                model = ImageRequest.Builder(LocalContext.current)
                    .data(data.logo)
                    .crossfade(true)
                    .build(),
                placeholder = painterResource(R.drawable.ic_product_category_placeholder),
                error = painterResource(R.drawable.ic_product_category_placeholder),
                contentDescription = "Product Category Placeholder",
                contentScale = ContentScale.Crop,
                onError = {
                    Timber.d("Image Loading error ${it.result}")
                }
            )
            Column(modifier = Modifier.padding(start = LocalDimens.current.dimen8)) {
                Row {
                    Text(
                        text = data.name,
                        modifier = Modifier,
                        style = LocalTypography.current.text14.medium.colorTxtTitle()
                    )
//                    Spacer(modifier = Modifier.width(LocalDimens.current.dimen8))
//                    BadgeText(
//                        label = data.venue,
//                        bgColor = LocalAppColor.current.bgAccent,
//                        textColor = LocalAppColor.current.txtLabel
//                    )
                    Spacer(modifier = Modifier.width(LocalDimens.current.dimen2))
//                    data.riskLevel?.RiskLevelBadge()
                }
                Row(
                    verticalAlignment = Alignment.CenterVertically,
                    modifier = Modifier.wrapContentWidth()
                ) {
                    Text(
                        text = data.symbol,
                        style = LocalTypography.current.text12.medium.colorTxtParagraph(),
                        maxLines = 1,
                        overflow = TextOverflow.Ellipsis,
                        modifier = Modifier.widthIn(max = 120.dp)
                    )
                    Spacer(modifier = Modifier.width(LocalDimens.current.dimen2))
                    data.riskLevel?.RiskLevelBadge()
                }
            }
        }
        MarketPriceChange(
            data = MarketPriceChangeDisplayData(
                marketValue = data.marketValue,
                currency = data.currency,
                unrealizedGL = data.unrealizedGl,
                unrealizedGLRate = data.unrealizedGlRate
            )
        )
    }
}

@Preview(showBackground = true, showSystemUi = true)
@Composable
fun PreviewPortfolioAssetItem() {
    MarketWatchListAssetItem(
        data = MarketAssetDisplayData(
            symbol = "ABC",
            venue = "EXC",
            name = "Test 01",
            riskLevel = RiskLevel.HIGH,
            exchange = "HKEX",
            marketPrice = "377.60",
            currency = "HKD",
            unrealizedGl = "2192.99",
            isFavorite = true,
            unrealizedGlRate = "2192.0",
            logo = "https://api.twelvedata.com/logo/apple.com"
        )
    )
}