package com.siriustech.home.domain.display

import com.siriustech.merit.app_common.component.common.WalletSummaryAllocationPercentageDisplay
import com.siriustech.merit.app_common.typeenum.PortfolioAssetType
import com.siriustech.merit.app_common.typeenum.RiskLevel

/**
 * Created by Hein Htet
 */

data class HomeWalletSummaryDisplayData(
    val userName: String = "",
    val riskLevel: RiskLevel? = null,
    val totalBalance: String = "",
    val currency: String = "",
    val priceChange: String = "",
    val priceChangeRate: String = "",
    val summaryList : List<WalletSummaryAllocationPercentageDisplay> = emptyList()
)