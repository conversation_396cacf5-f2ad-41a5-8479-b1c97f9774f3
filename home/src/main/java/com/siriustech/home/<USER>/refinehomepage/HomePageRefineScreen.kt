package com.siriustech.home.screen.refinehomepage

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.ExperimentalLayoutApi
import androidx.compose.foundation.layout.FlowRow
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.width
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.collectAsState
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.tooling.preview.Preview
import androidx.hilt.navigation.compose.hiltViewModel
import com.siriustech.core_ui_compose.base.SingleEventEffect
import com.siriustech.core_ui_compose.component.CommonToolbar
import com.siriustech.core_ui_compose.component.ToolbarProperties
import com.siriustech.merit.app_common.component.button.ButtonProperties
import com.siriustech.merit.app_common.component.button.SecondaryBorderButton
import com.siriustech.merit.app_common.component.button.SecondaryButton
import com.siriustech.merit.app_common.component.button.ToggleThirdButton
import com.siriustech.merit.app_common.component.button.ToggleThirdButtonProperties
import com.siriustech.merit.app_common.component.container.PaddingTop
import com.siriustech.merit.app_common.ext.colorTxtTitle
import com.siriustech.merit.app_common.theme.AppScreen
import com.siriustech.merit.app_common.theme.LocalAppColor
import com.siriustech.merit.app_common.theme.LocalDimens
import com.siriustech.merit.app_common.theme.LocalTypography
import com.siriustech.merit.app_common.typeenum.HomeMarketListType
import com.siriustech.merit.app_common.typeenum.PortfolioAssetType
import com.siriustech.merit.app_common.R as AppCommonR

/**
 * Created by Hein Htet
 */

@Composable
fun HomePageRefineScreen(
    modifier: Modifier = Modifier,
    onApply: (data: HomePageRefineArgument) -> Unit = {},
    onBackPressed: () -> Unit = {},
    viewModel: HomePageRefineViewModel = hiltViewModel(),
    argument: HomePageRefineArgument = HomePageRefineArgument(),
) {

    val selectedSortType = viewModel.outputs.selectedSortingType.collectAsState()
    val selectedAllocationClassType = viewModel.outputs.selectedAllocationClassType.collectAsState()
    val availableAllocationClass =
        viewModel.outputs.availableMarketWatchListAllocation.collectAsState()

    LaunchedEffect(Unit) {
        viewModel.inputs.init(argument)
    }

    SingleEventEffect(sideEffectFlow = viewModel.appAction) {
        when (it) {
            HomePageRefineAction.OnApplyRefine -> {
                onApply(viewModel.outputs.argument)
            }
            HomePageRefineAction.OnCancelRefine -> onBackPressed()
        }
    }

    AppScreen(vm = viewModel, ignorePaddingValue = true) {
        Column {
            CommonToolbar(
                properties = ToolbarProperties(
                    leftActionResId = AppCommonR.drawable.ic_action_close,
                    rightActionResId = AppCommonR.drawable.ic_action_refersh,
                    title = stringResource(id = AppCommonR.string.key1012),
                    iconTitle = painterResource(id = AppCommonR.drawable.ic_filter_black),
                    titleTextStyle = LocalTypography.current.text14.semiBold.colorTxtTitle()
                ),
                onLeftActionClicked = {
                    onBackPressed()
                },
                onRightActionClicked = {
                    viewModel.inputs.onReset()
                }
            )
            HomePageRefineContent(
                selectedSortType = selectedSortType.value,
                selectedAllocationClassType = selectedAllocationClassType.value,
                modifier = modifier,
                availableAllocationClass = availableAllocationClass.value,
                onTriggerActions = { viewModel.onTriggerActions(it) })
        }
    }
}

@Composable
fun HomePageRefineContent(
    modifier: Modifier = Modifier,
    onTriggerActions: (action: HomePageRefineAction) -> Unit,
    selectedSortType: HomeMarketListType = HomeMarketListType.GAINER,
    selectedAllocationClassType: PortfolioAssetType = PortfolioAssetType.CASH_EQUIVALENT,
    availableAllocationClass: List<PortfolioAssetType> = emptyList(),
) {
    Column(
        modifier = modifier,
    ) {
        RefineSortingType(
            selectedSortType = selectedSortType,
            onTriggerActions = onTriggerActions
        )
      //  SeparatorLine(modifier = Modifier.padding(vertical = LocalDimens.current.dimen12))
    //        FilterAllocationClassTypeSection(
    //            selectedAllocationClassType,
    //            availableAllocationClass,
    //            onTriggerActions = onTriggerActions
    //        )
        Spacer(modifier = Modifier.weight(1f))
        HomePageRefineActions(onTriggerActions = onTriggerActions)
    }
}


@Composable
fun RefineSortingType(
    selectedSortType: HomeMarketListType = HomeMarketListType.GAINER,
    onTriggerActions: (action: HomePageRefineAction) -> Unit = {},
) {
    val context = LocalContext.current
    Text(
        text = stringResource(id = AppCommonR.string.key0460),
        style = LocalTypography.current.text14.medium.colorTxtTitle()
    )
    PaddingTop(value = LocalDimens.current.dimen8)
    Row {
        ToggleThirdButton(
            onSelectedChanged = {
                onTriggerActions(
                    HomePageRefineAction.OnSelectSortType(HomeMarketListType.GAINER)
                )
            },
            isSelectedValue = selectedSortType == HomeMarketListType.GAINER,
            modifier = Modifier.weight(1f),
            properties = ToggleThirdButtonProperties(
                text = HomeMarketListType.GAINER.displayValue(
                    context
                )
            ),
        )
        Spacer(modifier = Modifier.width(LocalDimens.current.dimen8))
        ToggleThirdButton(
            onSelectedChanged = {
                onTriggerActions(
                    HomePageRefineAction.OnSelectSortType(HomeMarketListType.LOSER)
                )
            },
            isSelectedValue = selectedSortType == HomeMarketListType.LOSER,
            modifier = Modifier.weight(1f),
            properties = ToggleThirdButtonProperties(
                text = HomeMarketListType.LOSER.displayValue(
                    context
                ),
            ),
        )
    }
}


@OptIn(ExperimentalLayoutApi::class)
@Composable
fun FilterAllocationClassTypeSection(
    selectedAllocationClassType: PortfolioAssetType = PortfolioAssetType.CASH_EQUIVALENT,
    availableAllocationClass: List<PortfolioAssetType> = emptyList(),
    onTriggerActions: (action: HomePageRefineAction) -> Unit = {},
) {
    val row = 4
    val column = 2
    Row(verticalAlignment = Alignment.CenterVertically) {
        Text(
            text = stringResource(id = AppCommonR.string.key1013),
            style = LocalTypography.current.text14.medium.colorTxtTitle()
        )
    }
    FlowRow(
        modifier = Modifier
            .padding(top = LocalDimens.current.dimen8)
            .fillMaxWidth()
            .background(LocalAppColor.current.bgDefault),
        horizontalArrangement = Arrangement.spacedBy(LocalDimens.current.dimen4),
        maxItemsInEachRow = 2,
    ) {
        repeat(row * column) { index ->
            ToggleThirdButton(
                onSelectedChanged = {
                    onTriggerActions(
                        HomePageRefineAction.OnSelectAllocationClassType(
                            availableAllocationClass[index]
                        )
                    )
                },
                modifier = Modifier.weight(1f),
                isSelectedValue = selectedAllocationClassType == availableAllocationClass[index],
                properties = ToggleThirdButtonProperties(
                    text = availableAllocationClass[index].displayName
                )
            )
        }
    }
}

@Composable
fun HomePageRefineActions(
    onTriggerActions: (action: HomePageRefineAction) -> Unit = {},
) {
    Box(modifier = Modifier.fillMaxWidth(1f))
    Row {
        SecondaryBorderButton(
            properties = ButtonProperties(
                text = stringResource(id = AppCommonR.string.key0273),

                ),
            modifier = Modifier.weight(1f),
            onClicked = {
                onTriggerActions(HomePageRefineAction.OnCancelRefine)
            }
        )
        Spacer(modifier = Modifier.width(LocalDimens.current.dimen8))
        SecondaryButton(
            properties = ButtonProperties(
                text = stringResource(id = AppCommonR.string.key0411),

                ),
            modifier = Modifier.weight(1f),
            onClicked = {
                onTriggerActions(HomePageRefineAction.OnApplyRefine)
            }
        )
    }
}

@Preview(showBackground = true, showSystemUi = true)
@Composable
fun PreviewHomePageRefineScreen() {
    HomePageRefineContent(
        modifier = Modifier.padding(
            LocalDimens.current.dimen12
        ),
        onTriggerActions = {},
        availableAllocationClass = listOf(
            PortfolioAssetType.CASH_EQUIVALENT,
            PortfolioAssetType.FIX_INCOME,
            PortfolioAssetType.MULTI_ASSET,
            PortfolioAssetType.STRUCTURED_PRODUCTS,
            PortfolioAssetType.PRIVATE_CAPITAL,
            PortfolioAssetType.EQUITY,
            PortfolioAssetType.COMMODITIES,
            PortfolioAssetType.VIRTUAL_ASSET,
        )
    )
}