package com.siriustech.home.domain

import android.content.Context
import com.siriustech.merit.apilayer.service.home.notification.NotificationListResponse
import com.siriustech.merit.app_common.R
import com.siriustech.merit.app_common.component.common.NotificationItemDisplayData
import com.siriustech.merit.app_common.ext.DATE_FORMAT_11
import com.siriustech.merit.app_common.ext.toDateTimeWithZoneId
import com.siriustech.merit.app_common.typeenum.NotificationBusinessType
import com.siriustech.merit.app_common.typeenum.NotificationTagType
import org.threeten.bp.Instant
import org.threeten.bp.ZoneId
import org.threeten.bp.ZonedDateTime
import org.threeten.bp.temporal.ChronoField
import org.threeten.bp.temporal.ChronoUnit

/**
 * Created by He<PERSON>tet
 */
object NotificationMapper {

    fun NotificationListResponse?.mapToNotificationDisplay(homePage: Boolean = true): List<NotificationItemDisplayData> {
        return this?.list.orEmpty().map {
            NotificationItemDisplayData(
                id = it.id ?: -1,
                title = it.title.orEmpty(),
                description = it.content.orEmpty(),
                type = NotificationBusinessType.fromParam(it.businessType.orEmpty()),
                tag = if (homePage) null else NotificationTagType.fromParam(it.tag.orEmpty()),
                isRead = it.isRead,
                time = formatNotificationDate(it.datetime ?: 0L),
                showMoreInfoIcon = false,
                displayDateTime = it.datetime?.toDateTimeWithZoneId(
                    format = DATE_FORMAT_11,
                    zoneId = ZoneId.systemDefault()
                ).orEmpty(),
                datetime = it.datetime ?: 0L
            )
        }
    }
}

fun formatNotificationDate(apiResponseMillis: Long): String {
    val now = ZonedDateTime.now(ZoneId.systemDefault())
    val apiDate = Instant.ofEpochMilli(apiResponseMillis).atZone(ZoneId.systemDefault())
    val startOfToday = now.toLocalDate().atStartOfDay(ZoneId.systemDefault())
    val startOfYesterday = startOfToday.minusDays(1)

    return when {
        apiDate.isAfter(startOfToday) -> {
            val hoursAgo = ChronoUnit.HOURS.between(apiDate, now)
            "${hoursAgo}H"
        }

        apiDate.isAfter(startOfYesterday) -> {
            val hoursAgo = ChronoUnit.HOURS.between(apiDate, now)
            "${hoursAgo}H"
        }

        else -> {
            val daysAgo = ChronoUnit.DAYS.between(apiDate.toLocalDate(), now.toLocalDate())
            "${daysAgo}D"
        }
    }
}

fun groupNotificationsByDate(
    notifications: List<NotificationItemDisplayData>,
    context: Context?,
): List<NotificationItemDisplayData> {
    val now = ZonedDateTime.now(ZoneId.systemDefault())
    val startOfToday = now.toLocalDate().atStartOfDay(ZoneId.systemDefault())
    val startOfYesterday = startOfToday.minusDays(1)
    val startOfThisWeek = now.with(ChronoField.DAY_OF_WEEK, 1)

    val todayNotifications = mutableListOf<NotificationItemDisplayData>()
    val yesterdayNotifications = mutableListOf<NotificationItemDisplayData>()
    val thisWeekNotifications = mutableListOf<NotificationItemDisplayData>()

    for (notification in notifications) {
        val notificationDate =
            Instant.ofEpochMilli(notification.datetime).atZone(ZoneId.systemDefault())
        when {
            notificationDate.isAfter(startOfToday) -> todayNotifications.add(notification)
            notificationDate.isAfter(startOfYesterday) -> yesterdayNotifications.add(notification)
//            notificationDate.isAfter(startOfThisWeek) -> thisWeekNotifications.add(notification)
            else -> thisWeekNotifications.add(notification)
        }
    }

    val groupedNotifications = mutableListOf<NotificationItemDisplayData>()

    if (todayNotifications.isNotEmpty()) {
        groupedNotifications.add(
            NotificationItemDisplayData(
                headerDate = context?.getString(com.siriustech.merit.app_common.R.string.key0953)
                    ?: "Today",
                isRead = true,
                isHeader = true
            )
        )
        groupedNotifications.addAll(todayNotifications)
    }
    if (yesterdayNotifications.isNotEmpty()) {
        groupedNotifications.add(
            NotificationItemDisplayData(
                headerDate = context?.getString(R.string.key0962) ?: "Yesterday",
                isRead = true,
                isHeader = true
            )
        )
        groupedNotifications.addAll(yesterdayNotifications)
    }
    if (thisWeekNotifications.isNotEmpty()) {
        groupedNotifications.add(
            NotificationItemDisplayData(
                headerDate = context?.getString(R.string.key0963) ?: "This Week",
                isRead = true,
                isHeader = true
            )
        )
        groupedNotifications.addAll(thisWeekNotifications)
    }

    return groupedNotifications
}