package com.siriustech.home.screen.notification

import com.siriustech.merit.app_common.component.common.MarketProfileTabModel
import com.siriustech.merit.app_common.component.common.NotificationItemDisplayData
import com.siriustech.merit.app_common.theme.AppAction

/**
 * Created by <PERSON><PERSON>t
 */
sealed interface NotificationActions : AppAction {
    data class OnNavigateToTab(val data: MarketProfileTabModel) : NotificationActions
    data class ToggleDeleteAllConfirmationModal(val show: Boolean) : NotificationActions
    data object MarkAllRead : NotificationActions
    data object AllNotificationCleared : NotificationActions
    data class MarkAsReadItem(val item:NotificationItemDisplayData) : NotificationActions
    data class ToggleManageNotificationModal(val show : Boolean,val item: NotificationItemDisplayData? = null) : NotificationActions
    data class OnDeleteNotification(val data : NotificationItemDisplayData) : NotificationActions
    data class OnNavigateToMessageCenterDetails(val data: NotificationItemDisplayData) : NotificationActions
}