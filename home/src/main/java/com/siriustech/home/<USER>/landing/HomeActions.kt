package com.siriustech.home.screen.landing

import com.siriustech.merit.app_common.data.display.MarketAssetDisplayData
import com.siriustech.merit.app_common.theme.AppAction
import com.siriustech.merit.app_common.typeenum.HomeMarketListType
import com.siriustech.merit.app_common.typeenum.HomeShortcutMenuAction

/**
 * Created by He<PERSON> Htet
 */
sealed interface HomeActions : AppAction {
    data class OnToggleCurrencyPairModal(val show : Boolean) : HomeActions
    data class OnMarketListTypeChanged(val type: HomeMarketListType) :HomeActions
    data class OnMarketWatchListPageChanged(val page:Int ) : HomeActions
    data class OnMarketWatchListItemClicked(val item:MarketAssetDisplayData ) : HomeActions
    data object OnNavigateToMarketTab : HomeActions
    data object OnNavigateToNotificationList : HomeActions
    data object OnNavigateToSettings : HomeActions
    data object OnNavigateToChat : HomeActions
    data object OnNavigateToRefineActivity : HomeActions
    data class OnShortcutMenuItemSelected(val menuAction: HomeShortcutMenuAction) :HomeActions
}