package com.siriustech.home.screen.landing.component

import androidx.compose.foundation.layout.aspectRatio
import androidx.compose.foundation.pager.HorizontalPager
import androidx.compose.foundation.pager.rememberPagerState
import androidx.compose.runtime.Composable
import androidx.compose.runtime.snapshots.SnapshotStateList
import androidx.compose.ui.Modifier
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.tooling.preview.Preview
import androidx.constraintlayout.compose.ConstraintLayout
import androidx.constraintlayout.compose.Dimension
import coil3.compose.AsyncImage
import coil3.network.NetworkHeaders
import coil3.network.httpHeaders
import coil3.request.ImageRequest
import coil3.request.crossfade
import com.siriustech.home.domain.display.BannerDisplayModel
import com.siriustech.merit.app_common.R
import com.siriustech.merit.app_common.component.indicator.HorizontalPagerIndicator
import com.siriustech.merit.app_common.ext.noRippleClickable
import com.siriustech.merit.app_common.theme.LocalDimens
import timber.log.Timber

/**
 * Created by <PERSON><PERSON>
 */

@Composable
fun HomeBanner(
    modifier: Modifier = Modifier,
    bannerItems: SnapshotStateList<BannerDisplayModel>,
    sessionId: String,
    onItemClick: (item: BannerDisplayModel) -> Unit = {},
) {
    if (bannerItems.isNotEmpty()) {

//        val pageCount = bannerItems.size
//        val max = Short.MAX_VALUE.toInt()
//        val half = max / 2
//        val pagerPositionIndex = 0 + half - half % pageCount
////        val pagerState = rememberPagerState(pageCount = { max }, initialPage = pagerPositionIndex)
        val pagerState = rememberPagerState(pageCount = { bannerItems.size }, initialPage = 0)
        ConstraintLayout(modifier = modifier) {
            val (box) = createRefs()
            HorizontalPager(
                pageSpacing = LocalDimens.current.dimen12,
                state = pagerState, modifier = Modifier,
            ) { index ->
//                val page = index % pageCount
                val bannerItem = bannerItems[index]
                AsyncImage(
                    modifier = Modifier
                        .noRippleClickable {
                            onItemClick(bannerItem)
                        }
                        .constrainAs(box) {
                            linkTo(start = parent.start, end = parent.end)
                            top.linkTo(parent.top)
                            width = Dimension.fillToConstraints
                        }
                        .aspectRatio(ratio = 16f / 6f),
                    model = ImageRequest.Builder(LocalContext.current)
                        .data(bannerItem.url)
                        .crossfade(true)
                        .httpHeaders(
                            headers = NetworkHeaders.Builder()
                                .set("sid", sessionId)
                                .build()
                        )
                        .build(),
                    contentScale = ContentScale.FillBounds,
                    placeholder = painterResource(R.drawable.ic_banner_placeholder),
                    error = painterResource(R.drawable.ic_banner_placeholder),
                    contentDescription = "Banner Placeholder",
                    onError = {
                        Timber.d("Image Loading error ${it.result.throwable}")
                    }
                )
            }
            if (bannerItems.count() > 1) {
                HorizontalPagerIndicator(
                    modifier = Modifier
                        .constrainAs(box) {
                            bottom.linkTo(parent.bottom)
                            start.linkTo(parent.start)
                            end.linkTo(parent.end)
                        },
                    pagerState = pagerState,
                    selectedCurrentIndex = true,
                )
            }
        }
    }
}

@Preview
@Composable
fun PreviewHomeBanner() {
    HomeBanner(bannerItems = SnapshotStateList<BannerDisplayModel>().apply {
        add(
            BannerDisplayModel(
                url = "https://placehold.co/600x300",
                targetUrl = "https://placehold.co/600x300"
            ),
        )
        add(
            BannerDisplayModel(
                url = "https://placehold.co/600x300",
                targetUrl = "https://placehold.co/600x300"
            )
        )
    }, sessionId = "", onItemClick = {})
}