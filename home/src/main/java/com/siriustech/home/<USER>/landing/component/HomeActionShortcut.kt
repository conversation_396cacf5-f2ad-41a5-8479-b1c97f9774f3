package com.siriustech.home.screen.landing.component

import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.tooling.preview.Preview
import com.siriustech.merit.app_common.component.container.PaddingTop
import com.siriustech.merit.app_common.ext.colorTxtParagraph
import com.siriustech.merit.app_common.ext.noRippleClickable
import com.siriustech.merit.app_common.theme.LocalDimens
import com.siriustech.merit.app_common.theme.LocalTypography
import com.siriustech.merit.app_common.typeenum.HomeShortcutMenuAction

/**
 * Created by <PERSON><PERSON>
 */

@Composable
fun HomeActionShortcuts(
    modifier: Modifier = Modifier,
    onMenuSelected: (item: HomeShortcutMenuAction) -> Unit,
) {
    val menus = listOf(
        HomeShortcutMenuAction.DEPOSIT,
        HomeShortcutMenuAction.WITHDRAW,
        HomeShortcutMenuAction.STATEMENT,
        HomeShortcutMenuAction.FAVORITES,
    )
    val context = LocalContext.current
    Row(modifier = modifier.fillMaxWidth(), horizontalArrangement = Arrangement.SpaceEvenly) {
        repeat(menus.size) { index ->
            val menu = menus[index]
            HomeActionShortcut(
                iconResId = menu.onDisplayIconResId(),
                title = menu.onDisplayTitle(context),
                onItemClicked = {
                    onMenuSelected(menu)
                }
            )
        }
    }
}


@Composable
fun HomeActionShortcut(iconResId: Int, title: String, onItemClicked: () -> Unit = {}) {
    Column(
        modifier = Modifier.noRippleClickable { onItemClicked() },
        verticalArrangement = Arrangement.Center,
        horizontalAlignment = Alignment.CenterHorizontally
    ) {
        Image(painter = painterResource(id = iconResId), contentDescription = title)
        PaddingTop(value = LocalDimens.current.dimen4)
        Text(text = title, style = LocalTypography.current.text12.regular.colorTxtParagraph())
    }
}


@Preview()
@Composable
fun PreviewHomeActionShortcut() {
    HomeActionShortcuts(
        modifier = Modifier.background(Color.White), onMenuSelected = {}
    )
}