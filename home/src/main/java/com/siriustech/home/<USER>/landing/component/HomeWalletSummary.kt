package com.siriustech.home.screen.landing.component

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.tooling.preview.Preview
import com.siriustech.home.domain.display.HomeWalletSummaryDisplayData
import com.siriustech.merit.app_common.component.common.SummaryAllocationPercentage
import com.siriustech.merit.app_common.component.common.WalletSummaryAllocationPercentageDisplay
import com.siriustech.merit.app_common.component.common.WalletSummaryV2
import com.siriustech.merit.app_common.component.container.PaddingStart
import com.siriustech.merit.app_common.data.display.WalletSummaryDisplayData
import com.siriustech.merit.app_common.ext.colorTxtInactive
import com.siriustech.merit.app_common.ext.colorTxtTitle
import com.siriustech.merit.app_common.theme.LocalAppColor
import com.siriustech.merit.app_common.theme.LocalDimens
import com.siriustech.merit.app_common.theme.LocalTypography
import com.siriustech.merit.app_common.typeenum.PortfolioAssetType
import com.siriustech.merit.app_common.typeenum.RiskLevel
import com.siriustech.merit.app_common.R as AppCommonR

/**
 * Created by Hein Htet
 */

@Composable
fun HomeWalletSummary(
    modifier: Modifier = Modifier,
    showGreeting: Boolean = false,
    displayData: HomeWalletSummaryDisplayData = HomeWalletSummaryDisplayData(),
) {
    Column(
        modifier = Modifier
            .fillMaxWidth()
            .background(LocalAppColor.current.bgDefault)
            .then(modifier)
    ) {
        if (showGreeting) {
            Greeting(displayData)
        }
        WalletSummaryV2(
            modifier = Modifier.padding(top = LocalDimens.current.dimen16),
            showDetailsInfoIcon = false,
            showTotalBalanceText = true,
            data = WalletSummaryDisplayData(
                totalBalance = displayData.totalBalance,
                currency = displayData.currency,
                priceChange = displayData.priceChange,
                priceChangeRate = displayData.priceChangeRate
            )
        )
        if (displayData.summaryList.isNotEmpty()) {
//            SeparatorLine(modifier = Modifier.padding(vertical = LocalDimens.current.dimen8))
            SummaryAllocationPercentage(allocations = displayData.summaryList,modifier= Modifier.padding(top = LocalDimens.current.dimen12))
        }
    }
}

@Composable
fun Greeting(displayData: HomeWalletSummaryDisplayData) {
    Text(
        text = stringResource(id = AppCommonR.string.key0819),
        style = LocalTypography.current.text12.medium.colorTxtInactive(),
    )
    Row(verticalAlignment = Alignment.CenterVertically) {
        Text(
            text = displayData.userName.plus("!"),
            style = LocalTypography.current.text14.semiBold.colorTxtTitle()
        )
        PaddingStart(value = LocalDimens.current.dimen4)
        displayData.riskLevel?.RiskLevelBadge()
    }
}


@Preview(showBackground = true)
@Composable
fun PreviewHomeWalletSummary() {
    HomeWalletSummary(
        modifier = Modifier.padding(LocalDimens.current.dimen12),
        showGreeting = false,
        displayData = HomeWalletSummaryDisplayData(
            userName = "Johnathan!",
            riskLevel = RiskLevel.HIGH,
            totalBalance = "99999999",
            currency = "USD",
            priceChange = "55",
            priceChangeRate = "55",
            summaryList = listOf(
                WalletSummaryAllocationPercentageDisplay(
                    type = PortfolioAssetType.EQUITY,
                    percentage = 18.2f,
                ), WalletSummaryAllocationPercentageDisplay(
                    type = PortfolioAssetType.CASH_EQUIVALENT,
                    percentage = 20f,
                ),
                WalletSummaryAllocationPercentageDisplay(
                    type = PortfolioAssetType.VIRTUAL_ASSET,
                    percentage = 31.8f,
                ),
                WalletSummaryAllocationPercentageDisplay(
                    type = PortfolioAssetType.COMMODITIES,
                    percentage = 30f,
                )
            )
        )
    )
}