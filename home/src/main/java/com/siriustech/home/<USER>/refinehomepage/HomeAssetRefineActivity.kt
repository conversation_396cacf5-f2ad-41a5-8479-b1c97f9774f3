package com.siriustech.home.screen.refinehomepage

import android.content.Intent
import android.os.Bundle
import androidx.activity.compose.setContent
import androidx.compose.foundation.layout.padding
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.toArgb
import com.siriustech.core_ui_compose.ext.ChangeSystemBarsTheme
import com.siriustech.merit.app_common.theme.AppComposeActivity
import com.siriustech.merit.app_common.theme.AppScreen
import com.siriustech.merit.app_common.theme.AppViewModel
import com.siriustech.merit.app_common.theme.LocalDimens
import dagger.hilt.android.AndroidEntryPoint

/**
 * Created by <PERSON><PERSON><PERSON>
 */
@AndroidEntryPoint
class HomeAssetRefineActivity : AppComposeActivity() {
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContent {
            ChangeSystemBarsTheme(lightTheme = true, Color.White.toArgb())
            AppScreen(vm = AppViewModel()) {
                HomePageRefineScreen(
                    onBackPressed = {
                        this.finish()
                    },
                    argument = (intent.getSerializableExtra(EXTRA_HOME_REFINE_DATA) as? HomePageRefineArgument?)
                        ?: HomePageRefineArgument(),
                    modifier = Modifier.padding(LocalDimens.current.dimen12),
                    onApply = {
                        setResult(RESULT_OK, Intent().apply {
                            putExtra(EXTRA_HOME_REFINE_DATA, it)
                        })
                        finish()
                    }
                )
            }
        }
    }
    companion object {
        const val EXTRA_HOME_REFINE_DATA = "EXTRA_HOME_REFINE_DATA"
    }
}