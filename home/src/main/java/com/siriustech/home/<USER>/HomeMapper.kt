package com.siriustech.home.domain

import com.core.util.toAmount
import com.siriustech.home.domain.display.BannerDisplayModel
import com.siriustech.home.domain.display.HomeWalletSummaryDisplayData
import com.siriustech.merit.apilayer.service.home.banner.BannerListResponse
import com.siriustech.merit.apilayer.service.wallet.summary.WalletSummaryResponse
import com.siriustech.merit.app_common.Constants
import com.siriustech.merit.app_common.component.common.WalletSummaryAllocationPercentageDisplay
import com.siriustech.merit.app_common.data.AppCache
import com.siriustech.merit.app_common.ext.capitalizeWords
import com.siriustech.merit.app_common.typeenum.PortfolioAssetType

/**
 * Created by Hein Htet
 */
object HomeMapper {
    fun WalletSummaryResponse.mapToHomeWalletSummaryDisplayData(
        data: HomeWalletSummaryDisplayData,
        appCache: AppCache
    ): HomeWalletSummaryDisplayData {
        var info = data
        info = info.copy(
            totalBalance = this.totalValue?.toAmount(4) ?: "-",
            currency = this.currency ?: "-",
            priceChange = this.unrealizedGl?.toAmount(4) ?: "-",
            priceChangeRate = this.unrealizedGlRate?.toAmount(2) ?: "-",
            summaryList = this.summaryList.orEmpty().map {
                WalletSummaryAllocationPercentageDisplay(
                    type = PortfolioAssetType.fromParam(it.summaryName.orEmpty())
                        ?: PortfolioAssetType.UNKNOWN_TYPE,
                    rawSummaryName = it.summaryName.orEmpty(),
                    percentage = it.percentage?.toFloatOrNull() ?: 0f,
                    displaySummaryName = appCache.allocationClass.findLast { assetClass -> assetClass.id == it.summaryName }?.title
                        ?: it.summaryName.orEmpty().capitalizeWords()
                )
            }
        )
        return info
    }

    fun BannerListResponse.mapToBannerDisplayList(baseUrl:String): List<BannerDisplayModel> {

        val banners = ArrayList<BannerDisplayModel>()
        this.list.orEmpty().sortedBy { it.sortOrder }.map {
            banners.add(
                BannerDisplayModel(
                    url = "$baseUrl${Constants.COMMON_FILE_BASE_URL}${it.imageFileKey.orEmpty()}",
                    targetUrl = it.targetUrl.orEmpty(),
                    title = it.title.orEmpty()
                )
            )
        }
        return banners
    }

}