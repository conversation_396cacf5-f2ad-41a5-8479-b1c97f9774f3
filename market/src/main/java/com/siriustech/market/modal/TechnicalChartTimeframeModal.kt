package com.siriustech.market.modal

import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.ExperimentalLayoutApi
import androidx.compose.foundation.layout.FlowRow
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.ModalBottomSheet
import androidx.compose.material3.Text
import androidx.compose.material3.rememberModalBottomSheetState
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.mutableStateListOf
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.tooling.preview.Preview
import com.siriustech.market.domain.CandleChartTimeFrameType
import com.siriustech.merit.app_common.component.button.ButtonProperties
import com.siriustech.merit.app_common.component.button.SecondaryBorderButton
import com.siriustech.merit.app_common.component.button.SecondaryButton
import com.siriustech.merit.app_common.component.container.PaddingTop
import com.siriustech.merit.app_common.ext.colorTxtInactive
import com.siriustech.merit.app_common.ext.colorTxtParagraph
import com.siriustech.merit.app_common.ext.colorTxtTitle
import com.siriustech.merit.app_common.ext.noRippleClickable
import com.siriustech.merit.app_common.theme.LocalAppColor
import com.siriustech.merit.app_common.theme.LocalDimens
import com.siriustech.merit.app_common.theme.LocalTypography
import com.siriustech.merit.app_common.R as AppCommonR
import kotlinx.coroutines.launch

/**
 * Created by Hein Htet
 */
@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun TechnicalChartTimeframeModal(
    currentSelectedTimeFrame: CandleChartTimeFrameType = CandleChartTimeFrameType.ONE_DAY,
    onDismissed: () -> Unit = {},
    onTimeFrameSelected: (CandleChartTimeFrameType) -> Unit = {},
) {
    val sheetState = rememberModalBottomSheetState()
    val scope = rememberCoroutineScope()

    fun onDismiss() {
        scope.launch {
            sheetState.hide()
            onDismissed()
        }
    }
    ModalBottomSheet(
        dragHandle = {},
        shape = RoundedCornerShape(LocalDimens.current.dimen4),
        containerColor = LocalAppColor.current.bgDefault,
        onDismissRequest = onDismissed, sheetState = sheetState,
        modifier = Modifier
    ) {
        Column(
            modifier = Modifier
                .fillMaxWidth()
                .padding(
                    horizontal = LocalDimens.current.dimen14,
                    vertical = LocalDimens.current.dimen16
                )
        ) {
            TechnicalChartTimeframeModalContent(
                currentSelectedTimeFrame,
                onDismissed = { onDismiss() },
                onTimeFrameSelected = {
                    onTimeFrameSelected(it)
                }
            )
        }
    }
}

@OptIn(ExperimentalLayoutApi::class)
@Composable
fun TechnicalChartTimeframeModalContent(
    currentSelectedTimeFrame: CandleChartTimeFrameType,
    onDismissed: () -> Unit = {},
    onTimeFrameSelected: (CandleChartTimeFrameType) -> Unit = {},
) {

    val selectedTimeFrame = remember {
        mutableStateOf(CandleChartTimeFrameType.ONE_DAY)
    }

    LaunchedEffect(currentSelectedTimeFrame) {
        selectedTimeFrame.value = currentSelectedTimeFrame
    }

    val timeFrames = remember {
        mutableStateListOf(
            *listOf(
                CandleChartTimeFrameType.ONE_MIN,
                CandleChartTimeFrameType.FIVE_MIN,
                CandleChartTimeFrameType.FIFTEEN_MIN,
                CandleChartTimeFrameType.THIRTY_MIN,
                CandleChartTimeFrameType.ONE_HOUR,
                CandleChartTimeFrameType.TWO_HOUR,
                CandleChartTimeFrameType.FOUR_HOUR,
                CandleChartTimeFrameType.ONE_DAY,
                CandleChartTimeFrameType.ONE_WEEK,
                CandleChartTimeFrameType.ONE_MONTH,
            ).toTypedArray()
        )
    }

    Column(
        modifier = Modifier
            .fillMaxWidth()
            .padding(
                vertical = LocalDimens.current.dimen16
            )
    ) {
        Row(verticalAlignment = Alignment.CenterVertically) {
            Text(
                text = stringResource(id = AppCommonR.string.key0410),
                style = LocalTypography.current.text14.semiBold.colorTxtTitle()
            )
            Text(
                text = stringResource(id = AppCommonR.string.key0483),
                modifier = Modifier.weight(1f),
                style = LocalTypography.current.text14.regular.colorTxtParagraph()
            )
            Image(
                modifier = Modifier.noRippleClickable { onDismissed() },
                painter = painterResource(id = AppCommonR.drawable.ic_action_close),
                contentDescription = "Close Image Resource"
            )
        }

        val row = 2
        val column = 5
        FlowRow(
            modifier = Modifier
                .padding(top = LocalDimens.current.dimen8)
                .fillMaxWidth()
                .background(LocalAppColor.current.bgDefault),
            horizontalArrangement = Arrangement.spacedBy(LocalDimens.current.dimen4),
            maxItemsInEachRow = 5,
        ) {
            repeat(row * column) {
                val item = timeFrames[it]
                Box(
                    modifier = Modifier
                        .weight(1f)
                        .height(LocalDimens.current.dimen38)
                        .background(if (item == selectedTimeFrame.value) LocalAppColor.current.btn3rd else Color.Transparent)
                        .clip(RoundedCornerShape(LocalDimens.current.dimen2))
                        .noRippleClickable {
                            selectedTimeFrame.value = item
                        },
                    contentAlignment = Alignment.Center
                ) {
                    Text(
                        text = item.value,
                        style = if (item == selectedTimeFrame.value) LocalTypography.current.text14.medium.colorTxtParagraph() else LocalTypography.current.text14.light.colorTxtInactive()
                    )
                }
            }
        }
        PaddingTop(value = LocalDimens.current.dimen16)
        Row {
            SecondaryBorderButton(
                properties = ButtonProperties(text = stringResource(id = AppCommonR.string.key1011)),
                onClicked = {},
                modifier = Modifier.weight(1f)
            )
            Spacer(modifier = Modifier.width(LocalDimens.current.dimen8))
            SecondaryButton(
                properties = ButtonProperties(text = stringResource(id = AppCommonR.string.key0411)),
                onClicked = {
                    onTimeFrameSelected(selectedTimeFrame.value)
                    onDismissed()
                },
                modifier = Modifier.weight(1f)
            )
        }
    }
}

@Preview(showBackground = true, showSystemUi = true)
@Composable
fun TechnicalChartTimeframeModalPreview() {
    TechnicalChartTimeframeModalContent(CandleChartTimeFrameType.ONE_MIN)
}