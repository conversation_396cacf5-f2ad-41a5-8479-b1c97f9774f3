package com.siriustech.market.domain

import com.siriustech.market.domain.display.CandleChartDisplayModel
import com.siriustech.market.domain.display.SimpleChartDisplayModel
import com.siriustech.merit.apilayer.service.market.simplechart.SimpleChartResponse
import com.siriustech.merit.app_common.data.display.MarketAssetDisplayData
import com.siriustech.merit.app_common.ext.DATE_FORMAT_1
import com.siriustech.merit.app_common.ext.DATE_FORMAT_2
import com.siriustech.merit.app_common.ext.isPriceDown
import com.siriustech.merit.app_common.ext.isPriceUp
import com.siriustech.merit.app_common.ext.isPriceZero
import com.siriustech.merit.app_common.ext.toDateTimeWithZoneId
import com.siriustech.merit.app_common.typeenum.ChartTimeFrame
import com.siriustech.merit.app_common.typeenum.SimpleChartType
import org.threeten.bp.ZoneId

/**
 * Created by <PERSON><PERSON>tet
 */
object SimpleChartDataMapper {

    fun SimpleChartResponse.mapToTechnicalChartDisplayModel(
        marketAssetDisplayData: MarketAssetDisplayData,
        selectedTimeFrame: ChartTimeFrame,
    ): SimpleChartDisplayModel {
        var data = SimpleChartDisplayModel()
        val candles = ArrayList<CandleChartDisplayModel>()
        this.candles.orEmpty().forEach {
            candles.add(
                CandleChartDisplayModel(
                    it[INDEX_TIMESTAMP].toLong(),
                    it[INDEX_OPEN].toDoubleOrNull() ?: 0.0,
                    it[INDEX_HIGH].toDoubleOrNull() ?: 0.0,
                    it[INDEX_LOW].toDoubleOrNull() ?: 0.0,
                    it[INDEX_CLOSE].toDoubleOrNull() ?: 0.0,
                    it[INDEX_VOLUME].toDoubleOrNull() ?: 0.0,
                    currency = marketAssetDisplayData.currency
                )
            )

        }
        data = data.copy(candles = candles)
        val sortedList = candles.sortedBy { it.timestamp }
        if (sortedList.size >= 3) {
            val first = sortedList.first()
            val middle = sortedList[sortedList.size / 2]
            val last = sortedList.last()
            val timeframeYearList =
                listOf(ChartTimeFrame.ONE_YEAR, ChartTimeFrame.FIVE_YEAR, ChartTimeFrame.THREE_YEAR)
            val format =
                if (timeframeYearList.contains(selectedTimeFrame)) DATE_FORMAT_2 else DATE_FORMAT_1

            data = data.copy(
                xAxisDates = Triple(
                    first.timestamp.toDateTimeWithZoneId(format, zoneId = ZoneId.systemDefault())
                        .orEmpty(),
                    middle.timestamp.toDateTimeWithZoneId(format, zoneId = ZoneId.systemDefault())
                        .orEmpty(),
                    last.timestamp.toDateTimeWithZoneId(format, zoneId = ZoneId.systemDefault())
                        .orEmpty(),
                )
            )
        }
        val chartType = when {
            marketAssetDisplayData.unrealizedGlRate.isPriceUp() -> SimpleChartType.HIGH
            marketAssetDisplayData.unrealizedGlRate.isPriceDown() -> SimpleChartType.LOW
            marketAssetDisplayData.unrealizedGlRate.isPriceZero() -> SimpleChartType.NORMAL
            else -> SimpleChartType.HIGH
        }
        data = data.copy(simpleChartType = chartType)
        return data
    }


    private const val INDEX_TIMESTAMP = 0
    private const val INDEX_OPEN = 1
    private const val INDEX_HIGH = 2
    private const val INDEX_LOW = 3
    private const val INDEX_CLOSE = 4
    private const val INDEX_VOLUME = 5
}