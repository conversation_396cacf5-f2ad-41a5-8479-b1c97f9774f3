//package com.siriustech.market.domain
//
//import android.content.Context
//import android.text.SpannableStringBuilder
//import android.text.style.TextAppearanceSpan
//import androidx.core.text.inSpans
//import com.core.util.getDateFormatted
//import com.siriustech.market.domain.display.FinancialInfoDisplay
//import com.siriustech.market.domain.display.FinancialItemInfoDisplay
//import com.siriustech.merit.app_common.R
//import com.siriustech.merit.apilayer.service.market.marketprofile.FinancialResponse
//import com.siriustech.merit.app_common.ext.DATE_FORMAT_14
//import com.siriustech.merit.app_common.ext.DATE_FORMAT_3
//import com.siriustech.merit.app_common.ext.formatVolume
//import com.siriustech.merit.app_common.utils.getTypefaceSpan
//import com.siriustech.merit.app_common.utils.setCustomSpannableWithHighlight
//import java.util.Locale
//
///**
// * Created by <PERSON><PERSON>
// */
//class FinancialInfoMapper constructor(
//    private val context: Context,
//) {
//    fun toUI(data: FinancialResponse): FinancialInfoDisplay {
//        val date = data.periodDate?.getDateFormatted(
//            DATE_FORMAT_3,
//            DATE_FORMAT_14,
//            locale = Locale.getDefault()
//        ).orEmpty()
//
//        val items = arrayListOf<FinancialItemInfoDisplay>()
//
//        if (!data.sales?.yy.isNullOrEmpty() && data.sales?.yy != "0.00" || data.sales?.list?.filter { it == "0.00" }?.size != data.sales?.list?.size) {
//            items.add(
//                FinancialItemInfoDisplay(
//                    name = context.getString(R.string.SALES),
//                    getAmountValue(data.sales?.list?.firstOrNull() ?: "0.0"),
//                    yy = getYyValue(data.sales?.yy ?: "0.0"),
//                    barData = data.sales?.list.orEmpty().reversed().getFinancialBarData(),
//                )
//            )
//        }
//
//        if (!data.grossProfit?.yy.isNullOrEmpty() && data.grossProfit?.yy != "0" || data.grossProfit?.list?.filter { it == "0.00" }?.size != data.grossProfit?.list?.size) {
//            items.add(
//                FinancialItemInfoDisplay(
//                    name = context.getString(R.string.GROSS_PROFIT),
//                    getAmountValue(data.grossProfit?.list?.firstOrNull() ?: "0.0"),
//                    yy = getYyValue(data.grossProfit?.yy ?: "0.0"),
//                    barData = data.grossProfit?.list.orEmpty().reversed().getFinancialBarData(),
//                )
//            )
//        }
//
//        if (!data.operatingIncome?.yy.isNullOrEmpty() && data.operatingIncome?.yy != "0" || data.operatingIncome?.list?.filter { it == "0.00" }?.size != data.operatingIncome?.list?.size) {
//            items.add(
//                FinancialItemInfoDisplay(
//                    name = context.getString(R.string.OPERATION_INCOME),
//                    getAmountValue(data.operatingIncome?.list?.firstOrNull() ?: "0.0"),
//                    yyPercent = getYyValue(data.operatingIncome?.yy ?: "0.0"),
//                    barData = data.operatingIncome?.list.orEmpty().reversed()
//                        .getFinancialBarData(),
//
//                    )
//            )
//        }
//
//        if (!data.netIncome?.yy.isNullOrEmpty() && data.netIncome?.yy != "0" || data.netIncome?.list?.filter { it == "0.00" }?.size != data.netIncome?.list?.size) {
//            items.add(
//                FinancialItemInfoDisplay(
//                    name = context.getString(R.string.NET_INCOME),
//                    getAmountValue(data.netIncome?.list?.firstOrNull() ?: "0.0"),
//                    yy = getYyValue(data.netIncome?.yy ?: "0.0"),
//                    barData = data.netIncome?.list.orEmpty().reversed().orEmpty().getFinancialBarData(),
//                )
//            )
//        }
//
//        if (!data.operatingMargin?.yy.isNullOrEmpty() && data.operatingMargin?.yy != "0" || data.operatingMargin?.list?.filter { it == "0.00" }?.size != data.operatingMargin?.list?.size) {
//            items.add(
//                FinancialItemInfoDisplay(
//                    name = context.getString(R.string.OPERATION_MARGIN),
//                    getAmountValue(
//                        data.operatingMargin?.list?.firstOrNull() ?: "0.0",
//                        forceSuffix = "X"
//                    ),
//                    yy = getYyValue(data.operatingMargin?.yy ?: "0.0"),
//                    barData = data.operatingMargin?.list.orEmpty().reversed().orEmpty()
//                        .getFinancialBarData(),
//                )
//            )
//        }
//
//
//        if (!data.totalAssets?.yy.isNullOrEmpty() && data.totalAssets?.yy != "0" || data.totalAssets?.list?.filter { it == "0.00" }?.size != data.totalAssets?.list?.size) {
//            items.add(
//                FinancialItemInfoDisplay(
//                    name = context.getString(R.string.ASSETS),
//                    getAmountValue(data.totalAssets?.list?.firstOrNull() ?: "0.0"),
//                    yy = getYyValue(data.totalAssets?.yy ?: "0.0"),
//                    barData = data.totalAssets?.list.orEmpty().reversed().getFinancialBarData(),
//
//                    )
//            )
//        }
//
//
//        if (!data.earningsPerShare?.yy.isNullOrEmpty() && data.earningsPerShare?.yy != "0" || data.earningsPerShare?.list?.filter { it == "0.00" }?.size != data.earningsPerShare?.list?.size) {
//            items.add(
//
//                FinancialItemInfoDisplay(
//                    name = context.getString(R.string.EARNING_PER_SHARE),
//                    getAmountValue(data.earningsPerShare?.list?.firstOrNull() ?: "0.0"),
//                    yy = getYyValue(data.earningsPerShare?.yy ?: "0.0"),
//                    barData = data.earningsPerShare?.list.orEmpty().reversed()
//                        .getFinancialBarData(),
//
//                    )
//            )
//        }
//
//        if (!data.totalLiabilities?.yy.isNullOrEmpty() && data.totalLiabilities?.yy != "0" || data.totalLiabilities?.list?.filter { it == "0.00" }?.size != data.totalLiabilities?.list?.size) {
//            items.add(
//                FinancialItemInfoDisplay(
//                    name = context.getString(R.string.LIABILITIES),
//                    getAmountValue(data.totalLiabilities?.list?.firstOrNull() ?: "0.0"),
//                    yy = getYyValue(data.totalLiabilities?.yy ?: "0.0", true),
//                    barData = data.totalLiabilities?.list.orEmpty().reversed().orEmpty()
//                        .getFinancialBarData(),
//                ),
//            )
//        }
//        val displayDate = if (date.isEmpty()) {
//            ""
//        } else {
//            ", $date"
//        }
//        return FinancialInfoDisplay(period = "${data.period}$displayDate", items = items)
//    }
//
//    private fun getYyValue(yyValue: String, isReverse: Boolean = false): SpannableStringBuilder {
//        val sp = SpannableStringBuilder().apply {
//            if (yyValue.isEmpty()) {
//                setCustomSpannableWithHighlight(
//                    context,
//                    R.style.Text12_TxtLabel_SemiBold,
//                    "-",
//                    R.color.txtLabel
//                )
//            } else {
//                inSpans(
//                    getTypefaceSpan(context),
//                    TextAppearanceSpan(
//                        context,
//                        if (!yyValue.contains("-") && !isReverse) {
//                            R.style.Text12_TxtPositive_SemiBold
//                        } else {
//                            R.style.Text12_TxtNegative_SemiBold
//                        }
//                    ),
//                    builderAction = {
//                        append(if (yyValue.contains("-")) yyValue else "+$yyValue")
//                    }
//                )
//            }
//            if (yyValue.isNotEmpty()) {
//                setCustomSpannableWithHighlight(
//                    context,
//                    R.style.Text12_TxtLabel_SemiBold,
//                    " % Y/Y",
//                    R.color.txtLabel
//                )
//            }
//        }
//        return sp
//    }
//
//    private fun List<String>.getFinancialBarData(): List<Double> {
//        return this.map { it.toDoubleOrNull() ?: 0.0 }.asReversed()
//    }
//
//    private fun getAmountValue(
//        amount: String,
//        forceSuffix: String? = null,
//    ): SpannableStringBuilder {
//        val sp = SpannableStringBuilder().apply {
//            val formattedValue = amount.formatVolume()
//            val suffix = forceSuffix ?: formattedValue.substringAfter(" ")
//            val value = formattedValue.substringBefore(" ")
//            inSpans(
//                getTypefaceSpan(context),
//                TextAppearanceSpan(
//                    context,
//                    R.style.Text12_TxtTitle_SemiBold
//                ),
//                builderAction = {
//                    append(value)
//                }
//            )
//            setCustomSpannableWithHighlight(
//                context,
//                R.style.Text12_TxtLabel_SemiBold,
//                " $suffix",
//                R.color.txtLabel
//            )
//        }
//        return sp
//    }
//}