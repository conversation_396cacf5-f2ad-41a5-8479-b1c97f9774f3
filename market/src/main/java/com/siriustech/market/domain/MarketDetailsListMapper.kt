package com.siriustech.market.domain

import com.core.util.toAmount
import com.siriustech.merit.apilayer.service.market.marketlistdetails.MarketInstrumentListResponse
import com.siriustech.merit.app_common.data.display.MarketAssetDisplayData
import com.siriustech.merit.app_common.typeenum.MarketAssetType
import com.siriustech.merit.app_common.typeenum.RiskLevel

/**
 * Created by <PERSON><PERSON>t
 */
object MarketDetailsListMapper {

    fun MarketInstrumentListResponse.mapToMarketAssetDisplayItems(): List<MarketAssetDisplayData> {
        return this.instruments.orEmpty().map {
            MarketAssetDisplayData(
                id = it.id.toString(),
                symbol = it.symbol.orEmpty(),
                venue = it.exchange.orEmpty(),
                name = it.instrumentName.orEmpty(),
                riskLevel = RiskLevel.fromParam(it.riskLevel.orEmpty()),
                exchange = it.exchange.orEmpty(),
                marketPrice = it.lastPrice.orEmpty(),
                currency = it.currency.orEmpty(),
                unrealizedGl = it.priceChange.orEmpty(),
                marketValue = it.lastPrice?.toAmount(4).orEmpty(),
                isFavorite = it.favorite ?: false,
                unrealizedGlRate = it.priceChangePercentage.orEmpty(),
                marketAssetType = MarketAssetType.fromParam(it.instrumentClass.orEmpty()) ?: MarketAssetType.US_EQUITY,
                logo = it.logo.orEmpty()
            )
        }
    }

    fun MarketInstrumentListResponse.mapToMarketInstrumentViewedAssetDisplayItems(): List<MarketAssetDisplayData> {
        return this.instrumentList.orEmpty().map {
            MarketAssetDisplayData(
                id = it.id.toString(),
                symbol = it.symbol.orEmpty(),
                venue = it.exchange.orEmpty(),
                name = it.instrumentName.orEmpty(),
                riskLevel = RiskLevel.fromParam(it.riskLevel.orEmpty()),
                exchange = it.exchange.orEmpty(),
                marketPrice = it.lastPrice.orEmpty(),
                currency = it.currency.orEmpty(),
                unrealizedGl = it.priceChange.orEmpty(),
                marketValue = it.lastPrice?.toAmount(4).orEmpty(),
                isFavorite = it.favorite ?: false,
                unrealizedGlRate = it.priceChangePercentage.orEmpty(),
                marketAssetType = MarketAssetType.fromParam(it.instrumentClass.orEmpty()) ?: MarketAssetType.US_EQUITY,
                logo = it.logo.orEmpty()
            )
        }
    }
}