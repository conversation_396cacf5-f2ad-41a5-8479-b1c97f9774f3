package com.siriustech.market.domain.display

import com.siriustech.merit.app_common.typeenum.MarketAssetType
import com.siriustech.merit.app_common.typeenum.PortfolioAssetType

/**
 * Created by <PERSON><PERSON> Htet
 */
data class BasicInformationDisplayData(
    val micCode: String = "", // HK,US equity
    val productCategory: String = "",
    val allocationClass: PortfolioAssetType = PortfolioAssetType.EQUITY,
    val productType: String = "",
    val currency: String = "",
    val fundType: String = "", // mutual fund
    val fundManager: String = "",// mutual fund,
    val fundSize: String = "",// mutual fund,
    val inceptionDate: String = "",// mutual fund,
    val issuer: String = "", // bond
    val bondType: String = "",
    val tags: List<String> = emptyList(),
)
