package com.siriustech.market.domain.display

/**
 * Created by <PERSON><PERSON>
 */

data class CurrentSessionDisplayModel(
    val volume : String = "",
    val openPrice : String = "",
    val highestPrice : String = "",
    val lowestPrice : String = "",
    val averagePrice : String = "",
    val averageBuyPrice : String = "",
    val averageSellPrice : String = "",
    val ceilingPrice : String = "",
    val floorPrice : String = "",
    val currency:String = "",
    val asOfDate : String = "",
)