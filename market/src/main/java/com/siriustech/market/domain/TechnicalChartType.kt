package com.siriustech.market.domain

import androidx.compose.runtime.Composable
import androidx.compose.runtime.ReadOnlyComposable
import androidx.compose.ui.res.stringResource
import com.siriustech.merit.app_common.R as AppCommonR

/**
 * Created by <PERSON><PERSON>tet
 */
enum class TechnicalChartType(val value: String) {
    LINE_CHART("Line"),
    CANDLE_STICK_CHART("Candlestick");


    companion object {

        fun fromParam(value: String): TechnicalChartType {
            return if (value == CANDLE_STICK_CHART.value) {
                 CANDLE_STICK_CHART
            } else {
                 LINE_CHART
            }
        }

        @Composable
        @ReadOnlyComposable
        fun displayName(type: TechnicalChartType): String {
            return if (type == CANDLE_STICK_CHART) {
                return stringResource(id = AppCommonR.string.key0484)
            } else {
                stringResource(id = AppCommonR.string.key0485)
            }
        }
    }
}