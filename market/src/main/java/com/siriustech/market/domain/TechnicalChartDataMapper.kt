package com.siriustech.market.domain

import android.graphics.drawable.GradientDrawable
import androidx.compose.runtime.Composable
import androidx.compose.runtime.ReadOnlyComposable
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.toArgb
import com.core.util.toAmount
import com.github.mikephil.charting.data.BarData
import com.github.mikephil.charting.data.BarDataSet
import com.github.mikephil.charting.data.BarEntry
import com.github.mikephil.charting.data.CandleData
import com.github.mikephil.charting.data.CandleDataSet
import com.github.mikephil.charting.data.CandleEntry
import com.github.mikephil.charting.data.Entry
import com.github.mikephil.charting.data.LineData
import com.github.mikephil.charting.data.LineDataSet
import com.siriustech.market.domain.display.CandleChartDisplayModel
import com.siriustech.market.domain.display.LineChartDisplayModel
import com.siriustech.market.domain.display.TechnicalChartDisplayModel
import com.siriustech.market.domain.display.VolumeChartDisplayModel
import com.siriustech.merit.apilayer.service.market.candle.CandleChartDataResponse
import com.siriustech.merit.app_common.data.display.MarketAssetDisplayData
import com.siriustech.merit.app_common.ext.DATE_FORMAT_1
import com.siriustech.merit.app_common.ext.DATE_FORMAT_2
import com.siriustech.merit.app_common.ext.haveSameYear
import com.siriustech.merit.app_common.ext.toDateTimeWithZoneId
import com.siriustech.merit.app_common.theme.LocalAppColor
import java.util.Date
import org.threeten.bp.ZoneId
import timber.log.Timber

/**
 * Created by Hein Htet
 */
object TechnicalChartDataMapper {

    fun CandleChartDataResponse.mapToTechnicalChartDisplayModel(marketAssetDisplayData: MarketAssetDisplayData): TechnicalChartDisplayModel {
        var data = TechnicalChartDisplayModel()
        val candles = ArrayList<CandleChartDisplayModel>()
        val volumes = ArrayList<VolumeChartDisplayModel>()
        val lines = ArrayList<LineChartDisplayModel>()
        this.candles.orEmpty().forEach {
            candles.add(
                CandleChartDisplayModel(
                    it[INDEX_TIMESTAMP].toLong(),
                    it[INDEX_OPEN].toDoubleOrNull() ?: 0.0,
                    it[INDEX_HIGH].toDoubleOrNull() ?: 0.0,
                    it[INDEX_LOW].toDoubleOrNull() ?: 0.0,
                    it[INDEX_CLOSE].toDoubleOrNull() ?: 0.0,
                    it[INDEX_VOLUME].toDoubleOrNull() ?: 0.0,
                    currency = marketAssetDisplayData.currency
                )
            )
            volumes.add(
                VolumeChartDisplayModel(
                    volume = it[INDEX_VOLUME].toDoubleOrNull() ?: 0.0
                )
            )
            lines.add(
                LineChartDisplayModel(
                    price = it[INDEX_CLOSE].toDoubleOrNull() ?: 0.0,
                    data = CandleChartDisplayModel(
                        it[INDEX_TIMESTAMP].toLong(),
                        it[INDEX_OPEN].toDoubleOrNull() ?: 0.0,
                        it[INDEX_HIGH].toDoubleOrNull() ?: 0.0,
                        it[INDEX_LOW].toDoubleOrNull() ?: 0.0,
                        it[INDEX_CLOSE].toDoubleOrNull() ?: 0.0,
                        it[INDEX_VOLUME].toDoubleOrNull() ?: 0.0,
                        currency = marketAssetDisplayData.currency
                    )
                )
            )
        }
        val sortedCandles = candles.sortedBy { it.timestamp }
        val sortedLines = lines.sortedBy { it.data.timestamp }
        data = data.copy(candles = sortedCandles, volumes = volumes, lineEntries = sortedLines)
        if (sortedCandles.size >= 3) {
            val first = sortedCandles.first()
            val middle = sortedCandles[sortedCandles.size / 2]
            val last = sortedCandles.last()
            val dates = listOf(Date(first.timestamp), Date(middle.timestamp), Date(last.timestamp))
            val havingDiffYear = haveSameYear(dates)
            val format = if (havingDiffYear) DATE_FORMAT_2 else DATE_FORMAT_1
            data = data.copy(
                xAxisDates = Triple(
                    first.timestamp.toDateTimeWithZoneId(format, zoneId = ZoneId.systemDefault()).orEmpty(),
                    middle.timestamp.toDateTimeWithZoneId(format, zoneId = ZoneId.systemDefault()).orEmpty(),
                    last.timestamp.toDateTimeWithZoneId(format, zoneId = ZoneId.systemDefault()).orEmpty(),
                )
            )
        }
        return data
    }


    @Composable
    @ReadOnlyComposable
    fun List<CandleChartDisplayModel>.mapToCandleStickData(): CandleData {
        val entries = ArrayList<CandleEntry>()
        this.forEachIndexed { index, candleChartDisplayModel ->
            entries.add(
                CandleEntry(
                    index.toFloat(),
                    candleChartDisplayModel.high.toFloat(),  // high
                    candleChartDisplayModel.low.toFloat(),  // low
                    candleChartDisplayModel.open.toFloat(),  // open
                    candleChartDisplayModel.close.toFloat(),  // close,
                    candleChartDisplayModel
                )
            )
        }
        val candleData = CandleData((CandleDataSet(entries, "Candle Data Set")).apply {
            color = android.graphics.Color.rgb(80, 80, 80)
            shadowColor = Color.DarkGray.toArgb()
            shadowWidth = 0.8f
            decreasingColor = LocalAppColor.current.txtNegative.toArgb()
            decreasingPaintStyle = android.graphics.Paint.Style.FILL
            increasingColor = LocalAppColor.current.txtPositive.toArgb()
            increasingPaintStyle = android.graphics.Paint.Style.FILL
            neutralColor = LocalAppColor.current.txtDisabled.toArgb()
            shadowColorSameAsCandle = true
            highLightColor = Color.Transparent.toArgb()
            setDrawValues(false)
        })

        return candleData
    }


    @Composable
    fun List<VolumeChartDisplayModel>.mapToBarData(): BarData {
        Timber.d("BAR_COUNT ${this.count()}")
        val barEntries = this.mapIndexed { index, item ->

            BarEntry(index.toFloat(), item.volume.toFloat())
        }
        val barData = BarData(
            BarDataSet(barEntries, "Volume Data").apply {
                color = LocalAppColor.current.txtDisabled.toArgb()
            }
        ).apply {
            this.setDrawValues(false)
        }
        Timber.d("UPDATE $barData")
        return barData
    }


    @Composable
    fun List<LineChartDisplayModel>.mapToLineData(): LineData {
        val entries = ArrayList<Entry>()
        this.forEachIndexed { index, data ->
            entries.add(
                Entry(
                    index.toFloat(),
                    data.price.toFloat(),
                    data.data
                ),
            )
        }
        val lineDataSet = LineDataSet(entries, "Line Data").apply {
            setDrawValues(false)
            setDrawCircles(false)
            color = android.graphics.Color.parseColor("#4454A2")
            lineWidth = 1f
            setDrawHighlightIndicators(false)
            setDrawFilled(true)

            val gradient = GradientDrawable(
                GradientDrawable.Orientation.TOP_BOTTOM,
                intArrayOf(
                    android.graphics.Color.parseColor("#804454A2"),
                    android.graphics.Color.parseColor("#00FFFFFF")
                )
            )
            fillDrawable = gradient
        }
        val lineData = LineData(lineDataSet)

        return lineData
    }

    fun formatVolume(value: Float): String {
        val volume = value.toAmount(0).replace(",", "").toFloat()
        return when {
            volume >= 1_000_000_000 -> String.format(
                "%.2fB", (volume) / 1_000_000_000.0
            ) // Format billions
            volume >= 1_000_000 -> String.format("%.2fM", volume / 1_000_000.0) // Format millions
            volume >= 1_000 -> String.format("%.2fK", volume / 1_000.0) // Format thousands
            else -> volume.toString() // No formatting needed if less than 1,000
        }
    }

    private const val INDEX_TIMESTAMP = 0
    private const val INDEX_OPEN = 1
    private const val INDEX_HIGH = 2
    private const val INDEX_LOW = 3
    private const val INDEX_CLOSE = 4
    private const val INDEX_VOLUME = 5
}