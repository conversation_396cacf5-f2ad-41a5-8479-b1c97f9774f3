package com.siriustech.market.domain

import android.content.Context
import com.core.util.getDateFormatted
import com.core.util.toAmount
import com.siriustech.market.domain.display.AssetProfileDetailsDisplayData
import com.siriustech.market.domain.display.BasicInformationDisplayData
import com.siriustech.market.domain.display.CurrentSessionDisplayModel
import com.siriustech.market.domain.display.FinancialInfoDisplay
import com.siriustech.market.domain.display.FinancialItemInfoDisplay
import com.siriustech.market.domain.display.LatestNavStatisticsDisplayModel
import com.siriustech.market.domain.display.MarketQuoteDisplayData
import com.siriustech.market.screen.marketprofile.component.FundAllocationPieChartData
import com.siriustech.market.screen.marketprofile.component.FundTotalReturnBarChartData
import com.siriustech.market.screen.marketprofile.component.financial.FinancialInfoType
import com.siriustech.merit.apilayer.service.market.marketprofile.FinancialItemResponse
import com.siriustech.merit.apilayer.service.market.marketprofile.FinancialResponse
import com.siriustech.merit.apilayer.service.market.marketprofile.MarketInstrumentDetailsResponse
import com.siriustech.merit.apilayer.service.market.marketquote.MarketQuoteResponse
import com.siriustech.merit.app_common.component.container.LabelStarRatingProperties
import com.siriustech.merit.app_common.component.modalbts.ProductCategoryDetailsModalDisplayData
import com.siriustech.merit.app_common.data.display.MarketAssetDisplayData
import com.siriustech.merit.app_common.ext.DATE_FORMAT_14
import com.siriustech.merit.app_common.ext.DATE_FORMAT_2
import com.siriustech.merit.app_common.ext.DATE_FORMAT_3
import com.siriustech.merit.app_common.ext.displayPriceChange
import com.siriustech.merit.app_common.ext.formatMarketCap
import com.siriustech.merit.app_common.ext.formatVolume
import com.siriustech.merit.app_common.ext.toDateTimeWithZoneId
import com.siriustech.merit.app_common.typeenum.MarketAssetType
import com.siriustech.merit.app_common.typeenum.PortfolioAssetType
import java.util.Locale
import org.threeten.bp.ZoneId

/**
 * Created by Hein Htet
 */

object MarketProfileMapper {
    fun MarketAssetDisplayData.mapToMarketCategoryDetails(context : Context?): ProductCategoryDetailsModalDisplayData {
        return ProductCategoryDetailsModalDisplayData(
            symbol = this.symbol,
            logo = this.logo,
            exchange = this.exchange,
            riskLevel = this.riskLevel,
            marketPrice = this.marketPrice,
            unrealizedGl = this.unrealizedGl,
            unrealizedGlRate = this.unrealizedGlRate,
            currency = this.currency,
            marketValue = this.marketValue,
            name = this.name,
            costPrice = this.costPrice,
            country = this.region,
            asOfDate = "-"
        )
    }

    fun MarketQuoteResponse.mapToMarketQuoteDisplayData(assetDetails: MarketAssetDisplayData,context: Context?): MarketQuoteDisplayData {
        val asOfDate = this.lastMatchedTime?.toDateTimeWithZoneId(
            DATE_FORMAT_2,
            zoneId = ZoneId.systemDefault()
        )
        var data = MarketQuoteDisplayData(
            marketAssetType = MarketAssetType.fromParam(assetDetails.assetAllocationClass)
                ?: MarketAssetType.US_EQUITY
        )
        val currentSessionDisplayModel = CurrentSessionDisplayModel(
            volume = this.totalVolume?.toAmount(0).orEmpty(),
            openPrice = this.openPrice.orEmpty(),
            highestPrice = this.highPrice.orEmpty(),
            lowestPrice = this.lowPrice.orEmpty(),
            averagePrice = this.averagePrice.orEmpty(),
            averageBuyPrice = this.averageBuyPrice.orEmpty(),
            averageSellPrice = this.averageSellPrice.orEmpty(),
            ceilingPrice = this.ceilingPrice.orEmpty(),
            floorPrice = this.floorPrice.orEmpty(),
            currency = assetDetails.currency,
            asOfDate = asOfDate ?: "-"
        )
        data = data.copy(currentSessionDisplayModel = currentSessionDisplayModel)

        val navStatisticsDisplayModel = LatestNavStatisticsDisplayModel(
            latestNav = this.price?.toAmount(0).orEmpty(),
            oneDayChange = this.priceChange.orEmpty(),
            oneDayChangePercent = this.priceChangeRate.orEmpty(),
            asOfDate = "as of $asOfDate"
        )
        data = data.copy(navStatisticsDisplayModel = navStatisticsDisplayModel)
        return data
    }

    fun MarketInstrumentDetailsResponse.mapToBasicInformation(context: Context?): BasicInformationDisplayData {
        var data = BasicInformationDisplayData(
            productCategory = this.instrumentCategory.orEmpty(),
            allocationClass = PortfolioAssetType.fromParam(this.allocation.orEmpty())
                ?: PortfolioAssetType.EQUITY,
            currency = this.currency.orEmpty(),
            tags = this.tagList.orEmpty(),
            bondType = this.profileBond?.bondType.orEmpty(),
            issuer = this.profileBond?.issuer.orEmpty(),
            productType = this.instrumentClass.orEmpty()
        )
        val marketAssetType =
            MarketAssetType.fromParam(this.instrumentClass.orEmpty()) ?: MarketAssetType.US_EQUITY
        var micCode = ""
        var fundType = ""
        var fundManager = ""
        var inceptionDate = ""
        var fundSize = ""
        when (marketAssetType) {
            MarketAssetType.US_EQUITY -> {
                micCode = this.profileUSEquity?.mICCode.orEmpty()
            }

            MarketAssetType.HK_EQUITY -> {
                micCode = this.profileHKEquity?.mICCode.orEmpty()
            }

            MarketAssetType.MUTUAL_FUNDS -> {
                fundType = this.profileMutualFund?.fundType.orEmpty()
                fundManager = this.profileMutualFund?.fundManager.orEmpty()
                inceptionDate = this.profileMutualFund?.inceptionDate?.toDateTimeWithZoneId(
                    DATE_FORMAT_3,
                    zoneId = ZoneId.systemDefault()
                ).orEmpty()
                fundSize = this.profileMutualFund?.fundSize.orEmpty().formatMarketCap(context)
            }

            MarketAssetType.PRIVATE_EQUITY_FUNDS -> {
                fundType = this.profilePrivateEquityFund?.fundType.orEmpty()
                fundManager = this.profilePrivateEquityFund?.fundManager.orEmpty()
                fundSize = this.profilePrivateEquityFund?.fundSize.orEmpty().formatMarketCap(context)
            }

            else -> {
                micCode = ""
            }
        }
        data = data.copy(
            micCode = micCode,
            fundType = fundType,
            fundManager = fundManager,
            inceptionDate = inceptionDate,
            fundSize = fundSize,
        )
        return data
    }

    fun MarketInstrumentDetailsResponse.mapToAssetDetailsDisplayData(context: Context?): AssetProfileDetailsDisplayData {
        val marketAssetType =
            MarketAssetType.fromParam(this.instrumentClass.orEmpty()) ?: MarketAssetType.US_EQUITY

        var data = AssetProfileDetailsDisplayData(
            allocationClass = marketAssetType,
            description = this.description.orEmpty(),
            country = this.region.orEmpty()
        )
        when (marketAssetType) {
            MarketAssetType.US_EQUITY -> {
                data = data.copy(
                    marketCap = this.profileUSEquity?.investment?.marketCapitalization.orEmpty()
                        .formatMarketCap(context),
                    priceEarnings = this.profileUSEquity?.investment?.priceEarnings?.toAmount(4).orEmpty(),
                    priceSales = this.profileUSEquity?.investment?.priceSales?.toAmount(4).orEmpty(),
                    priceBook = this.profileUSEquity?.investment?.priceBook?.toAmount(4).orEmpty(),
                    dividendYield = this.profileUSEquity?.investment?.dividendYield.orEmpty(),
                    exDividendDate = this.profileUSEquity?.investment?.exDividendDate.orEmpty(),
                    sector = this.profileUSEquity?.investment?.sector.orEmpty(),
                    industry = this.profileUSEquity?.investment?.industry.orEmpty(),
                    currency = this.currency.orEmpty(),
                    productCategory = this.instrumentCategory.orEmpty(),
                    financialInfoDisplay = this.profileUSEquity?.financial.mapToFinancialDisplay()
                )
            }

            MarketAssetType.HK_EQUITY -> {
                data = data.copy(
                    marketCap = this.profileHKEquity?.investment?.marketCapitalization.orEmpty()
                        .formatMarketCap(context),
                    priceEarnings = this.profileHKEquity?.investment?.priceEarnings?.toAmount(4).orEmpty(),
                    priceSales = this.profileHKEquity?.investment?.priceSales?.toAmount(4).orEmpty(),
                    priceBook = this.profileHKEquity?.investment?.priceBook?.toAmount(4).orEmpty(),
                    dividendYield = this.profileHKEquity?.investment?.dividendYield
                        .orEmpty(),
                    exDividendDate = this.profileHKEquity?.investment?.exDividendDate.orEmpty(),
                    sector = this.profileHKEquity?.investment?.sector.orEmpty(),
                    industry = this.profileHKEquity?.investment?.industry.orEmpty(),
                    currency = this.currency.orEmpty(),
                    productCategory = this.instrumentCategory.orEmpty(),
                    country = this.country.orEmpty(),
                    financialInfoDisplay = this.profileHKEquity?.financial.mapToFinancialDisplay()
                )
            }

            MarketAssetType.MUTUAL_FUNDS -> {
                data = data.copy(
                    investmentObjective = this.profileMutualFund?.investment?.investmentObjective.orEmpty(),
                    investmentFocus = this.profileMutualFund?.investment?.investmentFocus.orEmpty(),
                    dividend = this.profileMutualFund?.investment?.dividend.orEmpty(),
                    minimumInvestment = (this.profileMutualFund?.investment?.minimumInvestment
                        ?: 0).toString(),
                    minimumAdditionalInvestment = (this.profileMutualFund?.investment?.minimumAdditional
                        ?: 0).toString(),
                    rating = LabelStarRatingProperties(
                        totalRating = this.profileMutualFund?.investment?.rating?.total ?: 5,
                        rating = this.profileMutualFund?.investment?.rating?.score ?: 0,
                        ratingDisplayValue = "${this.profileMutualFund?.investment?.rating?.score ?: 0}/${this.profileMutualFund?.investment?.rating?.total ?: 5}"
                    ),
                    performanceRating = LabelStarRatingProperties(
                        totalRating = this.profileMutualFund?.investment?.performanceRating?.total
                            ?: 5,
                        rating = this.profileMutualFund?.investment?.performanceRating?.score ?: 0,
                        ratingDisplayValue = "${this.profileMutualFund?.investment?.performanceRating?.score ?: 0}/${this.profileMutualFund?.investment?.rating?.total ?: 5}"
                    ),
                    returnRating = LabelStarRatingProperties(
                        totalRating = this.profileMutualFund?.investment?.returnRating?.total ?: 5,
                        rating = this.profileMutualFund?.investment?.returnRating?.score ?: 0,
                        ratingDisplayValue = "${this.profileMutualFund?.investment?.returnRating?.score ?: 0}/${this.profileMutualFund?.investment?.rating?.total ?: 5}"
                    ),
                    fundFeatSheet = this.profileMutualFund?.investment?.fundFactSheet.orEmpty(),
                    currency = this.currency.orEmpty(),
                    fundTotalReturnList = this.profileMutualFund?.performance.orEmpty().map {
                        FundTotalReturnBarChartData(
                            key = it.key.orEmpty(),
                            percentage = it.value ?: "0"
                        )
                    },
                    fundAssetAllocation = this.profileMutualFund?.assetAllocation.orEmpty().map {
                        FundAllocationPieChartData(
                            asset = it.key.orEmpty(),
                            percentage = it.value ?: "0"
                        )
                    }
                )
            }

            MarketAssetType.PRIVATE_EQUITY_FUNDS -> {
                data = data.copy(
                    investmentObjective = this.profilePrivateEquityFund?.investment?.investmentObjective.orEmpty(),
                    geographicFocus = this.profilePrivateEquityFund?.investment?.geographicFocus.orEmpty(),
                    investmentStrategy = this.profilePrivateEquityFund?.investment?.investmentStrategy.orEmpty(),
                    investmentFocus = this.profilePrivateEquityFund?.investment?.investmentFocus.orEmpty(),
                    dividend = this.profilePrivateEquityFund?.investment?.dividend.orEmpty(),
                    minimumInvestment = (this.profilePrivateEquityFund?.investment?.minimumInvestment
                        ?: 0).toString(),
                    minimumAdditionalInvestment = (this.profilePrivateEquityFund?.investment?.minimumAdditional
                        ?: 0).toString(),
                    rating = LabelStarRatingProperties(
                        totalRating = this.profilePrivateEquityFund?.investment?.rating?.total ?: 5,
                        rating = this.profilePrivateEquityFund?.investment?.rating?.score ?: 0,
                        ratingDisplayValue = "${this.profilePrivateEquityFund?.investment?.rating?.score ?: 0}/${this.profilePrivateEquityFund?.investment?.rating?.total ?: 5}"
                    ),
                    performanceRating = LabelStarRatingProperties(
                        totalRating = this.profilePrivateEquityFund?.investment?.performanceRating?.total
                            ?: 5,
                        rating = this.profilePrivateEquityFund?.investment?.performanceRating?.score
                            ?: 0,
                        ratingDisplayValue = "${this.profilePrivateEquityFund?.investment?.performanceRating?.score ?: 0}/${this.profilePrivateEquityFund?.investment?.rating?.total ?: 5}"
                    ),
                    returnRating = LabelStarRatingProperties(
                        totalRating = this.profilePrivateEquityFund?.investment?.returnRating?.total
                            ?: 5,
                        rating = this.profilePrivateEquityFund?.investment?.returnRating?.score
                            ?: 0,
                        ratingDisplayValue = "${this.profilePrivateEquityFund?.investment?.returnRating?.score ?: 0}/${this.profilePrivateEquityFund?.investment?.rating?.total ?: 5}"
                    ),
                    fundFeatSheet = this.profilePrivateEquityFund?.investment?.fundFactSheet.orEmpty(),
                    currency = this.currency.orEmpty(),
                    fundTotalReturnList = this.profilePrivateEquityFund?.performance.orEmpty().map {
                        FundTotalReturnBarChartData(
                            key = it.key.orEmpty(),
                            percentage = it.value ?: "0"
                        )
                    },
                    fundAssetAllocation = this.profilePrivateEquityFund?.assetAllocation.orEmpty()
                        .map {
                            FundAllocationPieChartData(
                                asset = it.key.orEmpty(),
                                percentage = it.value ?: "0"
                            )
                        },
                )
            }

            MarketAssetType.BONDS -> {
                val date = (this.profileBond?.investment?.maturityDate)
                data = data.copy(
                    couponRate = this.profileBond?.investment?.couponRate.orEmpty(),
                    couponYield = this.profileBond?.investment?.yield.orEmpty(),
                    creditRating = this.profileBond?.investment?.creditRating.orEmpty(),
                    maturityDate = if(date != null && date > 0) (date).toDateTimeWithZoneId(
                        DATE_FORMAT_3,
                        zoneId = ZoneId.systemDefault()
                    ).orEmpty() else "",
                    currency = this.currency.orEmpty()
                )
            }

            MarketAssetType.STRUCTURED_PRODUCTS -> {
                data = data.copy(
                    underlyingAssets = this.profileStructure?.investment?.underlyingAssets.orEmpty(),
                    principalProtection = this.profileStructure?.investment?.principalProtection.orEmpty(),
                    maturityDate = this.profileStructure?.investment?.maturityDate?.toDateTimeWithZoneId(
                        DATE_FORMAT_3,
                        zoneId = ZoneId.systemDefault()
                    ).orEmpty(),
                    couponRate = this.profileStructure?.investment?.couponRate.orEmpty(),
                    couponYield = this.profileStructure?.investment?.couponYield.orEmpty(),
                    participationRate = this.profileStructure?.investment?.participateRate.orEmpty(),
                    barrierLevel = this.profileStructure?.investment?.barrierLevel.orEmpty(),
                    redemptionTerms = this.profileStructure?.investment?.redemptionTerm.orEmpty(),
                    structureDescription = this.profileStructure?.investment?.structureDescription.orEmpty(),
                    strikePrice = this.profileStructure?.financial?.strikePrice.orEmpty(),
                    initialPrice = this.profileStructure?.financial?.initialPrice.orEmpty(),
                    returnCapInvestment = this.profileStructure?.financial?.returnCap.orEmpty(),
                    principalReturnMaturity = this.profileStructure?.financial?.principalReturnAtMaturity.orEmpty(),
                    issuePrice = this.profileStructure?.financial?.issuePrice.orEmpty(),
                    currency = this.currency.orEmpty()
                )
            }

            else -> {

            }
        }

        return data
    }
}


fun FinancialResponse?.mapToFinancialDisplay(): FinancialInfoDisplay {
    val items = ArrayList<FinancialItemInfoDisplay>()
    val date = this?.periodDate?.getDateFormatted(
        DATE_FORMAT_3,
        DATE_FORMAT_14,
        locale = Locale.getDefault(),
    ).orEmpty()


    fun FinancialItemResponse?.getFinancialData(): FinancialItemInfoDisplay? {
        return if (this != null && !this.list.isNullOrEmpty() && this.list.orEmpty()
                .none { it == "0.00" || it == "0" }
        ) {
            val barItems = ArrayList(this.list.orEmpty().map { it })
            FinancialItemInfoDisplay(
                price = (this.list.orEmpty().firstOrNull() ?: "0.0").formatVolume(),
                yy = this.yy?.displayPriceChange().orEmpty(),
                barData = barItems
            )
        } else {
            null
        }
    }

    this?.sales.getFinancialData()?.let {
        items.add(it.copy(name = FinancialInfoType.SALES))
    }

    this?.grossProfit.getFinancialData()?.let {
        items.add(it.copy(name = FinancialInfoType.GROSS_PROFIT))
    }

    this?.operatingIncome.getFinancialData()?.let {
        items.add(it.copy(name = FinancialInfoType.OPERATING_INCOME))
    }

    this?.netIncome.getFinancialData()?.let {
        items.add(it.copy(name = FinancialInfoType.NET_PROFIT))
    }

    this?.operatingMargin.getFinancialData()?.let {
        items.add(it.copy(name = FinancialInfoType.OPERATING_MARGIN))
    }

    this?.totalAssets.getFinancialData()?.let {
        items.add(it.copy(name = FinancialInfoType.ASSETS))
    }

    this?.earningsPerShare.getFinancialData()?.let {
        items.add(it.copy(name = FinancialInfoType.EARNING_PER_SHARE))
    }

    this?.totalLiabilities.getFinancialData()?.let {
        items.add(it.copy(name = FinancialInfoType.LIABILITIES))
    }

    return FinancialInfoDisplay(
        period = "Quarterly $date",
        items = items
    )
}

val mockData =
    "{\"instrumentClass\":\"MUTUAL_FUNDS\",\"instrumentCategory\":\"ULTRASHORT_BOND\",\"allocation\":\"50% Equity, 20% Bonds, 10% Cash\",\"symbol\":\"MPAA\",\"instrumentName\":\"Uriel\",\"riskLevel\":\"LOW\",\"instrumentStatus\":\"Normal\",\"exchange\":\"NASDAQ\",\"region\":\"US\",\"currency\":\"USD\",\"logo\":\"https://example.com/logo.png\",\"description\":\"A diversified mutual fund focusing on short-term bonds.\",\"tagList\":[\"bond\",\"mutual fund\",\"low risk\"],\"isHighlight\":false,\"profileHKEquity\":{\"mICCode\":\"XHKG\",\"investment\":{\"marketCapitalization\":\"200000000\",\"priceEarnings\":\"15.2\",\"priceSales\":\"1.5\",\"priceBook\":\"0.8\",\"dividendYield\":\"2.5\",\"exDividendDate\":\"2024-08-01\",\"sector\":\"Finance\",\"industry\":\"Asset Management\"},\"financial\":{\"period\":\"2023\",\"periodDate\":\"2023-12-31\",\"sales\":{\"yy\":\"10%\",\"list\":null},\"grossProfit\":{\"yy\":\"8%\",\"list\":null},\"operatingIncome\":{\"yy\":\"6%\",\"list\":null},\"netIncome\":{\"yy\":\"4%\",\"list\":null},\"operatingMargin\":{\"yy\":\"15%\",\"list\":null},\"totalAssets\":{\"yy\":\"500M\",\"list\":null},\"totalLiabilities\":{\"yy\":\"300M\",\"list\":null},\"earningsPerShare\":{\"yy\":\"0.75\",\"list\":null}}},\"profileUSEquity\":{\"mICCode\":\"XNYS\",\"investment\":{\"marketCapitalization\":\"500000000\",\"priceEarnings\":\"10.0\",\"priceSales\":\"2.0\",\"priceBook\":\"1.2\",\"dividendYield\":\"3.1\",\"exDividendDate\":\"2024-09-15\",\"sector\":\"Technology\",\"industry\":\"Software\"},\"financial\":{\"period\":\"2023\",\"periodDate\":\"2023-12-31\",\"sales\":{\"yy\":\"12%\",\"list\":null},\"grossProfit\":{\"yy\":\"10%\",\"list\":null},\"operatingIncome\":{\"yy\":\"7%\",\"list\":null},\"netIncome\":{\"yy\":\"5%\",\"list\":null},\"operatingMargin\":{\"yy\":\"18%\",\"list\":null},\"totalAssets\":{\"yy\":\"700M\",\"list\":null},\"totalLiabilities\":{\"yy\":\"350M\",\"list\":null},\"earningsPerShare\":{\"yy\":\"1.10\",\"list\":null}}},\"profileMutualFund\":{\"fundType\":\"Bond Fund\",\"fundManager\":\"Global Asset Mgmt\",\"fundSize\":\"200000000\",\"inceptionDate\":1733961600000,\"investment\":{\"investmentObjective\":\"To preserve capital with low volatility.\",\"investmentFocus\":\"Short-term bonds\",\"dividend\":\"2.1\",\"minimumInvestment\":\"1000\",\"minimumAditional\":\"100\",\"rating\":{\"total\":5,\"score\":4},\"performanceRating\":{\"total\":5,\"score\":3},\"returnRating\":{\"total\":5,\"score\":3},\"fundFactSheet\":\"https://example.com/fact-sheet.pdf\"},\"assetAllocation\":[{\"key\":\"equity\",\"value\":\"50\"},{\"key\":\"bonds\",\"value\":\"20\"},{\"key\":\"cash\",\"value\":\"10\"}],\"performance\":[{\"key\":\"3M\",\"value\":\"2.5\"},{\"key\":\"6M\",\"value\":\"4.8\"},{\"key\":\"1Y\",\"value\":\"7.0\"},{\"key\":\"3Y\",\"value\":\"15.0\"},{\"key\":\"5Y\",\"value\":\"25.0\"},{\"key\":\"ALL Time\",\"value\":\"35.0\"}]},\"profilePrivateEquityFund\":{\"fundType\":\"Private Equity\",\"fundManager\":\"Private Invest Co\",\"fundSize\":\"100000000\",\"investment\":{\"investmentFocus\":\"Growth-stage companies\",\"geographicFocus\":\"North America\",\"investmentStrategy\":\"Growth\",\"performanceRating\":{\"total\":5,\"score\":4},\"returnRating\":{\"total\":5,\"score\":4}},\"assetAllocation\":[{\"key\":\"equity\",\"value\":\"60\"},{\"key\":\"debt\",\"value\":\"30\"}],\"performance\":[{\"key\":\"1Y\",\"value\":\"10\"},{\"key\":\"3Y\",\"value\":\"15\"},{\"key\":\"5Y\",\"value\":\"25\"}]},\"profileBond\":{\"issuer\":\"GovCorp\",\"bondType\":\"Government Bond\",\"investment\":{\"couponRate\":\"1.75\",\"maturityDate\":1765488000000,\"creditRating\":\"AAA\",\"yield\":\"1.8\"}},\"profileStructure\":{\"issuer\":\"Financial Corp\",\"productType\":\"Structured Note\",\"investment\":{\"underlyingAssets\":\"US Large Cap Stocks\",\"structureDescription\":\"Principal protected structured product\",\"principalProtection\":\"Yes\",\"maturityDate\":1765488000000,\"couponRate\":\"5.0\",\"couponYield\":\"4.5\",\"participationRate\":\"80\",\"barrierLevel\":\"60\",\"redemptionTerm\":\"3 years\"},\"financial\":{\"strikePrice\":\"100\",\"initialPrice\":\"105\",\"issuePrice\":\"100\",\"returnCap\":\"20\",\"principalReturnAtMaturity\":\"100\"}}}"