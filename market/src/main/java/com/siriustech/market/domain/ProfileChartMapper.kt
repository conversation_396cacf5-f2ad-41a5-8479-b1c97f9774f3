package com.siriustech.market.domain

import android.graphics.Color
import android.graphics.drawable.GradientDrawable
import androidx.compose.runtime.Composable
import androidx.compose.runtime.ReadOnlyComposable
import com.github.mikephil.charting.data.Entry
import com.github.mikephil.charting.data.LineData
import com.github.mikephil.charting.data.LineDataSet
import com.siriustech.market.domain.display.SimpleChartDisplayModel
import com.siriustech.merit.app_common.data.display.MarketAssetDisplayData
import com.siriustech.merit.app_common.typeenum.SimpleChartType

/**
 * Created by Hein Htet
 */
object ProfileChartMapper {


    @Composable
    @ReadOnlyComposable
    fun SimpleChartDisplayModel.getProfileLineChartData(assetDisplayData: MarketAssetDisplayData = MarketAssetDisplayData()): LineData {
        val entries = ArrayList<Entry>()
        this.candles.forEachIndexed { index, candleChartDisplayModel ->
            entries.add(
                Entry(
                    index.toFloat(),
                    candleChartDisplayModel.close.toFloat(),
                    candleChartDisplayModel
                )
            )
        }

        val lineColor = when(simpleChartType){
            SimpleChartType.LOW -> "#FFC75959"
            SimpleChartType.NORMAL -> "#FF585858"
            else -> "#FF219D87"
        }

        val gradientColors = when(simpleChartType){
            SimpleChartType.LOW -> intArrayOf(
                Color.parseColor("#80C75959"),
                Color.parseColor("#00585858")
            )
            SimpleChartType.NORMAL -> intArrayOf(
                Color.parseColor("#80585858"),
                Color.parseColor("#00585858")
            )
            else -> intArrayOf(
                Color.parseColor("#802BD0B3"),
                Color.parseColor("#002BD0B3")
            )
        }

        return LineData(LineDataSet(entries, "Market Profile").apply {
            lineWidth = 1f
            setDrawCircles(false)
            setDrawValues(false)
            setDrawFilled(true)
            setDrawValues(false)
            setDrawCircles(false)
            color = Color.parseColor(lineColor)
            lineWidth = 1f
            setDrawHighlightIndicators(false)
            setDrawFilled(true)
            val gradient = GradientDrawable(
                GradientDrawable.Orientation.TOP_BOTTOM,
                gradientColors
            )
            fillDrawable = gradient
        })
    }
}
