package com.siriustech.market.domain.display

import com.siriustech.market.screen.marketprofile.component.financial.FinancialInfoType

data class FinancialInfoDisplay(
    val period: String = "",
    val items: List<FinancialItemInfoDisplay> = emptyList(),
)

data class FinancialItemInfoDisplay(
    val name: FinancialInfoType = FinancialInfoType.SALES,
    val price: String,
    val yy: String,
    val barData: List<String?>,
)