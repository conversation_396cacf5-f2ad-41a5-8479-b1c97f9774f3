package com.siriustech.market.domain

import com.siriustech.merit.app_common.data.display.MarketAssetDisplayData
import com.siriustech.merit.app_common.typeenum.RiskLevel
import kotlin.random.Random

/**
 * Created by <PERSON><PERSON><PERSON>
 */
object MarketAssetListMockData {

    fun getMockMarketAssetList(): List<MarketAssetDisplayData> {
        val mockMarketAssetDisplayData = List(50) { i ->
            MarketAssetDisplayData(
                id = i.toString(),
                symbol = "ABC $i",
                venue = "EXC",
                name = "a 0$i",
                riskLevel = RiskLevel.entries.random(),
                exchange = "HKEX",
                marketPrice = String.format("%.2f", Random.nextFloat() * 100 + 50), // Random price between 50 and 150
                currency = "HKD",
                unrealizedGl = String.format("%.2f", Random.nextFloat() * 200 - 100), // Random G/L between -100 and +100
                marketValue = String.format("%.2f", Random.nextFloat() * 1000 + 500), // Random value between 500 and 1500
                isFavorite = listOf(false, true).random(),
                unrealizedGlRate = String.format("%.2f", Random.nextFloat() * 0.2 - 0.1) // Random rate between -0.1 and +0.1
            )
        }
        return mockMarketAssetDisplayData
    }
}