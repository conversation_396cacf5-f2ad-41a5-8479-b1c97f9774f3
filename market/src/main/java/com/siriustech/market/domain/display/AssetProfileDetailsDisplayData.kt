package com.siriustech.market.domain.display

import com.siriustech.market.screen.marketprofile.component.FundAllocationPieChartData
import com.siriustech.market.screen.marketprofile.component.FundTotalReturnBarChartData
import com.siriustech.merit.apilayer.service.market.marketprofile.FinancialResponse
import com.siriustech.merit.app_common.component.container.LabelStarRatingProperties
import com.siriustech.merit.app_common.typeenum.MarketAssetType

/**
 * Created by <PERSON><PERSON>t
 */
data class AssetProfileDetailsDisplayData(
    val productCategory: String = "",
    val allocationClass: MarketAssetType = MarketAssetType.US_EQUITY,
    val marketCap: String = "",
    val currency: String = "",
    val priceEarnings: String = "",
    val priceSales: String = "",
    val priceBook: String = "",
    val dividendYield: String = "",
    val exDividendDate: String = "",
    val country: String = "",
    val sector: String = "",
    val industry: String = "",
    val description: String = "",
    val financialInfoDisplay: FinancialInfoDisplay = FinancialInfoDisplay(),

    // mutual fun
    val investmentObjective: String = "",
    val investmentFocus: String = "",
    val dividend: String = "",
    val minimumInvestment: String = "",
    val minimumAdditionalInvestment: String = "",
    val rating: LabelStarRatingProperties = LabelStarRatingProperties(),
    val performanceRating: LabelStarRatingProperties = LabelStarRatingProperties(),
    val returnRating: LabelStarRatingProperties = LabelStarRatingProperties(),
    val fundFeatSheet: String = "",
    val fundTotalReturnList: List<FundTotalReturnBarChartData> = arrayListOf(),
    val fundAssetAllocation: List<FundAllocationPieChartData> = arrayListOf(),


    // private fun
    val geographicFocus: String = "",
    val investmentStrategy: String = "",

    //bond
    val couponRate: String = "",
    val couponYield: String = "",
    val creditRating: String = "",
    val maturityDate: String = "", // bond

    // structured product
    val underlyingAssets: String = "",
    val principalProtection: String = "",
    val participationRate: String = "",
    val barrierLevel: String = "",
    val redemptionTerms: String = "",
    val bondCurrency: String = "",
    val structureDescription: String = "",
    val strikePrice: String = "",
    val initialPrice: String = "",
    val issuePrice: String = "",
    val returnCapInvestment: String = "",
    val principalReturnMaturity: String = "",

    )

