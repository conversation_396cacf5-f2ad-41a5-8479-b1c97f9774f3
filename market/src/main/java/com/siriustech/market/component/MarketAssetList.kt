package com.siriustech.market.component

import androidx.compose.animation.AnimatedVisibility
import androidx.compose.animation.core.tween
import androidx.compose.animation.fadeIn
import androidx.compose.animation.fadeOut
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.verticalScroll
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.tooling.preview.Preview
import com.siriustech.market.screen.assetListLanding.MarketAction
import com.siriustech.merit.app_common.component.container.PaddingBottom
import com.siriustech.merit.app_common.data.display.MarketAssetDisplayData
import com.siriustech.merit.app_common.ext.colorTxtInactive
import com.siriustech.merit.app_common.ext.noRippleClickable
import com.siriustech.merit.app_common.theme.LocalAppColor
import com.siriustech.merit.app_common.theme.LocalDimens
import com.siriustech.merit.app_common.theme.LocalTypography

/**
 * Created by Hein Htet
 */
@Composable
fun MarketAssetList(
    displayMarketAssetListItems: List<MarketAssetDisplayData>,
    onAction: (MarketAction) -> Unit = {},
    emptyText: String = "",
) {
    Box(modifier = Modifier.fillMaxSize()) {
        Column(
            modifier = Modifier
                .verticalScroll(rememberScrollState())
                .padding(horizontal = LocalDimens.current.dimen12),
        ) {
            repeat(displayMarketAssetListItems.size) {
                MarketAssetItem(
                    onItemClick = { onAction.invoke(MarketAction.OnMarketAssetClick(it)) },
                    toggleFavorite = {
                        onAction.invoke(
                            MarketAction.OnToggleFavoriteMarketAsset(
                                it,
                                !it.isFavorite
                            )
                        )
                    },
                    data = displayMarketAssetListItems[it]
                )
            }
            PaddingBottom(value = LocalDimens.current.dimen6)
        }
        EmptyMarketAssetList(displayMarketAssetListItems.isEmpty(), emptyText, onNavigateToMarketTab = {
//            onAction.invoke()
        })
    }
}

@Composable
fun EmptyMarketAssetList(visible: Boolean, emptyText: String,onNavigateToMarketTab : () -> Unit = {}) {
    AnimatedVisibility(
        visible = visible, enter = fadeIn(initialAlpha = 0.4f),
        exit = fadeOut(animationSpec = tween(durationMillis = 250))
    ) {
        Box(
            modifier = Modifier
                .fillMaxSize()
                .background(LocalAppColor.current.bgDefault)
                .noRippleClickable {  }
        ) {
            Text(
                text = emptyText,
                modifier = Modifier
                    .padding(LocalDimens.current.dimen24)
                    .fillMaxWidth(),
                textAlign = TextAlign.Center,
                style = LocalTypography.current.text14.light.colorTxtInactive()
            )
        }
    }
}

@Preview(showBackground = true, showSystemUi = true)
@Composable
fun PreviewMarketAssetList() {
//    MarketAssetList(MarketAssetListMockData.getMockMarketAssetList())
    MarketAssetList(displayMarketAssetListItems = emptyList())
}