package com.siriustech.market.screen.marketprofile.component

import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.ExperimentalLayoutApi
import androidx.compose.foundation.layout.FlowRow
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.tooling.preview.Preview
import com.siriustech.market.domain.display.BasicInformationDisplayData
import com.siriustech.merit.app_common.R
import com.siriustech.merit.app_common.component.common.FilterTagContent
import com.siriustech.merit.app_common.component.common.FilterTagDisplayModel
import com.siriustech.merit.app_common.component.container.LabelAmountCurrency
import com.siriustech.merit.app_common.component.container.LabelValue
import com.siriustech.merit.app_common.ext.colorTxtParagraph
import com.siriustech.merit.app_common.ext.colorTxtTitle
import com.siriustech.merit.app_common.theme.LocalDimens
import com.siriustech.merit.app_common.theme.LocalTypography
import com.siriustech.merit.app_common.typeenum.MarketAssetType

/**
 * Created by Hein Htet
 */
@Composable
fun BasicInformation(
    modifier: Modifier = Modifier,
    data: BasicInformationDisplayData = BasicInformationDisplayData(),
) {
    val productType = MarketAssetType.fromParam(data.productType)
    Column(
        modifier = Modifier
            .fillMaxWidth()
            .padding(horizontal = LocalDimens.current.dimen12)
            .then(modifier)
    ) {
        when (productType) {
            MarketAssetType.HK_EQUITY, MarketAssetType.US_EQUITY -> USHKBasicInformation(data)
            MarketAssetType.MUTUAL_FUNDS -> MutualFundBasicInformation(data)
            MarketAssetType.BONDS -> BondBasicInformation(data)
            MarketAssetType.PRIVATE_EQUITY_FUNDS -> PrivateEquityFundBasicInformation(data)
            else -> USHKBasicInformation(data)
        }
    }
}

@Composable
fun USHKBasicInformation(data: BasicInformationDisplayData = BasicInformationDisplayData()) {
    val context = LocalContext.current
    val valueTextStyle = LocalTypography.current.text14.regular.colorTxtTitle()
    val itemModifier = Modifier.padding(top = LocalDimens.current.dimen8)
    Text(
        text = stringResource(id = R.string.key0039),
        style = LocalTypography.current.text14.medium.colorTxtTitle()
    )
    LabelValue(
        label = stringResource(id = R.string.key0509),
        value = data.micCode,
        modifier = itemModifier,
        valueTextStyle = valueTextStyle
    )
    LabelValue(
        label = stringResource(id = R.string.key0510),
        value = data.productCategory,
        modifier = itemModifier,
        valueTextStyle = valueTextStyle
    )
    LabelValue(
        label = stringResource(id = R.string.key0511),
        value = data.allocationClass.displayName,
        modifier = itemModifier,
        valueTextStyle = valueTextStyle
    )
    LabelValue(
        label = stringResource(id = R.string.key0512),
        value = MarketAssetType.fromParam(data.productType)?.displayName(context).orEmpty(),
        modifier = itemModifier,
        valueTextStyle = valueTextStyle
    )
    LabelValue(
        label = stringResource(id = R.string.key0513),
        value = data.currency,
        modifier = itemModifier,
        valueTextStyle = valueTextStyle
    )
    TagList(tagList = data.tags)
}


@Composable
fun MutualFundBasicInformation(data: BasicInformationDisplayData = BasicInformationDisplayData()) {
    val context = LocalContext.current
    val valueTextStyle = LocalTypography.current.text14.regular.colorTxtTitle()
    val itemModifier = Modifier.padding(top = LocalDimens.current.dimen8)
    Text(
        text = stringResource(id = R.string.key0039),
        style = LocalTypography.current.text14.medium.colorTxtTitle()
    )
    LabelValue(
        label = stringResource(id = R.string.key0515),
        value = data.fundType,
        modifier = itemModifier,
        valueTextStyle = valueTextStyle
    )
    LabelValue(
        label = stringResource(id = R.string.key0516),
        value = data.fundManager,
        modifier = itemModifier,
        valueTextStyle = valueTextStyle
    )
    LabelValue(
        label = stringResource(id = R.string.key0510),
        value = data.productCategory,
        modifier = itemModifier,
        valueTextStyle = valueTextStyle
    )
    LabelValue(
        label = stringResource(id = R.string.key0511),
        value = data.allocationClass.displayName,
        modifier = itemModifier,
        valueTextStyle = valueTextStyle
    )
    LabelValue(
        label = stringResource(id = R.string.key0512),
        value = MarketAssetType.fromParam(data.productType)?.displayName(context).orEmpty(),
        modifier = itemModifier,
        valueTextStyle = valueTextStyle
    )
    LabelAmountCurrency(
        label = stringResource(id = R.string.key0518),
        amount = data.fundSize,
        modifier = itemModifier,
        amountTextStyle = valueTextStyle,
        currency = data.currency
    )
    LabelValue(
        label = stringResource(id = R.string.key0517),
        value = data.inceptionDate,
        modifier = itemModifier,
        valueTextStyle = valueTextStyle
    )
    TagList(tagList = data.tags)
}

@Composable
fun PrivateEquityFundBasicInformation(data: BasicInformationDisplayData = BasicInformationDisplayData()) {
    val context = LocalContext.current
    val valueTextStyle = LocalTypography.current.text14.regular.colorTxtTitle()
    val itemModifier = Modifier.padding(top = LocalDimens.current.dimen8)
    Text(
        text = stringResource(id = R.string.key0039),
        style = LocalTypography.current.text14.medium.colorTxtTitle()
    )
    LabelValue(
        label = stringResource(id = R.string.key0515),
        value = data.fundType,
        modifier = itemModifier,
        valueTextStyle = valueTextStyle
    )
    LabelValue(
        label = stringResource(id = R.string.key0516),
        value = data.fundManager,
        modifier = itemModifier,
        valueTextStyle = valueTextStyle
    )
    LabelValue(
        label = stringResource(id = R.string.key0510),
        value = data.productCategory,
        modifier = itemModifier,
        valueTextStyle = valueTextStyle
    )
    LabelValue(
        label = stringResource(id = R.string.key0511),
        value = data.allocationClass.displayName,
        modifier = itemModifier,
        valueTextStyle = valueTextStyle
    )
    LabelValue(
        label = stringResource(id = R.string.key0512),
        value = MarketAssetType.fromParam(data.productType)?.displayName(context).orEmpty(),
        modifier = itemModifier,
        valueTextStyle = valueTextStyle
    )
    LabelAmountCurrency(
        label = stringResource(id = R.string.key0518),
        amount = data.fundSize,
        modifier = itemModifier,
        amountTextStyle = valueTextStyle,
        currency = data.currency,

    )
    TagList(tagList = data.tags)
}


@Composable
fun BondBasicInformation(data: BasicInformationDisplayData = BasicInformationDisplayData()) {
    val context = LocalContext.current
    val valueTextStyle = LocalTypography.current.text14.regular.colorTxtTitle()
    val itemModifier = Modifier.padding(top = LocalDimens.current.dimen8)
    Text(
        text = stringResource(id = R.string.key0039),
        style = LocalTypography.current.text14.medium.colorTxtTitle()
    )
    LabelValue(
        label = stringResource(id = R.string.key0519),
        value = data.issuer,
        modifier = itemModifier,
        valueTextStyle = valueTextStyle
    )
    LabelValue(
        label = stringResource(id = R.string.key0520),
        value = data.bondType,
        modifier = itemModifier,
        valueTextStyle = valueTextStyle
    )
    LabelValue(
        label = stringResource(id = R.string.key0510),
        value = data.productCategory,
        modifier = itemModifier,
        valueTextStyle = valueTextStyle
    )
    LabelValue(
        label = stringResource(id = R.string.key0511),
        value = data.allocationClass.displayName,
        modifier = itemModifier,
        valueTextStyle = valueTextStyle
    )
    LabelValue(
        label = stringResource(id = R.string.key0512),
        value = MarketAssetType.fromParam(data.productType)?.displayName(context).orEmpty(),
        modifier = itemModifier,
        valueTextStyle = valueTextStyle
    )

    TagList(tagList = data.tags)
}


@OptIn(ExperimentalLayoutApi::class)
@Composable
fun TagList(tagList: List<String>) {
    if (tagList.isNotEmpty())
        Row(
            modifier = Modifier
                .padding(top = LocalDimens.current.dimen8)
                .fillMaxWidth(),
            horizontalArrangement = Arrangement.SpaceBetween
        ) {
            Text(
                text = stringResource(id = R.string.key0514),
                style = LocalTypography.current.text14.light.colorTxtParagraph()
            )
            FlowRow(
                modifier = Modifier,
                horizontalArrangement = Arrangement.spacedBy(LocalDimens.current.dimen4),
            ) {
                repeat(tagList.size) {
                    FilterTagContent(
                        item = FilterTagDisplayModel(
                            id = it.toString(),
                            text = tagList[it],
                            showClearButton = false
                        )
                    )
                }
            }
        }
}

@Preview(showBackground = true, showSystemUi = true)
@Composable
fun PreviewBasicInformation() {
    BasicInformation()
}