package com.siriustech.market.screen.refineasset

import com.siriustech.merit.app_common.component.common.FilterTagDisplayModel
import com.siriustech.merit.app_common.theme.AppAction
import com.siriustech.merit.app_common.typeenum.RefineAssetSortingType
import com.siriustech.merit.app_common.typeenum.RefineFilterProductType

/**
 * Created by <PERSON><PERSON>t
 */
sealed interface RefineAssetListAction : AppAction {
    data class OnSortTypeChanged(val type: RefineAssetSortingType) : RefineAssetListAction
    data class OnFilterProductTypeChanged(val type: RefineFilterProductType) : RefineAssetListAction
    data object OnGetProductCategory : RefineAssetListAction
    data class OnToggleProductCategoryModal(val show : Boolean) : RefineAssetListAction
    data class OnDeleteProductCategory(val filterTagDisplayModel: FilterTagDisplayModel) : RefineAssetListAction
    data class OnApplyFilter(val arguments: RefineAssetListArguments) : RefineAssetListAction
    data object OnPrepareFilterApply : RefineAssetListAction
}