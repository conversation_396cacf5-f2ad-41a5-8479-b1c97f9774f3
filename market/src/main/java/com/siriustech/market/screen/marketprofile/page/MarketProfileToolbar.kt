package com.siriustech.market.screen.marketprofile.page

import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.runtime.Composable
import androidx.compose.runtime.collectAsState
import androidx.compose.ui.Modifier
import com.siriustech.market.screen.assetListLanding.MarketAction
import com.siriustech.market.screen.marketprofile.MarketProfileAction
import com.siriustech.market.screen.marketprofile.MarketProfileViewModel
import com.siriustech.merit.app_common.component.header.DashboardToolbar
import com.siriustech.merit.app_common.component.header.DashboardToolbarProperties
import com.siriustech.merit.app_common.component.loading.HorizontalProgressBar
import com.siriustech.merit.app_common.component.marquee.StockExchangeMarquee
import com.siriustech.merit.app_common.theme.LocalDimens

@Composable
fun MarketProfileToolbar(viewModel: MarketProfileViewModel) {
    val showLoading = viewModel.outputs.showLoading.collectAsState()
    val exchangeMarqueeData = viewModel.outputs.displayStockExchangeItems
    DashboardToolbar(
        onSettingsClicked = { viewModel.onTriggerActions(MarketProfileAction.OnNavigateToSettings) },
        onBackPress = { viewModel.onTriggerActions(MarketProfileAction.OnNavigateBack) },
        properties = DashboardToolbarProperties(showBackArrow = true),
        onSearchInstrument = {
            viewModel.onTriggerActions(MarketProfileAction.OnNavigateInstrumentSearch)
        }, onNotificationClicked = {
            viewModel.onTriggerActions(MarketProfileAction.OnNavigateNotification)
        })
    Box(modifier = Modifier.fillMaxWidth()) {
        HorizontalProgressBar(visible = showLoading.value)
        StockExchangeMarquee(modifier = Modifier.padding(top = LocalDimens.current.dimen12),
            items = exchangeMarqueeData,
            onItemClick = {
                viewModel.onTriggerActions(MarketAction.OnToggleCurrencyPairModal(show = true))
            })
    }
}