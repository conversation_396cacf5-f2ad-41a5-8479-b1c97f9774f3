package com.siriustech.market.screen.marketprofile.component

import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.tooling.preview.Preview
import com.core.util.toAmount
import com.siriustech.market.domain.display.LatestNavStatisticsDisplayModel
import com.siriustech.merit.app_common.component.container.LabelAmountCurrency
import com.siriustech.merit.app_common.component.container.LabelAmountPercentage
import com.siriustech.merit.app_common.component.container.LabelValue
import com.siriustech.merit.app_common.ext.colorTxtInactive
import com.siriustech.merit.app_common.ext.colorTxtTitle
import com.siriustech.merit.app_common.theme.LocalDimens
import com.siriustech.merit.app_common.theme.LocalTypography
import com.siriustech.merit.app_common.R as AppCommonR
/**
 * Created by <PERSON>in Htet
 */

@Composable
fun LatestNavStatistics(
    modifier: Modifier = Modifier,
    data: LatestNavStatisticsDisplayModel = LatestNavStatisticsDisplayModel(),
) {
    val amountTextStyle = LocalTypography.current.text14.regular.colorTxtTitle()

    Column(
        modifier = Modifier
            .fillMaxWidth()
            .then(modifier)
    ) {
        val currency = data.currency
        val labelModifier = Modifier.padding(vertical = LocalDimens.current.dimen4)
        Row(modifier = Modifier.fillMaxWidth(), horizontalArrangement = Arrangement.SpaceBetween) {
            Text(
                text = stringResource(id = AppCommonR.string.key0504),
                style = LocalTypography.current.text14.medium.colorTxtTitle()
            )
            Text(text = data.asOfDate, style = LocalTypography.current.text14.light.colorTxtInactive())
        }
        LabelValue(label = stringResource(id = AppCommonR.string.key0505), value = data.latestNav.toAmount(0).orEmpty(), modifier = Modifier.padding(top = LocalDimens.current.dimen8))
        LabelAmountCurrency(label = stringResource(id = AppCommonR.string.key0496), amount = data.oneDayChange.toAmount(4), currency = currency, modifier = labelModifier, enableTextColoring = true, amountTextStyle = amountTextStyle)
        LabelAmountPercentage(label = stringResource(id = AppCommonR.string.key0496), amount = data.oneDayChange,  modifier = labelModifier, amountTextStyle = amountTextStyle)
    }
}

@Preview(showBackground = true, showSystemUi = true)
@Composable
fun PreviewLatestNavStatistics() {
    LatestNavStatistics()
}