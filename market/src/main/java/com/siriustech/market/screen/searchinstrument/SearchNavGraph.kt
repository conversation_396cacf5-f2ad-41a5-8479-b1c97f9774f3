package com.siriustech.market.screen.searchinstrument
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.navigation.NavHostController
import androidx.navigation.compose.NavHost
import androidx.navigation.toRoute
import com.siriustech.market.screen.marketprofile.MarketProfileScreen
import com.siriustech.merit.app_common.ext.composableWithTransaction
import com.siriustech.merit.app_common.navigation.MarketProfile
import com.siriustech.merit.app_common.navigation.argument.market.MarketProfileArgument
import kotlinx.serialization.Serializable

/**
 * Created by <PERSON><PERSON>tet
 */
@Composable
fun SearchNavGraph(modifier: Modifier = Modifier,navController: NavHostController) {
    NavHost(
        modifier = modifier,
        navController = navController,
        startDestination = SearchNavRouteName.SearchLanding
    ) {
        composableWithTransaction<SearchNavRouteName.SearchLanding> {
            SearchInstrumentScreen(navController = navController)
        }
        composableWithTransaction<MarketProfile>(
            typeMap = MarketProfileArgument.typeMap
        ){backStackEntry->
            val arguments = backStackEntry.toRoute<MarketProfile>().args
            MarketProfileScreen(navController = navController, data = arguments.data)
        }
        composableWithTransaction<MarketProfile>(
            typeMap = MarketProfileArgument.typeMap
        ){backStackEntry->
            val arguments = backStackEntry.toRoute<MarketProfile>().args
            MarketProfileScreen(navController = navController, data = arguments.data)
        }
    }
}

object SearchNavRouteName {
    @Serializable
    data object SearchLanding
}


