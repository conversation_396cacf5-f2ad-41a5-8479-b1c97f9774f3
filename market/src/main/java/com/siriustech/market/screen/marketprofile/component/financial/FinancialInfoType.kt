package com.siriustech.market.screen.marketprofile.component.financial

import androidx.compose.runtime.Composable
import androidx.compose.runtime.ReadOnlyComposable
import androidx.compose.ui.res.stringResource
import com.siriustech.merit.app_common.R as AppCommonR

/**
 * Created by Hein Htet
 */

enum class FinancialInfoType(val value: String) {
    SALES("SALES"),
    GROSS_PROFIT("GROSS_PROFIT"),
    OPERATING_INCOME("OPERATING_INCOME"),
    NET_PROFIT("NET_PROFIT"),
    OPERATING_MARGIN("OPERATING_MARGIN"),
    ASSETS("ASSETS"),
    EARNING_PER_SHARE("EARNING_PER_SHARE"),
    LIABILITIES("LIABILITIES");


    @Composable
    @ReadOnlyComposable
    fun displayName(): String {
        return when (this) {
            SALES -> stringResource(id = AppCommonR.string.key0564)
            GROSS_PROFIT -> stringResource(id = AppCommonR.string.key0565)
            OPERATING_INCOME -> stringResource(id = AppCommonR.string.key0566)
            NET_PROFIT -> stringResource(id = AppCommonR.string.key0567)
            OPERATING_MARGIN -> stringResource(id = AppCommonR.string.key0568)
            ASSETS -> stringResource(id = AppCommonR.string.key0569)
            EARNING_PER_SHARE -> stringResource(id = AppCommonR.string.key0570)
            LIABILITIES -> stringResource(id = AppCommonR.string.key0571)
        }
    }

}