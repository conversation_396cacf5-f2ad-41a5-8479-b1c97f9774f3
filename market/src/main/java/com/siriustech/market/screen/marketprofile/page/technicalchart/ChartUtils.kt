package com.siriustech.market.screen.marketprofile.page.technicalchart

import android.view.MotionEvent
import com.github.mikephil.charting.listener.ChartTouchListener
import com.github.mikephil.charting.listener.OnChartGestureListener

object ChartUtils {
    fun setupSynchronizedListener(
        chart1: com.github.mikephil.charting.charts.Chart<*>,
        chart2: com.github.mikephil.charting.charts.Chart<*>
    ) {
        val gestureListener1 = object : OnChartGestureListener {
            override fun onChartGestureStart(
                me: MotionEvent?,
                lastPerformedGesture: ChartTouchListener.ChartGesture?
            ) {}

            override fun onChartGestureEnd(
                me: MotionEvent?,
                lastPerformedGesture: ChartTouchListener.ChartGesture?
            ) {}

            override fun onChartLongPressed(me: MotionEvent?) {}
            override fun onChartDoubleTapped(me: MotionEvent?) {}
            override fun onChartSingleTapped(me: MotionEvent?) {
                me?.let {
                    val highlight = chart1.getHighlightByTouchPoint(me.x, me.y)
                    if (highlight != null) {
                        chart1.highlightValue(highlight)
                    }
                }
            }
            override fun onChartFling(
                me1: MotionEvent?,
                me2: MotionEvent?,
                velocityX: Float,
                velocityY: Float
            ) {}

            override fun onChartScale(me: MotionEvent?, scaleX: Float, scaleY: Float) {
                chart2.viewPortHandler.refresh(chart1.viewPortHandler.matrixTouch, chart2, true)
            }

            override fun onChartTranslate(me: MotionEvent?, dX: Float, dY: Float) {
                chart1.highlightValue(null)
                chart2.viewPortHandler.refresh(chart1.viewPortHandler.matrixTouch, chart2, true)
            }
        }

        val gestureListener2 = object : OnChartGestureListener {
            override fun onChartGestureStart(
                me: MotionEvent?,
                lastPerformedGesture: ChartTouchListener.ChartGesture?
            ) {}

            override fun onChartGestureEnd(
                me: MotionEvent?,
                lastPerformedGesture: ChartTouchListener.ChartGesture?
            ) {}

            override fun onChartLongPressed(me: MotionEvent?) {}
            override fun onChartDoubleTapped(me: MotionEvent?) {}
            override fun onChartSingleTapped(me: MotionEvent?) {
            }
            override fun onChartFling(
                me1: MotionEvent?,
                me2: MotionEvent?,
                velocityX: Float,
                velocityY: Float
            ) {}

            override fun onChartScale(me: MotionEvent?, scaleX: Float, scaleY: Float) {
                chart1.viewPortHandler.refresh(chart2.viewPortHandler.matrixTouch, chart1, true)
            }

            override fun onChartTranslate(me: MotionEvent?, dX: Float, dY: Float) {
                chart1.viewPortHandler.refresh(chart2.viewPortHandler.matrixTouch, chart1, true)
            }
        }

        // Set the gesture listener on both charts to synchronize both ways
        chart1.onChartGestureListener = gestureListener1
        chart2.onChartGestureListener = gestureListener2
    }

  }