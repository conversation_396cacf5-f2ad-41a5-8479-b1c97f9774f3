package com.siriustech.market.screen.searchinstrument

import android.app.Activity.RESULT_OK
import android.content.Intent
import androidx.compose.animation.AnimatedVisibility
import androidx.compose.animation.core.tween
import androidx.compose.animation.fadeIn
import androidx.compose.animation.fadeOut
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.padding
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.snapshots.SnapshotStateList
import androidx.compose.ui.Modifier
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.stringResource
import androidx.fragment.app.FragmentActivity
import androidx.hilt.navigation.compose.hiltViewModel
import androidx.navigation.NavController
import com.siriustech.core_ui_compose.base.SingleEventEffect
import com.siriustech.market.component.MarketAssetList
import com.siriustech.market.navigation.MarketNavigationEntryPoint
import com.siriustech.market.screen.assetListLanding.MarketAction
import com.siriustech.market.screen.searchinstrument.SearchMarketInstrumentActivity.Companion.EXTRA_NEED_RESULT_DATA
import com.siriustech.merit.app_common.Constants
import com.siriustech.merit.app_common.component.common.SearchInstrumentInput
import com.siriustech.merit.app_common.component.container.PaddingTop
import com.siriustech.merit.app_common.component.loading.HorizontalProgressBar
import com.siriustech.merit.app_common.data.display.MarketAssetDisplayData
import com.siriustech.merit.app_common.ext.colorTxtTitle
import com.siriustech.merit.app_common.navigation.argument.market.MarketProfileArgument
import com.siriustech.merit.app_common.theme.AppScreen
import com.siriustech.merit.app_common.theme.LocalDimens
import com.siriustech.merit.app_common.theme.LocalTypography
import dagger.hilt.android.EntryPointAccessors
import com.siriustech.merit.app_common.R as AppCommonR

/**
 * Created by Hein Htet
 */
@Composable
fun SearchInstrumentScreen(
    navController: NavController,
    viewModel: SearchInstrumentViewModel = hiltViewModel(),
) {
    val activity = LocalContext.current as FragmentActivity

    val displayMarketAssetListItems = viewModel.outputs.displayMarketAssetListItems
    val displayRecentlyViewedItems = viewModel.outputs.displayRecentlyViewedListItems
    val showLoading = viewModel.outputs.showLoading.collectAsState()
    val isVisibleRecentlyViewed = viewModel.outputs.showRecentlyViewed.collectAsState()
    var isNeedDataBack = remember {
        mutableStateOf(false)
    }
    LaunchedEffect(Unit) {
        isNeedDataBack.value =
            activity.intent.getBooleanExtra(EXTRA_NEED_RESULT_DATA, false)
        viewModel.inputs.onGetRecentlyViewed()
    }


    val navigation = remember {
        EntryPointAccessors.fromApplication(activity, MarketNavigationEntryPoint::class.java)
            .marketNavigation()
    }


    SingleEventEffect(sideEffectFlow = viewModel.appAction) {
        when (it) {
            is MarketAction.OnMarketAssetClick -> {
                // needed market asset list
                if (isNeedDataBack.value) {
                    activity.setResult(RESULT_OK, Intent().apply {
                        putExtra(Constants.RESULT_EXTRA_ASSET_DATA, it.data)
                    })
                    activity.finish()
                } else {
                    navigation.onNavigateToMarketProfile(
                        navController,
                        MarketProfileArgument(it.data)
                    )
                }
            }
        }
    }
    AppScreen(
        vm = viewModel,
        ignorePaddingValue = true
    ) {
        Column {
            SearchInstrumentInput(
                onBackPress = {
                    navController.popBackStack()
                },
                onTextChanged = { value ->
                    viewModel.inputs.onSearchInputChanged(value)
                })
            HorizontalProgressBar(visible = showLoading.value)
            PaddingTop(value = LocalDimens.current.dimen12)
            if (!isVisibleRecentlyViewed.value) {
                MarketAssetList(
                    displayMarketAssetListItems = displayMarketAssetListItems,
                    onAction = {
                        viewModel.onTriggerActions(it)
                    },
                    emptyText = stringResource(id = AppCommonR.string.key0458)
                )
            }
            RecentlyViewedList(
                onAction = { viewModel.onTriggerActions(it) },
                isVisibleRecentlyViewed.value,
                displayRecentlyViewedItems,
            )
        }
    }
}

@Composable
fun RecentlyViewedList(
    onAction: (MarketAction) -> Unit,
    visible: Boolean,
    items: SnapshotStateList<MarketAssetDisplayData>,
) {
    AnimatedVisibility(
        visible = visible,
        enter = fadeIn(initialAlpha = 0.4f),
        exit = fadeOut(animationSpec = tween(durationMillis = 250)),
    ) {
        Column(modifier = Modifier.padding(horizontal = LocalDimens.current.dimen12)) {
            Text(
                text = stringResource(id = AppCommonR.string.key0456),
                style = LocalTypography.current.text14.medium.colorTxtTitle()
            )
            MarketAssetList(
                onAction = onAction,
                displayMarketAssetListItems = items,
                emptyText = stringResource(id = AppCommonR.string.key0455)
            )
        }
    }
}
