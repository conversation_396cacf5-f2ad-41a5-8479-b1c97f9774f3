package com.siriustech.market.screen.marketprofile.component

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.remember
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.toArgb
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.unit.dp
import com.core.util.toAmount
import com.github.mikephil.charting.charts.PieChart
import com.github.mikephil.charting.data.BarEntry
import com.github.mikephil.charting.data.PieData
import com.github.mikephil.charting.data.PieDataSet
import com.github.mikephil.charting.data.PieEntry
import com.siriustech.merit.app_common.R
import com.siriustech.merit.app_common.component.chart.AppPieChart
import com.siriustech.merit.app_common.component.container.LabelAmountPercentage
import com.siriustech.merit.app_common.component.container.PaddingStart
import com.siriustech.merit.app_common.ext.capitalizeWords
import com.siriustech.merit.app_common.ext.colorTxtParagraph
import com.siriustech.merit.app_common.ext.colorTxtTitle
import com.siriustech.merit.app_common.theme.LocalAppColor
import com.siriustech.merit.app_common.theme.LocalDimens
import com.siriustech.merit.app_common.theme.LocalTypography
import timber.log.Timber

/**
 * Created by Hein Htet
 */
@Composable
fun FundAllocationPieChart(items: List<FundAllocationPieChartData> = emptyList()) {

    val context = LocalContext.current
    val colors = listOf(
        LocalAppColor.current.chartEmeraldGreen,
        LocalAppColor.current.chartPurple,
        LocalAppColor.current.chartOrange,
        LocalAppColor.current.chartBlue,
        LocalAppColor.current.chartRed,
        LocalAppColor.current.chartGreen,
    )

    val pieChart = remember {
        PieChart(context)
    }

    val entries = items.mapIndexed { index, item ->
        PieEntry(item.percentage.toFloatOrNull() ?: 0f)
    }
    val dataSet = PieDataSet(
        entries, "PieData"
    ).apply {
        this.colors = colors.map { it.toArgb() }
        this.setAutomaticallyDisableSliceSpacing(false)
        this.setDrawValues(false)
    }

    val pieData = PieData(dataSet)

    Column {
        Text(
            text = stringResource(id = R.string.key0541),
            style = LocalTypography.current.text14.medium.colorTxtTitle()
        )
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .padding(vertical = LocalDimens.current.dimen12),
            verticalAlignment = Alignment.CenterVertically
        ) {
            AppPieChart(
                pieChart = pieChart,
                pieData = pieData,
                modifier = Modifier
                    .size(150.dp),
            )
            Column(
                modifier = Modifier
                    .weight(1f)
                    .padding(start = LocalDimens.current.dimen8),
            ) {
                repeat(items.size) { index ->
                    Row(
                        modifier = Modifier.fillMaxWidth(),
                        horizontalArrangement = Arrangement.SpaceBetween
                    ) {
                        Row(verticalAlignment = Alignment.CenterVertically) {
                            Box(
                                modifier = Modifier
                                    .size(LocalDimens.current.dimen6)
                                    .clip(RoundedCornerShape(LocalDimens.current.dimen1))
                                    .background(if (index > items.size) colors.last() else colors[index])
                            )
                            PaddingStart(value = LocalDimens.current.dimen4)
                            Text(
                                text = items[index].asset.capitalizeWords(),
                                style = LocalTypography.current.text14.light.colorTxtParagraph()
                            )
                        }
                        LabelAmountPercentage(
                            label = "",
                            amount = items[index].percentage.toAmount(4) ?: "0.00",
                            amountTextStyle = LocalTypography.current.text14.regular.colorTxtParagraph()
                        )
                    }
                }
            }
        }
    }
}

data class FundAllocationPieChartData(
    val asset: String,
    val percentage: String,
)