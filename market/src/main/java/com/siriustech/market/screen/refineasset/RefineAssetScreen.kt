package com.siriustech.market.screen.refineasset

import android.app.Activity
import android.content.Intent
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxHeight
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.verticalScroll
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.rememberModalBottomSheetState
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.tooling.preview.Preview
import androidx.fragment.app.FragmentActivity
import androidx.hilt.navigation.compose.hiltViewModel
import com.siriustech.core_ui_compose.base.SingleEventEffect
import com.siriustech.core_ui_compose.ext.bottomStroke
import com.siriustech.market.screen.refineasset.component.RefineAssetListToolbar
import com.siriustech.market.screen.refineasset.component.RefineFilterProductTypeSection
import com.siriustech.market.screen.refineasset.component.RefineProductCategorySection
import com.siriustech.market.screen.refineasset.component.RefineSortBySection
import com.siriustech.merit.app_common.component.button.ButtonProperties
import com.siriustech.merit.app_common.component.button.SecondaryBorderButton
import com.siriustech.merit.app_common.component.button.SecondaryButton
import com.siriustech.merit.app_common.component.container.PaddingTop
import com.siriustech.merit.app_common.component.loading.HorizontalProgressBar
import com.siriustech.merit.app_common.component.modalbts.ModelListBottomSheet
import com.siriustech.merit.app_common.component.modalbts.ModelListBottomSheetProperties
import com.siriustech.merit.app_common.component.separator.SeparatorLine
import com.siriustech.merit.app_common.theme.LocalDimens
import com.siriustech.merit.app_common.typeenum.MarketCategoryType
import com.siriustech.merit.app_common.R as AppCommonR

/**
 * Created by Hein Htet
 */
@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun RefineAssetScreen(
    arguments: RefineAssetListArguments? = null,
    viewModel: RefineAssetListViewModel = hiltViewModel(),
) {

    val showLoading = viewModel.outputs.showLoading.collectAsState()
    val refineSortByType = viewModel.outputs.selectedRefineListSortingType.collectAsState()
    val selectedRefineProductTypes = viewModel.outputs.selectedRefineProductTypes.collectAsState()
    val displayProductCategoryItems = viewModel.outputs.productCategory
    val activity = LocalContext.current as FragmentActivity
    var showProductModalList by remember { mutableStateOf(false) }

    LaunchedEffect(Unit) {
        viewModel.inputs.initArgument(arguments)
    }


    SingleEventEffect(sideEffectFlow = viewModel.appAction) {
        when (it) {
            is RefineAssetListAction.OnToggleProductCategoryModal -> {
                showProductModalList = it.show
            }

            is RefineAssetListAction.OnApplyFilter -> {
                activity.setResult(Activity.RESULT_OK, Intent().apply {
                    putExtra(RefineAssetListActivity.RESULT_EXTRA_REFINE_RESULT, it.arguments)
                })
                activity.finish()
            }
        }
    }

    Column(modifier = Modifier.fillMaxSize()) {
        RefineAssetListToolbar(onReset = {
            viewModel.inputs.onResetDefault()
        })
        HorizontalProgressBar(visible = showLoading.value)
        Column(
            modifier = Modifier
                .verticalScroll(rememberScrollState())
                .padding(LocalDimens.current.dimen12)
        ) {
            PaddingTop(value = LocalDimens.current.dimen12)
            RefineSortBySection(
                arguments?.refineModel?.marketCategoryType ?: MarketCategoryType.CASH_EQUIVALENT.value,
                refineSortByType.value,
                onSortTypeChanged = {
                    viewModel.onTriggerActions(RefineAssetListAction.OnSortTypeChanged(it))
                })
            SeparatorLine(modifier = Modifier.padding(vertical = LocalDimens.current.dimen12))
            RefineFilterProductTypeSection(
                selectedRefineProductTypes.value.orEmpty(),
                onSortTypeChanged = {
                    viewModel.onTriggerActions(RefineAssetListAction.OnFilterProductTypeChanged(it))
                })
            PaddingTop(value = LocalDimens.current.dimen12)
            RefineProductCategorySection(
                displayProductCategoryItems,
                onSelectProductCategory = {
                    viewModel.onTriggerActions(RefineAssetListAction.OnGetProductCategory)
                },
                onDeleteProductCategory = {
                    viewModel.onTriggerActions(
                        RefineAssetListAction.OnDeleteProductCategory(it)
                    )
                })
        }
        Spacer(modifier = Modifier.weight(1f))
        Box(
            modifier = Modifier
                .fillMaxWidth()
                .height(LocalDimens.current.dimen1)
                .bottomStroke(color = Color.Gray.copy(alpha = 0.1f))
        ) {

        }
        PaddingTop(value = LocalDimens.current.dimen16)
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .padding(horizontal = LocalDimens.current.dimen12)
        ) {
            SecondaryBorderButton(
                onClicked = { activity.finish() },
                modifier = Modifier.weight(1f), properties = ButtonProperties(
                    text = stringResource(
                        id = AppCommonR.string.key1011,
                    )
                )
            )
            Spacer(modifier = Modifier.width(LocalDimens.current.dimen4))
            SecondaryButton(
                modifier = Modifier.weight(1f),
                properties = ButtonProperties(text = stringResource(id = AppCommonR.string.key0411)),
                onClicked = {
                    viewModel.onTriggerActions(RefineAssetListAction.OnPrepareFilterApply)
                }
            )
        }
    }
    if (showProductModalList) {
        ModelListBottomSheet(sheetState = rememberModalBottomSheetState(skipPartiallyExpanded = true),
            modifier = Modifier.fillMaxHeight(0.8f),
            onItemClicked = {
                viewModel.inputs.setSelectProductCategory(it)
            },
            properties = ModelListBottomSheetProperties(
                items = displayProductCategoryItems,
                title = stringResource(id = AppCommonR.string.key0476),
                prefixTitle = stringResource(id = AppCommonR.string.key0410),
                searchEnable = true,
                allowMultipleSelect = true,
                searchPlaceholder = stringResource(id = AppCommonR.string.key0476),
            ),
            onDismissed = {
                showProductModalList = false
            })
    }
}


@Preview(showBackground = true, showSystemUi = true)
@Composable
fun PreviewRefineAssetScreen() {
    RefineAssetScreen()
}