package com.siriustech.market.screen.marketprofile.page.technicalchart

import android.content.Context
import android.widget.TextView
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.mutableStateListOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.snapshots.SnapshotStateList
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.toArgb
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.tooling.preview.Preview
import androidx.core.content.ContextCompat
import androidx.core.content.res.ResourcesCompat
import androidx.hilt.navigation.compose.hiltViewModel
import com.core.util.toAmount
import com.github.mikephil.charting.charts.BarChart
import com.github.mikephil.charting.components.LimitLine
import com.github.mikephil.charting.components.MarkerView
import com.github.mikephil.charting.data.Entry
import com.github.mikephil.charting.formatter.ValueFormatter
import com.github.mikephil.charting.highlight.Highlight
import com.github.mikephil.charting.utils.MPPointF
import com.siriustech.market.R
import com.siriustech.market.domain.TechnicalChartDataMapper.formatVolume
import com.siriustech.market.domain.TechnicalChartDataMapper.mapToBarData
import com.siriustech.market.domain.TechnicalChartDataMapper.mapToCandleStickData
import com.siriustech.market.domain.TechnicalChartDataMapper.mapToLineData
import com.siriustech.market.domain.TechnicalChartType
import com.siriustech.market.domain.display.CandleChartDisplayModel
import com.siriustech.market.domain.display.TechnicalChartDisplayModel
import com.siriustech.market.screen.marketprofile.MarketProfileAction
import com.siriustech.market.screen.marketprofile.MarketProfileViewModel
import com.siriustech.market.screen.marketprofile.page.technicalchart.ChartUtils.setupSynchronizedListener
import com.siriustech.merit.app_common.component.chart.AppBarChart
import com.siriustech.merit.app_common.component.chart.AppCandleStickChart
import com.siriustech.merit.app_common.component.chart.AppLineChart
import com.siriustech.merit.app_common.component.chart.custom.CustomCandleChart
import com.siriustech.merit.app_common.component.chart.custom.CustomLineChart
import com.siriustech.merit.app_common.component.common.MarketProfileTabContent
import com.siriustech.merit.app_common.component.common.MarketProfileTabModel
import com.siriustech.merit.app_common.component.container.PaddingStart
import com.siriustech.merit.app_common.component.container.PaddingTop
import com.siriustech.merit.app_common.ext.DATE_FORMAT_3
import com.siriustech.merit.app_common.ext.colorTxtParagraph
import com.siriustech.merit.app_common.ext.noRippleClickable
import com.siriustech.merit.app_common.ext.toDateTimeWithZoneId
import com.siriustech.merit.app_common.theme.LocalAppColor
import com.siriustech.merit.app_common.theme.LocalDimens
import com.siriustech.merit.app_common.theme.LocalTypography
import org.threeten.bp.ZoneId
import timber.log.Timber
import com.siriustech.merit.app_common.R as AppCommonR

/**
 * Created by Hein Htet
 */

@Composable
fun TechnicalChart(viewModel: MarketProfileViewModel) {
    val candleName = TechnicalChartType.displayName(type = TechnicalChartType.CANDLE_STICK_CHART)
    val lineName = TechnicalChartType.displayName(type = TechnicalChartType.LINE_CHART)
    val selectedMenu = viewModel.outputs.currentTechnicalChartType.collectAsState()
    val tabs = remember {
        mutableStateListOf(
            *listOf(
                MarketProfileTabModel(
                    id = TechnicalChartType.CANDLE_STICK_CHART.value,
                    title = candleName,
                    isSelected = selectedMenu.value == TechnicalChartType.CANDLE_STICK_CHART,
                    icon = AppCommonR.drawable.ic_candle,
                ),
                MarketProfileTabModel(
                    id = TechnicalChartType.LINE_CHART.value,
                    title = lineName,
                    isSelected = selectedMenu.value == TechnicalChartType.LINE_CHART,
                    icon = AppCommonR.drawable.ic_line,
                )
            ).toTypedArray()
        )
    }
    val displayTechnicalChartItems = viewModel.outputs.displayTechnicalChartItems.collectAsState()
    val chartType = viewModel.outputs.currentTechnicalChartType.collectAsState()
    val timeFrame = viewModel.outputs.currentSimpleChartTimeframe.collectAsState()
    if (displayTechnicalChartItems.value.candles.isEmpty()) return

    val context = LocalContext.current
    val candleStickChart = remember {
        CustomCandleChart(context)
    }
    val barChart = remember {
        BarChart(context)
    }

    val lineChart = remember {
        CustomLineChart(context)
    }

    val typeface = remember {
        ResourcesCompat.getFont(
            candleStickChart.context,
            com.siriustech.merit.app_common.R.font.noto_sans_medium
        )
    }

    val lastPrice = viewModel.outputs.lastPrice.collectAsState()

    LaunchedEffect(key1 = lastPrice.value, key2 = timeFrame.value) {
        val newLastPrice = lastPrice.value
        candleStickChart.axisRight.removeAllLimitLines()
        val lastPriceLine = LimitLine(newLastPrice, newLastPrice.toAmount(4))
        lastPriceLine.lineColor = Color.Transparent.toArgb()
        lastPriceLine.textSize = 12f
        lastPriceLine.typeface = typeface
        lastPriceLine.textColor =
            ContextCompat.getColor(candleStickChart.context, AppCommonR.color.txtTitle)
        lastPriceLine.lineWidth = 0f
        candleStickChart.axisRight.addLimitLine(lastPriceLine)
        candleStickChart.notifyDataSetChanged()

        lineChart.axisRight.removeAllLimitLines()
        lineChart.axisRight.addLimitLine(lastPriceLine)
        lineChart.notifyDataSetChanged()
    }

    Column(
        modifier = Modifier
            .fillMaxSize()
    ) {
        PaddingTop(value = LocalDimens.current.dimen16)
        Column(
            modifier = Modifier
                .fillMaxWidth()
                .weight(1f)
        ) {
            val axisRightTextColor = LocalAppColor.current.txtParagraph.toArgb()
            if (chartType.value != TechnicalChartType.LINE_CHART) {
                Box(modifier = Modifier.weight(1f)) {
                    AppCandleStickChart(
                        lastPrice = lastPrice.value,
                        candleStickChart = candleStickChart,
                        candleData = displayTechnicalChartItems.value.candles.mapToCandleStickData(),
                        onConfigChart = { chart ->
                            chart.setPinchZoom(true)
                            chart.isDragEnabled = true
                            chart.isDragYEnabled = false
                            chart.isDragXEnabled = true
                            chart.isAutoScaleMinMaxEnabled = true
                            chart.setVisibleXRangeMaximum(10f)
                            chart.setVisibleXRangeMinimum(40f)
                            chart.axisRight.setLabelCount(3, true)
                            chart.marker = TechnicalChartMarker(context)
                            setupSynchronizedListener(chart, barChart)
                            chart.axisRight.textColor = axisRightTextColor
                            chart.axisRight.valueFormatter = object : ValueFormatter() {
                                override fun getFormattedValue(value: Float): String {
                                    return value.toAmount(4)
                                }
                            }
                        }
                    )
                }
            } else {
                Box(modifier = Modifier.weight(1f)) {
                    AppLineChart(lineChart = lineChart,
                        lineData = displayTechnicalChartItems.value.lineEntries.mapToLineData(),
                        onConfigChart = { chart ->
                            chart.setPinchZoom(true)
                            chart.isDragEnabled = true
                            chart.isDragYEnabled = false
                            chart.isDragXEnabled = true
                            chart.isAutoScaleMinMaxEnabled = true
                            chart.setVisibleXRangeMaximum(10f)
                            chart.setVisibleXRangeMinimum(40f)
                            chart.axisRight.setLabelCount(3, true)
                            chart.marker = TechnicalChartMarker(context)
                            setupSynchronizedListener(chart, barChart)
                            chart.axisRight.textColor = axisRightTextColor
                            chart.axisRight.valueFormatter = object : ValueFormatter() {
                                override fun getFormattedValue(value: Float): String {
                                    return value.toAmount(4)
                                }
                            }
                        })
                }
            }
            Spacer(modifier = Modifier.height(LocalDimens.current.dimen16))
            var bar = displayTechnicalChartItems.value.volumes.mapToBarData()
            Box(
                modifier = Modifier
                    .fillMaxWidth()
            ) {
                AppBarChart(
                    barChart = barChart,
                    modifier = Modifier
                        .fillMaxWidth()
                        .height(LocalDimens.current.dimen60),
                    barData = bar,
                    onConfigChart = {
                        it.axisRight.setLabelCount(2, true)
                        it.axisRight.textColor = axisRightTextColor
                        it.isDragEnabled = true
                        it.isDragYEnabled = false
                        it.isDragXEnabled = true
                        it.xAxis.setDrawLabels(false)
                        it.isAutoScaleMinMaxEnabled = true
                        it.axisRight.valueFormatter = object : ValueFormatter() {
                            override fun getFormattedValue(value: Float): String {
                                return if (value < 0) "0" else formatVolume(value)
//                                Timber.d("BAR_ITEM ${formatVolume(value.toFloat())}")
//                                return formatVolume(value)
                            }
                        }
                    }
                )
            }
        }
        PaddingTop(value = LocalDimens.current.dimen8)
        TechnicalChartXAxisDate(displayTechnicalChartItems.value)
        PaddingTop(value = LocalDimens.current.dimen8)
        TechnicalChartActions(tabs = tabs, viewModel)
    }
}

@Composable
fun TechnicalChartXAxisDate(displayTechnicalChartItems: TechnicalChartDisplayModel) {

    Row(
        modifier = Modifier
            .fillMaxWidth()
            .padding(horizontal = LocalDimens.current.dimen12),
        horizontalArrangement = Arrangement.SpaceBetween
    ) {
        Text(
            displayTechnicalChartItems.xAxisDates.first,
            style = LocalTypography.current.text12.medium.colorTxtParagraph()
        )
        Text(
            displayTechnicalChartItems.xAxisDates.second,
            style = LocalTypography.current.text12.medium.colorTxtParagraph()
        )
        Text(
            displayTechnicalChartItems.xAxisDates.third,
            style = LocalTypography.current.text12.medium.colorTxtParagraph()
        )
    }
}

@Composable
fun TechnicalChartActions(
    tabs: SnapshotStateList<MarketProfileTabModel>,
    viewModel: MarketProfileViewModel,
) {


    Row(
        modifier = Modifier
            .fillMaxWidth()
            .height(LocalDimens.current.dimen50)
            .padding(horizontal = LocalDimens.current.dimen12)
    ) {
        tabs.forEach { marketProfileTabModel ->
            MarketProfileTabContent(item = marketProfileTabModel, onTabSelected = {
                val index = tabs.indexOf(it)
                if (tabs[index].isSelected) return@MarketProfileTabContent
                tabs.forEachIndexed { i, marketProfileTabModel ->
                    if (index == i) {
                        tabs[index] = marketProfileTabModel.copy(isSelected = true)
                    } else {
                        tabs[i] = marketProfileTabModel.copy(isSelected = false)
                    }
                }
                viewModel.onTriggerActions(
                    MarketProfileAction.OnTechnicalChartTypeChanged(
                        TechnicalChartType.fromParam(marketProfileTabModel.id)
                    )
                )
            })
        }
        PaddingStart(value = LocalDimens.current.dimen4)
        Box(
            modifier = Modifier
                .width(LocalDimens.current.dimen50)
                .height(LocalDimens.current.dimen46)
                .background(LocalAppColor.current.btn3rd)
                .clip(RoundedCornerShape(LocalDimens.current.dimen24))
                .noRippleClickable {
                    viewModel.onTriggerActions(
                        MarketProfileAction.OnToggleTechnicalChartTimeFrameModal(
                            show = true
                        )
                    )
                },

            contentAlignment = Alignment.Center
        ) {
            Text(
                text = viewModel.outputs.currentSelectedChartType.collectAsState().value.value,
                style = LocalTypography.current.text14.semiBold.colorTxtParagraph()
            )
        }
    }
}

@Preview(showBackground = true, showSystemUi = true)
@Composable
fun PreviewTechnicalChart() {
    TechnicalChart(hiltViewModel())
}


class TechnicalChartMarker(context: Context) :
    MarkerView(
        context,
        R.layout.marker_technical_chart
    ) {

    private var screenWidth = 0
    private val tvDate: TextView = findViewById(R.id.tvDate)
    private val tvOpen: TextView = findViewById(R.id.tvOpen)
    private val tvHigh: TextView = findViewById(R.id.tvHigh)
    private val tvLow: TextView = findViewById(R.id.tvLow)
    private val tvClose: TextView = findViewById(R.id.tvClose)
    private val tvVolume: TextView = findViewById(R.id.tvVolume)


    private val tvCurrency = listOf<TextView>(
        findViewById(R.id.tvOpenCurrency),
        findViewById(R.id.tvHighCurrency),
        findViewById(R.id.tvLowCurrency),
        findViewById(R.id.tvCloseCurrency),
    )

    init {
        screenWidth = context.resources?.displayMetrics?.widthPixels ?: 0
    }

    override fun refreshContent(e: Entry?, highlight: Highlight?) {
        Timber.d("Technical Chart Marker $e")
        (e?.data as CandleChartDisplayModel).let {
            tvDate.text = it.timestamp.toDateTimeWithZoneId(
                format = DATE_FORMAT_3,
                zoneId = ZoneId.systemDefault()
            )
            tvOpen.text = it.open.toAmount(4)
            tvHigh.text = it.high.toAmount(4)
            tvLow.text = it.low.toAmount(4)
            tvClose.text = it.close.toAmount(4)
            tvVolume.text = it.volume.toAmount(0)
            tvCurrency.forEach { tv ->
                tv.text = it.currency
            }
        }
        super.refreshContent(e, highlight)
    }

    override fun getOffsetForDrawingAtPoint(posX: Float, posY: Float): MPPointF {
        val supposedX = posX + width
        val mpPointF = MPPointF()
        mpPointF.x = when {
            supposedX > screenWidth -> -width.toFloat()
            posX - width < 0 -> 0f
            else -> 0f
        }
        mpPointF.y = if (posY > height) {
            -height.toFloat()
        } else {
//            -(height.toFloat() / 2)
            ((height - posY) / 2f)
        }
        return mpPointF
    }
}
