package com.siriustech.market.screen.searchinstrument

import androidx.compose.runtime.mutableStateListOf
import androidx.compose.runtime.snapshots.SnapshotStateList
import androidx.lifecycle.viewModelScope
import com.siriustech.market.domain.MarketDetailsListMapper.mapToMarketAssetDisplayItems
import com.siriustech.market.domain.MarketDetailsListMapper.mapToMarketInstrumentViewedAssetDisplayItems
import com.siriustech.market.screen.assetListLanding.MarketAction
import com.siriustech.merit.apilayer.service.market.instrumentsearch.InstrumentSearchRequest
import com.siriustech.merit.apilayer.service.market.instrumentsearch.InstrumentSearchUseCase
import com.siriustech.merit.apilayer.service.market.recentlyviewed.RecentlyViewedListDetailsUseCase
import com.siriustech.merit.apilayer.service.market.togglefavorite.ToggleFavoriteRequest
import com.siriustech.merit.apilayer.service.market.togglefavorite.ToggleFavoriteUseCase
import com.siriustech.merit.app_common.data.display.MarketAssetDisplayData
import com.siriustech.merit.app_common.theme.AppAction
import com.siriustech.merit.app_common.theme.AppViewModel
import dagger.hilt.android.lifecycle.HiltViewModel
import javax.inject.Inject
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.Job
import kotlinx.coroutines.delay
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.SharingStarted
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.catch
import kotlinx.coroutines.flow.collectLatest
import kotlinx.coroutines.flow.map
import kotlinx.coroutines.flow.onCompletion
import kotlinx.coroutines.flow.onStart
import kotlinx.coroutines.flow.stateIn
import kotlinx.coroutines.launch
import timber.log.Timber

/**
 * Created by Hein Htet
 */
@HiltViewModel
class SearchInstrumentViewModel @Inject constructor(
    private val recentlyViewedListDetailsUseCase: RecentlyViewedListDetailsUseCase,
    private val instrumentSearchUseCase: InstrumentSearchUseCase,
    private val toggleFavoriteUseCase: ToggleFavoriteUseCase,
) : AppViewModel() {

    private val _showLoading = MutableStateFlow(false)
    private val _displayMarketAssetListItems = mutableStateListOf<MarketAssetDisplayData>()
    private val _displayRecentlyViewedListItems = mutableStateListOf<MarketAssetDisplayData>()
    private val _searchQuery = MutableStateFlow("")
    private var _searchQueryScope: Job? = null

    inner class SearchInstrumentInputs : BaseInputs() {
        fun onGetRecentlyViewed() = getRecentlyViewedAssetListDetails()

        fun onSearchInputChanged(value: String) {
            _searchQuery.value = value
            _searchQueryScope?.cancel()
            _searchQueryScope = CoroutineScope(Dispatchers.Main).launch {
                delay(500)
                if (value.isNotEmpty()) {
                    searchInstrument()
                } else {
                    _displayMarketAssetListItems.clear()
                }
            }
        }
    }

    inner class SearchInstrumentOutputs : BaseOutputs() {
        val showLoading: StateFlow<Boolean>
            get() = _showLoading

        val displayMarketAssetListItems: SnapshotStateList<MarketAssetDisplayData>
            get() = _displayMarketAssetListItems

        val displayRecentlyViewedListItems: SnapshotStateList<MarketAssetDisplayData>
            get() = _displayRecentlyViewedListItems

        val showRecentlyViewed = _searchQuery.map {
            it.isEmpty()
        }.stateIn(
            viewModelScope,
            initialValue = false,
            started = SharingStarted.WhileSubscribed(5000)
        )

    }

    override fun onTriggerActions(action: AppAction) {
        when (action) {
            is MarketAction.OnToggleFavoriteMarketAsset -> {
                toggleFavorite(action.data, action.favorite)
            }

            else -> super.onTriggerActions(action)
        }
    }

    override val inputs = SearchInstrumentInputs()
    override val outputs = SearchInstrumentOutputs()

    private fun getRecentlyViewedAssetListDetails() {
        scope.launch {
            recentlyViewedListDetailsUseCase()
                .onStart { _showLoading.value = true }
                .onCompletion { _showLoading.value = false }
                .catch {
                    Timber.d("getRecentlyViewedAssetListDetails $it")
                }
                .collectLatest {
                    _displayRecentlyViewedListItems.clear()
                    _displayRecentlyViewedListItems.addAll(it.mapToMarketInstrumentViewedAssetDisplayItems())
                }
        }
    }

    private fun searchInstrument() {
        scope.launch {
            instrumentSearchUseCase(param = InstrumentSearchRequest(keyword = _searchQuery.value))
                .onStart { _showLoading.value = true }
                .onCompletion { _showLoading.value = false }
                .catch { }
                .collectLatest {
                    _displayMarketAssetListItems.clear()
                    _displayMarketAssetListItems.addAll(it.mapToMarketAssetDisplayItems())
                }
        }
    }

    private fun toggleFavorite(item: MarketAssetDisplayData, favorite: Boolean) {
        scope.launch {
            toggleFavoriteUseCase(
                param = ToggleFavoriteRequest(instrumentId = item.id.toIntOrNull() ?: -1)
            )
                .onStart { _showLoading.value = true }
                .onCompletion { _showLoading.value = false }
                .catch { }
                .collectLatest {
                    if (_searchQuery.value.isNotEmpty()) {
                        val index = _displayMarketAssetListItems.indexOfFirst { it.id == item.id }
                        _displayMarketAssetListItems[index] =
                            _displayMarketAssetListItems[index].copy(isFavorite = favorite)
                    } else {
                        val index =
                            _displayRecentlyViewedListItems.indexOfFirst { it.id == item.id }
                        _displayRecentlyViewedListItems[index] =
                            _displayRecentlyViewedListItems[index].copy(isFavorite = favorite)
                    }

                }
        }
    }
}