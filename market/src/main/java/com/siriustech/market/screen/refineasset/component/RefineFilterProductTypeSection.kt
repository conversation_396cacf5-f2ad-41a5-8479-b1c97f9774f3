package com.siriustech.market.screen.refineasset.component

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.ExperimentalLayoutApi
import androidx.compose.foundation.layout.FlowRow
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.res.stringResource
import com.siriustech.merit.app_common.R as AppCommonR
import com.siriustech.merit.app_common.component.button.ToggleThirdButton
import com.siriustech.merit.app_common.component.button.ToggleThirdButtonProperties
import com.siriustech.merit.app_common.component.container.PaddingStart
import com.siriustech.merit.app_common.component.text.BadgeText
import com.siriustech.merit.app_common.ext.colorTxtTitle
import com.siriustech.merit.app_common.theme.LocalAppColor
import com.siriustech.merit.app_common.theme.LocalDimens
import com.siriustech.merit.app_common.theme.LocalTypography
import com.siriustech.merit.app_common.typeenum.RefineFilterProductType
import timber.log.Timber

@OptIn(ExperimentalLayoutApi::class)
@Composable
fun RefineFilterProductTypeSection(
    currentItems: List<RefineFilterProductType> = emptyList(),
    onSortTypeChanged: (RefineFilterProductType) -> Unit = {},
) {
    Timber.d("RefineSortBySection ${currentItems}")
    val row = 4
    val column = 2
    Row(verticalAlignment = Alignment.CenterVertically) {
        Text(
            text = stringResource(id = AppCommonR.string.key0473),
            style = LocalTypography.current.text14.medium.colorTxtTitle()
        )
        PaddingStart(value = LocalDimens.current.dimen8)
        BadgeText(
            label = stringResource(id = AppCommonR.string.key0474),
            bgColor = LocalAppColor.current.bgAccent,
            textColor = LocalAppColor.current.txtLabel
        )
    }
    FlowRow(
        modifier = Modifier
            .padding(top = LocalDimens.current.dimen8)
            .fillMaxWidth()
            .background(LocalAppColor.current.bgDefault),
        horizontalArrangement = Arrangement.spacedBy(LocalDimens.current.dimen4),
        maxItemsInEachRow = 2,
    ) {
        val items = listOf(
            ToggleThirdButtonProperties(
                text = stringResource(id = AppCommonR.string.key0466),
            ),
            ToggleThirdButtonProperties(
                text = stringResource(id = AppCommonR.string.key0467),
            ),
            ToggleThirdButtonProperties(
                text = stringResource(id = AppCommonR.string.key0468),

                ),
            ToggleThirdButtonProperties(
                text = stringResource(id = AppCommonR.string.key0469),
            ),
            ToggleThirdButtonProperties(
                text = stringResource(id = AppCommonR.string.key0470),
            ),
            ToggleThirdButtonProperties(
                text = stringResource(id = AppCommonR.string.key0471),
            ),
            ToggleThirdButtonProperties(
                text = stringResource(id = AppCommonR.string.key0472),
            ),
        )
        repeat(row * column) { index ->
            if (index > items.size - 1) {
                Box(modifier = Modifier.weight(1f)){}
            } else {
                ToggleThirdButton(
                    onSelectedChanged = { onSortTypeChanged(RefineFilterProductType.entries[index]) },
                    isSelectedValue = currentItems.contains(RefineFilterProductType.entries[index]),
                    modifier = Modifier.weight(1f),
                    properties = items[index]
                )
            }
        }
    }
}