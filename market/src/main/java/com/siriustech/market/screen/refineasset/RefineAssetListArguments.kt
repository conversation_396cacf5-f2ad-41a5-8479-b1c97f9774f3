package com.siriustech.market.screen.refineasset

import com.siriustech.merit.app_common.data.display.MarketCategoryFilterModel
import kotlinx.serialization.Serializable

/**
 * Created by <PERSON><PERSON><PERSON>
 */


@Serializable
data class RefineAssetListArguments(
//    val sortBy : RefineAssetSortingType? = null,
//    val filterProductTypes : List<RefineFilterProductType> = emptyList(),
//    val productCategoryTypes : List<ModalListDataContent> = emptyList(),
    val refineModel: MarketCategoryFilterModel = MarketCategoryFilterModel()
): java.io.Serializable