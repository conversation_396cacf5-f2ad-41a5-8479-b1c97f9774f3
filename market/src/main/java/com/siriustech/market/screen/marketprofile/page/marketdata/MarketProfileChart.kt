package com.siriustech.market.screen.marketprofile.page.marketdata

import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.remember
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.toArgb
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import com.core.util.toAmount
import com.github.mikephil.charting.charts.LineChart
import com.github.mikephil.charting.formatter.ValueFormatter
import com.siriustech.market.domain.ProfileChartMapper.getProfileLineChartData
import com.siriustech.market.domain.display.SimpleChartDisplayModel
import com.siriustech.market.screen.marketprofile.MarketProfileAction
import com.siriustech.market.screen.marketprofile.MarketProfileViewModel
import com.siriustech.market.screen.marketprofile.page.technicalchart.TechnicalChartMarker
import com.siriustech.merit.app_common.component.chart.AppLineChart
import com.siriustech.merit.app_common.component.common.TimeFrameSelector
import com.siriustech.merit.app_common.component.common.TimeFrameSelectorProperties
import com.siriustech.merit.app_common.component.container.PaddingTop
import com.siriustech.merit.app_common.ext.colorTxtParagraph
import com.siriustech.merit.app_common.theme.LocalAppColor
import com.siriustech.merit.app_common.theme.LocalDimens
import com.siriustech.merit.app_common.theme.LocalTypography
import com.siriustech.merit.app_common.typeenum.ChartTimeFrame

/**
 * Created by Hein Htet
 */

@Composable
fun MarketProfileChart(
    modifier: Modifier = Modifier,
    viewModel: MarketProfileViewModel,
) {
    val displaySimpleChartData = viewModel.outputs.displaySimpleChartData.collectAsState()
    val selectedTimeframe = viewModel.outputs.currentSimpleChartTimeframe.collectAsState()
    val displayMarketProfileChartTimeFrames = viewModel.outputs.displayMarketProfileChartTimeFrames.collectAsState()
    Column(
        modifier = Modifier
            .then(modifier)
    ) {
        MarketProfileChartContent(
            displayMarketProfileChartTimeFrames.value,
            displaySimpleChartData.value,
            selectedTimeframe.value,
            onTimeFrameSelected = {
                viewModel.onTriggerActions(MarketProfileAction.OnSimpleChartTimeFrameChanged(it))
            }
        )
    }
}

@Composable
fun MarketProfileChartContent(
    timeframes : List<ChartTimeFrame> = emptyList(),
    displayData: SimpleChartDisplayModel = SimpleChartDisplayModel(),
    timeframe: ChartTimeFrame = ChartTimeFrame.ONE_WEEK,
    onTimeFrameSelected: (ChartTimeFrame) -> Unit = {}
) {
    val activity = LocalContext.current
    val lineChart = remember {
        LineChart(activity)
    }
    val axisRightTextColor = LocalAppColor.current.txtParagraph
    Column {
        Box(modifier = Modifier.height(200.dp)) {
            if(displayData.candles.isNotEmpty()){
                AppLineChart(
                    modifier = Modifier.fillMaxSize(),
                    lineChart = lineChart,
                    lineData = displayData.getProfileLineChartData(),
                    onConfigChart = { chart ->
                        chart.axisRight.valueFormatter = object : ValueFormatter() {
                            override fun getFormattedValue(value: Float): String {
                                return value.toAmount(4)
                            }
                        }
                        chart.marker = TechnicalChartMarker(activity)
                        chart.axisRight.setLabelCount(3, true)
                        chart.axisRight.textColor = axisRightTextColor.toArgb()
                    })
            }
        }
        ProfileChartXAxisDate(dates = Triple(displayData.xAxisDates.first, displayData.xAxisDates.second, displayData.xAxisDates.third))
        PaddingTop(value = LocalDimens.current.dimen8)
        TimeFrameSelector(
            properties = TimeFrameSelectorProperties(
                selectedTimeFrame = timeframe,
                items = timeframes
            ),
            onTimeFrameSelect = {
                lineChart.highlightValue(null)
                onTimeFrameSelected(it)
            }
        )
    }
}

@Composable
fun ProfileChartXAxisDate(dates: Triple<String, String, String>) {
    PaddingTop(value = LocalDimens.current.dimen8)
    Row(
        modifier = Modifier
            .fillMaxWidth()
            .padding(horizontal = LocalDimens.current.dimen12),
        horizontalArrangement = Arrangement.SpaceBetween
    ) {
        Text(
            dates.first,
            style = LocalTypography.current.text12.medium.colorTxtParagraph()
        )
        Text(
            dates.second,
            style = LocalTypography.current.text12.medium.colorTxtParagraph()
        )
        Text(
            dates.third,
            style = LocalTypography.current.text12.medium.colorTxtParagraph()
        )
    }
}

@Preview(showBackground = true, showSystemUi = true)
@Composable
fun PreviewMarketProfileChart() {
    MarketProfileChartContent(emptyList(), SimpleChartDisplayModel(), ChartTimeFrame.ONE_YEAR)
}