package com.siriustech.market.screen.assetListLanding

import android.content.Context
import androidx.compose.runtime.mutableStateListOf
import androidx.compose.runtime.snapshots.SnapshotStateList
import com.core.network.base.getError
import com.siriustech.market.domain.MarketDetailsListMapper.mapToMarketAssetDisplayItems
import com.siriustech.market.screen.refineasset.RefineAssetListArguments
import com.siriustech.market.screen.selectcategory.SelectedCategoryResultArguments
import com.siriustech.merit.apilayer.service.authentication.common.eumeration.EnumerationRequest
import com.siriustech.merit.apilayer.service.authentication.common.eumeration.GetEnumerationUseCase
import com.siriustech.merit.apilayer.service.market.exchangecurrency.MarketCurrencyRequest
import com.siriustech.merit.apilayer.service.market.exchangecurrency.MarketExchangeCurrencyUseCase
import com.siriustech.merit.apilayer.service.market.exchangecurrency.defaultMarketExchangeCurrencyRequestList
import com.siriustech.merit.apilayer.service.market.favorite.FavoriteListDetailsUseCase
import com.siriustech.merit.apilayer.service.market.marketlistdetails.MarketInstrumentListRequest
import com.siriustech.merit.apilayer.service.market.marketlistdetails.MarketListDetailsUseCase
import com.siriustech.merit.apilayer.service.market.recentlyviewed.RecentlyViewedListDetailsUseCase
import com.siriustech.merit.apilayer.service.market.togglefavorite.ToggleFavoriteRequest
import com.siriustech.merit.apilayer.service.market.togglefavorite.ToggleFavoriteUseCase
import com.siriustech.merit.app_common.Constants
import com.siriustech.merit.app_common.component.common.FilterTagDisplayModel
import com.siriustech.merit.app_common.component.marquee.StockExchangeMarqueeData
import com.siriustech.merit.app_common.data.AppCache
import com.siriustech.merit.app_common.data.display.AssetClassModel
import com.siriustech.merit.app_common.data.display.MarketAssetDisplayData
import com.siriustech.merit.app_common.data.display.MarketCategoryFilterModel
import com.siriustech.merit.app_common.mapper.MarketExchangeCurrencyMapper.mapToStockExchangeDisplayData
import com.siriustech.merit.app_common.theme.AppAction
import com.siriustech.merit.app_common.theme.AppViewModel
import com.siriustech.merit.app_common.typeenum.DropDownEnumeration
import com.siriustech.merit.app_common.typeenum.MarketCategoryType
import com.siriustech.merit.app_common.typeenum.PortfolioAssetType
import com.siriustech.merit.app_common.typeenum.RefineAssetSortingType
import com.siriustech.merit.app_common.typeenum.RiskLevel
import com.siriustech.merit.app_common.utils.ErrorCode.mapToErrorDisplay
import dagger.hilt.android.lifecycle.HiltViewModel
import javax.inject.Inject
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.Job
import kotlinx.coroutines.delay
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.SharingStarted
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.catch
import kotlinx.coroutines.flow.collectLatest
import kotlinx.coroutines.flow.map
import kotlinx.coroutines.flow.onCompletion
import kotlinx.coroutines.flow.onStart
import kotlinx.coroutines.flow.stateIn
import kotlinx.coroutines.isActive
import kotlinx.coroutines.launch
import timber.log.Timber

/**
 * Created by Hein Htet
 */
@HiltViewModel
class MarketViewModel @Inject constructor(
    private val marketExchangeCurrencyUseCase: MarketExchangeCurrencyUseCase,
    private val marketListDetailsUseCase: MarketListDetailsUseCase,
    private val favoriteListDetailsUseCase: FavoriteListDetailsUseCase,
    private val recentlyViewedListDetailsUseCase: RecentlyViewedListDetailsUseCase,
    private val toggleFavoriteUseCase: ToggleFavoriteUseCase,
    private val getEnumerationUseCase: GetEnumerationUseCase,
    private val appCache: AppCache,
) : AppViewModel() {


    private var _mContext: Context? = null

    private val _showLoading = MutableStateFlow(false)
    private val _displayStockExchangeItems = mutableStateListOf<StockExchangeMarqueeData>()
    private val _displayMarketAssetListItems = mutableStateListOf<MarketAssetDisplayData>()
    private val _selectedMarketCategory = MutableStateFlow(appCache.marketSelectedAllocationClass)
    private val _marketCategoryMap = MutableStateFlow(
        appCache.marketFilterTypeCache
    )
    private val _selectedCategoryDisplayTitle =
        MutableStateFlow(appCache.marketSelectedAllocationClassTitle)
    private val _refineFilterModel = MutableStateFlow<RefineAssetListArguments?>(
        RefineAssetListArguments(refineModel = MarketCategoryFilterModel())
    )
    private val _refineFilterTags = mutableStateListOf<FilterTagDisplayModel>()


    private val _assetClassList = mutableStateListOf<AssetClassModel>()
    private val _selectedAssetClass = MutableStateFlow(AssetClassModel())

    private var _job: Job? = null

    override val inputs = MarketInputs()
    override val outputs = MarketOutputs()


    inner class MarketInputs : BaseInputs() {

        init {
            getAllocationClassFromEnum()
        }

        fun onApiCall() {
            getMarketExchangeCurrency()
            onGetMarketList()
            appCache.updateMenuCache(null) // clear action for home shortcut
        }

        fun setDefaultMarketCategory(arguments: SelectedCategoryResultArguments, context: Context) {
            if (_selectedCategoryDisplayTitle.value.isEmpty()) {
                updateSelectedMarketCategoryType(arguments)
            }
            _mContext = context
            startPeriodicPriceUpdates()
        }

        fun updateSelectedMarketCategoryType(arguments: SelectedCategoryResultArguments) {
            _selectedMarketCategory.value = arguments.selectedValue
            appCache.updateSelectedMarketAllocationClass(
                arguments.selectedValue,
                arguments.title.orEmpty()
            )
            val refineModel = _marketCategoryMap.value[arguments.selectedValue]
                ?: MarketCategoryFilterModel()
            refineModel.updateRefineFilterTags()
            _selectedCategoryDisplayTitle.value = arguments.title.orEmpty()
            onGetMarketList()
        }

        fun onGetMarketList(showLoading: Boolean = true) {
            when (_selectedMarketCategory.value) {
                MarketCategoryType.FAVORITE.value -> getFavoriteAssetListDetails()
                MarketCategoryType.RECOMMENDED.value -> getMarketListDetails(showLoading)
                MarketCategoryType.RECENTLY_VIEWED.value -> getRecentlyViewedAssetListDetails()
                else -> getMarketListDetails(showLoading)
            }
        }

        fun onStartPeriodicPriceUpdates() {
            if (_job == null || _job?.isCompleted == true) {
                _job = scope.launch(Dispatchers.IO) {
                    while (isActive) {
                        onGetMarketList(showLoading = false)
                        delay(Constants.INTERVAL_API_CALL_IN_SEC * 1000)
                    }
                }
            }
        }

        fun onStopPeriodicPriceUpdates() {
            _job?.cancel()
            _job = null
        }
    }

    inner class MarketOutputs : BaseOutputs() {
        val showLoading: StateFlow<Boolean>
            get() = _showLoading
        val displayStockExchangeItems: SnapshotStateList<StockExchangeMarqueeData>
            get() = _displayStockExchangeItems

        val displayMarketAssetListItems: SnapshotStateList<MarketAssetDisplayData>
            get() = _displayMarketAssetListItems

        val selectedCategoryDisplayTitle: StateFlow<String>
            get() = _selectedCategoryDisplayTitle

        val selectedMarketCategoryType: StateFlow<String>
            get() = _selectedMarketCategory
        val refineFilterModel: StateFlow<RefineAssetListArguments?>
            get() = _refineFilterModel

        val refineFilterTags: SnapshotStateList<FilterTagDisplayModel>
            get() = _refineFilterTags

        val displayRefineIcon = _selectedMarketCategory.map {
            val item = MarketCategoryType.fromParam(it)
            item.isRefineFilterEnabled()
        }.stateIn(
            scope,
            initialValue = true,
            started = SharingStarted.WhileSubscribed(5000)
        )

        val isRefineRiskLevelFilterEnabled = _selectedMarketCategory.map {
            val item = MarketCategoryType.fromParam(it)
            item.isRefineRiskLevelFilterEnabled()
        }.stateIn(
            scope,
            initialValue = true,
            started = SharingStarted.WhileSubscribed(5000)
        )

        val filterModel: MarketCategoryFilterModel
            get() = _marketCategoryMap.value[_selectedMarketCategory.value]?.copy(
                marketCategoryType = _selectedMarketCategory.value
            )
                ?: MarketCategoryFilterModel()

        val assetAllocationClass: SnapshotStateList<AssetClassModel>
            get() = _assetClassList

        val selectedAssetClass: StateFlow<AssetClassModel>
            get() = _selectedAssetClass
    }

    override fun onTriggerActions(action: AppAction) {
        when (action) {
            is MarketAction.OnToggleFavoriteMarketAsset -> {
                onToggleFavorite(action.data, action.favorite)
            }

            is MarketAction.OnRefineFilterApply -> {
//                _marketCategoryMap.value[_selectedMarketCategory.value] = MarketCategoryFilterModel(
//                    sortingType = action.arguments.sortBy ?: RefineAssetSortingType.PERCENT_CHANGE_DOWN,
//                    productTypes = action.arguments.filterProductTypes,
//                    productCategoryTypes = action.arguments.productCategoryTypes
//                )
//                _refineFilterModel.value = action.arguments
                val refineModal = action.arguments.refineModel
                _marketCategoryMap.value[_selectedMarketCategory.value] = refineModal
                refineModal.updateRefineFilterTags()
                _displayMarketAssetListItems.clear()
                getMarketListDetails()
                appCache.updateMarketFilterMap(_marketCategoryMap.value) // cache
            }

            else -> super.onTriggerActions(action)
        }
    }

    private fun MarketCategoryFilterModel.updateRefineFilterTags() {
        _mContext?.let { context ->
            _refineFilterTags.clear()
            val refineModal = this
            refineModal.sortingType?.let {
                _refineFilterTags.add(
                    FilterTagDisplayModel(
                        id = it.value,
                        text = it.displayName(context),
                        showClearButton = false
                    )
                )
            }
            refineModal.productTypes.forEach {
                _refineFilterTags.add(
                    FilterTagDisplayModel(
                        id = it.value,
                        text = it.displayName(context),
                        showClearButton = false
                    )
                )
            }
            refineModal.productCategoryTypes.forEach {
                _refineFilterTags.add(
                    FilterTagDisplayModel(
                        id = it.value,
                        text = it.title,
                        showClearButton = false
                    )
                )
            }
        }
    }

    private fun getMarketExchangeCurrency() {
        scope.launch {
            marketExchangeCurrencyUseCase(
                param = MarketCurrencyRequest(
                    currencyList = defaultMarketExchangeCurrencyRequestList
                )
            )
                .onStart { _showLoading.value = true }
                .onCompletion { _showLoading.value = false }
                .catch { }
                .collectLatest {
                    _displayStockExchangeItems.clear()
                    _displayStockExchangeItems.addAll(it.mapToStockExchangeDisplayData())
                }
        }
    }

    private fun getMarketListDetails(showLoading: Boolean = true) {
        if (_selectedAssetClass.value.id == "") {
            return
        }
        var request = MarketInstrumentListRequest()

        val category = _selectedMarketCategory.value
        val filterModel =
            _marketCategoryMap.value[_selectedMarketCategory.value] ?: MarketCategoryFilterModel()

        val riskLevel = RiskLevel.fromParam(category)
        val allocationClassType = PortfolioAssetType.fromParam(category)
        if (riskLevel != null) {
            request = request.copy(riskLevelList = listOf(riskLevel.value))
        }
        if (allocationClassType != null) {
            request = request.copy(
                allocationList = listOf(allocationClassType.assetName)
            )
        }
//        _refineFilterModel.value?.let { filterModel ->
//            request = request.copy(
//                productTypeList = filterModel.filterProductTypes.map { it.value },
//                productCategoryList = filterModel.productCategoryTypes.map { it.id },
//            )
//        }

        filterModel.let { model ->
            request = request.copy(
                productTypeList = model.productTypes.map { it.value },
                productCategoryList = model.productCategoryTypes.map { it.id },
            )
        }

        if (_selectedMarketCategory.value == MarketCategoryType.RECOMMENDED.value) {
            request = request.copy(
                listCode = MarketCategoryType.RECOMMENDED.value
            )
        }

        scope.launch {
            marketListDetailsUseCase(
                param = request
            )
                .onStart { _showLoading.value = true && showLoading }
                .onCompletion { _showLoading.value = false }
                .catch { }
                .collectLatest {
                    Timber.d("Market List Details: $it")
                    _displayMarketAssetListItems.clear()
                    _displayMarketAssetListItems.addAll(it.mapToMarketAssetDisplayItems())
                    onRefineLocalSortingApply()
                }
        }
    }

    private fun getFavoriteAssetListDetails() {
        scope.launch {
            favoriteListDetailsUseCase()
                .onStart { _showLoading.value = true }
                .onCompletion { _showLoading.value = false }
                .catch { }
                .collectLatest {
                    _displayMarketAssetListItems.clear()
                    _displayMarketAssetListItems.addAll(it.mapToMarketAssetDisplayItems())
                }
        }
    }

    private fun getRecommendedAssetListDetails() {
        scope.launch {
            recentlyViewedListDetailsUseCase()
                .onStart { _showLoading.value = true }
                .onCompletion { _showLoading.value = false }
                .catch { }
                .collectLatest {
                    _displayMarketAssetListItems.clear()
                    _displayMarketAssetListItems.addAll(it.mapToMarketAssetDisplayItems())
                }
        }
    }

    private fun getRecentlyViewedAssetListDetails() {
        scope.launch {
            recentlyViewedListDetailsUseCase()
                .onStart { _showLoading.value = true }
                .onCompletion { _showLoading.value = false }
                .catch { }
                .collectLatest {
                    _displayMarketAssetListItems.clear()
                    _displayMarketAssetListItems.addAll(it.mapToMarketAssetDisplayItems())
                }
        }
    }

    private fun onToggleFavorite(item: MarketAssetDisplayData, favorite: Boolean) {
        scope.launch {
            toggleFavoriteUseCase(
                param = ToggleFavoriteRequest(instrumentId = item.id.toIntOrNull() ?: -1)
            )
                .onStart { _showLoading.value = true }
                .onCompletion { _showLoading.value = false }
                .catch { }
                .collectLatest {
                    val index = _displayMarketAssetListItems.indexOfFirst { it.id == item.id }
                    if (_selectedMarketCategory.value == MarketCategoryType.FAVORITE.value) {
                        _displayMarketAssetListItems.removeAt(index)
                    } else {
                        _displayMarketAssetListItems[index] =
                            _displayMarketAssetListItems[index].copy(isFavorite = favorite)
                    }
                }
        }
    }

    private fun onRefineLocalSortingApply() {
        val currentItems = _displayMarketAssetListItems.toList()
        val refineModel = _marketCategoryMap.value[_selectedMarketCategory.value]
        refineModel?.sortingType?.let {
            _displayMarketAssetListItems.clear()
            when (it) {
                RefineAssetSortingType.PERCENT_CHANGE_UP -> {
                    _displayMarketAssetListItems.addAll(currentItems.sortedBy {
                        it.unrealizedGlRate.toFloatOrNull() ?: 0f
                    })
                }

                RefineAssetSortingType.PERCENT_CHANGE_DOWN -> {
                    _displayMarketAssetListItems.addAll(currentItems.sortedByDescending {
                        it.unrealizedGlRate.toFloatOrNull() ?: 0f
                    })
                }

                RefineAssetSortingType.RISK_LOW_TO_HIGH -> {
                    _displayMarketAssetListItems.addAll(currentItems.sortedBy {
                        RiskLevel.getLevelNumber(
                            it.riskLevel
                        )
                    })
                }

                RefineAssetSortingType.RISK_HIGH_TO_LOW -> {
                    _displayMarketAssetListItems.addAll(currentItems.sortedByDescending {
                        RiskLevel.getLevelNumber(
                            it.riskLevel
                        )
                    })
                }

                RefineAssetSortingType.A_TO_Z -> {
                    _displayMarketAssetListItems.addAll(currentItems.sortedByDescending { it.symbol })
                }

                RefineAssetSortingType.Z_TO_A -> {
                    _displayMarketAssetListItems.addAll(currentItems.sortedBy { it.symbol })
                }
            }
        }
    }

    private fun startPeriodicPriceUpdates() {
        scope.launch(Dispatchers.IO) {
            while (true) {
//                inputs.onGetMarketList(showLoading = false)
                delay(Constants.INTERVAL_API_CALL_IN_SEC * 1000)
            }
        }
    }

    private fun getAllocationClassFromEnum() {
        scope.launch {
            getEnumerationUseCase(param = EnumerationRequest(codeList = listOf(DropDownEnumeration.ASSET_CLASS.name)))
                .onStart { _showLoading.value = true }
                .onCompletion { _showLoading.value = false }
                .catch {
                    emitError(it.getError().mapToErrorDisplay())
                }
                .collectLatest {
                    val allocationClass = it.list.firstOrNull()?.list.orEmpty()
                    val displayData = allocationClass.map {
                        AssetClassModel(
                            id = it.value,
                            title = it.desc,
                        )
                    }
                    if (displayData.any { it.id == MarketCategoryType.CASH_EQUIVALENT.value }) {
                        _selectedAssetClass.value =
                            displayData.findLast { it.id == MarketCategoryType.CASH_EQUIVALENT.value }
                                ?: AssetClassModel()
                    } else {
                        _selectedAssetClass.value = displayData.firstOrNull() ?: AssetClassModel()
                    }
                    appCache.updateAssetAllocationClass(displayData)
                    inputs.onGetMarketList()
                    _assetClassList.addAll(displayData)
                }
        }
    }
}
// B