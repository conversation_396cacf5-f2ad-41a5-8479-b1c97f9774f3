package com.siriustech.market.screen.marketprofile.component

import androidx.compose.foundation.layout.Column
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.res.stringResource
import com.siriustech.merit.app_common.R
import com.siriustech.merit.app_common.component.container.PaddingTop
import com.siriustech.merit.app_common.ext.colorTxtParagraph
import com.siriustech.merit.app_common.ext.colorTxtTitle
import com.siriustech.merit.app_common.theme.LocalDimens
import com.siriustech.merit.app_common.theme.LocalTypography

/**
 * Created by <PERSON><PERSON> <PERSON>tet
 */

@Composable
fun AssetDescription(modifier: Modifier = Modifier, description: String) {
    Column(modifier) {
        Text(
            text = stringResource(id = R.string.key0542),
            style = LocalTypography.current.text14.medium.colorTxtTitle()
        )
        PaddingTop(value = LocalDimens.current.dimen12)
        Text(description, style = LocalTypography.current.text14.light.colorTxtParagraph())
    }
}