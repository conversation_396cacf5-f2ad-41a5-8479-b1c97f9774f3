package com.siriustech.market.screen.marketprofile

import androidx.activity.compose.rememberLauncherForActivityResult
import androidx.activity.result.contract.ActivityResultContracts
import androidx.compose.foundation.Image
import androidx.compose.foundation.border
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.pager.HorizontalPager
import androidx.compose.foundation.pager.PagerState
import androidx.compose.foundation.pager.rememberPagerState
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateListOf
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.runtime.setValue
import androidx.compose.runtime.snapshotFlow
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.fragment.app.FragmentActivity
import androidx.hilt.navigation.compose.hiltViewModel
import androidx.lifecycle.Lifecycle
import androidx.lifecycle.compose.LifecycleEventEffect
import androidx.navigation.NavController
import com.siriustech.core_ui_compose.base.SingleEventEffect
import com.siriustech.market.modal.TechnicalChartTimeframeModal
import com.siriustech.market.navigation.MarketNavigationEntryPoint
import com.siriustech.market.screen.marketprofile.page.MarketProfileToolbar
import com.siriustech.market.screen.marketprofile.page.marketdata.MarketDataPage
import com.siriustech.market.screen.marketprofile.page.marketdata.MarketProfilePage
import com.siriustech.market.screen.marketprofile.page.technicalchart.TechnicalChart
import com.siriustech.merit.app_common.component.alert.BannerAlertProperties
import com.siriustech.merit.app_common.component.button.ButtonProperties
import com.siriustech.merit.app_common.component.button.SecondaryButton
import com.siriustech.merit.app_common.component.common.MarketProfileTabModel
import com.siriustech.merit.app_common.component.common.MarketProfileTabType
import com.siriustech.merit.app_common.component.common.MarketProfileTabs
import com.siriustech.merit.app_common.component.common.ProductCategoryDetailsItem
import com.siriustech.merit.app_common.component.common.onGetMarketTabs
import com.siriustech.merit.app_common.component.container.PaddingEnd
import com.siriustech.merit.app_common.component.container.PaddingTop
import com.siriustech.merit.app_common.component.modalbts.LimitedAccessBottomSheet
import com.siriustech.merit.app_common.component.modalbts.ProductCategoryDetailsModalDisplayData
import com.siriustech.merit.app_common.data.display.MarketAssetDisplayData
import com.siriustech.merit.app_common.ext.noRippleClickable
import com.siriustech.merit.app_common.navigation.InstrumentSearch
import com.siriustech.merit.app_common.theme.AppScreen
import com.siriustech.merit.app_common.theme.LocalAppColor
import com.siriustech.merit.app_common.theme.LocalDimens
import com.siriustech.merit.app_common.typeenum.MarketAssetType
import com.siriustech.merit.app_common.typeenum.UserStatus
import dagger.hilt.android.EntryPointAccessors
import kotlinx.coroutines.launch
import timber.log.Timber
import com.siriustech.merit.app_common.R as AppCommonR

/**
 * Created by Hein Htet
 */

@Composable
fun MarketProfileScreen(
    navController: NavController,
    data: MarketAssetDisplayData,
    viewModel: MarketProfileViewModel = hiltViewModel(),
) {


    val activity = LocalContext.current as FragmentActivity

    val tabs = remember {
        mutableStateListOf<MarketProfileTabModel>()
    }
    val pagerState = rememberPagerState(pageCount = { tabs.size })
    val coroutineScope = rememberCoroutineScope()
    val displayMarketProfileHeaderDetails =
        viewModel.outputs.displayMarketProfileHeaderDetails.collectAsState()
    val currentSelectedChartType = viewModel.outputs.currentSelectedChartType.collectAsState()
    val currentMenuTab = viewModel.outputs.currentSelectedTab.collectAsState()
    val displayBasicInformation = viewModel.outputs.displayBasicInformation.collectAsState()
    val isFavorite = viewModel.outputs.isFavorite.collectAsState()
    var showTechnicalChartTimeFrames by remember {
        mutableStateOf(false)
    }
    var showLimitedAccessModal by remember {
        mutableStateOf(false)
    }


    val navigation = remember {
        EntryPointAccessors.fromApplication(activity, MarketNavigationEntryPoint::class.java)
            .marketNavigation()
    }

    LaunchedEffect(displayBasicInformation.value) {
        if (displayBasicInformation.value.productType.isNotEmpty()) {
            tabs.clear()
            tabs.addAll(
                onGetMarketTabs(
                    MarketAssetType.fromParam(displayBasicInformation.value.productType)?.value
                        ?: MarketAssetType.US_EQUITY.value,
                    activity
                )
            )
        }
    }

    LaunchedEffect(Unit) {
        Timber.d("MARKET_ASSET_DISPLAY $data")
        viewModel.inputs.onInitMarketAssetData(data)
        viewModel.inputs.onApiCall()
    }

    LaunchedEffect(key1 = Unit, block = {
        if (tabs.isNotEmpty()) {
            val initPage = tabs.indexOfFirst { it.id == currentMenuTab.value?.id }
            if (initPage >= 0) {
                pagerState.scrollToPage(initPage)
            }
        }
    })

    LifecycleEventEffect(Lifecycle.Event.ON_RESUME) {
        viewModel.inputs.startPeriodicTask()
    }

    LifecycleEventEffect(Lifecycle.Event.ON_PAUSE) {
        viewModel.inputs.stopPeriodicTask()
    }


    SingleEventEffect(sideEffectFlow = viewModel.appAction) {
        when (it) {
            MarketProfileAction.OnNavigateInstrumentSearch -> {
//                navigation.onNavigateToInstrumentSearchActivity(activity)
                navController.navigate(InstrumentSearch)
            }

            is MarketProfileAction.OnNavigateBack -> {
                navController.popBackStack()
            }

            is MarketProfileAction.OnNavigateToTab -> {
                if (tabs.isNotEmpty()) {
                    val index = tabs.indexOfFirst { tab -> it.item.id == tab.id }
                    coroutineScope.launch {
                        pagerState.animateScrollToPage(index)
                    }
                }
            }

            is MarketProfileAction.OnToggleTechnicalChartTimeFrameModal -> {
                showTechnicalChartTimeFrames = it.show
            }

            is MarketProfileAction.OnToggleChangedFavorite -> {
                viewModel.emitBannerAlert(
                    BannerAlertProperties(
                        activity.getString(if (it.isFavorite) AppCommonR.string.key0491 else AppCommonR.string.key0493),
                        activity.getString(
                            if (it.isFavorite) AppCommonR.string.key0492 else AppCommonR.string.key0494,
                            it.assetName
                        ),
                    )
                )
            }

            is MarketProfileAction.OnNavigateToSettings -> {
                navigation.onNavigateToSettings(activity)
            }

            is MarketProfileAction.OnNavigateNotification -> {
                navigation.onNavigateToNotification(activity)
            }
        }
    }


    LaunchedEffect(pagerState(pagerState)) {
        snapshotFlow { pagerState.currentPage }.collect { page ->
            if (tabs.isNotEmpty()) {
                when (tabs[page].id) {
                    MarketProfileTabType.CHART.value -> viewModel.inputs.onGetTechnicalChartData()
                    MarketProfileTabType.MARKET_DATA.value -> viewModel.inputs.onGetMarketQuoteData()
                    MarketProfileTabType.PROFILE.value -> viewModel.inputs.onGetMarketInstrumentProfileDetails()
                }
            }
        }
    }

    val wealthPlanResult =
        rememberLauncherForActivityResult(contract = ActivityResultContracts.StartActivityForResult()) {


        }

    LaunchedEffect(Unit) {
        viewModel.inputs.initContext(context = activity)
    }

    AppScreen(vm = viewModel, ignorePaddingValue = true) {
        Column(modifier = Modifier.fillMaxSize()) {
            MarketProfileToolbar(viewModel = viewModel)
            Column(
                modifier = Modifier
                    .fillMaxSize()
            ) {
                PaddingTop(value = LocalDimens.current.dimen8)
                Column(modifier = Modifier.padding(horizontal = LocalDimens.current.dimen12)) {
                    ProductCategoryDetailsItem(
                        data = displayMarketProfileHeaderDetails.value
                            ?: ProductCategoryDetailsModalDisplayData()
                    )
                    if (tabs.isNotEmpty()) {
                        MarketProfileTabs(
                            defaultTabs = tabs,
                            currentSelectedTab = currentMenuTab.value,
                            onTabSelected = {
                                viewModel.onTriggerActions(MarketProfileAction.OnNavigateToTab(it))
                            })
                    }
                }
                Box(modifier = Modifier.weight(1f)) {
                    if (tabs.isNotEmpty()) {
                        HorizontalPager(
                            modifier = Modifier
                                .fillMaxSize(),
                            state = pagerState,
                            userScrollEnabled = false,
                        ) {
                            val currentTabType = tabs[it].id
                            when (currentTabType) {
                                MarketProfileTabType.CHART.value -> TechnicalChart(viewModel = viewModel)
                                MarketProfileTabType.MARKET_DATA.value -> MarketDataPage(viewModel = viewModel)
                                MarketProfileTabType.PROFILE.value -> MarketProfilePage(viewModel = viewModel)
                            }
                        }
                    }
                }
                Row(
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(
                            horizontal = LocalDimens.current.dimen12,
                            vertical = LocalDimens.current.dimen12
                        )
                ) {
                    SecondaryButton(
                        onClicked = {
                            if (viewModel.outputs.userInfo?.userStatus == UserStatus.NORMAL && viewModel.outputs.hasFullAccess) {
                                navigation.onNavigateToCreateWealthPlanActivity(
                                    activity,
                                    wealthPlanResult
                                )
                            } else {
                                showLimitedAccessModal = true
                            }
                        },
                        properties = ButtonProperties(
                            text = stringResource(id = AppCommonR.string.key0966),
                            icon = painterResource(
                                id = AppCommonR.drawable.ic_wealth_plan
                            ),
                            iconModifier = Modifier.size(LocalDimens.current.dimen12),
                        ),
                        modifier = Modifier.weight(1f)
                    )
                    PaddingEnd(value = LocalDimens.current.dimen4)
                    Box(
                        modifier = Modifier
                            .size(LocalDimens.current.dimen44)
                            .border(
                                LocalDimens.current.dimen1,
                                color = LocalAppColor.current.txtParagraph,
                                RoundedCornerShape(50)
                            )
                            .noRippleClickable {
                                viewModel.onTriggerActions(MarketProfileAction.OnToggleFavorite)
                            },
                        contentAlignment = Alignment.Center
                    ) {
                        Image(
                            painter = painterResource(id = if (isFavorite.value) AppCommonR.drawable.ic_favorite_selected else AppCommonR.drawable.ic_favorite_normal),
                            contentDescription = "Favorite Image Resource"
                        )
                    }
                }
            }
        }

        if (showTechnicalChartTimeFrames) {
            TechnicalChartTimeframeModal(
                onDismissed = {
                    viewModel.onTriggerActions(
                        MarketProfileAction.OnToggleTechnicalChartTimeFrameModal(
                            show = false
                        )
                    )
                },
                currentSelectedTimeFrame = currentSelectedChartType.value,
                onTimeFrameSelected = {
                    viewModel.onTriggerActions(
                        MarketProfileAction.OnTechnicalChartTimeFrameSelected(
                            it
                        )
                    )

                })
        }

        if (showLimitedAccessModal) {
            LimitedAccessBottomSheet(
                userStatus = viewModel.outputs.userInfo?.userStatus ?: UserStatus.NORMAL,
                onDismissed = {
                    showLimitedAccessModal = false
                }, onResumeOnboardingStep = {
                    navigation.onNavigateToSettings(activity)
                }
            )
        }
    }
}

@Composable
private fun pagerState(pagerState: PagerState) = pagerState


