package com.siriustech.market.screen.marketprofile

import com.siriustech.market.domain.CandleChartTimeFrameType
import com.siriustech.market.domain.TechnicalChartType
import com.siriustech.merit.app_common.component.common.MarketProfileTabModel
import com.siriustech.merit.app_common.theme.AppAction
import com.siriustech.merit.app_common.typeenum.ChartTimeFrame

/**
 * Created by <PERSON><PERSON>tet
 */
sealed interface MarketProfileAction : AppAction {

    data object OnNavigateInstrumentSearch : MarketProfileAction
    data object OnNavigateBack : MarketProfileAction
    data class OnNavigateToTab(val item: MarketProfileTabModel) : MarketProfileAction
    data class OnToggleTechnicalChartTimeFrameModal(val show : Boolean)  : MarketProfileAction
    data class OnTechnicalChartTimeFrameSelected(val timeFrame: CandleChartTimeFrameType) : MarketProfileAction
    data class OnTechnicalChartTypeChanged(val type: TechnicalChartType) : MarketProfileAction
    data object OnToggleFavorite : MarketProfileAction
    data class OnToggleChangedFavorite(val assetName:String, val isFavorite : Boolean) : MarketProfileAction
    data class OnSimpleChartTimeFrameChanged(val timeFrame: ChartTimeFrame) : MarketProfileAction
    data object OnNavigateToSettings : MarketProfileAction
    data object OnNavigateNotification : MarketProfileAction
}