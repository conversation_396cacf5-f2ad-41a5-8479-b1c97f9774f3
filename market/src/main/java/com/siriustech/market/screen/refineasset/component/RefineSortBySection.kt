package com.siriustech.market.screen.refineasset.component

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.ExperimentalLayoutApi
import androidx.compose.foundation.layout.FlowRow
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import com.siriustech.merit.app_common.component.button.ToggleThirdButton
import com.siriustech.merit.app_common.component.button.ToggleThirdButtonProperties
import com.siriustech.merit.app_common.ext.colorTxtTitle
import com.siriustech.merit.app_common.theme.LocalAppColor
import com.siriustech.merit.app_common.theme.LocalDimens
import com.siriustech.merit.app_common.theme.LocalTypography
import com.siriustech.merit.app_common.typeenum.MarketCategoryType
import com.siriustech.merit.app_common.typeenum.RefineAssetSortingType
import timber.log.Timber
import com.siriustech.merit.app_common.R as AppCommonR

@OptIn(ExperimentalLayoutApi::class)
@Composable
fun RefineSortBySection(
    type: String = MarketCategoryType.CASH_EQUIVALENT.value,
    value: RefineAssetSortingType? = null,
    onSortTypeChanged: (RefineAssetSortingType) -> Unit = {},
) {
    Timber.d("RefineSortBySection ${value}")
    val isSortingByRiskLevel = listOf(
        MarketCategoryType.MEDIUM.value,
        MarketCategoryType.HIGH.value,
        MarketCategoryType.LOW.value
    ).contains(type)
    val row = 2
    val column = if (isSortingByRiskLevel) 2 else 3
    Text(
        text = stringResource(id = AppCommonR.string.key0460),
        style = LocalTypography.current.text14.medium.colorTxtTitle()
    )
    FlowRow(
        modifier = Modifier
            .padding(top = LocalDimens.current.dimen8)
            .fillMaxWidth()
            .background(LocalAppColor.current.bgDefault),
        horizontalArrangement = Arrangement.spacedBy(LocalDimens.current.dimen4),
        maxItemsInEachRow = 2,
    ) {
        val items = if (isSortingByRiskLevel) {
            arrayListOf(
                ToggleThirdButtonProperties(
                    text = stringResource(id = AppCommonR.string.key0461),
                    startIcon = painterResource(id = AppCommonR.drawable.ic_exchange_price_up),
                ),
                ToggleThirdButtonProperties(
                    text = stringResource(id = AppCommonR.string.key0461),
                    startIcon = painterResource(id = AppCommonR.drawable.ic_exchange_price_down),
                ),
                ToggleThirdButtonProperties(
                    text = stringResource(id = AppCommonR.string.key0464),
                ),
                ToggleThirdButtonProperties(
                    text = stringResource(id = AppCommonR.string.key0465),
                )
            )
        } else {
            arrayListOf(
                ToggleThirdButtonProperties(
                    text = stringResource(id = AppCommonR.string.key0461),
                    startIcon = painterResource(id = AppCommonR.drawable.ic_exchange_price_up),
                ),
                ToggleThirdButtonProperties(
                    text = stringResource(id = AppCommonR.string.key0461),
                    startIcon = painterResource(id = AppCommonR.drawable.ic_exchange_price_down),
                ),
                ToggleThirdButtonProperties(
                    text = stringResource(id = AppCommonR.string.key0462),

                    ),
                ToggleThirdButtonProperties(
                    text = stringResource(id = AppCommonR.string.key0463),
                ),
                ToggleThirdButtonProperties(
                    text = stringResource(id = AppCommonR.string.key0464),
                ),
                ToggleThirdButtonProperties(
                    text = stringResource(id = AppCommonR.string.key0465),
                )
            )
        }


        repeat(row * column) { index ->
            ToggleThirdButton(
                onSelectedChanged = { onSortTypeChanged(RefineAssetSortingType.entries[index]) },
                isSelectedValue = value == RefineAssetSortingType.entries[index],
                modifier = Modifier.weight(1f),
                properties = items[index]
            )
        }
    }
}