package com.siriustech.market.screen.marketprofile.component

import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import com.siriustech.market.domain.display.AssetProfileDetailsDisplayData
import com.siriustech.market.screen.marketprofile.component.financial.FinancialInformation
import com.siriustech.merit.app_common.R
import com.siriustech.merit.app_common.component.button.AccentButton
import com.siriustech.merit.app_common.component.button.ButtonProperties
import com.siriustech.merit.app_common.component.container.LabelAmountCurrency
import com.siriustech.merit.app_common.component.container.LabelAmountPercentage
import com.siriustech.merit.app_common.component.container.LabelStarRating
import com.siriustech.merit.app_common.component.container.LabelValue
import com.siriustech.merit.app_common.component.separator.SeparatorLine
import com.siriustech.merit.app_common.ext.colorTxtTitle
import com.siriustech.merit.app_common.theme.LocalDimens
import com.siriustech.merit.app_common.theme.LocalTypography
import com.siriustech.merit.app_common.typeenum.MarketAssetType

/**
 * Created by Hein Htet
 */

@Composable
fun AssetProfileDetails(
    modifier: Modifier = Modifier,
    onNavigateToFundFactSheet: (url: String) -> Unit = {},
    data: AssetProfileDetailsDisplayData,
) {
    Column(
        modifier = Modifier
            .fillMaxWidth()
            .padding(horizontal = LocalDimens.current.dimen12)
            .then(modifier)
    ) {
        Text(
            text = stringResource(id = R.string.key0529),
            style = LocalTypography.current.text14.medium.colorTxtTitle()
        )
        when (data.allocationClass) {
            MarketAssetType.HK_EQUITY, MarketAssetType.US_EQUITY -> USHKAssetProfileDetails(data)
            MarketAssetType.MUTUAL_FUNDS -> MutualFundAssetProfileDetails(
                data,
                onNavigateToFundFactSheet = onNavigateToFundFactSheet
            )

            MarketAssetType.PRIVATE_EQUITY_FUNDS -> PrivateFundAssetProfileDetails(data)
            MarketAssetType.BONDS -> BondProfileDetails(data)
            MarketAssetType.STRUCTURED_PRODUCTS -> StructureProductDetails(data)
            else -> {}
        }
    }
}


@Composable
fun USHKAssetProfileDetails(data: AssetProfileDetailsDisplayData) {
    val context = LocalContext.current
    val valueTextStyle = LocalTypography.current.text14.regular.colorTxtTitle()
    val itemModifier = Modifier.padding(top = LocalDimens.current.dimen8)
    val xCurrency = "x"

    LabelAmountCurrency(
        label = stringResource(id = R.string.key0521),
        amount = data.marketCap,
        modifier = itemModifier,
        amountTextStyle = LocalTypography.current.text14.light.colorTxtTitle(),
        currency = data.currency
    )

    LabelAmountCurrency(
        label = stringResource(id = R.string.key0522),
        amount = data.priceEarnings,
        modifier = itemModifier,
        amountTextStyle = valueTextStyle,
        currency = xCurrency
    )

    LabelAmountCurrency(
        label = stringResource(id = R.string.key0523),
        amount = data.priceSales,
        modifier = itemModifier,
        amountTextStyle = valueTextStyle,
        currency = xCurrency
    )

    LabelAmountCurrency(
        label = stringResource(id = R.string.key0524),
        amount = data.priceBook,
        modifier = itemModifier,
        amountTextStyle = valueTextStyle,
        currency = xCurrency
    )

    LabelAmountPercentage(
        label = stringResource(id = R.string.key0525),
        amount = data.dividendYield,
        modifier = itemModifier,
        amountTextStyle = valueTextStyle,
    )

    LabelValue(
        label = stringResource(id = R.string.key0526),
        value = data.exDividendDate,
        modifier = itemModifier,
        valueTextStyle = valueTextStyle
    )

    LabelValue(
        label = stringResource(id = R.string.key0527),
        value = data.country,
        valueTextStyle = valueTextStyle,
        modifier = itemModifier,
//        flagPainter = painterResource(
//            id = R.drawable.ic_flag_us
//        )
    )

    LabelValue(
        label = stringResource(id = R.string.key0530),
        value = data.sector,
        modifier = itemModifier,
        valueTextStyle = valueTextStyle
    )

    LabelValue(
        label = stringResource(id = R.string.key0322),
        value = data.industry,
        modifier = itemModifier,
        valueTextStyle = valueTextStyle
    )
    SeparatorLine(modifier = Modifier.padding(vertical = LocalDimens.current.dimen16))
    FinancialInformation(data.financialInfoDisplay,data.currency)
    SeparatorLine(modifier = Modifier.padding(vertical = LocalDimens.current.dimen16))
    AssetDescription(description = data.description)
}


@Composable
fun MutualFundAssetProfileDetails(
    data: AssetProfileDetailsDisplayData,
    onNavigateToFundFactSheet: (url: String) -> Unit = {},
) {
    val context = LocalContext.current
    val valueTextStyle = LocalTypography.current.text14.regular.colorTxtTitle()
    val itemModifier = Modifier.padding(top = LocalDimens.current.dimen8)

    LabelValue(
        label = stringResource(id = R.string.key0531),
        value = data.investmentObjective,
        modifier = itemModifier,
        valueTextStyle = valueTextStyle,
    )

    LabelValue(
        label = stringResource(id = R.string.key0532),
        value = data.investmentFocus,
        modifier = itemModifier,
        valueTextStyle = valueTextStyle,
    )

    LabelAmountPercentage(
        label = stringResource(id = R.string.key0533),
        amount = data.dividend,
        modifier = itemModifier,
        amountTextStyle = valueTextStyle,
    )

    LabelAmountCurrency(
        label = stringResource(id = R.string.key0534),
        amount = data.minimumInvestment,
        modifier = itemModifier,
        amountTextStyle = valueTextStyle,
        currency = data.currency
    )

    LabelAmountCurrency(
        label = stringResource(id = R.string.key0538),
        amount = data.minimumAdditionalInvestment,
        modifier = itemModifier,
        amountTextStyle = valueTextStyle,
        currency = data.currency
    )

    LabelStarRating(
        properties = data.rating.copy(label = stringResource(id = R.string.key0535)),
        modifier = itemModifier,
        valueTextStyle = valueTextStyle
    )

    LabelStarRating(
        properties = data.performanceRating.copy(label = stringResource(id = R.string.key0536)),
        modifier = itemModifier,
        valueTextStyle = valueTextStyle
    )

    LabelStarRating(
        properties = data.returnRating.copy(label = stringResource(id = R.string.key0537)),
        modifier = itemModifier,
        valueTextStyle = valueTextStyle
    )

    AccentButton(
        onClicked = { onNavigateToFundFactSheet(data.fundFeatSheet) },
        modifier = itemModifier,
        properties = ButtonProperties(
            text = stringResource(id = R.string.key0539),
            icon = painterResource(id = R.drawable.ic_sheet_black),
            textStyle = LocalTypography.current.text14.medium.colorTxtTitle(),
        )
    )
    SeparatorLine(modifier = Modifier.padding(vertical = LocalDimens.current.dimen16))
    FundTotalReturnBarChart(data.fundTotalReturnList)
    SeparatorLine(modifier = Modifier.padding(vertical = LocalDimens.current.dimen16))
    FundAllocationPieChart(items = data.fundAssetAllocation)
    SeparatorLine(modifier = Modifier.padding(vertical = LocalDimens.current.dimen16))
    AssetDescription(description = data.description)
}

@Composable
fun PrivateFundAssetProfileDetails(data: AssetProfileDetailsDisplayData) {
    val valueTextStyle = LocalTypography.current.text14.regular.colorTxtTitle()
    val itemModifier = Modifier.padding(top = LocalDimens.current.dimen8)

    LabelValue(
        label = stringResource(id = R.string.key0532),
        value = data.investmentFocus,
        modifier = itemModifier,
        valueTextStyle = valueTextStyle
    )

    LabelValue(
        label = stringResource(id = R.string.key0543),
        value = data.geographicFocus,
        modifier = itemModifier,
        valueTextStyle = valueTextStyle,
    )

    LabelValue(
        label = stringResource(id = R.string.key0544),
        value = data.investmentStrategy,
        modifier = itemModifier,
        valueTextStyle = valueTextStyle,
    )

    LabelStarRating(
        properties = data.performanceRating.copy(label = stringResource(id = R.string.key0536)),
        modifier = itemModifier,
        valueTextStyle = valueTextStyle
    )

    LabelStarRating(
        properties = data.returnRating.copy(label = stringResource(id = R.string.key0537)),
        modifier = itemModifier,
        valueTextStyle = valueTextStyle
    )

    AccentButton(
        onClicked = {},
        modifier = itemModifier,
        properties = ButtonProperties(
            text = stringResource(id = R.string.key0539),
            icon = painterResource(id = R.drawable.ic_sheet_black),
            textStyle = LocalTypography.current.text14.medium.colorTxtTitle(),
        )
    )
    SeparatorLine(modifier = Modifier.padding(vertical = LocalDimens.current.dimen16))
    FundTotalReturnBarChart(data.fundTotalReturnList)
    SeparatorLine(modifier = Modifier.padding(vertical = LocalDimens.current.dimen16))
    FundAllocationPieChart(items = data.fundAssetAllocation)
    SeparatorLine(modifier = Modifier.padding(vertical = LocalDimens.current.dimen16))
    AssetDescription(description = data.description)
}

@Composable
fun BondProfileDetails(data: AssetProfileDetailsDisplayData) {
    val context = LocalContext.current
    val valueTextStyle = LocalTypography.current.text14.regular.colorTxtTitle()
    val itemModifier = Modifier.padding(top = LocalDimens.current.dimen8)

    LabelAmountPercentage(
        label = stringResource(id = R.string.key0547),
        amount = data.couponRate,
        modifier = itemModifier,
        amountTextStyle = valueTextStyle,
    )

    LabelAmountPercentage(
        label = stringResource(id = R.string.key0548),
        amount = data.couponYield,
        modifier = itemModifier,
        amountTextStyle = valueTextStyle,
    )


    LabelValue(
        label = stringResource(id = R.string.key0549),
        value = data.creditRating,
        modifier = itemModifier,
        valueTextStyle = valueTextStyle,
    )
    LabelValue(
        label = stringResource(id = R.string.key0550),
        value = data.maturityDate,
        modifier = itemModifier,
        valueTextStyle = valueTextStyle,
    )
    SeparatorLine(modifier = Modifier.padding(vertical = LocalDimens.current.dimen16))
    AssetDescription(description = data.description)
}


@Composable
fun StructureProductDetails(data: AssetProfileDetailsDisplayData) {
    val valueTextStyle = LocalTypography.current.text14.regular.colorTxtTitle()
    val itemModifier = Modifier.padding(top = LocalDimens.current.dimen8)

    LabelValue(
        label = stringResource(id = R.string.key0551),
        value = data.underlyingAssets,
        modifier = itemModifier,
        valueTextStyle = valueTextStyle,
    )

    LabelValue(
        label = stringResource(id = R.string.key0552),
        value = data.principalProtection,
        modifier = itemModifier,
        valueTextStyle = valueTextStyle,
    )

    LabelValue(
        label = stringResource(id = R.string.key0553),
        value = data.maturityDate,
        modifier = itemModifier,
        valueTextStyle = valueTextStyle,
    )

    LabelAmountPercentage(
        label = stringResource(id = R.string.key0547),
        amount = data.couponRate,
        modifier = itemModifier,
        amountTextStyle = valueTextStyle
    )

    LabelAmountPercentage(
        label = stringResource(id = R.string.key0548),
        amount = data.couponYield,
        modifier = itemModifier,
        amountTextStyle = valueTextStyle
    )

    LabelValue(
        label = stringResource(id = R.string.key0555),
        value = data.barrierLevel,
        modifier = itemModifier,
        valueTextStyle = valueTextStyle,
    )

    LabelValue(
        label = stringResource(id = R.string.key0556),
        value = data.redemptionTerms,
        modifier = itemModifier,
        valueTextStyle = valueTextStyle,
    )

    LabelValue(
        label = stringResource(id = R.string.key0513),
        value = data.currency,
        modifier = itemModifier,
        valueTextStyle = valueTextStyle,
    )

    LabelValue(
        label = stringResource(id = R.string.key0557),
        value = data.structureDescription,
        modifier = itemModifier
            .then(Modifier),
        valueTextStyle = valueTextStyle,
    )

    LabelAmountCurrency(
        label = stringResource(id = R.string.key0558),
        amount = data.strikePrice,
        modifier = itemModifier,
        amountTextStyle = valueTextStyle,
        currency = data.currency
    )

    LabelAmountPercentage(
        label = stringResource(id = R.string.key0559),
        amount = data.initialPrice,
        modifier = itemModifier,
        amountTextStyle = valueTextStyle,
    )

    LabelAmountCurrency(
        label = stringResource(id = R.string.key0560),
        amount = data.issuePrice,
        modifier = itemModifier,
        amountTextStyle = valueTextStyle,
        currency = data.currency
    )

    LabelAmountCurrency(
        label = stringResource(id = R.string.key0561),
        amount = data.returnCapInvestment,
        modifier = itemModifier,
        amountTextStyle = valueTextStyle,
        currency = "x"
    )

    LabelAmountCurrency(
        label = stringResource(id = R.string.key0562),
        amount = data.principalReturnMaturity,
        modifier = itemModifier,
        amountTextStyle = valueTextStyle,
        currency = "x"
    )
    SeparatorLine(modifier = Modifier.padding(vertical = LocalDimens.current.dimen16))
    AssetDescription(description = data.description)
}