package com.siriustech.market.screen.marketprofile.page.marketdata

import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.BoxWithConstraints
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.heightIn
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.verticalScroll
import androidx.compose.runtime.Composable
import androidx.compose.runtime.collectAsState
import androidx.compose.ui.Modifier
import androidx.compose.ui.unit.dp
import com.siriustech.market.domain.display.MarketQuoteDisplayData
import com.siriustech.market.screen.marketprofile.MarketProfileViewModel
import com.siriustech.market.screen.marketprofile.component.CurrentSession
import com.siriustech.market.screen.marketprofile.component.LatestNavStatistics
import com.siriustech.merit.app_common.component.container.PaddingTop
import com.siriustech.merit.app_common.component.separator.SeparatorLine
import com.siriustech.merit.app_common.theme.LocalDimens
import com.siriustech.merit.app_common.typeenum.MarketAssetType

/**
 * Created by Hein Htet
 */

@Composable
fun MarketDataPage(viewModel: MarketProfileViewModel) {
    val scrollState = rememberScrollState()
    val marketQuoteDisplayData = viewModel.outputs.marketQuoteDisplayData.collectAsState()
    BoxWithConstraints(
        modifier = Modifier
            .fillMaxSize()
            .padding(0.dp)
    ) {
        Column(
            modifier = Modifier
                .heightIn(min = maxHeight)
                .verticalScroll(scrollState)
        ) {
            PaddingTop(value = LocalDimens.current.dimen16)
            MarketProfileChart(viewModel = viewModel, modifier = Modifier)
            SeparatorLine(modifier = Modifier.padding(vertical = LocalDimens.current.dimen16))
            MarketQuoteBodyContent(marketQuoteDisplayData.value)
        }
    }
}

@Composable
fun MarketQuoteBodyContent(marketQuoteDisplayData: MarketQuoteDisplayData = MarketQuoteDisplayData()) {
    Box(modifier = Modifier
        .padding(horizontal = LocalDimens.current.dimen12)){
        when (marketQuoteDisplayData.marketAssetType) {
            MarketAssetType.US_EQUITY, MarketAssetType.HK_EQUITY -> {
                CurrentSession(
                    data = marketQuoteDisplayData.currentSessionDisplayModel,
                )
            }

            else -> {
                LatestNavStatistics(
                    modifier = Modifier.padding(horizontal = LocalDimens.current.dimen12),
                )
            }
        }
    }
}
