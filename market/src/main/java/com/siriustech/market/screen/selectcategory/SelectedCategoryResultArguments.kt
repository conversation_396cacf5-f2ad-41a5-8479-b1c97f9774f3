package com.siriustech.market.screen.selectcategory

import com.siriustech.merit.app_common.data.display.AssetClassModel
import com.siriustech.merit.app_common.typeenum.MarketCategoryType
import kotlinx.serialization.Serializable

/**
 * Created by <PERSON><PERSON>
 */
@Serializable
data class SelectedCategoryResultArguments(
    val other: String? = null,
    val title: String? = null,
    val selectedValue : String
) : java.io.Serializable