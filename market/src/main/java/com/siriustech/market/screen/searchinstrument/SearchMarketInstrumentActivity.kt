package com.siriustech.market.screen.searchinstrument

import android.os.Bundle
import androidx.activity.compose.setContent
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.toArgb
import androidx.navigation.compose.rememberNavController
import com.siriustech.core_ui_compose.ext.ChangeSystemBarsTheme
import com.siriustech.merit.app_common.theme.AppComposeActivity
import com.siriustech.merit.app_common.theme.AppScreen
import com.siriustech.merit.app_common.theme.AppViewModel
import dagger.hilt.android.AndroidEntryPoint

/**
 * Created by <PERSON><PERSON>tet
 */
@AndroidEntryPoint
class SearchMarketInstrumentActivity : AppComposeActivity() {


    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContent {
            ChangeSystemBarsTheme(lightTheme = true, Color.White.toArgb())
            AppScreen(vm = AppViewModel()) {
                SearchNavGraph(navController = rememberNavController())
            }
        }
    }
    companion object {
        const val RESULT_EXTRA_ASSET_DATA = "RESULT_EXTRA_ASSET_DATA"
        const val EXTRA_NEED_RESULT_DATA = "EXTRA_NEED_RESULT_DATA"
    }
}