package com.siriustech.market.screen.assetListLanding

import android.app.Activity
import androidx.activity.compose.rememberLauncherForActivityResult
import androidx.activity.result.ActivityResult
import androidx.activity.result.contract.ActivityResultContracts
import androidx.compose.animation.AnimatedVisibility
import androidx.compose.animation.core.tween
import androidx.compose.animation.fadeIn
import androidx.compose.animation.fadeOut
import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.Text
import androidx.compose.material3.pulltorefresh.pullToRefresh
import androidx.compose.material3.pulltorefresh.rememberPullToRefreshState
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.fragment.app.FragmentActivity
import androidx.hilt.navigation.compose.hiltViewModel
import androidx.lifecycle.Lifecycle
import androidx.lifecycle.compose.LifecycleEventEffect
import androidx.navigation.NavController
import androidx.navigation.compose.rememberNavController
import com.siriustech.core_ui_compose.base.SingleEventEffect
import com.siriustech.market.component.MarketAssetList
import com.siriustech.market.navigation.MarketNavigationEntryPoint
import com.siriustech.market.screen.refineasset.RefineAssetListActivity.Companion.RESULT_EXTRA_REFINE_RESULT
import com.siriustech.market.screen.refineasset.RefineAssetListArguments
import com.siriustech.market.screen.selectcategory.SelectMarketCategoryActivity
import com.siriustech.market.screen.selectcategory.SelectedCategoryResultArguments
import com.siriustech.merit.app_common.component.common.FilterTags
import com.siriustech.merit.app_common.component.container.PaddingStart
import com.siriustech.merit.app_common.component.container.PaddingTop
import com.siriustech.merit.app_common.component.header.DashboardToolbar
import com.siriustech.merit.app_common.component.header.DashboardToolbarProperties
import com.siriustech.merit.app_common.component.loading.HorizontalProgressBar
import com.siriustech.merit.app_common.component.marquee.StockExchangeMarquee
import com.siriustech.merit.app_common.component.modalbts.ExchangeCurrencyModalBottomSheet
import com.siriustech.merit.app_common.ext.capitalizeWords
import com.siriustech.merit.app_common.ext.colorTxtTitle
import com.siriustech.merit.app_common.ext.noRippleClickable
import com.siriustech.merit.app_common.navigation.InstrumentSearch
import com.siriustech.merit.app_common.navigation.argument.market.MarketProfileArgument
import com.siriustech.merit.app_common.theme.AppScreen
import com.siriustech.merit.app_common.theme.LocalAppColor
import com.siriustech.merit.app_common.theme.LocalDimens
import com.siriustech.merit.app_common.theme.LocalTypography
import com.siriustech.merit.app_common.typeenum.MarketCategoryType
import com.siriustech.merit.app_common.typeenum.UIConfig
import dagger.hilt.android.EntryPointAccessors
import timber.log.Timber
import com.siriustech.merit.app_common.R as AppCommonR

/**
 * Created by Hein Htet
 */

@Composable
fun MarketScreen(
    navController: NavController,
    viewModel: MarketViewModel = hiltViewModel(),
) {
    AppScreen(vm = viewModel, ignorePaddingValue = true) {
        MarketScreenContent(navController, viewModel)
    }
}

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun MarketScreenContent(navController: NavController, viewModel: MarketViewModel) {
    val showLoading = viewModel.outputs.showLoading.collectAsState()
    val exchangeMarqueeData = viewModel.outputs.displayStockExchangeItems
    val displayMarketAssetListItems = viewModel.outputs.displayMarketAssetListItems
    val pullToRefreshState = rememberPullToRefreshState()
    var showCurrencyExchangeModal by remember {
        mutableStateOf(false)
    }

    val context = LocalContext.current


    LaunchedEffect(Unit) {
        viewModel.inputs.setDefaultMarketCategory(
            SelectedCategoryResultArguments(
                title = context.getString(AppCommonR.string.key0402),
                selectedValue = MarketCategoryType.CASH_EQUIVALENT.value
            ), context
        )
        viewModel.inputs.onApiCall()
    }

    LifecycleEventEffect(Lifecycle.Event.ON_RESUME) {
        viewModel.inputs.onStartPeriodicPriceUpdates()
    }

    LifecycleEventEffect(Lifecycle.Event.ON_PAUSE) {
        viewModel.inputs.onStopPeriodicPriceUpdates()
    }


    val activity = LocalContext.current as FragmentActivity

    val navigation = remember {
        EntryPointAccessors.fromApplication(activity, MarketNavigationEntryPoint::class.java)
            .marketNavigation()
    }


    val marketCategorySelectionResultCallback =
        rememberLauncherForActivityResult(ActivityResultContracts.StartActivityForResult()) { result: ActivityResult ->
            if (result.resultCode == Activity.RESULT_OK) {
                val resultArgs =
                    result.data?.getSerializableExtra(SelectMarketCategoryActivity.RESULT_EXTRA_SELECT_CATEGORY) as? SelectedCategoryResultArguments
                if (resultArgs != null) {
                    viewModel.inputs.updateSelectedMarketCategoryType(resultArgs)
                }
            }
        }

    val refineAssetListResultCallback =
        rememberLauncherForActivityResult(ActivityResultContracts.StartActivityForResult()) { result ->
            if (result.resultCode == Activity.RESULT_OK) {
                val resultArgs =
                    result.data?.getSerializableExtra(RESULT_EXTRA_REFINE_RESULT) as? RefineAssetListArguments?
                Timber.d("RESULT_EXTRA_REFINE_RESULT: $resultArgs")
                resultArgs?.let {
                    viewModel.onTriggerActions(
                        MarketAction.OnRefineFilterApply(
                            resultArgs,
                            context
                        )
                    )
                }
            }
        }

    val instrumentSearchResultCallback =
        rememberLauncherForActivityResult(ActivityResultContracts.StartActivityForResult()) { result ->
            if (result.resultCode == Activity.RESULT_OK) {

            }
        }

    SingleEventEffect(sideEffectFlow = viewModel.appAction) {

        when (it) {
            is MarketAction.OnToggleCurrencyPairModal -> {
                showCurrencyExchangeModal = it.show
            }

            is MarketAction.OnNavigateToMarketCategorySelection -> {
                navigation.onNavigateToSelectMarketCategoryActivity(
                    marketCategorySelectionResultCallback,
                    activity,
                    viewModel.outputs.selectedMarketCategoryType.value
                )
            }

            is MarketAction.OnNavigateToRefineAssetList -> {
                navigation.onNavigateToRefineAssetListActivity(
                    activity,
                    arguments = viewModel.outputs.refineFilterModel.value?.copy(
                        refineModel = viewModel.outputs.filterModel.copy(
                            marketCategoryType = viewModel.outputs.selectedMarketCategoryType.value,
                            selectedAllocationClass = viewModel.outputs.selectedAssetClass.value
                        ),
                    ),
                    refineAssetListResultCallback
                )
            }

            is MarketAction.OnMarketAssetClick -> {
                navigation.onNavigateToMarketProfile(
                    navController = navController,
                    args = MarketProfileArgument(data = it.data)
                )
            }

            is MarketAction.OnNavigateToSettings -> {
                navigation.onNavigateToSettings(activity)
            }

            is MarketAction.OnNavigateToChat -> {
                navigation.onNavigateToChatActivity(activity)
            }
        }
    }

    Column {
        DashboardToolbar(
            properties = DashboardToolbarProperties(showBackArrow = false),
            onSearchInstrument = {
//                navigation.onNavigateToInstrumentSearchActivity(activity)
                navController.navigate(InstrumentSearch)
            }, onNotificationClicked = {
                navigation.onNavigateToNotification(activity)
            }, onSettingsClicked = {
                viewModel.onTriggerActions(MarketAction.OnNavigateToSettings)
            }, onChatIconClicked = {
                viewModel.onTriggerActions(MarketAction.OnNavigateToChat)
            })

        Box(modifier = Modifier.fillMaxWidth()) {
            HorizontalProgressBar(visible = showLoading.value)
            StockExchangeMarquee(modifier = Modifier.padding(top = LocalDimens.current.dimen12),
                items = exchangeMarqueeData,
                onItemClick = {
                    viewModel.onTriggerActions(MarketAction.OnToggleCurrencyPairModal(show = true))
                })
        }
        Column(
            modifier = Modifier
                .padding(top = LocalDimens.current.dimen18)
                .fillMaxSize()
                .pullToRefresh(
                    showLoading.value,
                    state = pullToRefreshState,
                    enabled = true,
                    onRefresh = {
                        viewModel.inputs.onApiCall()
                    })
        ) {
            MarketHeaderActions(viewModel)
            androidx.compose.animation.AnimatedVisibility(
                enter = fadeIn(initialAlpha = 0.4f),
                exit = fadeOut(animationSpec = tween(durationMillis = 250)),
                visible = viewModel.outputs.refineFilterTags.isNotEmpty()
            ) {
                FilterTags(
                    items = viewModel.outputs.refineFilterTags,
                    modifier = Modifier.padding(horizontal = LocalDimens.current.dimen12)
                )
            }
            PaddingTop(value = LocalDimens.current.dimen6)
            MarketAssetList(
                displayMarketAssetListItems,
                emptyText = stringResource(id = AppCommonR.string.key0457),
                onAction = { viewModel.onTriggerActions(it) })
        }
    }

    if (showCurrencyExchangeModal) {
        ExchangeCurrencyModalBottomSheet(items = exchangeMarqueeData, onDismissed = {
            viewModel.onTriggerActions(MarketAction.OnToggleCurrencyPairModal(show = false))
        })
    }
}


@Composable
fun MarketHeaderActions(viewModel: MarketViewModel) {
    val selectedCategoryDisplayTitle =
        viewModel.outputs.selectedCategoryDisplayTitle.collectAsState()
    val isRefineFilterEnable = viewModel.outputs.displayRefineIcon.collectAsState()


    Row(
        horizontalArrangement = Arrangement.SpaceBetween,
        modifier = Modifier
            .padding(horizontal = LocalDimens.current.dimen12)
            .fillMaxWidth()
            .background(LocalAppColor.current.bgDefault)
    ) {
        Box(
            modifier = Modifier
                .clip(RoundedCornerShape(UIConfig.TOGGLE_BUTTON_CORNER_RADIUS.dp))
                .background(LocalAppColor.current.bgTone)
                .padding(
                    horizontal = LocalDimens.current.dimen12,
                    vertical = LocalDimens.current.dimen14
                )
                .noRippleClickable { viewModel.onTriggerActions(MarketAction.OnNavigateToMarketCategorySelection) }
        ) {
            Row(verticalAlignment = Alignment.CenterVertically) {
                Text(
                    text = selectedCategoryDisplayTitle.value.capitalizeWords(),
                    style = LocalTypography.current.text14.semiBold.colorTxtTitle()
                )
                PaddingStart(value = LocalDimens.current.dimen24)
                Image(
                    painter = painterResource(id = AppCommonR.drawable.ic_right_arrow),
                    contentDescription = "Arrow ImageResource"
                )
            }
        }

        AnimatedVisibility(
            visible = isRefineFilterEnable.value,
            enter = fadeIn(initialAlpha = 0.4f),
            exit = fadeOut(animationSpec = tween(durationMillis = 250)),
        ) {
            Box(
                modifier = Modifier
                    .background(LocalAppColor.current.bgTone)
                    .size(LocalDimens.current.dimen44)
                    .padding(
                        horizontal = LocalDimens.current.dimen12,
                        vertical = LocalDimens.current.dimen14
                    )
                    .clip(RoundedCornerShape(LocalDimens.current.dimen8))
                    .noRippleClickable { viewModel.onTriggerActions(MarketAction.OnNavigateToRefineAssetList) },
                contentAlignment = Alignment.Center
            ) {
                Image(
                    painter = painterResource(id = AppCommonR.drawable.ic_filter_black),
                    contentDescription = "Filter Image Resource"
                )
            }
        }
    }
}

@Preview(showBackground = true)
@Composable
fun PreviewMarketScreen() {
    MarketScreen(navController = rememberNavController(), viewModel = hiltViewModel())
}

