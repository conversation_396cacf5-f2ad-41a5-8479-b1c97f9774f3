package com.siriustech.market.screen.marketprofile.component

import android.graphics.Canvas
import android.graphics.Color
import android.graphics.LinearGradient
import android.graphics.Paint
import android.graphics.Shader
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.remember
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.toArgb
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.unit.dp
import androidx.compose.ui.viewinterop.AndroidView
import androidx.core.content.res.ResourcesCompat
import com.core.util.toAmount
import com.github.mikephil.charting.charts.BarChart
import com.github.mikephil.charting.components.XAxis
import com.github.mikephil.charting.components.YAxis
import com.github.mikephil.charting.data.BarData
import com.github.mikephil.charting.data.BarDataSet
import com.github.mikephil.charting.data.BarEntry
import com.github.mikephil.charting.formatter.ValueFormatter
import com.github.mikephil.charting.interfaces.datasets.IBarDataSet
import com.github.mikephil.charting.renderer.BarChartRenderer
import com.github.mikephil.charting.utils.ViewPortHandler
import com.siriustech.merit.app_common.R
import com.siriustech.merit.app_common.component.chart.AppBarChart
import com.siriustech.merit.app_common.component.chart.renderer.RoundedBarChartRenderer
import com.siriustech.merit.app_common.component.container.PaddingStart
import com.siriustech.merit.app_common.ext.colorTxtParagraph
import com.siriustech.merit.app_common.ext.colorTxtTitle
import com.siriustech.merit.app_common.ext.displayPriceChangeRate
import com.siriustech.merit.app_common.theme.LocalAppColor
import com.siriustech.merit.app_common.theme.LocalDimens
import com.siriustech.merit.app_common.theme.LocalTypography
import com.siriustech.merit.app_common.R as AppCommonR

/**
 * Created by Hein Htet
 */

@Composable
fun FundTotalReturnBarChart(items: List<FundTotalReturnBarChartData> = listOf()) {
    val context = LocalContext.current
    val barChart = remember {
        BarChart(context)
    }

    val typeface = remember {
        ResourcesCompat.getFont(context, R.font.noto_sans_medium)
    }
    val xAxisTypeface = remember {
        ResourcesCompat.getFont(context, R.font.noto_sans_light)
    }
    val entries = ArrayList<BarEntry>()
    items.forEachIndexed { index, item ->
        entries.add(BarEntry(index.toFloat(), item.percentage.toFloat()))
    }

    val barDataSet = BarDataSet(entries, "Total Return Data Set").apply {
        this.setDrawValues(true)
        this.setDrawIcons(false)
    }

    barDataSet.setColors(
        *entries.map { if (it.y >= 0) LocalAppColor.current.txtPositive.toArgb() else LocalAppColor.current.txtNegative.toArgb() }
            .toIntArray()
    )
    val valueTextColors =
        entries.map { if (it.y >= 0) LocalAppColor.current.txtPositive.toArgb() else LocalAppColor.current.txtNegative.toArgb() }
    barDataSet.setValueTextColors(valueTextColors)
    barDataSet.valueTextSize = 12f
    barDataSet.valueTypeface = typeface
    val barData = BarData(barDataSet)
    barData.barWidth = 0.8f
    barDataSet.valueFormatter = object : ValueFormatter() {
        override fun getFormattedValue(value: Float): String {
            return value.toAmount(4).displayPriceChangeRate()
        }
    }
    barData.addDataSet(barDataSet)
    val xAxisTextColor = LocalAppColor.current.txtInactive.toArgb()
    Column(modifier = Modifier.fillMaxWidth()) {
        Row(verticalAlignment = Alignment.CenterVertically) {
            Text(
                text = stringResource(id = AppCommonR.string.key0540),
                style = LocalTypography.current.text14.medium.colorTxtTitle()
            )
            PaddingStart(value = LocalDimens.current.dimen4)
            Text("%", style = LocalTypography.current.text14.regular.colorTxtParagraph())
        }
        Box(
            modifier = Modifier
                .fillMaxWidth()
                .height(250.dp)
        ) {
            AndroidView(
                modifier = Modifier.fillMaxSize(),
                factory = {
                    barChart.apply {
                        setDrawGridBackground(false)
                        setDrawBorders(false)
                        setDrawMarkers(true)
                        isDoubleTapToZoomEnabled = false
                        setPinchZoom(false)
                        axisRight.setDrawLabels(false)
                        isHighlightPerTapEnabled = false
                        axisRight.setPosition(YAxis.YAxisLabelPosition.INSIDE_CHART)
                        axisLeft.setPosition(YAxis.YAxisLabelPosition.INSIDE_CHART)
                        axisRight.setLabelCount(2, true)
                        legend.isEnabled = false
                        description.isEnabled = false
                        axisLeft.setDrawLabels(false)
                        xAxis.setDrawLabels(false)
                        xAxis.setDrawGridLines(false)
                        axisLeft.setDrawGridLines(false)
                        axisRight.setDrawGridLines(false)
                        axisRight.textColor = Color.parseColor("#585858")
                        xAxis.setDrawAxisLine(false)
                        axisLeft.setDrawAxisLine(false)
                        axisRight.setDrawAxisLine(false)
                        axisRight.textSize = 12f
                        axisRight.typeface = typeface
                        axisLeft.textSize = 12f
                        axisLeft.typeface = typeface
                        setFitBars(true)
                        val yAxis = axisLeft
                        yAxis.zeroLineColor = Color.GRAY
                        isHighlightPerDragEnabled = false
                        yAxis.setDrawZeroLine(true)
                        axisRight.isEnabled = false
                        xAxis.isEnabled = true
                        xAxis.setDrawLabels(true)
                        xAxis.valueFormatter = object : ValueFormatter() {
                            override fun getFormattedValue(value: Float): String {
                                return if (value >= 0 && items.isNotEmpty()) {
                                     items[value.toInt()].key
                                } else {
                                    ""
                                }
                            }
                        }
                        barChart.renderer = RoundedBarChartRenderer(
                            barChart,
                            barChart.animator,
                            barChart.viewPortHandler,
                        ).apply {
                            setRadius(4)
                        }
                        axisRight.setDrawLabels(false)
                    }
                    barChart
                },
                update = {
                    barChart.xAxis.apply {
                        axisMinimum = -0.5f
                        axisMaximum = entries.size - 0.5f
                        textSize = 12f
                        textColor = xAxisTextColor
                        setTypeface(xAxisTypeface)
                        position = XAxis.XAxisPosition.BOTTOM
                    }
                    barChart.apply {
                        data = barData
                        val allPositive = (items.all { it.percentage.toFloat() > 0 })
                        val allNegative = (items.all { it.percentage.toFloat() < 0 })
                        if (!allNegative && !allPositive) {
                            axisLeft.axisMinimum = -20f
                        }
                        invalidate()
                    }
                }
            )
        }
    }
}

data class FundTotalReturnBarChartData(
    val key: String,
    val percentage: String,
)
