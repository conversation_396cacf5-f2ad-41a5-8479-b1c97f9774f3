package com.siriustech.market.screen.refineasset.component

import androidx.compose.animation.AnimatedVisibility
import androidx.compose.animation.core.tween
import androidx.compose.animation.fadeIn
import androidx.compose.animation.fadeOut
import androidx.compose.foundation.layout.Row
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import com.siriustech.merit.app_common.component.common.FilterTagDisplayModel
import com.siriustech.merit.app_common.component.common.FilterTags
import com.siriustech.merit.app_common.component.container.PaddingStart
import com.siriustech.merit.app_common.component.container.PaddingTop
import com.siriustech.merit.app_common.component.modalbts.ModalListDataContent
import com.siriustech.merit.app_common.component.text.BadgeText
import com.siriustech.merit.app_common.component.textfield.InputBox
import com.siriustech.merit.app_common.component.textfield.InputProperties
import com.siriustech.merit.app_common.ext.colorTxtTitle
import com.siriustech.merit.app_common.ext.noRippleClickable
import com.siriustech.merit.app_common.theme.LocalAppColor
import com.siriustech.merit.app_common.theme.LocalDimens
import com.siriustech.merit.app_common.theme.LocalTypography
import com.siriustech.merit.app_common.R as AppCommonR

/**
 * Created by Hein Htet
 */

@Composable
fun RefineProductCategorySection(
    productCategoryItems: List<ModalListDataContent>,
    onSelectProductCategory: () -> Unit = {},
    onDeleteProductCategory: (item :FilterTagDisplayModel) -> Unit = {},
) {
    Row(verticalAlignment = Alignment.CenterVertically) {
        Text(
            text = stringResource(id = AppCommonR.string.key0475),
            style = LocalTypography.current.text14.medium.colorTxtTitle()
        )
        PaddingStart(value = LocalDimens.current.dimen8)
        BadgeText(
            label = stringResource(id = AppCommonR.string.key0474),
            bgColor = LocalAppColor.current.bgAccent,
            textColor = LocalAppColor.current.txtLabel
        )
    }
    PaddingTop(value = LocalDimens.current.dimen8)
    InputBox(
        inputBoxModifier = Modifier.noRippleClickable { onSelectProductCategory() },
        properties = InputProperties(
            onPickerIconClick = { onSelectProductCategory() },
            placeholder = stringResource(id = AppCommonR.string.key0954),
            editable = false,
            pickerTypeIcon = painterResource(
                id = AppCommonR.drawable.ic_dropdown_arrow
            )
        )
    )
    AnimatedVisibility(
        visible = productCategoryItems.any { it.isSelected },
        enter = fadeIn(initialAlpha = 0.4f),
        exit = fadeOut(animationSpec = tween(durationMillis = 250)),
    ) {
        FilterTags(
            onItemDelete = onDeleteProductCategory,
            items = productCategoryItems.filter { it.isSelected }
                .map { FilterTagDisplayModel(id = it.id, text = it.title) }
        )
    }
}