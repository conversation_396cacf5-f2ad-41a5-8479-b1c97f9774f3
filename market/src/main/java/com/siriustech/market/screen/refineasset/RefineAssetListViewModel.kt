package com.siriustech.market.screen.refineasset

import androidx.compose.runtime.mutableStateListOf
import androidx.compose.runtime.snapshots.SnapshotStateList
import com.siriustech.merit.apilayer.service.authentication.common.eumeration.DropDownEnumeration
import com.siriustech.merit.apilayer.service.authentication.common.eumeration.EnumerationRequest
import com.siriustech.merit.apilayer.service.authentication.common.eumeration.GetEnumerationUseCase
import com.siriustech.merit.app_common.component.modalbts.ModalListDataContent
import com.siriustech.merit.app_common.data.display.MarketCategoryFilterModel
import com.siriustech.merit.app_common.theme.AppAction
import com.siriustech.merit.app_common.theme.AppViewModel
import com.siriustech.merit.app_common.typeenum.RefineAssetSortingType
import com.siriustech.merit.app_common.typeenum.RefineFilterProductType
import dagger.hilt.android.lifecycle.HiltViewModel
import javax.inject.Inject
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.catch
import kotlinx.coroutines.flow.collectLatest
import kotlinx.coroutines.flow.onCompletion
import kotlinx.coroutines.flow.onStart
import kotlinx.coroutines.launch

/**
 * Created by Hein Htet
 */
@HiltViewModel
class RefineAssetListViewModel @Inject constructor(
    private val enumerationUseCase: GetEnumerationUseCase,
) : AppViewModel() {
    private val _showLoading = MutableStateFlow(false)
    private val _selectedRefineListSortingType = MutableStateFlow<RefineAssetSortingType?>(null)
    private val _selectedRefineProductTypes =
        MutableStateFlow<List<RefineFilterProductType>>(emptyList())
    private val _productCategory = mutableStateListOf<ModalListDataContent>()
    private val _selectedProductCategory = mutableStateListOf<ModalListDataContent>()


    init {
        getProductCategory()
    }

    inner class RefineAssetListInputs : BaseInputs() {

        fun initArgument(arguments: RefineAssetListArguments?) {
            arguments?.let {
                _selectedRefineListSortingType.value = it.refineModel.sortingType
                _selectedRefineProductTypes.value = it.refineModel.productTypes
                _selectedProductCategory.clear()
                _selectedProductCategory.addAll(it.refineModel.productCategoryTypes)
            }
        }

        fun setSelectedRefineListSortingType(sortingType: RefineAssetSortingType) {
            if (sortingType == _selectedRefineListSortingType.value) {
                _selectedRefineListSortingType.value = null
            } else {
                _selectedRefineListSortingType.value = sortingType
            }
        }

        fun setSelectedRefineFilterProductType(type: RefineFilterProductType) {
            val currentItems = _selectedRefineProductTypes.value
            if (currentItems.contains(type)) {
                _selectedRefineProductTypes.value = currentItems.filter { it != type }
            } else {
                _selectedRefineProductTypes.value = currentItems.plus(type)
            }
        }

        fun setSelectProductCategory(category: ModalListDataContent) {
            val index = _productCategory.indexOfLast { it.id == category.id }
            val item = _productCategory[index]
            _productCategory[index] = item.copy(isSelected = !item.isSelected)
        }

        fun removeSelectedProductCategory(id: String) {
            val index = _productCategory.indexOfLast { it.id == id }
            val item = _productCategory[index]
            _productCategory[index] = item.copy(isSelected = false)
        }

        fun onResetDefault() {
            _selectedRefineListSortingType.value = RefineAssetSortingType.PERCENT_CHANGE_DOWN
            _selectedRefineProductTypes.value = emptyList()
            _selectedProductCategory.clear()
            val category = _productCategory.map {
                it.isSelected = false
                it
            }
            _productCategory.clear()
            _productCategory.addAll(category)

        }
    }

    inner class RefineAssetListOutputs : BaseOutputs() {

        val selectedRefineListSortingType: StateFlow<RefineAssetSortingType?>
            get() = _selectedRefineListSortingType
        val selectedRefineProductTypes: StateFlow<List<RefineFilterProductType>?>
            get() = _selectedRefineProductTypes

        val showLoading: StateFlow<Boolean>
            get() = _showLoading

        val productCategory: SnapshotStateList<ModalListDataContent>
            get() = _productCategory
    }

    override fun onTriggerActions(action: AppAction) {
        when (action) {
            is RefineAssetListAction.OnSortTypeChanged -> {
                inputs.setSelectedRefineListSortingType(action.type)
            }

            is RefineAssetListAction.OnFilterProductTypeChanged -> {
                inputs.setSelectedRefineFilterProductType(action.type)
            }

            is RefineAssetListAction.OnGetProductCategory -> {
                getProductCategory()
            }

            is RefineAssetListAction.OnDeleteProductCategory -> {
                inputs.removeSelectedProductCategory(action.filterTagDisplayModel.id)
            }

            is RefineAssetListAction.OnPrepareFilterApply -> {
                val argument = RefineAssetListArguments(
                    refineModel = MarketCategoryFilterModel(
                        productCategoryTypes = _productCategory.filter { it.isSelected },
                        sortingType = _selectedRefineListSortingType.value ?: RefineAssetSortingType.PERCENT_CHANGE_DOWN,
                        productTypes = _selectedRefineProductTypes.value
                    ),
//                    sortBy = _selectedRefineListSortingType.value,
//                    filterProductTypes = _selectedRefineProductTypes.value,
//                    productCategoryTypes = _productCategory.filter { it.isSelected }
                )
                onTriggerActions(RefineAssetListAction.OnApplyFilter(argument))
            }

            else -> super.onTriggerActions(action)
        }
    }


    override val inputs = RefineAssetListInputs()
    override val outputs = RefineAssetListOutputs()


    private fun getProductCategory(isInit: Boolean = true) {
        if (_productCategory.isNotEmpty()) {
            onTriggerActions(RefineAssetListAction.OnToggleProductCategoryModal(show = true))
            return
        }
        scope.launch {
            enumerationUseCase(param = EnumerationRequest(codeList = listOf(DropDownEnumeration.PRODUCT_CATEGORY.name)))
                .onStart { _showLoading.value = true }
                .onCompletion { _showLoading.value = false }
                .catch { }
                .collectLatest {
                    _productCategory.clear()
                    _productCategory.addAll(
                        it.list.findLast { it.code == DropDownEnumeration.PRODUCT_CATEGORY.name }?.list.orEmpty()
                            .map {
                                ModalListDataContent(
                                    id = it.value,
                                    title = it.desc,
                                    isSelected = _selectedProductCategory.findLast { selected -> selected.id == it.value } != null)
                            })
                    if (!isInit) {
                        onTriggerActions(RefineAssetListAction.OnToggleProductCategoryModal(show = true))
                    }
                }
        }
    }
}