package com.siriustech.market.screen.selectcategory

import android.app.Activity.RESULT_OK
import android.content.Intent
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.ExperimentalLayoutApi
import androidx.compose.foundation.layout.FlowRow
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.verticalScroll
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.tooling.preview.Preview
import androidx.fragment.app.FragmentActivity
import androidx.hilt.navigation.compose.hiltViewModel
import com.siriustech.core_ui_compose.component.CommonToolbar
import com.siriustech.core_ui_compose.component.ToolbarProperties
import com.siriustech.market.screen.selectcategory.SelectMarketCategoryActivity.Companion.RESULT_EXTRA_SELECT_CATEGORY
import com.siriustech.merit.app_common.R
import com.siriustech.merit.app_common.component.common.MarketCategoryContentData
import com.siriustech.merit.app_common.component.common.MarketCategoryItem
import com.siriustech.merit.app_common.component.container.PaddingStart
import com.siriustech.merit.app_common.component.container.PaddingTop
import com.siriustech.merit.app_common.component.separator.SeparatorLine
import com.siriustech.merit.app_common.data.display.AssetClassModel
import com.siriustech.merit.app_common.ext.colorTxtInactive
import com.siriustech.merit.app_common.ext.colorTxtTitle
import com.siriustech.merit.app_common.theme.LocalAppColor
import com.siriustech.merit.app_common.theme.LocalDimens
import com.siriustech.merit.app_common.theme.LocalTypography
import com.siriustech.merit.app_common.typeenum.MarketCategoryType
import com.siriustech.merit.app_common.typeenum.PortfolioAssetType
import com.siriustech.merit.app_common.typeenum.RiskLevel

/**
 * Created by Hein Htet
 */

@Composable
fun SelectMarketCategoryScreen(
    viewModel: SelectMarketAllocationClassViewModel = hiltViewModel(),
    selectedData: String?,
) {
    val activity = LocalContext.current as FragmentActivity
    val scrollState = rememberScrollState()
    val availableAllocationClass = viewModel.outputs.availableAllocationClass

    fun onCategoryItemSelect(item: MarketCategoryContentData) {
        val type = if (item.allocationClassType != null) {
            item.allocationClassType?.assetName.orEmpty()
        } else if (item.riskLevel != null) {
            item.riskLevel?.value.orEmpty()
        } else {
            item.other.orEmpty()
        }
        activity.setResult(RESULT_OK, Intent().apply {
            putExtra(
                RESULT_EXTRA_SELECT_CATEGORY, SelectedCategoryResultArguments(
                    selectedValue = MarketCategoryType.fromParam(type).value,
                    other = item.other,
                    title = item.title
                )
            )
        })
        activity.finish()
    }

    fun onAssetClassSelected(item: AssetClassModel) {
        activity.setResult(RESULT_OK, Intent().apply {
            putExtra(
                RESULT_EXTRA_SELECT_CATEGORY, SelectedCategoryResultArguments(
                    title = item.title,
                    selectedValue = item.id
                )
            )
        })
        activity.finish()
    }

    Column(
        modifier = Modifier
    ) {
        CommonToolbar(
            onRightActionClicked = { activity.finish() },
            properties = ToolbarProperties(
                titleTextStyle = LocalTypography.current.text14.semiBold.colorTxtTitle(),
                leftActionResId = null,
                rightActionResId = R.drawable.ic_action_close,
                title = stringResource(id = R.string.key0445),
            ),
        )
        PaddingTop(value = LocalDimens.current.dimen24)
        Column(
            modifier = Modifier
                .padding(LocalDimens.current.dimen12)
                .verticalScroll(scrollState)
        ) {
            ForYou(selectedData, onCategorySelected = ::onCategoryItemSelect)
            SeparatorLine(modifier = Modifier.padding(vertical = LocalDimens.current.dimen12))
            RiskLevelItem(selectedData, onCategorySelected = ::onCategoryItemSelect)
            SeparatorLine(modifier = Modifier.padding(vertical = LocalDimens.current.dimen12))
            AllocationClass(
                availableAllocationClass,
                selectedData,
                onAssetClassSelected = {
                    onAssetClassSelected(it)
                }
            )
        }
    }
}


@OptIn(ExperimentalLayoutApi::class)
@Composable
fun ForYou(
    selectedData: String?,
    onCategorySelected: (item: MarketCategoryContentData) -> Unit = {},
) {
    Text(
        text = stringResource(id = R.string.key0446),
        style = LocalTypography.current.text14.semiBold.colorTxtTitle()
    )
    FlowRow(
        modifier = Modifier
            .padding(top = LocalDimens.current.dimen8)
            .fillMaxWidth()
            .background(LocalAppColor.current.bgDefault),
        horizontalArrangement = Arrangement.spacedBy(LocalDimens.current.dimen4),
        maxItemsInEachRow = 3,
    ) {
        MarketCategoryItem(
            allowShrinkText = true,
            isSelected = MarketCategoryType.RECOMMENDED.value == selectedData,
            onItemClicked = onCategorySelected,
            item = MarketCategoryContentData(
                other = MarketCategoryType.RECOMMENDED.value,
                title = stringResource(id = R.string.key0442),
                icon = painterResource(id = R.drawable.ic_recommended_thumb),
            )
        )
        MarketCategoryItem(
            allowShrinkText = true,
            onItemClicked = onCategorySelected,
            isSelected = MarketCategoryType.FAVORITE.value == selectedData,
            item = MarketCategoryContentData(
                other = MarketCategoryType.FAVORITE.value,
                title = stringResource(id = R.string.key0443),
                icon = painterResource(id = R.drawable.ic_favorite_border_blue),
            )
        )
        MarketCategoryItem(
            allowShrinkText = true,
            onItemClicked = onCategorySelected,
            isSelected = MarketCategoryType.RECENTLY_VIEWED.value == selectedData,
            item = MarketCategoryContentData(
                other = MarketCategoryType.RECENTLY_VIEWED.value,
                title = stringResource(id = R.string.key0444),
                icon = painterResource(id = R.drawable.ic_recently_viewed),
            )
        )
    }
}


@OptIn(ExperimentalLayoutApi::class)
@Composable
fun AllocationClass(
    availableAllocationClass: List<AssetClassModel> = emptyList(),
    selectedData: String?,
    onAssetClassSelected: (item: AssetClassModel) -> Unit = {},
) {

    val selectedOtherSection = listOf(
        MarketCategoryType.FAVORITE,
        MarketCategoryType.RECOMMENDED,
        MarketCategoryType.RECENTLY_VIEWED,
        MarketCategoryType.LOW,
        MarketCategoryType.MEDIUM,
        MarketCategoryType.HIGH
    )

    val count = availableAllocationClass.count()
    val column = 3
    val row = (count + column - 1) / column

    Text(
        text = stringResource(id = R.string.key0447),
        style = LocalTypography.current.text14.semiBold.colorTxtTitle()
    )
    FlowRow(
        modifier = Modifier
            .padding(top = LocalDimens.current.dimen8)
            .fillMaxWidth()
            .background(LocalAppColor.current.bgDefault),
        horizontalArrangement = Arrangement.spacedBy(LocalDimens.current.dimen4),
        maxItemsInEachRow = 3,
    ) {
        val items = listOf(
            MarketCategoryContentData(
                allocationClassType = PortfolioAssetType.CASH_EQUIVALENT,
                title = stringResource(id = R.string.key0402),
                icon = painterResource(id = R.drawable.ic_cash_equivalent)
            ),
            MarketCategoryContentData(
                allocationClassType = PortfolioAssetType.FIX_INCOME,
                title = stringResource(id = R.string.key0403),
                icon = painterResource(id = R.drawable.ic_fix_income)
            ),
            MarketCategoryContentData(
                allocationClassType = PortfolioAssetType.PRIVATE_EQUITY,
                title = stringResource(id = R.string.key0439),
                icon = painterResource(id = R.drawable.ic_private_capital)
            ),
            MarketCategoryContentData(
                allocationClassType = PortfolioAssetType.MULTI_ASSET,
                title = stringResource(id = R.string.key0409),
                icon = painterResource(id = R.drawable.ic_multi_asset)
            ),
            MarketCategoryContentData(
                allocationClassType = PortfolioAssetType.STRUCTURED_PRODUCTS,
                title = stringResource(id = R.string.key0404),
                icon = painterResource(id = R.drawable.ic_structured_product)
            ),
            MarketCategoryContentData(
                allocationClassType = PortfolioAssetType.EQUITY,
                title = stringResource(id = R.string.key0408),
                icon = painterResource(id = R.drawable.ic_equity)
            ),
            MarketCategoryContentData(
                allocationClassType = PortfolioAssetType.COMMODITIES,
                title = stringResource(id = R.string.key0407),
                icon = painterResource(id = R.drawable.ic_commodities)
            ),
            MarketCategoryContentData(
                allocationClassType = PortfolioAssetType.VIRTUAL_ASSET,
                title = stringResource(id = R.string.key0406),
                icon = painterResource(id = R.drawable.ic_virtual_asset)
            ),
            MarketCategoryContentData(
                allocationClassType = PortfolioAssetType.PRIVATE_CREDIT,
                title = stringResource(id = R.string.key0422),
                icon = painterResource(id = R.drawable.ic_private_credit)
            ),
        )
        var previousIndex = -1
        repeat(row * column) {
            val index = it % availableAllocationClass.size
            if (index > previousIndex) {
                previousIndex = index
            }
            MarketCategoryItem(
                isSelected = availableAllocationClass[index].id == selectedData,
                item = MarketCategoryContentData(
                    assetClassModel = availableAllocationClass[index],
                    title = availableAllocationClass[index].title,
                    icon = null
                ),
                isExtraBox = index < previousIndex,
                onItemClicked = {
                    it.assetClassModel?.let {
                        onAssetClassSelected(it)
                    }
                }
            )
        }
    }
}


@OptIn(ExperimentalLayoutApi::class)
@Composable
fun RiskLevelItem(
    selectedData: String?,
    onCategorySelected: (item: MarketCategoryContentData) -> Unit = {},
) {
    val row = 3
    Row(modifier = Modifier.fillMaxWidth()) {
        Text(
            text = stringResource(id = R.string.key0448),
            style = LocalTypography.current.text14.semiBold.colorTxtTitle()
        )
        PaddingStart(value = LocalDimens.current.dimen4)
        Text(
            text = stringResource(id = R.string.key0449),
            style = LocalTypography.current.text14.medium.colorTxtInactive()
        )
    }
    FlowRow(
        modifier = Modifier
            .padding(top = LocalDimens.current.dimen8)
            .fillMaxWidth()
            .background(LocalAppColor.current.bgDefault),
        horizontalArrangement = Arrangement.spacedBy(LocalDimens.current.dimen4),
        maxItemsInEachRow = 3,
    ) {
        val items = listOf(
            MarketCategoryContentData(
                riskLevel = RiskLevel.LOW,
//                title = stringResource(id = R.string.key0454),
                title = stringResource(id = R.string.key0619),
            ),
            MarketCategoryContentData(
                riskLevel = RiskLevel.MEDIUM,
                title = stringResource(id = R.string.key0619),
            ),

            MarketCategoryContentData(
                riskLevel = RiskLevel.HIGH,
                title = stringResource(id = R.string.key0619),
            ),

            )
        repeat(row) {
            MarketCategoryItem(
                isSelected = items[it].riskLevel?.value == selectedData,
                item = items[it],
                onItemClicked = onCategorySelected
            )
        }
    }
}


@Preview(showBackground = true, showSystemUi = true)
@Composable
fun PreviewSelectMarketCategoryScreen() {
    SelectMarketCategoryScreen(hiltViewModel(), null)
}