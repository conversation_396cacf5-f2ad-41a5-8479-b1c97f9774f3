package com.siriustech.market.screen.marketprofile.page.marketdata

import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.BoxWithConstraints
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.heightIn
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.verticalScroll
import androidx.compose.runtime.Composable
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.remember
import androidx.compose.ui.Modifier
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.unit.dp
import androidx.fragment.app.FragmentActivity
import com.siriustech.market.domain.display.MarketQuoteDisplayData
import com.siriustech.market.navigation.MarketNavigationEntryPoint
import com.siriustech.market.screen.marketprofile.MarketProfileViewModel
import com.siriustech.market.screen.marketprofile.component.AssetProfileDetails
import com.siriustech.market.screen.marketprofile.component.BasicInformation
import com.siriustech.market.screen.marketprofile.component.CurrentSession
import com.siriustech.market.screen.marketprofile.component.LatestNavStatistics
import com.siriustech.merit.app_common.component.container.PaddingTop
import com.siriustech.merit.app_common.component.separator.SeparatorLine
import com.siriustech.merit.app_common.screen.pdfviewer.PDFViewerArguments
import com.siriustech.merit.app_common.theme.LocalDimens
import com.siriustech.merit.app_common.typeenum.MarketAssetType
import dagger.hilt.android.EntryPointAccessors
import com.siriustech.merit.app_common.R as AppCommonR

/**
 * Created by Hein Htet
 */

@Composable
fun MarketProfilePage(viewModel: MarketProfileViewModel) {
    val scrollState = rememberScrollState()
    val displayBasicInformationDisplayData = viewModel.outputs.displayBasicInformation.collectAsState()
    val displayAssetProfileDetailsDisplayData = viewModel.outputs.displayAssetProfileDetails.collectAsState()

    val activity = LocalContext.current as FragmentActivity

    val navigation = remember {
        EntryPointAccessors.fromApplication(activity, MarketNavigationEntryPoint::class.java)
            .marketNavigation()
    }

    BoxWithConstraints(
        modifier = Modifier
            .fillMaxSize()
            .padding(0.dp)
    ) {
        Column(
            modifier = Modifier
                .heightIn(min = maxHeight)
                .verticalScroll(scrollState)
        ) {
            PaddingTop(value = LocalDimens.current.dimen16)
            BasicInformation(data = displayBasicInformationDisplayData.value)
            SeparatorLine(modifier = Modifier.padding(vertical = LocalDimens.current.dimen12))
            AssetProfileDetails(data = displayAssetProfileDetailsDisplayData.value, onNavigateToFundFactSheet = {
                navigation.onNavigateToPDFViewerActivity(activity, PDFViewerArguments(url = "https://myreport.altervista.org/Lorem_Ipsum.pdf", title = activity.getString(AppCommonR.string.key0563)))
            })
        }
    }
}
