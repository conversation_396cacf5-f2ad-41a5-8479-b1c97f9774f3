package com.siriustech.market.screen.selectcategory

import android.os.Bundle
import androidx.activity.compose.setContent
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.toArgb
import com.siriustech.core_ui_compose.ext.ChangeSystemBarsTheme
import com.siriustech.merit.app_common.theme.AppComposeActivity
import com.siriustech.merit.app_common.theme.AppScreen
import com.siriustech.merit.app_common.theme.AppViewModel
import dagger.hilt.android.AndroidEntryPoint


/**
 * Created by <PERSON><PERSON><PERSON><PERSON>
 */
@AndroidEntryPoint
class SelectMarketCategoryActivity : AppComposeActivity() {

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        val data = intent.getSerializableExtra(EXTRA_SELECT_CATEGORY) as? String
        setContent {
            ChangeSystemBarsTheme(lightTheme = true, Color.White.toArgb())
            AppScreen(vm = AppViewModel()) {
                SelectMarketCategoryScreen(selectedData = data)
            }
        }
    }



    companion object {
        const val RESULT_EXTRA_SELECT_CATEGORY = "RESULT_EXTRA_SELECT_CATEGORY"
        const val EXTRA_SELECT_CATEGORY = "EXTRA_SELECT_CATEGORY"
    }
}