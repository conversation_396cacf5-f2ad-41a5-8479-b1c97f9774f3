package com.siriustech.market.screen.marketprofile.component

import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.tooling.preview.Preview
import com.core.util.toAmount
import com.siriustech.market.domain.display.CurrentSessionDisplayModel
import com.siriustech.merit.app_common.component.container.LabelAmountCurrency
import com.siriustech.merit.app_common.component.container.LabelValue
import com.siriustech.merit.app_common.ext.colorTxtInactive
import com.siriustech.merit.app_common.ext.colorTxtTitle
import com.siriustech.merit.app_common.ext.formatVolume
import com.siriustech.merit.app_common.theme.LocalDimens
import com.siriustech.merit.app_common.theme.LocalTypography
import com.siriustech.merit.app_common.R as AppCommonR
/**
 * Created by <PERSON><PERSON>
 */

@Composable
fun CurrentSession(
    modifier: Modifier = Modifier,
    data: CurrentSessionDisplayModel = CurrentSessionDisplayModel(),
) {
    Column(
        modifier = Modifier
            .fillMaxWidth()
            .then(modifier)
    ) {
        val currency = data.currency
        val labelModifier = Modifier.padding(vertical = LocalDimens.current.dimen4)
        val amountTextStyle = LocalTypography.current.text14.regular.colorTxtTitle()
        Row(modifier = Modifier.fillMaxWidth(), horizontalArrangement = Arrangement.SpaceBetween) {
            Text(
                text = stringResource(id = AppCommonR.string.key0495),
                style = LocalTypography.current.text14.medium.colorTxtTitle()
            )
            Text(
                text = stringResource(id = AppCommonR.string.key0580, data.asOfDate),
                style = LocalTypography.current.text14.light.colorTxtInactive()
            )
        }
        LabelValue(label = stringResource(id = AppCommonR.string.key0490), value = data.volume.formatVolume(), modifier = Modifier.padding(top = LocalDimens.current.dimen8),)
        LabelAmountCurrency(label = stringResource(id = AppCommonR.string.key0496), amount = data.openPrice.toAmount(4), currency = currency, modifier = labelModifier, amountTextStyle = amountTextStyle)
        LabelAmountCurrency(label = stringResource(id = AppCommonR.string.key0497), amount = data.highestPrice.toAmount(4), currency = currency,modifier = labelModifier, amountTextStyle = amountTextStyle)
        LabelAmountCurrency(label = stringResource(id = AppCommonR.string.key0498), amount = data.lowestPrice.toAmount(4), currency = currency,modifier = labelModifier, amountTextStyle = amountTextStyle)
        LabelAmountCurrency(label = stringResource(id = AppCommonR.string.key0499), amount = data.averagePrice.toAmount(4), currency = currency,modifier = labelModifier, amountTextStyle = amountTextStyle)
        LabelAmountCurrency(label = stringResource(id = AppCommonR.string.key0500), amount = data.averageBuyPrice.toAmount(4), currency = currency,modifier = labelModifier, amountTextStyle = amountTextStyle)
        LabelAmountCurrency(label = stringResource(id = AppCommonR.string.key0501), amount = data.averageSellPrice.toAmount(4), currency = currency,modifier = labelModifier, amountTextStyle = amountTextStyle)
        LabelAmountCurrency(label = stringResource(id = AppCommonR.string.key0502), amount = data.ceilingPrice.toAmount(4), currency = currency,modifier = labelModifier, amountTextStyle = amountTextStyle)
        LabelAmountCurrency(label = stringResource(id = AppCommonR.string.key0503), amount = data.floorPrice.toAmount(4), currency = currency,modifier = labelModifier, amountTextStyle = amountTextStyle)
    }
}

@Preview(showBackground = true, showSystemUi = true)
@Composable
fun PreviewCurrentSession() {
    CurrentSession()
}