package com.siriustech.market.screen.refineasset

import android.os.Bundle
import androidx.activity.compose.setContent
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.toArgb
import com.siriustech.core_ui_compose.ext.ChangeSystemBarsTheme
import com.siriustech.merit.app_common.theme.AppComposeActivity
import com.siriustech.merit.app_common.theme.AppScreen
import com.siriustech.merit.app_common.theme.AppViewModel
import dagger.hilt.android.AndroidEntryPoint

/**
 * Created by <PERSON><PERSON><PERSON><PERSON>
 */
@AndroidEntryPoint
class RefineAssetListActivity : AppComposeActivity() {
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        val arguments = intent.getSerializableExtra(RESULT_EXTRA_REFINE_RESULT) as? RefineAssetListArguments?
        setContent {
            ChangeSystemBarsTheme(lightTheme = true, Color.White.toArgb())
            AppScreen(vm = AppViewModel()) {
                RefineAssetScreen(arguments)
            }
        }
    }

    companion object  {
        const val RESULT_EXTRA_REFINE_RESULT = "RESULT_EXTRA_REFINE_RESULT"
    }
}