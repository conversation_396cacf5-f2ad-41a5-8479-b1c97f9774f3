package com.siriustech.market.screen.marketprofile

import android.content.Context
import androidx.compose.runtime.mutableStateListOf
import androidx.compose.runtime.snapshots.SnapshotStateList
import androidx.lifecycle.SavedStateHandle
import androidx.lifecycle.viewModelScope
import com.core.network.base.getError
import com.siriustech.market.domain.CandleChartTimeFrameType
import com.siriustech.market.domain.MarketProfileMapper
import com.siriustech.market.domain.MarketProfileMapper.mapToAssetDetailsDisplayData
import com.siriustech.market.domain.MarketProfileMapper.mapToBasicInformation
import com.siriustech.market.domain.MarketProfileMapper.mapToMarketCategoryDetails
import com.siriustech.market.domain.MarketProfileMapper.mapToMarketQuoteDisplayData
import com.siriustech.market.domain.SimpleChartDataMapper.mapToTechnicalChartDisplayModel
import com.siriustech.market.domain.TechnicalChartDataMapper.mapToTechnicalChartDisplayModel
import com.siriustech.market.domain.TechnicalChartType
import com.siriustech.market.domain.display.AssetProfileDetailsDisplayData
import com.siriustech.market.domain.display.BasicInformationDisplayData
import com.siriustech.market.domain.display.MarketQuoteDisplayData
import com.siriustech.market.domain.display.SimpleChartDisplayModel
import com.siriustech.market.domain.display.TechnicalChartDisplayModel
import com.siriustech.merit.apilayer.service.authentication.common.eumeration.EnumerationRequest
import com.siriustech.merit.apilayer.service.authentication.common.eumeration.EnumerationResponse
import com.siriustech.merit.apilayer.service.authentication.common.eumeration.GetEnumerationUseCase
import com.siriustech.merit.apilayer.service.market.candle.CandleChartDataRequest
import com.siriustech.merit.apilayer.service.market.candle.CandleChartDataUseCase
import com.siriustech.merit.apilayer.service.market.exchangecurrency.MarketCurrencyRequest
import com.siriustech.merit.apilayer.service.market.exchangecurrency.MarketExchangeCurrencyUseCase
import com.siriustech.merit.apilayer.service.market.exchangecurrency.defaultMarketExchangeCurrencyRequestList
import com.siriustech.merit.apilayer.service.market.marketprofile.MarketInstrumentDetailsRequest
import com.siriustech.merit.apilayer.service.market.marketprofile.MarketInstrumentDetailsResponse
import com.siriustech.merit.apilayer.service.market.marketprofile.MarketInstrumentDetailsUseCase
import com.siriustech.merit.apilayer.service.market.marketquote.MarketQuoteDataUseCase
import com.siriustech.merit.apilayer.service.market.marketquote.MarketQuoteRequest
import com.siriustech.merit.apilayer.service.market.simplechart.SimpleChartRequest
import com.siriustech.merit.apilayer.service.market.simplechart.SimpleChartUseCase
import com.siriustech.merit.apilayer.service.market.togglefavorite.ToggleFavoriteRequest
import com.siriustech.merit.apilayer.service.market.togglefavorite.ToggleFavoriteUseCase
import com.siriustech.merit.apilayer.service.user.recentlyview.RecentlyViewedRequest
import com.siriustech.merit.apilayer.service.user.recentlyview.RecentlyViewedUpdateUseCase
import com.siriustech.merit.app_common.Constants
import com.siriustech.merit.app_common.component.common.MarketProfileTabModel
import com.siriustech.merit.app_common.component.marquee.StockExchangeMarqueeData
import com.siriustech.merit.app_common.data.AppCache
import com.siriustech.merit.app_common.data.display.MarketAssetDisplayData
import com.siriustech.merit.app_common.data.display.UserBasicInfoDisplay
import com.siriustech.merit.app_common.mapper.MarketExchangeCurrencyMapper.mapToStockExchangeDisplayData
import com.siriustech.merit.app_common.theme.AppAction
import com.siriustech.merit.app_common.theme.AppViewModel
import com.siriustech.merit.app_common.typeenum.ChartTimeFrame
import com.siriustech.merit.app_common.typeenum.DropDownEnumeration
import com.siriustech.merit.app_common.typeenum.MarketAssetType
import com.siriustech.merit.app_common.utils.ErrorCode.mapToErrorDisplay
import dagger.hilt.android.lifecycle.HiltViewModel
import javax.inject.Inject
import kotlin.random.Random
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.Job
import kotlinx.coroutines.delay
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.SharingStarted
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.catch
import kotlinx.coroutines.flow.collectLatest
import kotlinx.coroutines.flow.flatMapLatest
import kotlinx.coroutines.flow.map
import kotlinx.coroutines.flow.onCompletion
import kotlinx.coroutines.flow.onStart
import kotlinx.coroutines.flow.stateIn
import kotlinx.coroutines.isActive
import kotlinx.coroutines.launch
import timber.log.Timber

/**
 * Created by Hein Htet
 */
@HiltViewModel
class MarketProfileViewModel @Inject constructor(
    savedStateHandle: SavedStateHandle,
    private val marketExchangeCurrencyUseCase: MarketExchangeCurrencyUseCase,
    private val candleChartDataUseCase: CandleChartDataUseCase,
    private val toggleFavoriteUseCase: ToggleFavoriteUseCase,
    private val simpleChartUseCase: SimpleChartUseCase,
    private val marketQuoteDataUseCase: MarketQuoteDataUseCase,
    private val marketInstrumentDetailsUseCase: MarketInstrumentDetailsUseCase,
    private val recentlyViewedUpdateUseCase: RecentlyViewedUpdateUseCase,
    private val enumerationUseCase: GetEnumerationUseCase,
    private val appCache: AppCache,

    ) : AppViewModel() {
    private val millisecondsInOneYear = 365.25 * 24 * 60 * 60 * 1000


    private val _showLoading = MutableStateFlow(false)
    private val _displayStockExchangeItems = mutableStateListOf<StockExchangeMarqueeData>()
    private val _displayMarketAssetDetails = MutableStateFlow(MarketAssetDisplayData())
    private val _currentSelectedTab = MutableStateFlow<MarketProfileTabModel?>(null)
    private val _currentCandleChartType = MutableStateFlow(CandleChartTimeFrameType.ONE_DAY)
    private val _displayTechnicalChartItems = MutableStateFlow(TechnicalChartDisplayModel())
    private val _selectedTechnicalChartType =
        MutableStateFlow(TechnicalChartType.CANDLE_STICK_CHART)
    private val _isFavorite = MutableStateFlow<Boolean>(false)
    private val _selectedTimeFrame =
        MutableStateFlow(ChartTimeFrame.ONE_WEEK)
    private val _displaySimpleChartData =
        MutableStateFlow(SimpleChartDisplayModel())
    private val _lastPrice = MutableStateFlow(0f)

    private val _marketQuoteDisplayData = MutableStateFlow<MarketQuoteDisplayData>(
        MarketQuoteDisplayData()
    )

    private val _marketInstrumentProfileDetails = MutableStateFlow<MarketInstrumentDetailsResponse>(
        MarketInstrumentDetailsResponse()
    )

    private var job: Job? = null
    private var _context: Context? = null

    private var _mockPeriodicTask = false
    private lateinit var marketProfileMapper: MarketProfileMapper


    init {
//        val args = MarketProfileArgument.from(savedStateHandle = savedStateHandle).args
//        Timber.d("MarketProfileData ${args.data}")
//        _displayMarketAssetDetails.value = args.data
//        getTechnicalChartData()
//        _isFavorite.value = _displayMarketAssetDetails.value.isFavorite
//        _lastPrice.value = _displayMarketAssetDetails.value.marketPrice.toFloatOrNull() ?: 0f
//        getSimpleChartData()
//        getMarketQuoteData()
    }

    override val inputs = MarketProfileInputs()
    override val outputs = MarketProfileOutputs()

    inner class MarketProfileInputs : BaseInputs() {

        fun initContext(context: Context) {
            _context = context
        }

        fun onInitMarketAssetData(data: MarketAssetDisplayData) {
            _displayMarketAssetDetails.value = data
            Timber.d("MarketProfileData ${data}")
            _displayMarketAssetDetails.value = data
            getTechnicalChartData()
            _isFavorite.value = _displayMarketAssetDetails.value.isFavorite
            _lastPrice.value = _displayMarketAssetDetails.value.marketPrice.toFloatOrNull() ?: 0f
            getSimpleChartData()
            startPeriodicTask()
            updateRecentlyViewed()
        }

        fun onApiCall() {
            scope.launch {
                getMarketExchangeCurrency()
                getMarketQuoteData()
                getMarketInstrumentProfileDetails()
            }
        }

        fun onGetSimpleChartData() = getSimpleChartData()

        fun onGetTechnicalChartData() = getTechnicalChartData()

        fun onGetMarketQuoteData() = getMarketQuoteData()

        fun onGetMarketInstrumentProfileDetails() = getMarketInstrumentProfileDetails()

        fun startPeriodicTask() {
            if (job == null || job?.isCompleted == true) {
                job = scope.launch(Dispatchers.IO) {
                    while (isActive) {
                        getMarketQuoteData(isPeriodicCall = true)
                        delay(Constants.INTERVAL_API_CALL_IN_SEC * 1000)
                    }
                }
            }
        }

        fun stopPeriodicTask() {
            job?.cancel()
            job = null
        }
    }

    inner class MarketProfileOutputs : BaseOutputs() {
        val showLoading: StateFlow<Boolean>
            get() = _showLoading
        val displayStockExchangeItems: SnapshotStateList<StockExchangeMarqueeData>
            get() = _displayStockExchangeItems

        val displayMarketAssetDetails: StateFlow<MarketAssetDisplayData>
            get() = _displayMarketAssetDetails

        val displayMarketProfileHeaderDetails = displayMarketAssetDetails.map {
            it.mapToMarketCategoryDetails(context = _context)
        }.stateIn(
            viewModelScope,
            initialValue = null,
            started = SharingStarted.WhileSubscribed(5000)
        )

        val displayTechnicalChartItems: StateFlow<TechnicalChartDisplayModel>
            get() = _displayTechnicalChartItems

        val currentSelectedChartType: StateFlow<CandleChartTimeFrameType>
            get() = _currentCandleChartType

        val currentTechnicalChartType: StateFlow<TechnicalChartType>
            get() = _selectedTechnicalChartType

        val isFavorite: StateFlow<Boolean>
            get() = _isFavorite

        val currentSelectedTab: StateFlow<MarketProfileTabModel?>
            get() = _currentSelectedTab

        val currentSimpleChartTimeframe: StateFlow<ChartTimeFrame>
            get() = _selectedTimeFrame

        val displaySimpleChartData: StateFlow<SimpleChartDisplayModel>
            get() = _displaySimpleChartData

        val lastPrice: StateFlow<Float>
            get() = _lastPrice

        val marketQuoteDisplayData: StateFlow<MarketQuoteDisplayData>
            get() = _marketQuoteDisplayData


        val displayBasicInformation = _marketInstrumentProfileDetails.map {
            it.mapToBasicInformation(_context)
        }.stateIn(
            viewModelScope,
            initialValue = BasicInformationDisplayData(),
            started = SharingStarted.WhileSubscribed(5000)
        )


        val displayAssetProfileDetails = _marketInstrumentProfileDetails.map {
            it.mapToAssetDetailsDisplayData(_context)
        }.stateIn(
            viewModelScope,
            initialValue = AssetProfileDetailsDisplayData(),
            started = SharingStarted.WhileSubscribed(5000)
        )


        val displayMarketProfileChartTimeFrames = _marketInstrumentProfileDetails.map {
            val type = MarketAssetType.fromParam(it.instrumentClass.orEmpty())
            val items =
                if (type == MarketAssetType.US_EQUITY || type == MarketAssetType.HK_EQUITY) {
                    listOf(
//                        ChartTimeFrame.ONE_DAY,
                        ChartTimeFrame.ONE_WEEK,
                        ChartTimeFrame.ONE_MONTH,
                        ChartTimeFrame.ONE_YEAR,
                        ChartTimeFrame.THREE_YEAR,
                        ChartTimeFrame.FIVE_YEAR,
                    )
                } else {
                    listOf(
                        ChartTimeFrame.ONE_WEEK,
                        ChartTimeFrame.ONE_MONTH,
                        ChartTimeFrame.ONE_YEAR,
                        ChartTimeFrame.THREE_YEAR,
                        ChartTimeFrame.FIVE_YEAR,
                    )
                }
            _selectedTimeFrame.value = items.first()
            items
        }.stateIn(
            viewModelScope,
            initialValue = emptyList(),
            started = SharingStarted.WhileSubscribed(5000)
        )

        val userInfo: UserBasicInfoDisplay?
            get() = appCache.userBasicInfoDisplay

        val hasFullAccess: Boolean
            get() = appCache.hasFullAccess

    }

    override fun onTriggerActions(action: AppAction) {
        when (action) {
            is MarketProfileAction.OnNavigateToTab -> {
                if (_currentSelectedTab.value != action.item) {
                    _currentSelectedTab.value = action.item
                    super.onTriggerActions(action)
                }
            }

            is MarketProfileAction.OnTechnicalChartTimeFrameSelected -> {
                _currentCandleChartType.value = action.timeFrame
                getTechnicalChartData()
            }

            is MarketProfileAction.OnTechnicalChartTypeChanged -> {
                _selectedTechnicalChartType.value = action.type
            }

            is MarketProfileAction.OnToggleFavorite -> {
                toggleFavorite()
            }

            is MarketProfileAction.OnSimpleChartTimeFrameChanged -> {
                _selectedTimeFrame.value = action.timeFrame
                getSimpleChartData()
            }
        }
        super.onTriggerActions(action)
    }

    private fun getMarketExchangeCurrency() {
        scope.launch {
            marketExchangeCurrencyUseCase(
                param = MarketCurrencyRequest(
                    currencyList = defaultMarketExchangeCurrencyRequestList
                )
            )
                .onStart { _showLoading.value = true }
                .onCompletion { _showLoading.value = false }
                .catch { }
                .collectLatest {
                    _displayStockExchangeItems.clear()
                    _displayStockExchangeItems.addAll(it.mapToStockExchangeDisplayData())
                }
        }
    }


    private fun getTechnicalChartData() {
        val request = CandleChartDataRequest(
            limit = 1000,
            instrumentId = _displayMarketAssetDetails.value.id.toInt(),
            candleType = _currentCandleChartType.value.value,
            fromTime = (millisecondsInOneYear * 5).toLong(),
            toTime = System.currentTimeMillis()
        )
        scope.launch {
            candleChartDataUseCase(param = request)
                .onStart { _showLoading.value = true }
                .onCompletion { _showLoading.value = false }
                .catch { emitError(it.getError().mapToErrorDisplay()) }
                .collectLatest {
                    _displayTechnicalChartItems.value =
                        it.mapToTechnicalChartDisplayModel(_displayMarketAssetDetails.value)
                }
        }
    }

    private fun toggleFavorite() {
        scope.launch {
            toggleFavoriteUseCase(
                param = ToggleFavoriteRequest(
                    instrumentId = _displayMarketAssetDetails.value.id.toIntOrNull() ?: -1
                )
            )
                .onStart { _showLoading.value = true }
                .onCompletion { _showLoading.value = false }
                .catch { emitError(it.getError().mapToErrorDisplay()) }
                .collectLatest {
                    _isFavorite.value = !_isFavorite.value
                    onTriggerActions(
                        MarketProfileAction.OnToggleChangedFavorite(
                            assetName = _displayMarketAssetDetails.value?.symbol.orEmpty(),
                            isFavorite = _isFavorite.value
                        )
                    )
                }
        }
    }


    private fun getSimpleChartData() {
        val request = SimpleChartRequest(
            interval = _selectedTimeFrame.value.getApiRequest(),
            instrumentId = _displayMarketAssetDetails.value.id.toInt()
        )
        scope.launch {
            simpleChartUseCase(param = request)
                .onStart { _showLoading.value = true }
                .onCompletion { _showLoading.value = false }
                .catch {}
                .collectLatest {
                    _displaySimpleChartData.value =
                        it.mapToTechnicalChartDisplayModel(
                            _displayMarketAssetDetails.value,
                            _selectedTimeFrame.value
                        )
                }
        }
    }

    private fun getMarketQuoteData(isPeriodicCall: Boolean = false) {
        scope.launch {
            marketQuoteDataUseCase(param = MarketQuoteRequest(instrumentId = _displayMarketAssetDetails.value.id.toInt()))
                .onStart { _showLoading.value = true && !isPeriodicCall }
                .onCompletion { _showLoading.value = false }
                .catch {}
                .collectLatest {
                    val data =
                        it.mapToMarketQuoteDisplayData(_displayMarketAssetDetails.value, _context)
                    _marketQuoteDisplayData.value = data
                    if (_mockPeriodicTask) {
                        _displayMarketAssetDetails.value = _displayMarketAssetDetails.value.copy(
                            marketValue = Random.nextFloat().toString(),
                            unrealizedGl = Random.nextFloat().toString(),
                            unrealizedGlRate = Random.nextFloat().toString(),
                        )
                    } else {
                        _displayMarketAssetDetails.value = _displayMarketAssetDetails.value.copy(
                            marketValue = it.price ?: "0.0",
                            unrealizedGl = it.priceChange ?: "0.0",
                            unrealizedGlRate = it.priceChangeRate ?: "0.0",
                        )
                    }
                }
        }
    }

    private fun getMarketInstrumentProfileDetails() {
        var categoryEnum : EnumerationResponse? = null
        scope.launch {
            enumerationUseCase(param = EnumerationRequest(codeList = listOf(
                DropDownEnumeration.PRODUCT_CATEGORY.name
            )))
                .flatMapLatest { enum ->
                    categoryEnum = enum
                    marketInstrumentDetailsUseCase(
                        param = MarketInstrumentDetailsRequest(
                            instrumentId = _displayMarketAssetDetails.value.id.toInt()
                        )
                    )
                }
                .onStart { _showLoading.value = true }
                .onCompletion { _showLoading.value = false }
                .catch { emitError(it.getError().mapToErrorDisplay()) }
                .collectLatest {
                    Timber.d("MarketInstrumentProfileDetails $it")
                    _displayMarketAssetDetails.value = _displayMarketAssetDetails.value.copy(
                        region = it.region.orEmpty(),
                    )
                    _marketInstrumentProfileDetails.value = it.copy(
                        instrumentCategory = categoryEnum?.list?.firstOrNull()?.list?.findLast { enumValue -> enumValue.value == it.instrumentCategory.orEmpty() }?.desc.orEmpty()
                    )
                }
        }
    }

    private fun updateRecentlyViewed() {
        scope.launch {
            recentlyViewedUpdateUseCase(
                param = RecentlyViewedRequest(
                    instrumentIdList = listOf(
                        _displayMarketAssetDetails.value.id.toIntOrNull() ?: -1
                    )
                )
            )
                .onStart { _showLoading.value = true }
                .onCompletion { _showLoading.value = false }
                .catch { emitError(it.getError().mapToErrorDisplay()) }
                .collectLatest {
                }
        }
    }


}
