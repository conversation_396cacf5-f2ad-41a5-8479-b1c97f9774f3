package com.siriustech.market.screen.assetListLanding

import android.content.Context
import com.siriustech.market.screen.refineasset.RefineAssetListArguments
import com.siriustech.merit.app_common.data.display.MarketAssetDisplayData
import com.siriustech.merit.app_common.theme.AppAction

/**
 * Created by <PERSON><PERSON>
 */
sealed interface MarketAction : AppAction{
    data class OnToggleCurrencyPairModal(val show : Boolean) : MarketAction
    data class OnToggleFavoriteMarketAsset(val data: MarketAssetDisplayData, val favorite : Boolean) : MarketAction
    data object OnNavigateToMarketCategorySelection : MarketAction
    data object OnNavigateToRefineAssetList : MarketAction
    data class OnRefineFilterApply(val arguments: RefineAssetListArguments,val context: Context) : MarketAction
    data class OnMarketAssetClick(val data: MarketAssetDisplayData) : MarketAction
    data object OnNavigateToSettings : MarketAction
    data object OnNavigateToChat : MarketAction
}