package com.siriustech.market.screen.refineasset.component

import androidx.compose.runtime.Composable
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.fragment.app.FragmentActivity
import com.siriustech.core_ui_compose.component.CommonToolbar
import com.siriustech.core_ui_compose.component.ToolbarProperties
import com.siriustech.merit.app_common.R as AppCommonR


@Composable
fun RefineAssetListToolbar(onReset: () -> Unit = {}) {
    val activity = LocalContext.current as FragmentActivity
    CommonToolbar(
        onRightActionClicked = { onReset() },
        onLeftActionClicked = {activity.finish()},
        properties = ToolbarProperties(
            rightActionResId = AppCommonR.drawable.ic_action_refersh,
            leftActionResId = AppCommonR.drawable.ic_action_close,
            title = stringResource(id = AppCommonR.string.key0459), iconTitle = painterResource(
                id = AppCommonR.drawable.ic_filter_black
            )
        )
    )
}