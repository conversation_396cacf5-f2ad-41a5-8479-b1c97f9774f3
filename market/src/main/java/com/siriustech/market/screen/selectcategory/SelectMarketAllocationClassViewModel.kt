package com.siriustech.market.screen.selectcategory

import androidx.compose.runtime.mutableStateListOf
import androidx.compose.runtime.snapshots.SnapshotStateList
import com.siriustech.merit.app_common.data.AppCache
import com.siriustech.merit.app_common.data.display.AssetClassModel
import com.siriustech.merit.app_common.theme.AppViewModel
import dagger.hilt.android.lifecycle.HiltViewModel
import javax.inject.Inject

/**
 * Created by <PERSON><PERSON><PERSON>
 */
@HiltViewModel
class SelectMarketAllocationClassViewModel @Inject constructor(
    private val appCache: AppCache,
) : AppViewModel() {

    private val _availableAllocationClass = mutableStateListOf<AssetClassModel>()


    inner class SelectMarketAllocationClassInputs : BaseInputs() {
        init {
            _availableAllocationClass.addAll(appCache.allocationClass)
        }
    }

    inner class SelectMarketAllocationClassOutputs : BaseOutputs() {

        val availableAllocationClass: SnapshotStateList<AssetClassModel>
            get() = _availableAllocationClass
    }

    override val inputs = SelectMarketAllocationClassInputs()
    override val outputs = SelectMarketAllocationClassOutputs()


}