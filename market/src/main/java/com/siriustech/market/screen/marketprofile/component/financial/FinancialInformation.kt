package com.siriustech.market.screen.marketprofile.component.financial

import android.graphics.Color
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.RowScope
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.remember
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.viewinterop.AndroidView
import androidx.core.content.ContextCompat
import androidx.core.content.res.ResourcesCompat
import com.github.mikephil.charting.charts.BarChart
import com.github.mikephil.charting.components.XAxis
import com.github.mikephil.charting.components.YAxis
import com.github.mikephil.charting.data.BarData
import com.github.mikephil.charting.data.BarDataSet
import com.github.mikephil.charting.data.BarEntry
import com.github.mikephil.charting.model.GradientColor
import com.siriustech.market.domain.display.FinancialInfoDisplay
import com.siriustech.market.domain.display.FinancialItemInfoDisplay
import com.siriustech.merit.app_common.R
import com.siriustech.merit.app_common.component.container.NonlazyGrid
import com.siriustech.merit.app_common.component.container.PaddingTop
import com.siriustech.merit.app_common.ext.AttributeStringData
import com.siriustech.merit.app_common.ext.buildAttrString
import com.siriustech.merit.app_common.ext.colorTxtInactive
import com.siriustech.merit.app_common.ext.colorTxtParagraph
import com.siriustech.merit.app_common.ext.colorTxtTitle
import com.siriustech.merit.app_common.ext.displayPriceChange
import com.siriustech.merit.app_common.ext.formatVolume
import com.siriustech.merit.app_common.ext.isPriceDown
import com.siriustech.merit.app_common.ext.isPriceUp
import com.siriustech.merit.app_common.theme.LocalAppColor
import com.siriustech.merit.app_common.theme.LocalDimens
import com.siriustech.merit.app_common.theme.LocalTypography
import com.siriustech.merit.app_common.R as AppCommonR

/**
 * Created by Hein Htet
 */

@Composable
fun FinancialInformation(financialInfoDisplay: FinancialInfoDisplay, currency: String = "USD") {

    Column(modifier = Modifier.fillMaxWidth()) {
        Row(
            modifier = Modifier.fillMaxWidth(),
            horizontalArrangement = Arrangement.SpaceBetween
        ) {
            Text(
                text = stringResource(id = AppCommonR.string.key0546),
                style = LocalTypography.current.text14.medium.colorTxtTitle()
            )
            Text(
                modifier = Modifier.weight(1f),
                textAlign = TextAlign.End,
                text = financialInfoDisplay.period,
                style = LocalTypography.current.text14.light.colorTxtParagraph()
            )
            PaddingTop(value = LocalDimens.current.dimen12)
        }
        PaddingTop(value = LocalDimens.current.dimen12)
        NonlazyGrid(
            columns = 2,
            itemCount = financialInfoDisplay.items.size
        ) {
            FinancialBarItem(it, financialInfoDisplay, currency)
        }
    }
}


@Composable
fun FinancialBarItem(index: Int, infoDisplay: FinancialInfoDisplay, currency: String) {
    val financialItemInfoDisplay = infoDisplay.items[index]
    Column(modifier = Modifier) {
        Text(
            text = financialItemInfoDisplay.name.displayName(),
            style = LocalTypography.current.text14.regular.colorTxtTitle()
        )
        Row(verticalAlignment = Alignment.CenterVertically) {
            Column(modifier = Modifier.weight(1f)) {
                Text(
                    text = buildAttrString(
                        listOf(
                            AttributeStringData(
                                financialItemInfoDisplay.price,
                                LocalTypography.current.text14.medium.colorTxtParagraph()
                            ),
                            AttributeStringData(
                                " $currency",
                                LocalTypography.current.text12.medium.colorTxtInactive()
                            ),
                        )
                    )
                )
                Text(
                    text = buildAttrString(
                        listOf(
                            AttributeStringData(
                                financialItemInfoDisplay.yy,
                                LocalTypography.current.text14.medium.copy(
                                    color = if (financialItemInfoDisplay.yy.isPriceDown()) {
                                        LocalAppColor.current.txtNegative
                                    } else if (financialItemInfoDisplay.yy.isPriceUp()) {
                                        LocalAppColor.current.txtPositive
                                    } else {
                                        LocalAppColor.current.txtParagraph
                                    }
                                )
                            ),
                            AttributeStringData(
                                " %Y/Y",
                                LocalTypography.current.text12.medium.colorTxtInactive()
                            ),
                        )
                    )
                )
            }
            FinancialBar(
                financialItemInfoDisplay = financialItemInfoDisplay
            )
        }
    }
}


@Composable
fun RowScope.FinancialBar(
    financialItemInfoDisplay: FinancialItemInfoDisplay,
) {

    val items = financialItemInfoDisplay.barData.map {
        FinancialBarDisplayData(
            percentage = it ?: "0",
            yy = financialItemInfoDisplay.yy
        )
    }

    val context = LocalContext.current
    val typeface = remember {
        ResourcesCompat.getFont(context, R.font.noto_sans_medium)
    }
    val barChart = remember {
        BarChart(context)
    }
    val entries = ArrayList<BarEntry>()
    val max = items.map { it.percentage.toFloatOrNull() ?: 0f }.maxOf { it }
    var itemsNeeded = 0
    if (items.size < 5) {
        itemsNeeded = 5 - items.size
        for (i in 0 until itemsNeeded) {
            entries.add(BarEntry(i.toFloat(), max * 0.1f)) // 10 percent of max data
        }
    }
    items.forEachIndexed { index, item ->
        entries.add(BarEntry((index + itemsNeeded).toFloat(), item.percentage.toFloat()))
    }
    fun onGetLastBarColor(): GradientColor {
        val greenGradient = GradientColor(
            ContextCompat.getColor(context, R.color.barChartGreen0),
            ContextCompat.getColor(context, R.color.barChartGreen80)
        )

        val redGradient = GradientColor(
            ContextCompat.getColor(context, R.color.barChartRed0),
            ContextCompat.getColor(context, R.color.barChartRed80)
        )

        val color = when {
            financialItemInfoDisplay.yy.isPriceUp() -> {
                return if (financialItemInfoDisplay.name == FinancialInfoType.LIABILITIES) {
                    redGradient
                } else {
                    greenGradient
                }
            }

            financialItemInfoDisplay.yy.isPriceDown() -> {
                return if (financialItemInfoDisplay.name == FinancialInfoType.LIABILITIES) {
                    greenGradient
                } else {
                    redGradient
                }
            }

            else -> greenGradient
        }

        return color
    }

    val barDataSet = BarDataSet(entries, "Bar Data Set").apply {
        this.setDrawValues(true)
        this.setDrawIcons(false)
        gradientColors = List(entries.size) { index ->
            if (index == entries.lastIndex) onGetLastBarColor() else GradientColor(
                ContextCompat.getColor(context, R.color.barChartGray0),
                ContextCompat.getColor(context, R.color.barChartGray80),
            )
        }
    }
    val barData = BarData(barDataSet)
    barData.setDrawValues(false)
    AndroidView(
        modifier = Modifier
            .weight(1f)
            .height(55.dp),
        factory = {
            barChart.apply {
                setDrawGridBackground(false)
                setDrawBorders(false)
                setDrawMarkers(true)
                isDoubleTapToZoomEnabled = false
                setPinchZoom(false)
                setNoDataText("")
                axisRight.setDrawLabels(false)
                isHighlightPerTapEnabled = false
                axisRight.setPosition(YAxis.YAxisLabelPosition.INSIDE_CHART)
                axisLeft.setPosition(YAxis.YAxisLabelPosition.INSIDE_CHART)
                axisRight.setLabelCount(2, true)
                legend.isEnabled = false
                description.isEnabled = false
                axisLeft.setDrawLabels(false)
                xAxis.setDrawLabels(false)
                xAxis.setDrawGridLines(false)
                axisLeft.setDrawGridLines(false)
                axisRight.setDrawGridLines(false)
                axisRight.textColor = Color.parseColor("#585858")
                xAxis.setDrawAxisLine(false)
                axisLeft.setDrawAxisLine(false)
                axisRight.setDrawAxisLine(false)
                axisRight.textSize = 12f
                axisRight.typeface = typeface
                axisLeft.textSize = 12f
                axisLeft.typeface = typeface
                setFitBars(true)
                val yAxis = axisLeft
                yAxis.zeroLineColor = Color.WHITE
                isHighlightPerDragEnabled = false
                yAxis.setDrawZeroLine(true)
                axisRight.isEnabled = false
                xAxis.isEnabled = false
                xAxis.setDrawLabels(false)
                axisRight.setDrawLabels(false)
                barChart.invalidate()
            }
            barChart
        },
        update = {
            barChart.xAxis.apply {
                axisMinimum = -0.5f
                axisMaximum = entries.size - 0.5f
                textSize = 12f
                position = XAxis.XAxisPosition.BOTTOM
            }
            barChart.apply {
                data = barData
                val allPositive = (items.all { it.percentage.toFloat() > 0 })
                val allNegative = (items.all { it.percentage.toFloat() < 0 })
                if (!allNegative && !allPositive) {
                    axisLeft.axisMinimum = -20f
                }
                invalidate()
            }
        }
    )
}

data class FinancialBarDisplayData(
    val yy: String,
    val percentage: String,
)

@Preview(showBackground = true)
@Composable
fun FinancialInformationPreview() {
    FinancialInformation(
        financialInfoDisplay = FinancialInfoDisplay(
            period = "April 2024",
            items = listOf(
                FinancialItemInfoDisplay(
                    name = FinancialInfoType.SALES,
                    price = "269999".formatVolume(),
                    yy = "262.12".displayPriceChange(),
                    barData = listOf("10", "20", "30", "-20", "50")
                ),
                FinancialItemInfoDisplay(
                    name = FinancialInfoType.GROSS_PROFIT,
                    price = "269999".formatVolume(),
                    yy = "262.12".displayPriceChange(),
                    barData = listOf("10", "20", "30", "-20", "50")
                ),
                FinancialItemInfoDisplay(
                    name = FinancialInfoType.OPERATING_INCOME,
                    price = "269999".formatVolume(),
                    yy = "262.12".displayPriceChange(),
                    barData = listOf("10", "20", "30", "-20", "50")
                ),
                FinancialItemInfoDisplay(
                    name = FinancialInfoType.NET_PROFIT,
                    price = "269999".formatVolume(),
                    yy = "262.12".displayPriceChange(),
                    barData = listOf("10", "20", "30", "-20", "50")
                ),
                FinancialItemInfoDisplay(
                    name = FinancialInfoType.OPERATING_MARGIN,
                    price = "269999".formatVolume(),
                    yy = "262.12".displayPriceChange(),
                    barData = listOf("10", "20", "30", "-20", "50")
                ),
            )
        )
    )
}

