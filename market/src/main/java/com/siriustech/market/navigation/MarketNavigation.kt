package com.siriustech.market.navigation

import android.content.Intent
import androidx.activity.compose.ManagedActivityResultLauncher
import androidx.activity.result.ActivityResult
import androidx.fragment.app.FragmentActivity
import androidx.navigation.NavController
import com.siriustech.market.screen.refineasset.RefineAssetListArguments
import com.siriustech.merit.app_common.navigation.argument.market.MarketProfileArgument
import com.siriustech.merit.app_common.screen.pdfviewer.PDFViewerArguments
import dagger.hilt.EntryPoint
import dagger.hilt.InstallIn
import dagger.hilt.components.SingletonComponent

/**
 * Created by He<PERSON>tet
 */
interface MarketNavigation {
    fun onNavigateToSelectMarketCategoryActivity(
        resultCallback: ManagedActivityResultLauncher<Intent, ActivityResult>,
        activity: FragmentActivity,
        selectedValue : String
    )

    fun onNavigateToInstrumentSearchActivity(activity: FragmentActivity)
    fun onNavigateToRefineAssetListActivity(
        activity: FragmentActivity,
        arguments: RefineAssetListArguments? = null,
        refineAssetListResultCallback: ManagedActivityResultLauncher<Intent, ActivityResult>,
    )

    fun onNavigateToPDFViewerActivity(activity: FragmentActivity, arguments: PDFViewerArguments)
    fun onNavigateToMarketProfile(navController: NavController, args: MarketProfileArgument)
    fun onNavigateToNotification(activity: FragmentActivity)
    fun onNavigateToSettings(activity: FragmentActivity)
    fun onNavigateToChatActivity(activity: FragmentActivity)
    fun onNavigateToCreateWealthPlanActivity(
        activity: FragmentActivity,
        createWealthPlanActivityResultCallback: ManagedActivityResultLauncher<Intent, ActivityResult>,
    )

    fun onNavigateToOnboardingSetup(activity: FragmentActivity)
}

@EntryPoint
@InstallIn(SingletonComponent::class)
interface MarketNavigationEntryPoint {
    fun marketNavigation(): MarketNavigation
}