<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content"
    android:background="@drawable/bg_tone_radius_2"
    android:orientation="vertical"
    android:padding="6dp">


    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal">

        <androidx.appcompat.widget.AppCompatTextView
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:fontFamily="@font/noto_sans_light"
            android:text="Date"
            android:textColor="#585858"
            android:textSize="12sp" />

        <androidx.appcompat.widget.AppCompatTextView
            android:id="@+id/tvDate"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="16dp"
            android:fontFamily="@font/noto_sans_medium"
            android:textColor="#000000"
            android:textSize="12sp"
            tools:text="Date" />


    </LinearLayout>


    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal">

        <androidx.appcompat.widget.AppCompatTextView
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:fontFamily="@font/noto_sans_light"
            android:text="@string/key0486"
            android:textColor="#585858"
            android:textSize="12sp" />

        <androidx.appcompat.widget.AppCompatTextView
            android:id="@+id/tvOpen"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="16dp"
            android:fontFamily="@font/noto_sans_medium"
            android:textColor="#000000"
            android:textSize="12sp"
            tools:text="20.00" />

        <androidx.appcompat.widget.AppCompatTextView
            android:id="@+id/tvOpenCurrency"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:fontFamily="@font/noto_sans_semi_bold"
            android:paddingStart="4dp"
            android:text="USD"
            android:textColor="#9F9F9F"
            android:textSize="8dp" />


    </LinearLayout>

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal">

        <androidx.appcompat.widget.AppCompatTextView
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:fontFamily="@font/noto_sans_light"
            android:text="@string/key0487"
            android:textColor="#585858"
            android:textSize="12sp" />

        <androidx.appcompat.widget.AppCompatTextView
            android:id="@+id/tvHigh"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="16dp"
            android:fontFamily="@font/noto_sans_medium"
            android:textColor="#000000"
            android:textSize="12sp"
            tools:text="20.00" />

        <androidx.appcompat.widget.AppCompatTextView
            android:id="@+id/tvHighCurrency"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:fontFamily="@font/noto_sans_semi_bold"
            android:paddingStart="4dp"
            android:text="USD"
            android:textColor="#9F9F9F"
            android:textSize="8dp" />


    </LinearLayout>

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal">

        <androidx.appcompat.widget.AppCompatTextView
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:fontFamily="@font/noto_sans_light"
            android:text="@string/key0488"
            android:textColor="#585858"
            android:textSize="12sp" />

        <androidx.appcompat.widget.AppCompatTextView
            android:id="@+id/tvLow"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="16dp"
            android:fontFamily="@font/noto_sans_medium"
            android:textColor="#000000"
            android:textSize="12sp"
            tools:text="20.00" />

        <androidx.appcompat.widget.AppCompatTextView
            android:id="@+id/tvLowCurrency"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:fontFamily="@font/noto_sans_semi_bold"
            android:paddingStart="4dp"
            android:text="USD"
            android:textColor="#9F9F9F"
            android:textSize="8dp" />


    </LinearLayout>

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal">

        <androidx.appcompat.widget.AppCompatTextView
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:fontFamily="@font/noto_sans_light"
            android:text="@string/key0489"
            android:textColor="#585858"
            android:textSize="12sp" />

        <androidx.appcompat.widget.AppCompatTextView
            android:id="@+id/tvClose"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="16dp"
            android:fontFamily="@font/noto_sans_medium"
            android:textColor="#000000"
            android:textSize="12sp"
            tools:text="20.00" />

        <androidx.appcompat.widget.AppCompatTextView
            android:id="@+id/tvCloseCurrency"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:fontFamily="@font/noto_sans_semi_bold"
            android:paddingStart="4dp"
            android:text="USD"
            android:textColor="#9F9F9F"
            android:textSize="8dp" />


    </LinearLayout>

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal">

        <androidx.appcompat.widget.AppCompatTextView
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:fontFamily="@font/noto_sans_light"
            android:text="@string/key0490"
            android:textColor="#585858"
            android:textSize="12sp" />

        <androidx.appcompat.widget.AppCompatTextView
            android:id="@+id/tvVolume"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="16dp"
            android:fontFamily="@font/noto_sans_medium"
            android:textColor="#000000"
            android:textSize="12sp"
            tools:text="20.00" />

    </LinearLayout>


</LinearLayout>