package com.siriustech.history.screen.refinehistory.component

import androidx.compose.runtime.Composable
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.fragment.app.FragmentActivity
import com.siriustech.core_ui_compose.component.CommonToolbar
import com.siriustech.core_ui_compose.component.ToolbarProperties
import com.siriustech.merit.app_common.ext.colorTxtTitle
import com.siriustech.merit.app_common.theme.LocalTypography
import com.siriustech.merit.app_common.typeenum.HistoryFilterType
import com.siriustech.merit.app_common.R as AppCommonR

@Composable
fun RefineHistoryListToolbar(
    filterType: HistoryFilterType = HistoryFilterType.TRADE_HISTORY,
    onReset: () -> Unit = {},
) {
    val titleResId = when (filterType) {
        HistoryFilterType.TRADE_HISTORY -> {
            AppCommonR.string.key0685
        }

        HistoryFilterType.DEPOSIT_HISTORY -> {
            AppCommonR.string.key0789
        }

        HistoryFilterType.WITHDRAWAL_HISTORY -> {
            AppCommonR.string.key0790
        }
    }
    val activity = LocalContext.current as FragmentActivity
    CommonToolbar(
        onRightActionClicked = { onReset() },
        onLeftActionClicked = { activity.finish() },
        properties = ToolbarProperties(
            rightActionResId = AppCommonR.drawable.ic_action_refersh,
            leftActionResId = AppCommonR.drawable.ic_action_close,
            titleTextStyle = LocalTypography.current.text14.semiBold.colorTxtTitle(),
            title = stringResource(id = titleResId), iconTitle = painterResource(
                id = AppCommonR.drawable.ic_filter_black
            )
        )
    )
}