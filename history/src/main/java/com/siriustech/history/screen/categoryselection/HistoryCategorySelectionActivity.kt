package com.siriustech.history.screen.categoryselection

import android.os.Build
import android.os.Bundle
import androidx.activity.compose.setContent
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.toArgb
import com.siriustech.core_ui_compose.ext.ChangeSystemBarsTheme
import com.siriustech.merit.app_common.R
import com.siriustech.merit.app_common.theme.AppComposeActivity
import com.siriustech.merit.app_common.theme.AppScreen
import com.siriustech.merit.app_common.theme.AppViewModel
import dagger.hilt.android.AndroidEntryPoint

/**
 * Created by <PERSON><PERSON><PERSON>
 */
@AndroidEntryPoint
class HistoryCategorySelectionActivity : AppComposeActivity() {

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContent {
            ChangeSystemBarsTheme(lightTheme = true, Color.White.toArgb())
            AppScreen(vm = AppViewModel()) {
                HistoryCategorySelectionScreen()
            }
        }
    }

    companion object {
        const val RESULT_EXTRA_ASSET_DATA = "RESULT_EXTRA_CATEGORY_DATA"
    }
}