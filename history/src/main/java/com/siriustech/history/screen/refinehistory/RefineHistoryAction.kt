package com.siriustech.history.screen.refinehistory

import com.siriustech.merit.app_common.component.common.FilterTagDisplayModel
import com.siriustech.merit.app_common.theme.AppAction
import org.threeten.bp.LocalDate

/**
 * Created by <PERSON><PERSON> Htet
 */
sealed interface RefineHistoryAction : AppAction {
    data class OnToggleProductCategoryModal(val show: Boolean) : RefineHistoryAction
    data object OnGetProductCategory : RefineHistoryAction
    data class OnDeleteProductCategory(val tag : FilterTagDisplayModel) : RefineHistoryAction
    data object OnPrepareFilterApply : RefineHistoryAction
    data object OnReset : RefineHistoryAction
    data class OnChangeDate(val date : LocalDate) : RefineHistoryAction
    data class OnToggleDatePicker(val show : Boolean) : RefineHistoryAction
}