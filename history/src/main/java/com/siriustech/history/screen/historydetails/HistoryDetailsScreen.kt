package com.siriustech.history.screen.historydetails

import androidx.compose.foundation.Image
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.unit.dp
import androidx.fragment.app.FragmentActivity
import androidx.hilt.navigation.compose.hiltViewModel
import androidx.navigation.NavController
import androidx.navigation.compose.rememberNavController
import coil3.compose.AsyncImage
import coil3.request.ImageRequest
import coil3.request.crossfade
import com.siriustech.core_ui_compose.base.SingleEventEffect
import com.siriustech.core_ui_compose.ext.bottomStroke
import com.siriustech.history.component.HistoryItemDisplayData
import com.siriustech.history.domain.display.HistoryDisplayModel
import com.siriustech.history.navigation.HistoryNavigationEntryPoint
import com.siriustech.history.screen.historydetails.components.OrderDetails
import com.siriustech.history.screen.historydetails.components.TransactionDetails
import com.siriustech.merit.app_common.R
import com.siriustech.merit.app_common.component.container.PaddingEnd
import com.siriustech.merit.app_common.component.container.PaddingStart
import com.siriustech.merit.app_common.component.loading.HorizontalProgressBar
import com.siriustech.merit.app_common.component.marquee.StockExchangeMarquee
import com.siriustech.merit.app_common.component.modalbts.ExchangeCurrencyModalBottomSheet
import com.siriustech.merit.app_common.component.separator.SeparatorLine
import com.siriustech.merit.app_common.data.display.MarketAssetDisplayData
import com.siriustech.merit.app_common.ext.colorTxtParagraph
import com.siriustech.merit.app_common.ext.colorTxtTitle
import com.siriustech.merit.app_common.navigation.argument.market.MarketProfileArgument
import com.siriustech.merit.app_common.theme.AppScreen
import com.siriustech.merit.app_common.theme.LocalDimens
import com.siriustech.merit.app_common.theme.LocalTypography
import com.siriustech.merit.app_common.typeenum.HistoryFilterType
import dagger.hilt.android.EntryPointAccessors
import timber.log.Timber
import com.siriustech.merit.app_common.R as AppCommonR

/**
 * Created by Hein Htet
 */
@Composable
fun HistoryDetailsScreen(
    navController: NavController = rememberNavController(),
    historyItemDisplayData: HistoryItemDisplayData,
    viewModel: HistoryDetailsViewModel = hiltViewModel(),
) {
    val activity = LocalContext.current as FragmentActivity
    val orderDetails = viewModel.outputs.displayOrderDetails.collectAsState()
    val transactionDetails = viewModel.outputs.displayTransactionDetails.collectAsState()
    val showLoading = viewModel.outputs.showLoading.collectAsState()
    val exchangeMarqueeData = viewModel.outputs.displayStockExchangeItems

    val navigation = remember {
        EntryPointAccessors.fromApplication(activity, HistoryNavigationEntryPoint::class.java)
            .historyNavigation()
    }

    var showCurrencyExchangeModal by remember {
        mutableStateOf(false)
    }

    LaunchedEffect(Unit) {
        viewModel.inputs.init(historyItemDisplayData)
    }

    SingleEventEffect(viewModel.appAction) {
        when (it) {
            is HistoryDetailsAction.OnToggleCurrencyPairModal -> {
                showCurrencyExchangeModal = it.show
            }

            is HistoryDetailsAction.OnNavigateToMarketAssetDetails -> {
                navigation.onNavigateToMarketProfile(
                    navController,
                    MarketProfileArgument(
                        MarketAssetDisplayData(
                            id = (it.item.instrumentId ?: -1).toString(),
                            symbol = it.item.assetSymbol.orEmpty(),
                            logo = it.item.logo,
                            unrealizedGl = it.item.priceChange,
                            unrealizedGlRate = it.item.priceChangeRate,
                            marketPrice = it.item.price,
                            exchange = it.item.exchange
                        )
                    )
                )
            }

            is HistoryDetailsAction.OnNavigateToPreviewPDF -> {
                navigation.onNavigateToPDFViewer(it.arguments, activity)
            }
        }
    }

    AppScreen(vm = viewModel, ignorePaddingValue = true) {
        Column {
            HistoryDetailsToolbar(
                type = historyItemDisplayData.historyFilterType,
                logo = orderDetails.value.logo,
                id = if (historyItemDisplayData.historyFilterType == HistoryFilterType.TRADE_HISTORY) orderDetails.value.orderId else transactionDetails.value.transactionId,
                showLogo = historyItemDisplayData.historyFilterType == HistoryFilterType.TRADE_HISTORY
            )
            Box(modifier = Modifier.fillMaxWidth()) {
                HorizontalProgressBar(visible = showLoading.value)
                StockExchangeMarquee(modifier = Modifier.padding(top = LocalDimens.current.dimen12),
                    items = exchangeMarqueeData,
                    onItemClick = {
                        viewModel.onTriggerActions(
                            HistoryDetailsAction.OnToggleCurrencyPairModal(
                                show = true
                            )
                        )
                    })
            }
            if (historyItemDisplayData.historyFilterType == HistoryFilterType.TRADE_HISTORY) {
                OrderDetails(details = orderDetails.value,
                    onTriggerAction = {
                        viewModel.onTriggerActions(it)
                    })
            } else {
                TransactionDetails(
                    details = transactionDetails.value,
                    sid = viewModel.outputs.sid,
                    onTriggerAction = {
                        viewModel.onTriggerActions(it)
                    })
            }
        }
    }
    if (showCurrencyExchangeModal) {
        ExchangeCurrencyModalBottomSheet(items = exchangeMarqueeData, onDismissed = {
            viewModel.onTriggerActions(HistoryDetailsAction.OnToggleCurrencyPairModal(show = false))
        })
    }
}


@Composable
fun HistoryDetailsToolbar(
    type: HistoryFilterType,
    id: Int,
    logo: String? = null,
    showLogo: Boolean = false,
) {
    val context = LocalContext.current
    val viewType = when (type) {
        HistoryFilterType.TRADE_HISTORY -> stringResource(id = AppCommonR.string.key0720)
        HistoryFilterType.DEPOSIT_HISTORY -> stringResource(id = AppCommonR.string.key0350)
        HistoryFilterType.WITHDRAWAL_HISTORY -> stringResource(id = AppCommonR.string.key0349)

    }
    Box(
        modifier = Modifier
            .fillMaxWidth()
            .height(LocalDimens.current.dimen44)
            .bottomStroke(color = Color.Gray.copy(alpha = 0.1f))
            .padding(LocalDimens.current.dimen12),
    ) {
        Image(
            modifier = Modifier
                .clickable {
                    (context as FragmentActivity).finish()
                },
            painter = painterResource(id = AppCommonR.drawable.ic_action_close),
            contentDescription = "Back Arrow Resource"
        )
        Row(
            modifier = Modifier.fillMaxWidth(),
            horizontalArrangement = Arrangement.Center,
            verticalAlignment = Alignment.CenterVertically
        ) {
            Text(
                text = stringResource(id = AppCommonR.string.key0714).plus(" $viewType : "),
                style = LocalTypography.current.text14.semiBold.colorTxtTitle()
            )
            if (showLogo) {
                PaddingStart(value = LocalDimens.current.dimen4)
                AsyncImage(
                    modifier = Modifier.size(LocalDimens.current.dimen14)
                        .clip(RoundedCornerShape(50)),
                    model = ImageRequest.Builder(LocalContext.current)
                        .data(logo)
                        .crossfade(true)
                        .build(),
                    placeholder = painterResource(R.drawable.ic_product_category_placeholder),
                    error = painterResource(R.drawable.ic_product_category_placeholder),
                    contentDescription = "Product Category Placeholder",
                    contentScale = ContentScale.Crop,
                    onError = {
                        Timber.d("Image Loading error ${it.result}")
                    }
                )
                PaddingEnd(value = LocalDimens.current.dimen4)
            }
            Text(
                text = id.toString(),
                style = LocalTypography.current.text14.light.colorTxtParagraph()
            )
        }
    }
}


