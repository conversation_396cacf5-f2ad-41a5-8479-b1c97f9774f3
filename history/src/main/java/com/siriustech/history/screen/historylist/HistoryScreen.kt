package com.siriustech.history.screen.historylist

import android.app.Activity
import androidx.activity.compose.rememberLauncherForActivityResult
import androidx.activity.result.contract.ActivityResultContracts
import androidx.compose.animation.core.tween
import androidx.compose.animation.fadeIn
import androidx.compose.animation.fadeOut
import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.Text
import androidx.compose.material3.pulltorefresh.pullToRefresh
import androidx.compose.material3.pulltorefresh.rememberPullToRefreshState
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.unit.dp
import androidx.fragment.app.FragmentActivity
import androidx.hilt.navigation.compose.hiltViewModel
import androidx.navigation.NavController
import com.siriustech.core_ui_compose.base.SingleEventEffect
import com.siriustech.history.component.HistoryList
import com.siriustech.history.navigation.HistoryNavigationEntryPoint
import com.siriustech.history.screen.categoryselection.HistoryCategorySelectionActivity
import com.siriustech.history.screen.refinehistory.RefineHistoryListActivity
import com.siriustech.merit.app_common.R
import com.siriustech.merit.app_common.component.common.FilterTags
import com.siriustech.merit.app_common.component.container.PaddingStart
import com.siriustech.merit.app_common.component.container.PaddingTop
import com.siriustech.merit.app_common.component.header.DashboardToolbar
import com.siriustech.merit.app_common.component.header.DashboardToolbarProperties
import com.siriustech.merit.app_common.component.loading.HorizontalProgressBar
import com.siriustech.merit.app_common.component.marquee.StockExchangeMarquee
import com.siriustech.merit.app_common.component.modalbts.ExchangeCurrencyModalBottomSheet
import com.siriustech.merit.app_common.data.display.HistoryFilterModel
import com.siriustech.merit.app_common.ext.colorTxtTitle
import com.siriustech.merit.app_common.ext.noRippleClickable
import com.siriustech.merit.app_common.navigation.InstrumentSearch
import com.siriustech.merit.app_common.navigation.argument.history.OrderHistoryListArgument
import com.siriustech.merit.app_common.theme.AppScreen
import com.siriustech.merit.app_common.theme.AppViewModel
import com.siriustech.merit.app_common.theme.LocalAppColor
import com.siriustech.merit.app_common.theme.LocalDimens
import com.siriustech.merit.app_common.theme.LocalTypography
import com.siriustech.merit.app_common.typeenum.HistoryFilterType
import com.siriustech.merit.app_common.typeenum.UIConfig
import dagger.hilt.android.EntryPointAccessors

/**
 * Created by Hein Htet
 */

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun HistoryScreen(
    navController: NavController,
    ignorePaddingValue: Boolean = true,
    viewModel: HistoryViewModel = hiltViewModel(),
    argument: OrderHistoryListArgument? = null,
    isDetailsMode: Boolean = false,
) {

    val activity = LocalContext.current as FragmentActivity
    val navigation = remember {
        EntryPointAccessors.fromApplication(activity, HistoryNavigationEntryPoint::class.java)
            .historyNavigation()
    }

    var showCurrencyExchangeModal by remember {
        mutableStateOf(false)
    }
    val pullToRefreshState = rememberPullToRefreshState()
    val showLoading = viewModel.outputs.showLoading.collectAsState()
    val exchangeMarqueeData = viewModel.outputs.displayStockExchangeItems
    val displayHistoryItem = viewModel.outputs.displayHistoryItem
    val currentFilterMap = viewModel.outputs.currentFilterMap.collectAsState()
    val selectedCategoryDisplayTitle = viewModel.outputs.currentFilterType.collectAsState()
    val filterTags = viewModel.outputs.filterTags.collectAsState()

    LaunchedEffect(Unit) {
        viewModel.inputs.onInitContext(context = activity)
    }

    val categorySelectionResultCallback =
        rememberLauncherForActivityResult(contract = ActivityResultContracts.StartActivityForResult()) { result ->
            if (result.resultCode == Activity.RESULT_OK) {
                val type =
                    result.data?.getSerializableExtra(HistoryCategorySelectionActivity.RESULT_EXTRA_ASSET_DATA) as? HistoryFilterType?
                type?.let {
                    viewModel.inputs.onUpdateFilterType(it)
                }
            }
        }

    val refineHistoryResultCallback =
        rememberLauncherForActivityResult(contract = ActivityResultContracts.StartActivityForResult()) { result ->
            if (result.resultCode == Activity.RESULT_OK) {
                val filterModel =
                    result.data?.getSerializableExtra(RefineHistoryListActivity.RESULT_EXTRA_REFINE_RESULT) as? HistoryFilterModel?
                filterModel?.let {
                    viewModel.inputs.updateOnFilterModel(filterModel)
                }
            }
        }

    val historyDetailsCallback =
        rememberLauncherForActivityResult(contract = ActivityResultContracts.StartActivityForResult()) { result ->

        }

    SingleEventEffect(sideEffectFlow = viewModel.appAction) {
        when (it) {
            is HistoryAction.OnToggleCurrencyPairModal -> {
                showCurrencyExchangeModal = it.show
            }

            HistoryAction.OnNavigateToCategorySelection -> {
                navigation.onNavigateToSelectHistoryCategoryActivity(
                    categorySelectionResultCallback,
                    activity
                )
            }

            is HistoryAction.OnNavigateToRefineHistory -> {
                navigation.onNavigateToRefineHistoryListActivity(
                    activity,
                    viewModel.outputs.currentFilterModel,
                    refineHistoryResultCallback,
                )
            }

            is HistoryAction.OnNavigateToHistoryDetails -> {
                navigation.onNavigateToHistoryDetails(activity, it.data, historyDetailsCallback)
            }

            is HistoryAction.OnNavigateToChat -> {
                navigation.onNavigateToChatActivity(activity)
            }
        }
    }

    LaunchedEffect(Unit) {
        if (argument != null) {
            viewModel.inputs.onInitArgument(argument.historyFilterType, argument.historyFilterModel)
        }
    }



    AppScreen(vm = AppViewModel(), ignorePaddingValue = ignorePaddingValue) {
        Column {
            DashboardToolbar(
                onBackPress = {
                    navController.popBackStack()
                },
                properties = DashboardToolbarProperties(showBackArrow = isDetailsMode),
                onSearchInstrument = {
//                    navigation.onNavigateToInstrumentSearchActivity(activity)
                    navController.navigate(InstrumentSearch)
                }, onNotificationClicked = {
                    navigation.onNavigateToNotification(activity)
                }, onSettingsClicked = {
                    navigation.onNavigateToSettings(activity)
                }, onChatIconClicked = {
                    viewModel.onTriggerActions(HistoryAction.OnNavigateToChat)
                })

            Box(modifier = Modifier.fillMaxWidth()) {
                HorizontalProgressBar(visible = showLoading.value)
                StockExchangeMarquee(modifier = Modifier.padding(top = LocalDimens.current.dimen12),
                    items = exchangeMarqueeData,
                    onItemClick = {
                        viewModel.onTriggerActions(HistoryAction.OnToggleCurrencyPairModal(show = true))
                    })
            }
            Column(
                modifier = Modifier
                    .padding(top = LocalDimens.current.dimen18)
                    .fillMaxSize()
                    .pullToRefresh(
                        showLoading.value,
                        state = pullToRefreshState,
                        enabled = true,
                        onRefresh = {
                            viewModel.inputs.onApiCall()
                        })
            ) {
                HistoryHeaderActions(viewModel)
                androidx.compose.animation.AnimatedVisibility(
                    enter = fadeIn(initialAlpha = 0.4f),
                    exit = fadeOut(animationSpec = tween(durationMillis = 250)),
                    visible = filterTags.value.isNotEmpty()
                ) {
                    FilterTags(
                        items = filterTags.value,
                        modifier = Modifier.padding(horizontal = LocalDimens.current.dimen12)
                    )
                }
                PaddingTop(value = LocalDimens.current.dimen6)
                HistoryList(
                    historyFilterType = selectedCategoryDisplayTitle.value,
                    items = displayHistoryItem,
                    modifier = Modifier.padding(
                        horizontal = LocalDimens.current.dimen12,
                        vertical = LocalDimens.current.dimen16
                    ),
                    onItemClicked = {
                        viewModel.onTriggerActions(HistoryAction.OnNavigateToHistoryDetails(it))
                    },
                    onNavigationToMarket = {
                        if (selectedCategoryDisplayTitle.value == HistoryFilterType.TRADE_HISTORY) {
                            navigation.onNavigateToMarketTab(navController)
                        } else {
                            navigation.onNavigateToPortfolio(navController)
                        }
                    }
                )
            }
        }
        if (showCurrencyExchangeModal) {
            ExchangeCurrencyModalBottomSheet(items = exchangeMarqueeData, onDismissed = {
                viewModel.onTriggerActions(HistoryAction.OnToggleCurrencyPairModal(show = false))
            })
        }
    }
}

@Composable
fun HistoryHeaderActions(viewModel: HistoryViewModel) {
    val selectedCategoryDisplayTitle = viewModel.outputs.currentFilterType.collectAsState()
    Row(
        horizontalArrangement = Arrangement.SpaceBetween,
        modifier = Modifier
            .padding(horizontal = LocalDimens.current.dimen12)
            .fillMaxWidth()
            .background(LocalAppColor.current.bgDefault)
    ) {
        Box(
            modifier = Modifier
                .clip(RoundedCornerShape(UIConfig.TOGGLE_BUTTON_CORNER_RADIUS.dp))
                .background(LocalAppColor.current.bgTone)
                .padding(
                    horizontal = LocalDimens.current.dimen12,
                    vertical = LocalDimens.current.dimen14,
                )
                .noRippleClickable { viewModel.onTriggerActions(HistoryAction.OnNavigateToCategorySelection) }

        ) {
            Row(verticalAlignment = Alignment.CenterVertically) {
                Text(
                    text = selectedCategoryDisplayTitle.value.getDisplayName(),
                    style = LocalTypography.current.text14.semiBold.colorTxtTitle()
                )
                PaddingStart(value = LocalDimens.current.dimen24)
                Image(
                    painter = painterResource(id = R.drawable.ic_right_arrow),
                    contentDescription = "Arrow ImageResource"
                )
            }
        }
        Box(
            modifier = Modifier
                .background(LocalAppColor.current.bgTone)
                .size(LocalDimens.current.dimen44)
                .padding(
                    horizontal = LocalDimens.current.dimen12,
                    vertical = LocalDimens.current.dimen14
                )
                .clip(RoundedCornerShape(LocalDimens.current.dimen8))
                .noRippleClickable { viewModel.onTriggerActions(HistoryAction.OnNavigateToRefineHistory) },
            contentAlignment = Alignment.Center
        ) {
            Image(
                painter = painterResource(id = R.drawable.ic_filter_black),
                contentDescription = "Filter Image Resource"
            )
        }
    }
}


