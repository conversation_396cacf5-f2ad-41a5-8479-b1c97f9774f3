package com.siriustech.history.screen.historydetails

import com.siriustech.history.domain.display.HistoryDisplayModel
import com.siriustech.merit.app_common.screen.pdfviewer.PDFViewerArguments
import com.siriustech.merit.app_common.theme.AppAction

/**
 * Created by <PERSON><PERSON>
 */
sealed interface HistoryDetailsAction : AppAction {
    data class OnToggleCurrencyPairModal(val show : Boolean) : HistoryDetailsAction
    data class OnNavigateToMarketAssetDetails(val item : HistoryDisplayModel) : HistoryDetailsAction
    data class OnNavigateToPreviewPDF(val arguments: PDFViewerArguments) : HistoryDetailsAction
}