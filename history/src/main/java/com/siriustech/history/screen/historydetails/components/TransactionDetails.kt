package com.siriustech.history.screen.historydetails.components

import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.aspectRatio
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.verticalScroll
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableFloatStateOf
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.style.TextAlign
import coil3.compose.AsyncImage
import coil3.compose.SubcomposeAsyncImage
import coil3.network.NetworkHeaders
import coil3.network.httpHeaders
import coil3.request.CachePolicy
import coil3.request.ImageRequest
import coil3.request.crossfade
import com.siriustech.history.domain.display.TransactionDetailsDisplayModel
import com.siriustech.history.screen.historydetails.HistoryDetailsAction
import com.siriustech.merit.app_common.R
import com.siriustech.merit.app_common.component.button.ButtonProperties
import com.siriustech.merit.app_common.component.button.ThirdButton
import com.siriustech.merit.app_common.component.container.LabelAmountCurrency
import com.siriustech.merit.app_common.component.container.LabelValue
import com.siriustech.merit.app_common.component.container.PaddingTop
import com.siriustech.merit.app_common.component.separator.SeparatorLine
import com.siriustech.merit.app_common.ext.colorTxtInactive
import com.siriustech.merit.app_common.screen.pdfviewer.PDFViewerArguments
import com.siriustech.merit.app_common.theme.LocalDimens
import com.siriustech.merit.app_common.theme.LocalTypography
import kotlinx.coroutines.launch
import timber.log.Timber

/**
 * Created by Hein Htet
 */

@Composable
fun TransactionDetails(
    modifier: Modifier = Modifier,
    details: TransactionDetailsDisplayModel = TransactionDetailsDisplayModel(),
    onTriggerAction: (action: HistoryDetailsAction) -> Unit = {},
    sid: String = "",
) {
    val activity = LocalContext.current
    var proofImageAvailable by remember {
        mutableStateOf(true)
    }
    val itemModifier = Modifier.padding(top = LocalDimens.current.dimen4)

    LaunchedEffect(Unit) {

    }

    Column(
        modifier = modifier
            .fillMaxSize()
            .verticalScroll(rememberScrollState())
            .padding(LocalDimens.current.dimen12)
    ) {
        LabelValue(
            label = stringResource(id = R.string.key0716),
            value = details.transactionId.toString()
        )
        LabelValue(
            modifier = itemModifier,
            label = stringResource(id = R.string.key0717),
            value = details.transactionDate
        )

        LabelValue(
            modifier = itemModifier,
            label = stringResource(id = R.string.key0718),
            value = details.transactionType
        )

        LabelValue(
            modifier = itemModifier,
            label = stringResource(id = R.string.key0513),
            value = details.currency
        )


        LabelAmountCurrency(
            modifier = itemModifier,
            label = stringResource(id = R.string.key0384),
            currency = details.currency,
            amount = details.amount
        )
        LabelValue(
            modifier = itemModifier,
            label = stringResource(id = R.string.key0719),
            value = details.transactionStatus?.getDisplayName().orEmpty(),
            extraUi = {
                details.transactionStatus?.Badge()
            }
        )

        LabelValue(
            modifier = itemModifier,
            label = stringResource(id = R.string.key0382),
            value = details.bankAcc
        )
        PaddingTop(value = LocalDimens.current.dimen12)
        SeparatorLine(modifier = Modifier.padding(vertical = LocalDimens.current.dimen12))

        if (proofImageAvailable && !details.proofPdfUrl.isNullOrEmpty()) {
            Box(modifier = Modifier.fillMaxWidth(), contentAlignment = Alignment.Center) {
                ThirdButton(
                    onClicked = {
                        onTriggerAction(
                            HistoryDetailsAction.OnNavigateToPreviewPDF(
                                arguments =
                                PDFViewerArguments(
                                    url = details.proofPdfUrl,
                                    title = activity.getString(
                                        R.string.key0791
                                    )
                                )
                            )
                        )
                    },
                    modifier = Modifier,
                    properties = ButtonProperties(
                        text = stringResource(id = R.string.key0791)
                    )
                )
            }
        } else if (proofImageAvailable && !details.proofImageUrl.isNullOrEmpty()) {
            ImageWithActualAspectRatio(
                sid = sid,
                modifier = Modifier,
                ratio = details.proofImageSize,
                imageUrl = details.proofImageUrl,
                onLoadError = {
                    proofImageAvailable = false
                })
        } else {
            PaddingTop(value = LocalDimens.current.dimen24)
            Text(
                text = stringResource(id = R.string.key0788),
                modifier = Modifier.fillMaxWidth(),
                textAlign = TextAlign.Center,
                style = LocalTypography.current.text14.light.colorTxtInactive()
            )
        }
    }
}

@Composable
fun ImageWithActualAspectRatio(
    modifier: Modifier = Modifier,
    ratio : Float? = null,
    imageUrl: String,
    sid: String,
    onLoadError: () -> Unit = {},
) {
    var aspectRatio by remember { mutableFloatStateOf(1f) }
    var scope = rememberCoroutineScope()

    // Load image metadata
    LaunchedEffect(imageUrl) {
        scope.launch {
            aspectRatio = ratio ?: -1f
            if (aspectRatio == -1f) {
                onLoadError()
            }
        }
    }

    SubcomposeAsyncImage(
        model = imageUrl,
        contentDescription = null,
        modifier = modifier.fillMaxWidth(),
        contentScale = ContentScale.FillWidth, // Adjust based on your requirement
        loading = {
        },
        success = {
            Box(
                modifier = Modifier
                    .fillMaxWidth()
                    .aspectRatio(aspectRatio)
            ) {
                AsyncImage(
                    modifier = Modifier
                        .fillMaxSize(),
                    model = ImageRequest.Builder(LocalContext.current)
                        .data(imageUrl)
                        .crossfade(true)
                        .httpHeaders(
                            headers = NetworkHeaders.Builder()
                                .set("sid", sid)
                                .build()
                        )
                        .diskCachePolicy(CachePolicy.ENABLED)
                        .build(),
                    placeholder = painterResource(R.drawable.user_default_profile),
                    error = painterResource(R.drawable.user_default_profile),
                    contentDescription = "Product Category Placeholder",
                    contentScale = ContentScale.Crop,
                    onError = {
                        Timber.d("PROOF_IMAGE_Image Loading error ${it.result.throwable.message}")
                    }
                )
            }
        },
        error = {
            onLoadError()
        }
    )
}
