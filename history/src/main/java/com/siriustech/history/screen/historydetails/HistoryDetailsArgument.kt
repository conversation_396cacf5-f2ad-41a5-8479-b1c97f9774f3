package com.siriustech.history.screen.historydetails

import androidx.lifecycle.SavedStateHandle
import androidx.navigation.toRoute
import com.siriustech.history.HistoryRouteName
import com.siriustech.history.component.HistoryItemDisplayData
import com.siriustech.merit.app_common.ext.serializableType
import kotlin.reflect.typeOf
import kotlinx.serialization.Serializable

/**
 * Created by <PERSON><PERSON>
 */

@Serializable
data class HistoryDetailsArguments(
    val historyItemDisplayData: HistoryItemDisplayData,
) {
    companion object {
        val typeMap =
            mapOf(typeOf<HistoryDetailsArguments>() to serializableType<HistoryDetailsArguments>())

        fun from(savedStateHandle: SavedStateHandle) =
            savedStateHandle.toRoute<HistoryRouteName.HistoryDetails>(typeMap)
    }
}
