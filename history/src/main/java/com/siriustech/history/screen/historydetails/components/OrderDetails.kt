package com.siriustech.history.screen.historydetails.components

import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.padding
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.tooling.preview.Preview
import com.siriustech.history.domain.display.HistoryDisplayModel
import com.siriustech.history.screen.historydetails.HistoryDetailsAction
import com.siriustech.merit.app_common.component.button.AccentButton
import com.siriustech.merit.app_common.component.button.ButtonProperties
import com.siriustech.merit.app_common.component.common.ProductCategoryDetailsItem
import com.siriustech.merit.app_common.component.common.WalletSummary
import com.siriustech.merit.app_common.component.container.LabelAmountCurrency
import com.siriustech.merit.app_common.component.container.LabelValue
import com.siriustech.merit.app_common.component.modalbts.ProductCategoryDetailsModalDisplayData
import com.siriustech.merit.app_common.component.separator.SeparatorLine
import com.siriustech.merit.app_common.data.display.WalletSummaryDisplayData
import com.siriustech.merit.app_common.theme.LocalAppColor
import com.siriustech.merit.app_common.theme.LocalDimens
import com.siriustech.merit.app_common.typeenum.OrderSide
import com.siriustech.merit.app_common.typeenum.OrderStatus
import com.siriustech.merit.app_common.R as AppCommonR

/**
 * Created by Hein Htet
 */
@Composable
fun OrderDetails(
    modifier: Modifier = Modifier,
    details: HistoryDisplayModel = HistoryDisplayModel(),
    onTriggerAction: (action: HistoryDetailsAction) -> Unit = {},
) {
    val itemModifier = Modifier.padding(top = LocalDimens.current.dimen4)
    Column(
        modifier = modifier
            .fillMaxSize()
            .padding(LocalDimens.current.dimen12)
    ) {
        ProductCategoryDetailsItem(
            modifier = Modifier,
            data = ProductCategoryDetailsModalDisplayData(
                unrealizedGl = details.priceChange,
                unrealizedGlRate = details.priceChangeRate,
                currency = details.currency,
                symbol = details.assetSymbol.orEmpty(),
                exchange = details.exchange,
                name = details.assetName,
                riskLevel = details.riskLevel,
                marketValue = details.price,
            ),
        )
        SeparatorLine(modifier = Modifier.padding(vertical = LocalDimens.current.dimen12))
        LabelValue(
            label = stringResource(id = AppCommonR.string.key0702),
            value = details.orderId.toString()
        )
        details.orderStatus?.let {
            LabelValue(
                modifier = itemModifier,
                label = stringResource(id = AppCommonR.string.key0703),
                value = it.getDisplayName(),
                extraUi = {
                    it.Badge()
                }
            )
        }
        LabelValue(
            modifier = itemModifier,
            label = stringResource(id = AppCommonR.string.key0704),
            value = details.tradeDate
        )
        LabelValue(
            modifier = itemModifier,
            label = stringResource(id = AppCommonR.string.key0705),
            value = details.settleDate
        )

        LabelValue(
            modifier = itemModifier,
            label = stringResource(id = AppCommonR.string.key0706),
            value = details.orderSide?.getDisplayName().orEmpty(),
            valueTextStyle = TextStyle(
                color = details.orderSide?.getColor() ?: LocalAppColor.current.txtParagraph
            )
        )
        LabelValue(
            modifier = itemModifier,
            label = stringResource(id = AppCommonR.string.key0707),
            value = details.currency
        )
        LabelAmountCurrency(
            modifier = itemModifier,
            label = stringResource(id = AppCommonR.string.key0708),
            currency = details.currency,
            amount = details.unitPrice
        )
        LabelValue(
            modifier = itemModifier,
            label = stringResource(id = AppCommonR.string.key0709),
            value = details.quantity
        )
        LabelAmountCurrency(
            modifier = itemModifier,
            label = stringResource(id = AppCommonR.string.key0710),
            currency = details.currency,
            amount = details.brokerAgeAmount
        )
        LabelAmountCurrency(
            modifier = itemModifier,
            label = stringResource(id = AppCommonR.string.key0711),
            currency = details.currency,
            amount = details.otherFee
        )
        SeparatorLine(modifier = Modifier.padding(vertical = LocalDimens.current.dimen12))

        LabelAmountCurrency(
            modifier = itemModifier,
            label = stringResource(id = AppCommonR.string.key0712),
            currency = details.currency,
            amount = details.transactionCost
        )
        LabelAmountCurrency(
            modifier = itemModifier,
            label = stringResource(id = AppCommonR.string.key0713),
            currency = details.currency,
            amount = details.settleAmount
        )
        Spacer(modifier = Modifier.weight(1f))
        AccentButton(
            onClicked = {
                onTriggerAction(HistoryDetailsAction.OnNavigateToMarketAssetDetails(item = details))
            },
            properties = ButtonProperties(
                text = stringResource(id = AppCommonR.string.key0715),
                icon = painterResource(id = AppCommonR.drawable.ic_right_arrow)
            )
        )
    }
}


@Preview(showBackground = true, showSystemUi = true)
@Composable
fun PreviewOrderDetails() {
    OrderDetails(
        modifier = Modifier,
        HistoryDisplayModel(
            orderId = 123456,
            orderStatus = OrderStatus.PENDING,
            tradeDate = "23-12-2024 14:56:23",
            settleDate = "23-12-2024 14:56:23",
            orderSide = OrderSide.BUY,
            currency = "USD",
            unitPrice = "333.33",
            quantity = "10",
            brokerAgeAmount = "555",
            otherFee = "55555555",
            transactionCost = "555",
            settleAmount = "555"
        )
    )
}