package com.siriustech.history.screen.historydetails

import android.content.Context
import androidx.compose.runtime.mutableStateListOf
import androidx.compose.runtime.snapshots.SnapshotStateList
import coil3.ImageLoader
import coil3.network.NetworkHeaders
import coil3.network.httpHeaders
import coil3.request.ImageRequest
import coil3.request.SuccessResult
import coil3.size.Size
import com.core.network.base.getError
import com.siriustech.history.component.HistoryItemDisplayData
import com.siriustech.history.domain.HistoryDetailsMapper.mapToHistoryDetailsDisplay
import com.siriustech.history.domain.HistoryDetailsMapper.mapToTransactionDetailsDisplayModel
import com.siriustech.history.domain.display.HistoryDisplayModel
import com.siriustech.history.domain.display.TransactionDetailsDisplayModel
import com.siriustech.merit.apilayer.service.history.orderdetails.OrderDetailsRequest
import com.siriustech.merit.apilayer.service.history.orderdetails.OrderDetailsUseCase
import com.siriustech.merit.apilayer.service.history.transactiondetails.TransactionDetailsRequest
import com.siriustech.merit.apilayer.service.history.transactiondetails.TransactionDetailsUseCase
import com.siriustech.merit.apilayer.service.market.exchangecurrency.MarketCurrencyRequest
import com.siriustech.merit.apilayer.service.market.exchangecurrency.MarketExchangeCurrencyUseCase
import com.siriustech.merit.apilayer.service.market.exchangecurrency.defaultMarketExchangeCurrencyRequestList
import com.siriustech.merit.app_common.component.marquee.StockExchangeMarqueeData
import com.siriustech.merit.app_common.data.CommonSharedPreferences
import com.siriustech.merit.app_common.mapper.MarketExchangeCurrencyMapper.mapToStockExchangeDisplayData
import com.siriustech.merit.app_common.theme.AppViewModel
import com.siriustech.merit.app_common.typeenum.HistoryFilterType
import com.siriustech.merit.app_common.utils.ErrorCode.mapToErrorDisplay
import dagger.hilt.android.lifecycle.HiltViewModel
import dagger.hilt.android.qualifiers.ApplicationContext
import javax.inject.Inject
import javax.inject.Named
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.catch
import kotlinx.coroutines.flow.collectLatest
import kotlinx.coroutines.flow.map
import kotlinx.coroutines.flow.onCompletion
import kotlinx.coroutines.flow.onStart
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import timber.log.Timber

/**
 * Created by Hein Htet
 */
@HiltViewModel
class HistoryDetailsViewModel @Inject constructor(
    private val orderDetailsUseCase: OrderDetailsUseCase,
    private val marketExchangeCurrencyUseCase: MarketExchangeCurrencyUseCase,
    private val transactionDetailsUseCase: TransactionDetailsUseCase,
    private val commonSharedPreferences: CommonSharedPreferences,
    @Named("BaseUrl") val baseUrl: String,
    @ApplicationContext private val context: Context,

    ) : AppViewModel() {
    private val historyItemDisplayData = MutableStateFlow<HistoryItemDisplayData?>(null)
    private val _showLoading = MutableStateFlow(false)
    private val _displayOrderDetails = MutableStateFlow(HistoryDisplayModel())
    private val _displayTransactionDetails = MutableStateFlow(TransactionDetailsDisplayModel())
    private val _displayStockExchangeItems = mutableStateListOf<StockExchangeMarqueeData>()


    init {
        getMarketExchangeCurrency()
    }

    inner class HistoryDetailsInputs : BaseInputs() {
        fun init(value: HistoryItemDisplayData) {
            historyItemDisplayData.value = value
            if (value.historyFilterType == HistoryFilterType.TRADE_HISTORY) {
                getOrderHistory()
            } else {
                getTransactionHistory()
            }
        }
    }

    inner class HistoryDetailsOutputs : BaseOutputs() {

        val showLoading: StateFlow<Boolean>
            get() = _showLoading
        val displayStockExchangeItems: SnapshotStateList<StockExchangeMarqueeData>
            get() = _displayStockExchangeItems

        val displayOrderDetails: StateFlow<HistoryDisplayModel>
            get() = _displayOrderDetails

        val displayTransactionDetails: StateFlow<TransactionDetailsDisplayModel>
            get() = _displayTransactionDetails

        val sid: String
            get() = commonSharedPreferences.sessionId
    }


    override val inputs = HistoryDetailsInputs()
    override val outputs = HistoryDetailsOutputs()


    private fun getOrderHistory() {
        scope.launch {
            orderDetailsUseCase(
                param = OrderDetailsRequest(
                    orderId = historyItemDisplayData.value?.id ?: -1
                )
            )
                .onStart { _showLoading.value = true }
                .onCompletion { _showLoading.value = false }
                .catch { emitError(it.getError().mapToErrorDisplay()) }
                .map { it.mapToHistoryDetailsDisplay() }
                .collectLatest {
                    _displayOrderDetails.value = it
                }
        }
    }

    private fun getTransactionHistory() {
        scope.launch {
            transactionDetailsUseCase(
                param = TransactionDetailsRequest(
                    transactionId = historyItemDisplayData.value?.id ?: -1
                )
            )
                .onStart { _showLoading.value = true }
                .onCompletion { _showLoading.value = false }
                .catch { emitError(it.getError().mapToErrorDisplay()) }
                .map { it.mapToTransactionDetailsDisplayModel(baseUrl) }
                .collectLatest {
                    if (it.proofImageUrl != null) {
                        val imageRatio = loadImage(
                            context = context,
                            url = it.proofImageUrl,
                            sid = commonSharedPreferences.sessionId
                        )
                        _displayTransactionDetails.value = it.copy(proofImageSize = imageRatio)
                    } else {
                        _displayTransactionDetails.value = it
                    }
                }
        }
    }

    private fun getMarketExchangeCurrency() {
        scope.launch {
            marketExchangeCurrencyUseCase(
                param = MarketCurrencyRequest(
                    currencyList = defaultMarketExchangeCurrencyRequestList
                )
            )
                .onStart { _showLoading.value = true }
                .onCompletion { _showLoading.value = false }
                .catch { }
                .collectLatest {
                    _displayStockExchangeItems.clear()
                    _displayStockExchangeItems.addAll(it.mapToStockExchangeDisplayData())
                }
        }
    }

    private suspend fun loadImage(context: Context, sid: String, url: String): Float? {
        return withContext(Dispatchers.IO) {
            try {
                val imageLoader = ImageLoader(context)
                val request = ImageRequest.Builder(context)
                    .data(url)
                    .httpHeaders(
                        headers = NetworkHeaders.Builder()
                            .set("sid", sid)
                            .build()
                    )
                    .size(Size.ORIGINAL)
                    .build()
                val result = (imageLoader.execute(request) as? SuccessResult)?.image
                Timber.d("LOAD_IMAGE $result")
                result?.width?.let { width ->
                    result.height.let { height ->
                        if (height != 0) width.toFloat() / height.toFloat() else null
                    }
                }
            } catch (e: Exception) {
                Timber.d("LOAD_IMAGE Image Loading error ${e.message}")
                null // Handle errors gracefully
            }
        }
    }
}