package com.siriustech.history.screen.categoryselection

import android.app.Activity
import android.content.Intent
import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.mutableStateListOf
import androidx.compose.runtime.remember
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.fragment.app.FragmentActivity
import com.siriustech.core_ui_compose.component.CommonToolbar
import com.siriustech.core_ui_compose.component.ToolbarProperties
import com.siriustech.merit.app_common.component.container.PaddingTop
import com.siriustech.merit.app_common.ext.colorTxtParagraph
import com.siriustech.merit.app_common.ext.colorTxtTitle
import com.siriustech.merit.app_common.ext.noRippleClickable
import com.siriustech.merit.app_common.theme.AppScreen
import com.siriustech.merit.app_common.theme.AppViewModel
import com.siriustech.merit.app_common.theme.LocalAppColor
import com.siriustech.merit.app_common.theme.LocalDimens
import com.siriustech.merit.app_common.theme.LocalTypography
import com.siriustech.merit.app_common.typeenum.HistoryFilterType
import com.siriustech.merit.app_common.R as AppCommonR

/**
 * Created by Hein Htet
 */
@Composable
fun HistoryCategorySelectionScreen() {
    val categoryTypes = remember {
        mutableStateListOf<HistoryFilterType>()
    }

    val activity = LocalContext.current as FragmentActivity

    LaunchedEffect(Unit) {
        categoryTypes.addAll(
            listOf(
                HistoryFilterType.TRADE_HISTORY,
                HistoryFilterType.DEPOSIT_HISTORY,
                HistoryFilterType.WITHDRAWAL_HISTORY
            )
        )
    }

    AppScreen(vm = AppViewModel(), ignorePaddingValue = true) {
        Column(modifier = Modifier.fillMaxSize()) {
            CommonToolbar(
                onRightActionClicked = {
                    activity.finish()
                },
                properties = ToolbarProperties(
                    titleTextStyle = LocalTypography.current.text14.semiBold.colorTxtTitle(),
                    title = stringResource(id = AppCommonR.string.key0445),
                    rightActionResId = AppCommonR.drawable.ic_action_close,
                )
            )
            PaddingTop(value = LocalDimens.current.dimen16)
            categoryTypes.forEachIndexed { index, historyFilterType ->
                Box(
                    modifier = Modifier
                        .padding(horizontal = LocalDimens.current.dimen12)
                        .fillMaxWidth()
                        .height(LocalDimens.current.dimen90)
                        .background(LocalAppColor.current.bgTone)
                        .clip(RoundedCornerShape(LocalDimens.current.dimen4))
                        .noRippleClickable {
                            activity.setResult(Activity.RESULT_OK, Intent().apply {
                                putExtra(
                                    HistoryCategorySelectionActivity.RESULT_EXTRA_ASSET_DATA,
                                    historyFilterType
                                )
                            })
                            activity.finish()
                        },
                    contentAlignment = Alignment.Center
                ) {
                    Column(
                        horizontalAlignment = Alignment.CenterHorizontally,
                        verticalArrangement = Arrangement.Center
                    ) {
                        Image(
                            painter = painterResource(id = historyFilterType.getIconResId()),
                            contentDescription = "Icon Image Resource"
                        )
                        PaddingTop(value = LocalDimens.current.dimen8)
                        Text(
                            text = historyFilterType.getDisplayName(),
                            style = LocalTypography.current.text14.medium.colorTxtParagraph()
                        )
                    }
                }
                PaddingTop(value = LocalDimens.current.dimen8)
            }
        }
    }
}