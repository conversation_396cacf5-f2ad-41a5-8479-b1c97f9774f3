package com.siriustech.history.screen.refinehistory

import android.app.Activity
import android.content.Intent
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxHeight
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.width
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.rememberModalBottomSheetState
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.tooling.preview.Preview
import androidx.fragment.app.FragmentActivity
import androidx.hilt.navigation.compose.hiltViewModel
import com.core.util.toDate
import com.siriustech.core_ui_compose.base.SingleEventEffect
import com.siriustech.core_ui_compose.ext.bottomStroke
import com.siriustech.history.screen.refinehistory.component.RefineHistoryCurrencyBySection
import com.siriustech.history.screen.refinehistory.component.RefineHistoryListToolbar
import com.siriustech.history.screen.refinehistory.component.RefineHistoryProductCategorySection
import com.siriustech.history.screen.refinehistory.component.RefineOrderSideBySection
import com.siriustech.history.screen.refinehistory.component.RefineOrderStatusBySection
import com.siriustech.merit.app_common.component.button.ButtonProperties
import com.siriustech.merit.app_common.component.button.SecondaryBorderButton
import com.siriustech.merit.app_common.component.button.SecondaryButton
import com.siriustech.merit.app_common.component.common.DateRangeInputPicker
import com.siriustech.merit.app_common.component.common.DateRangeInputPickerProperties
import com.siriustech.merit.app_common.component.container.PaddingTop
import com.siriustech.merit.app_common.component.modalbts.ModelListBottomSheet
import com.siriustech.merit.app_common.component.modalbts.ModelListBottomSheetProperties
import com.siriustech.merit.app_common.component.wheel_picker_compose.modal.WheelDatePickerModal
import com.siriustech.merit.app_common.component.wheel_picker_compose.modal.WheelDatePickerModalProperties
import com.siriustech.merit.app_common.data.display.HistoryFilterModel
import com.siriustech.merit.app_common.ext.DATE_FORMAT_16
import com.siriustech.merit.app_common.ext.toEpochMilli
import com.siriustech.merit.app_common.ext.toLocalDate
import com.siriustech.merit.app_common.theme.AppScreen
import com.siriustech.merit.app_common.theme.LocalDimens
import com.siriustech.merit.app_common.typeenum.HistoryFilterType
import org.threeten.bp.LocalTime
import com.siriustech.merit.app_common.R as AppCommonR

/**
 * Created by Hein Htet
 */

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun RefineHistoryListScreen(viewModel: RefineHistoryViewModel = hiltViewModel()) {
    val refineModel = viewModel.outputs.refineModal.collectAsState()
    val displayProductCategoryItems = viewModel.outputs.productCategory
    var showProductModalList by remember { mutableStateOf(false) }
    val activity = LocalContext.current as FragmentActivity

    LaunchedEffect(Unit) {
        val model =
            activity.intent.getSerializableExtra(RefineHistoryListActivity.RESULT_EXTRA_REFINE_RESULT) as? HistoryFilterModel
        model?.let {
            viewModel.inputs.onInitFilterModal(model)
        }
    }

    var showDatePickerModal by remember { mutableStateOf(false) }
    var pickDateType by remember {
        mutableStateOf("")
    }

    SingleEventEffect(sideEffectFlow = viewModel.appAction) {
        when (it) {
            is RefineHistoryAction.OnToggleProductCategoryModal -> {
                showProductModalList = it.show
            }

            is RefineHistoryAction.OnPrepareFilterApply -> {
                activity.setResult(Activity.RESULT_OK, Intent().apply {
                    putExtra(
                        RefineHistoryListActivity.RESULT_EXTRA_REFINE_RESULT,
                        viewModel.outputs.refineModal.value.copy(
                            productCategories = displayProductCategoryItems.filter { it.isSelected }
                        )
                    )
                })
                activity.finish()
            }

            is RefineHistoryAction.OnChangeDate -> {
                var date = it.date
                if (pickDateType == "start") {
                    val endDate = refineModel.value.endDate
                    if (endDate != null) {
                        if (it.date.isAfter(endDate.toLocalDate())) {
                            viewModel.outputs.refineModal.value.copy(
                                startDate = endDate.toLocalDate().atTime(LocalTime.MIN)
                                    .toEpochMilli()
                            )
                        } else {
                            viewModel.inputs.onUpdateFilterModel(
                                viewModel.outputs.refineModal.value.copy(
                                    startDate = date.atTime(LocalTime.MIN).toEpochMilli()
                                )
                            )
                        }
                    } else {
                        viewModel.inputs.onUpdateFilterModel(
                            viewModel.outputs.refineModal.value.copy(
                                startDate = date.atTime(LocalTime.MIN).toEpochMilli()
                            )
                        )
                    }
                } else {
                    val startDate = refineModel.value.startDate
                    if (startDate != null) {
                        if (it.date.isBefore(startDate.toLocalDate()) || it.date == startDate.toLocalDate()) {
                            viewModel.inputs.onUpdateFilterModel(
                                viewModel.outputs.refineModal.value.copy(
                                    endDate = startDate.toLocalDate().atTime(LocalTime.MAX)
                                        .toEpochMilli()
                                )
                            )
                        } else {
                            viewModel.inputs.onUpdateFilterModel(
                                viewModel.outputs.refineModal.value.copy(
                                    endDate = date.atTime(LocalTime.MAX).toEpochMilli()
                                )
                            )
                        }
                    } else {
                        viewModel.inputs.onUpdateFilterModel(
                            viewModel.outputs.refineModal.value.copy(
                                endDate = date.atTime(LocalTime.MAX).toEpochMilli()
                            )
                        )
                    }
                }
            }

            is RefineHistoryAction.OnToggleDatePicker -> {
                showDatePickerModal = it.show
            }
        }
    }

    val isTradeHistoryFilterType = refineModel.value.type == HistoryFilterType.TRADE_HISTORY

    AppScreen(vm = viewModel, ignorePaddingValue = true) {
        Column {
            RefineHistoryListToolbar(
                filterType = refineModel.value.type,
                onReset = {
                    viewModel.onTriggerActions(RefineHistoryAction.OnReset)
                })
            Column(
                modifier = Modifier
                    .fillMaxSize()
                    .padding(
                        top = LocalDimens.current.dimen16,
                    ),
            ) {
                Column(
                    modifier = Modifier
                        .weight(1f)
                        .padding(
                            horizontal = LocalDimens.current.dimen12,
                        )
                ) {
                    if (isTradeHistoryFilterType) {
                        RefineOrderSideBySection(refineModel.value.side, onSortTypeChanged = {
                            viewModel.inputs.onUpdateFilterModel(refineModel.value.copy(side = it))
                        })
                        PaddingTop(value = LocalDimens.current.dimen16)
                    }
                    RefineOrderStatusBySection(
                        statusTitle = stringResource(id = if (isTradeHistoryFilterType) AppCommonR.string.key0689 else AppCommonR.string.key0787),
                        refineModel.value.status,isTradeHistoryFilterType, onSortTypeChanged = {
                            viewModel.inputs.onUpdateFilterModel(refineModel.value.copy(status = it))
                        })
                    PaddingTop(value = LocalDimens.current.dimen12)

                    if (!isTradeHistoryFilterType) {
                        RefineHistoryCurrencyBySection(
                            refineModel.value.currency, onSortTypeChanged = {
                                viewModel.inputs.onUpdateFilterModel(refineModel.value.copy(currency = it))
                            })
                        PaddingTop(value = LocalDimens.current.dimen12)
                    }

                    if (isTradeHistoryFilterType) {
                        RefineHistoryProductCategorySection(
                            displayProductCategoryItems,
                            onSelectProductCategory = {
                                viewModel.onTriggerActions(RefineHistoryAction.OnGetProductCategory)
                            },
                            onDeleteProductCategory = {
                                viewModel.onTriggerActions(
                                    RefineHistoryAction.OnDeleteProductCategory(it)
                                )
                            })
                        PaddingTop(value = LocalDimens.current.dimen12)
                    }
                    DateRangeInputPicker(
                        onClearStartDate = {
                            viewModel.inputs.onUpdateFilterModel(
                                viewModel.outputs.refineModal.value.copy(
                                    startDate = null,
                                )
                            )
                        },
                        onClearEndDate = {
                            viewModel.inputs.onUpdateFilterModel(
                                viewModel.outputs.refineModal.value.copy(
                                    endDate = null,
                                )
                            )
                        },
                        properties = DateRangeInputPickerProperties(
                            title = stringResource(id = AppCommonR.string.key0690),
                            startDate = refineModel.value.startDate?.toDate(DATE_FORMAT_16)
                                .orEmpty(),
                            endDate = refineModel.value.endDate?.toDate(DATE_FORMAT_16).orEmpty(),
                        ),
                        onPickStatDate = {
                            pickDateType = "start"
                            viewModel.onTriggerActions(RefineHistoryAction.OnToggleDatePicker(show = true))
                        },
                        onPickEndDate = {
                            pickDateType = "end"
                            viewModel.onTriggerActions(RefineHistoryAction.OnToggleDatePicker(show = true))
                        }
                    )
                }
                Box(
                    modifier = Modifier
                        .fillMaxWidth()
                        .height(LocalDimens.current.dimen1)
                        .bottomStroke(color = Color.Gray.copy(alpha = 0.1f))
                ) {

                }
                PaddingTop(value = LocalDimens.current.dimen16)
                Row(
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(horizontal = LocalDimens.current.dimen12)
                ) {
                    SecondaryBorderButton(
                        onClicked = { activity.finish() },
                        modifier = Modifier.weight(1f), properties = ButtonProperties(
                            text = stringResource(
                                id = AppCommonR.string.key1011,
                            )
                        )
                    )
                    Spacer(modifier = Modifier.width(LocalDimens.current.dimen4))
                    SecondaryButton(
                        modifier = Modifier.weight(1f),
                        properties = ButtonProperties(text = stringResource(id = AppCommonR.string.key0411)),
                        onClicked = {
                            viewModel.onTriggerActions(RefineHistoryAction.OnPrepareFilterApply)
                        }
                    )
                }
            }
        }
    }

    if (showProductModalList) {
        ModelListBottomSheet(sheetState = rememberModalBottomSheetState(skipPartiallyExpanded = true),
            modifier = Modifier.fillMaxHeight(0.8f),
            onItemClicked = {
                viewModel.inputs.setSelectProductCategory(it)
            },
            properties = ModelListBottomSheetProperties(
                items = displayProductCategoryItems,
                title = stringResource(id = AppCommonR.string.key0476),
                prefixTitle = stringResource(id = AppCommonR.string.key0410),
                searchEnable = true,
                allowMultipleSelect = true,
                searchPlaceholder = stringResource(id = AppCommonR.string.key0476),
            ),
            onDismissed = {
                showProductModalList = false
            })
    }

    if (showDatePickerModal) {
        WheelDatePickerModal(
            currentLocalDate = (if (pickDateType == "start") refineModel.value.startDate else refineModel.value.endDate).toLocalDate(),
            onDismissed = { showDatePickerModal = false },
            properties = WheelDatePickerModalProperties(
                title = stringResource(id = AppCommonR.string.key0599),
                prefixTitle = stringResource(id = AppCommonR.string.key0102),
                showDate = true,
                showConfirmButton = true
            ),
            onSnappedDate = {
                viewModel.onTriggerActions(RefineHistoryAction.OnChangeDate(it))
            }
        )
    }
}

@Preview(showBackground = true, showSystemUi = true)
@Composable
fun PreviewRefineHistoryListScreen() {
    RefineHistoryListScreen()
}

