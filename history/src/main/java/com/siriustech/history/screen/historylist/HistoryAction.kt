package com.siriustech.history.screen.historylist

import com.siriustech.history.component.HistoryItemDisplayData
import com.siriustech.merit.app_common.theme.AppAction

/**
 * Created by <PERSON><PERSON> <PERSON><PERSON><PERSON>
 */
sealed interface HistoryAction : AppAction {
    data class OnToggleCurrencyPairModal(val show: Boolean) : HistoryAction
    data object OnNavigateToCategorySelection : HistoryAction
    data object OnNavigateToRefineHistory : HistoryAction
    data class OnNavigateToHistoryDetails(val data : HistoryItemDisplayData) : HistoryAction
    data object OnNavigateToChat : HistoryAction
}