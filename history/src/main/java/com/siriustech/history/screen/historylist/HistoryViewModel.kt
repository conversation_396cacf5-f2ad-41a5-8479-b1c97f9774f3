package com.siriustech.history.screen.historylist

import android.annotation.SuppressLint
import android.content.Context
import androidx.compose.runtime.mutableStateListOf
import androidx.compose.runtime.snapshots.SnapshotStateList
import coil3.ImageLoader
import coil3.network.NetworkHeaders
import coil3.network.httpHeaders
import coil3.request.ImageRequest
import coil3.request.SuccessResult
import coil3.size.Size
import com.core.network.base.getError
import com.siriustech.history.component.HistoryItemDisplayData
import com.siriustech.history.domain.HistoryMapper.groupNotificationsByDate
import com.siriustech.history.domain.HistoryMapper.mapToHistoryItemDisplay
import com.siriustech.merit.apilayer.service.history.list.GetHistoryListUseCase
import com.siriustech.merit.apilayer.service.history.list.GetHistoryRequest
import com.siriustech.merit.apilayer.service.history.transactions.TransactionHistoryListUseCase
import com.siriustech.merit.apilayer.service.history.transactions.TransactionHistoryRequest
import com.siriustech.merit.apilayer.service.market.exchangecurrency.MarketCurrencyRequest
import com.siriustech.merit.apilayer.service.market.exchangecurrency.MarketExchangeCurrencyUseCase
import com.siriustech.merit.apilayer.service.market.exchangecurrency.defaultMarketExchangeCurrencyRequestList
import com.siriustech.merit.apilayer.service.market.marketlistdetails.MarketListDetailsUseCase
import com.siriustech.merit.app_common.Constants
import com.siriustech.merit.app_common.component.common.FilterTagDisplayModel
import com.siriustech.merit.app_common.component.marquee.StockExchangeMarqueeData
import com.siriustech.merit.app_common.data.AppCache
import com.siriustech.merit.app_common.data.display.HistoryFilterModel
import com.siriustech.merit.app_common.ext.toDisplayDate
import com.siriustech.merit.app_common.ext.toLocalDate
import com.siriustech.merit.app_common.mapper.MarketExchangeCurrencyMapper.mapToStockExchangeDisplayData
import com.siriustech.merit.app_common.theme.AppViewModel
import com.siriustech.merit.app_common.typeenum.HistoryFilterType
import com.siriustech.merit.app_common.typeenum.OrderSide
import com.siriustech.merit.app_common.typeenum.OrderStatus
import com.siriustech.merit.app_common.utils.ErrorCode.mapToErrorDisplay
import dagger.hilt.android.lifecycle.HiltViewModel
import javax.inject.Inject
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.catch
import kotlinx.coroutines.flow.collectLatest
import kotlinx.coroutines.flow.onCompletion
import kotlinx.coroutines.flow.onStart
import kotlinx.coroutines.launch
import timber.log.Timber

/**
 * Created by Hein Htet
 */
@HiltViewModel
class HistoryViewModel @Inject constructor(
    private val marketExchangeCurrencyUseCase: MarketExchangeCurrencyUseCase,
    private val marketListDetailsUseCase: MarketListDetailsUseCase,
    private val getHistoryListUseCase: GetHistoryListUseCase,
    private val getTransactionHistoryListUseCase: TransactionHistoryListUseCase,
    private val appCache: AppCache,
) : AppViewModel() {
    private val _showLoading = MutableStateFlow(false)
    private val _displayStockExchangeItems = mutableStateListOf<StockExchangeMarqueeData>()
    private val _currentFilterType = MutableStateFlow(HistoryFilterType.TRADE_HISTORY)
    private val _currentFilterMap =
        MutableStateFlow(
            appCache.historyFilterTypeCache
        )
    private val _displayHistoryItem = mutableStateListOf<HistoryItemDisplayData>()
    private val _filterTags = MutableStateFlow<List<FilterTagDisplayModel>>(emptyList())

    @SuppressLint("StaticFieldLeak")
    private var _context: Context? = null


    init {
//        _currentFilterMap.value = mutableMapOf(
//            HistoryFilterType.TRADE_HISTORY to HistoryFilterModel(type = HistoryFilterType.TRADE_HISTORY),
//            HistoryFilterType.DEPOSIT_HISTORY to HistoryFilterModel(type = HistoryFilterType.DEPOSIT_HISTORY),
//            HistoryFilterType.WITHDRAWAL_HISTORY to HistoryFilterModel(type = HistoryFilterType.WITHDRAWAL_HISTORY),
//        )
        appCache.updateHistoryFilterMap(_currentFilterMap.value)
        getMarketExchangeCurrency()
        getHistoryList()
    }

    inner class HistoryInputs : BaseInputs() {

        fun onInitContext(context: Context) {
            _context = context
        }

        fun onApiCall() {
            getHistoryList()
        }

        fun onUpdateFilterType(type: HistoryFilterType) {
            _currentFilterType.value = type
            _currentFilterMap.value[_currentFilterType.value]?.updateFilterTags()
            appCache.updateHistoryFilterMap(_currentFilterMap.value)
            getHistoryList()
        }

        fun updateOnFilterModel(filterModel: HistoryFilterModel) {
            val map = _currentFilterMap.value
            map[_currentFilterType.value] = filterModel
            _currentFilterMap.value = map
            Timber.d("UpdateFilterModel $filterModel")
            Timber.d("UpdateFilterModel ${_currentFilterMap.value}")
            filterModel.updateFilterTags()
            getHistoryList()
        }

        fun onInitArgument(type: HistoryFilterType, filterModel: HistoryFilterModel) {
            _currentFilterType.value = type
            val map = _currentFilterMap.value
            map[_currentFilterType.value] = filterModel
            _currentFilterMap.value = map
            filterModel.updateFilterTags()
            Timber.d("UpdateFilterModel $filterModel")
            Timber.d("UpdateFilterModel ${_currentFilterMap.value}")
            getHistoryList()
        }
    }

    private fun HistoryFilterModel.updateFilterTags() {
        val filterTags = ArrayList<FilterTagDisplayModel>()
        Timber.d("Update_FILTER_TAG $this")
        this.apply {
            if (this.side != OrderSide.ALL) {
                filterTags.add(
                    FilterTagDisplayModel(
                        id = this.side.value,
                        text = this.side.getDisplayName(_context),
                        showClearButton = false
                    )
                )
            }

            if (this.status != OrderStatus.ALL) {
                filterTags.add(
                    FilterTagDisplayModel(
                        id = this.status.value,
                        text = this.status.getDisplayName(_context),
                        showClearButton = false
                    )
                )
            }


            if (this.currency.isNotEmpty() && this.currency != "All") {
                filterTags.add(
                    FilterTagDisplayModel(
                        id = this.currency,
                        text = this.currency,
                        showClearButton = false
                    )
                )
            }

            this.productCategories.forEach {
                filterTags.add(
                    FilterTagDisplayModel(
                        id = it.id,
                        text = it.title,
                        showClearButton = false
                    )
                )
            }
            if (this.startDate != null && this.endDate != null) {
                filterTags.add(
                    FilterTagDisplayModel(
                        id = "filter date",
                        text = "${
                            this.startDate.toLocalDate().toDisplayDate(outputFormat = "dd-MM-yyyy")
                        } ~ ${
                            this.endDate.toLocalDate().toDisplayDate(outputFormat = "dd-MM-yyyy")
                        }",
                        showClearButton = false
                    )
                )
            } else if (this.startDate != null) {
                filterTags.add(
                    FilterTagDisplayModel(
                        id = "filter date",
                        text = "${_context?.getString(com.siriustech.merit.app_common.R.string.key0951)} " + this.startDate.toLocalDate()
                            .toDisplayDate(outputFormat = "dd-MM-yyyy"),
                        showClearButton = false
                    )
                )
            } else if (this.endDate != null) {
                filterTags.add(
                    FilterTagDisplayModel(
                        id = "filter date",
                        text = "${_context?.getString(com.siriustech.merit.app_common.R.string.key0952)} " + this.endDate.toLocalDate()
                            .toDisplayDate(outputFormat = "dd-MM-yyyy"),
                        showClearButton = false
                    )
                )
            }
        }
        _filterTags.value = filterTags
    }

    inner class HistoryOutputs : BaseOutputs() {
        val showLoading: StateFlow<Boolean>
            get() = _showLoading
        val displayStockExchangeItems: SnapshotStateList<StockExchangeMarqueeData>
            get() = _displayStockExchangeItems

        val currentFilterMap: StateFlow<MutableMap<HistoryFilterType, HistoryFilterModel>>
            get() = _currentFilterMap

        val currentFilterType: StateFlow<HistoryFilterType>
            get() = _currentFilterType

        val currentFilterModel: HistoryFilterModel?
            get() {
                return _currentFilterMap.value[_currentFilterType.value]
            }

        val displayHistoryItem: SnapshotStateList<HistoryItemDisplayData>
            get() = _displayHistoryItem

        val filterTags: StateFlow<List<FilterTagDisplayModel>>
            get() = _filterTags
    }


    override val inputs = HistoryInputs()
    override val outputs = HistoryOutputs()


    private fun getMarketExchangeCurrency() {
        scope.launch {
            marketExchangeCurrencyUseCase(
                param = MarketCurrencyRequest(
                    currencyList = defaultMarketExchangeCurrencyRequestList
                )
            )
                .onStart { _showLoading.value = true }
                .onCompletion { _showLoading.value = false }
                .catch { }
                .collectLatest {
                    _displayStockExchangeItems.clear()
                    _displayStockExchangeItems.addAll(it.mapToStockExchangeDisplayData())
                }
        }
    }

    private fun getHistoryList() {
        if (_currentFilterType.value == HistoryFilterType.TRADE_HISTORY) {
            getOrderHistoryList()
        } else {
            getTransactionHistoryList()
        }
    }

    private fun getTransactionHistoryList() {
        val filterModel = _currentFilterMap.value[_currentFilterType.value]
        val request = TransactionHistoryRequest(
            transactionType = _currentFilterType.value.value,
            transactionStatus = (filterModel?.status ?: OrderStatus.ALL).getApiValue(),
            currency = if (filterModel?.currency == "All") "" else filterModel?.currency
                ?: Constants.CURRENCY_USD,
            startTime = filterModel?.startDate ?: 0,
            endTime = filterModel?.endDate ?: 0
        )
        _displayHistoryItem.clear()
        scope.launch {
            getTransactionHistoryListUseCase(param = request)
                .onStart { _showLoading.value = true }
                .onCompletion { _showLoading.value = false }
                .catch { emitError(it.getError().mapToErrorDisplay()) }
                .collectLatest {
                    Timber.d("TRANSACTION_HISTORY ${it}")
                    _displayHistoryItem.clear()
                    _displayHistoryItem.addAll(
                        it.transactionList?.mapToHistoryItemDisplay(_currentFilterType.value)
                            .orEmpty().groupNotificationsByDate()
                    )
                }
        }
    }

    private fun getOrderHistoryList() {
        val filterModel = _currentFilterMap.value[_currentFilterType.value]
        val request = GetHistoryRequest(
            orderSide = (filterModel?.side ?: OrderSide.ALL).getApiValue(),
            orderStatus = (filterModel?.status ?: OrderStatus.ALL).getApiValue(),
            productCategoryList = filterModel?.productCategories.orEmpty().map { it.id },
            orderDateFrom = filterModel?.startDate ?: 0,
            orderDateTo = filterModel?.endDate ?: 0
        )
        _displayHistoryItem.clear()
        scope.launch {
            getHistoryListUseCase(param = request)
                .onStart { _showLoading.value = true }
                .onCompletion { _showLoading.value = false }
                .catch { emitError(it.getError().mapToErrorDisplay()) }
                .collectLatest {
                    _displayHistoryItem.clear()
                    _displayHistoryItem.addAll(
                        it.list?.mapToHistoryItemDisplay().orEmpty().groupNotificationsByDate()
                    )
                }
        }
    }

    private fun loadImage(context: Context, sid: String, url: String) {
        scope.launch {
            try {
                val imageLoader = ImageLoader(context)
                val request = ImageRequest.Builder(context)
                    .data(url)
                    .httpHeaders(
                        headers = NetworkHeaders.Builder()
                            .set("sid", sid)
                            .build()
                    )
                    .size(Size.ORIGINAL)
                    .build()
                val result = (imageLoader.execute(request) as? SuccessResult)?.image
                result?.width?.let { width ->
                    result.height.let { height ->
                        if (height != 0) width.toFloat() / height.toFloat() else null
                    }
                }
            } catch (e: Exception) {
                Timber.d("PROOF_IMAGE_ fetchImageAspectRatio Image Loading error ${e.message}")
                null // Handle errors gracefully
            }
        }
    }
}