package com.siriustech.history.screen.refinehistory

import androidx.compose.runtime.mutableStateListOf
import androidx.compose.runtime.snapshots.SnapshotStateList
import com.siriustech.merit.apilayer.service.authentication.common.eumeration.DropDownEnumeration
import com.siriustech.merit.apilayer.service.authentication.common.eumeration.EnumerationRequest
import com.siriustech.merit.apilayer.service.authentication.common.eumeration.GetEnumerationUseCase
import com.siriustech.merit.app_common.component.modalbts.ModalListDataContent
import com.siriustech.merit.app_common.data.display.HistoryFilterModel
import com.siriustech.merit.app_common.theme.AppAction
import com.siriustech.merit.app_common.theme.AppViewModel
import dagger.hilt.android.lifecycle.HiltViewModel
import javax.inject.Inject
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.catch
import kotlinx.coroutines.flow.collectLatest
import kotlinx.coroutines.flow.onCompletion
import kotlinx.coroutines.flow.onStart
import kotlinx.coroutines.launch

/**
 * Created by Hein Htet
 */
@HiltViewModel
class RefineHistoryViewModel @Inject constructor(
    private val enumerationUseCase: GetEnumerationUseCase,
) : AppViewModel() {
    private val _refineModal = MutableStateFlow<HistoryFilterModel>(HistoryFilterModel())
    private val _showLoading = MutableStateFlow(false)
    private val _productCategory = mutableStateListOf<ModalListDataContent>()
    private val _selectedProductCategory = mutableStateListOf<ModalListDataContent>()

    init {
        getProductCategory()
    }


    inner class RefineHistoryInputs() : BaseInputs() {
        fun onInitFilterModal(model: HistoryFilterModel?) {
            model?.let {
                _refineModal.value = it
                _selectedProductCategory.addAll(it.productCategories)
            }
        }

        fun onUpdateFilterModel(model: HistoryFilterModel) {
            _refineModal.value = model
        }

        fun setSelectProductCategory(category: ModalListDataContent) {
            val index = _productCategory.indexOfLast { it.id == category.id }
            val item = _productCategory[index]
            _productCategory[index] = item.copy(isSelected = !item.isSelected)
        }

        fun removeSelectedProductCategory(id: String) {
            val index = _productCategory.indexOfLast { it.id == id }
            val item = _productCategory[index]
            _productCategory[index] = item.copy(isSelected = false)
        }

        fun onResetDefault() {
            _selectedProductCategory.clear()
            val category = _productCategory.map {
                it.isSelected = false
                it
            }
            _refineModal.value = HistoryFilterModel()
            _productCategory.clear()
            _productCategory.addAll(category)
        }
    }

    inner class RefineHistoryOutputs() : BaseOutputs() {

        val refineModal: StateFlow<HistoryFilterModel>
            get() = _refineModal

        val productCategory: SnapshotStateList<ModalListDataContent>
            get() = _productCategory

    }

    override val inputs = RefineHistoryInputs()
    override val outputs = RefineHistoryOutputs()

    override fun onTriggerActions(action: AppAction) {
        when (action) {
            RefineHistoryAction.OnGetProductCategory -> {
                getProductCategory(false)
            }

            is RefineHistoryAction.OnDeleteProductCategory -> {
                inputs.removeSelectedProductCategory(action.tag.id)
            }

            is RefineHistoryAction.OnReset -> {
                inputs.onResetDefault()
            }
            else -> super.onTriggerActions(action)
        }
    }

    private fun getProductCategory(isInit: Boolean = true) {
        if (_productCategory.isNotEmpty()) {
            onTriggerActions(RefineHistoryAction.OnToggleProductCategoryModal(show = true))
            return
        }
        scope.launch {
            enumerationUseCase(param = EnumerationRequest(codeList = listOf(DropDownEnumeration.PRODUCT_CATEGORY.name)))
                .onStart { _showLoading.value = true }
                .onCompletion { _showLoading.value = false }
                .catch { }
                .collectLatest {
                    _productCategory.clear()
                    _productCategory.addAll(
                        it.list.findLast { it.code == DropDownEnumeration.PRODUCT_CATEGORY.name }?.list.orEmpty()
                            .map {
                                ModalListDataContent(
                                    id = it.value,
                                    title = it.desc,
                                    isSelected = _selectedProductCategory.findLast { selected -> selected.id == it.value } != null)
                            })
                    if (!isInit) {
                        onTriggerActions(RefineHistoryAction.OnToggleProductCategoryModal(show = true))
                    }
                }
        }
    }

}