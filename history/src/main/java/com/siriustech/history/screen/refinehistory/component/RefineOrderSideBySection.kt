package com.siriustech.history.screen.refinehistory.component

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.ExperimentalLayoutApi
import androidx.compose.foundation.layout.FlowRow
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.tooling.preview.Preview
import com.siriustech.merit.app_common.R as AppCommonR
import com.siriustech.merit.app_common.component.button.ToggleThirdButton
import com.siriustech.merit.app_common.component.button.ToggleThirdButtonProperties
import com.siriustech.merit.app_common.ext.colorTxtTitle
import com.siriustech.merit.app_common.theme.LocalAppColor
import com.siriustech.merit.app_common.theme.LocalDimens
import com.siriustech.merit.app_common.theme.LocalTypography
import com.siriustech.merit.app_common.typeenum.OrderSide
import timber.log.Timber

@OptIn(ExperimentalLayoutApi::class)
@Composable
fun RefineOrderSideBySection(
    value: OrderSide? = null,
    onSortTypeChanged: (OrderSide) -> Unit = {},
) {
    Column(
        modifier = Modifier
    ) {
        Timber.d("RefineOrderSide ${value}")
        val row = 2
        val column = 2
        Text(
            text = stringResource(id = AppCommonR.string.key0684),
            style = LocalTypography.current.text14.medium.colorTxtTitle()
        )
        FlowRow(
            modifier = Modifier
                .padding(top = LocalDimens.current.dimen8)
                .fillMaxWidth()
                .background(LocalAppColor.current.bgDefault),
            horizontalArrangement = Arrangement.spacedBy(LocalDimens.current.dimen4),
            maxItemsInEachRow = 2,
        ) {
            val items = listOf(
                ToggleThirdButtonProperties(
                    text = stringResource(id = AppCommonR.string.key0681),
                ),
                ToggleThirdButtonProperties(
                    text = stringResource(id = AppCommonR.string.key0682),
                ),
                ToggleThirdButtonProperties(
                    text = stringResource(id = AppCommonR.string.key0683),
                ),
            )
            repeat(row * column) { index ->
                if (index > items.size - 1) {
                    Box(modifier = Modifier.weight(1f)) {}
                } else {
                    ToggleThirdButton(
                        onSelectedChanged = { onSortTypeChanged(OrderSide.entries[index]) },
                        isSelectedValue = value == (OrderSide.entries[index]),
                        modifier = Modifier.weight(1f),
                        properties = items[index]
                    )
                }
            }
        }
    }
}

@Preview(showBackground = true, showSystemUi = true)
@Composable
fun PreviewRefineOrderSideBySection() {
    RefineOrderSideBySection()
}