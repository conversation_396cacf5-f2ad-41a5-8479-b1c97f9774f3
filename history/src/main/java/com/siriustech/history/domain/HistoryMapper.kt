package com.siriustech.history.domain

import com.core.util.toAmount
import com.siriustech.history.component.HistoryItemDisplayData
import com.siriustech.merit.apilayer.service.history.list.HistoryResponse
import com.siriustech.merit.apilayer.service.history.transactions.TransactionHistoryResponse
import com.siriustech.merit.app_common.ext.TIME_FORMAT_1
import com.siriustech.merit.app_common.ext.toDateTimeWithZoneId
import com.siriustech.merit.app_common.typeenum.HistoryFilterType
import com.siriustech.merit.app_common.typeenum.OrderSide
import com.siriustech.merit.app_common.typeenum.OrderStatus
import com.siriustech.merit.app_common.typeenum.RiskLevel
import org.threeten.bp.Instant
import org.threeten.bp.LocalDate
import org.threeten.bp.ZoneId
import org.threeten.bp.format.DateTimeFormatter

/**
 * Created by Hein Htet
 */
object HistoryMapper {

    fun List<HistoryResponse>?.mapToHistoryItemDisplay(): List<HistoryItemDisplayData> {
        return this.orEmpty()
            .sortedByDescending { it.orderDate }
            .map {
            HistoryItemDisplayData(
                name = it.assetSymbol.orEmpty(),
                exchange = it.exchange.orEmpty(),
                logo = it.assetLogo.orEmpty(),
                orderStatus = OrderStatus.fromParams(it.orderStatus.orEmpty()),
                orderSide = OrderSide.fromParam(it.orderSide.orEmpty()),
                id = it.orderId,
                riskLevel = RiskLevel.fromParam(it.riskLevel.orEmpty()),
                orderTime = it.orderDate?.toDateTimeWithZoneId(
                    format = TIME_FORMAT_1,
                    zoneId = ZoneId.systemDefault()
                ).orEmpty(),
                datetime = it.tradeDate,
                instrumentId = it.instrumentId ?: -1,
                historyFilterType = HistoryFilterType.TRADE_HISTORY
            )
        }
    }

    fun List<TransactionHistoryResponse>?.mapToHistoryItemDisplay(type: HistoryFilterType): List<HistoryItemDisplayData> {
        return this.orEmpty()
            .sortedByDescending { it.createTime }
            .map {
            HistoryItemDisplayData(
                id = it.transactionId ?: -1,
                orderTime = it.createTime?.toDateTimeWithZoneId(
                    format = TIME_FORMAT_1,
                    zoneId = ZoneId.systemDefault()
                ).orEmpty(),
                datetime = it.tradeDate,
                historyFilterType = type,
                currency = it.currency.orEmpty(),
                amount = it.amount?.toAmount(4).orEmpty(),
                bankName = it.bankName.orEmpty(),
                orderStatus = OrderStatus.fromParams(it.transactionStatus.orEmpty())
            )
        }
    }



    fun List<HistoryItemDisplayData>.groupNotificationsByDate(
    ): List<HistoryItemDisplayData> {
        val groupedData = LinkedHashMap<String, List<HistoryItemDisplayData>>()
        val today = LocalDate.now(ZoneId.systemDefault())
        val yesterday = today.minusDays(1)
        val formatter = DateTimeFormatter.ofPattern("dd-MM-yyyy")

        this.sortedByDescending { it.datetime }.groupBy { notification ->
            val notificationDate = Instant.ofEpochMilli(notification.datetime ?: 0L)
                .atZone(ZoneId.systemDefault())
                .toLocalDate()

//            when (notificationDate) {
//                today -> "Today"
////                yesterday -> "Yesterday"
////                else -> formatter.format(notificationDate)
//                else -> formatter.format(notificationDate)
//            }
            formatter.format(notificationDate)
        }.forEach { (key, value) ->
            groupedData[key] = value
        }
        val displayItems = mutableListOf<HistoryItemDisplayData>()
        groupedData.forEach { (header, items) ->
            displayItems.add(HistoryItemDisplayData(isHeader = true, headerDate = header, historyFilterType = HistoryFilterType.TRADE_HISTORY))
            displayItems.addAll(items)
        }
        return displayItems
    }
}