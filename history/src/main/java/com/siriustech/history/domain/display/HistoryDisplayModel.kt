package com.siriustech.history.domain.display

import com.siriustech.merit.app_common.typeenum.OrderSide
import com.siriustech.merit.app_common.typeenum.OrderStatus
import com.siriustech.merit.app_common.typeenum.RiskLevel
import kotlinx.serialization.Serializable

/**
 * Created by <PERSON><PERSON>tet
 */

@Serializable
data class HistoryDisplayModel(
    val orderId: Int = -1,
    val orderStatus: OrderStatus? = null,
    val tradeDate: String = "",
    val settleDate: String = "",
    val orderSide: OrderSide? = null,
    val currency: String = "",
    val unitPrice: String = "",
    val quantity: String = "",
    val brokerAgeAmount: String = "",
    val otherFee: String = "",
    val transactionCost: String = "",
    val settleAmount: String = "",
    val priceChange: String = "",
    val priceChangeRate: String = "",
    val price: String = "",
    val assetName: String = "",
    val logo: String = "",
    val exchange: String = "",
    val riskLevel: RiskLevel? = null,
    val assetSymbol: String? = null,
    val instrumentId : Int? = null
)