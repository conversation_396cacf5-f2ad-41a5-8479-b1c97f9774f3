package com.siriustech.history.domain

import com.siriustech.history.domain.display.HistoryDisplayModel
import com.siriustech.history.domain.display.TransactionDetailsDisplayModel
import com.siriustech.merit.apilayer.service.history.orderdetails.OrderDetailsResponse
import com.siriustech.merit.apilayer.service.history.transactiondetails.TransactionDetailsResponse
import com.siriustech.merit.app_common.Constants
import com.siriustech.merit.app_common.ext.DATE_FORMAT_11
import com.siriustech.merit.app_common.ext.toDateTimeWithZoneId
import com.siriustech.merit.app_common.typeenum.OrderSide
import com.siriustech.merit.app_common.typeenum.OrderStatus
import com.siriustech.merit.app_common.typeenum.RiskLevel
import org.threeten.bp.ZoneId

/**
 * Created by Hein Htet
 */
object HistoryDetailsMapper {

    fun OrderDetailsResponse.mapToHistoryDetailsDisplay(): HistoryDisplayModel {
        return HistoryDisplayModel(
            orderId = this.orderId ?: -1,
            orderStatus = OrderStatus.fromParams(this.orderStatus.orEmpty()),
            orderSide = OrderSide.fromParam(this.orderSide.orEmpty()),
            tradeDate = if (this.tradeDate != null) (this.tradeDate ?: 0L).toDateTimeWithZoneId(
                zoneId = ZoneId.systemDefault(),
                format = DATE_FORMAT_11
            ).orEmpty() else "-",
            settleDate = if(this.settleDate != null) (this.settleDate ?: 0L).toDateTimeWithZoneId(
                zoneId = ZoneId.systemDefault(),
                format = DATE_FORMAT_11
            ).orEmpty() else "-",
            currency = this.currency.orEmpty(),
            unitPrice = this.unitPrice.orEmpty(),
            transactionCost = this.transactionCost.orEmpty(),
            quantity = this.quantity.orEmpty(),
            otherFee = this.otherFee.orEmpty(),
            settleAmount = this.settleAmountWithBroker.orEmpty(),
            brokerAgeAmount = this.brokerageAmount.orEmpty(),
            priceChange = this.priceChange.orEmpty(),
            priceChangeRate = this.priceChangeRate.orEmpty(),
            price = this.price.orEmpty(),
            assetName = this.assetName.orEmpty(),
            assetSymbol = this.assetSymbol.orEmpty(),
            logo = this.assetLogo.orEmpty(),
            exchange = this.exchange.orEmpty(),
            riskLevel = RiskLevel.fromParam(this.riskLevel.orEmpty()),
            instrumentId = this.instrumentId
        )
    }

    fun TransactionDetailsResponse.mapToTransactionDetailsDisplayModel(baseUrl: String = ""): TransactionDetailsDisplayModel {
        var image: String? = null
        var pdf: String? = null

        if (this.proofFile?.documentFileType.orEmpty().contains("pdf")) {
            pdf = "$baseUrl${Constants.COMMON_FILE_BASE_URL}${this.proofFile?.fileKey.orEmpty()}"
        } else {
            image = "$baseUrl${Constants.COMMON_FILE_BASE_URL}${this.proofFile?.fileKey.orEmpty()}"
        }


        return TransactionDetailsDisplayModel(
            transactionId = this.transactionId ?: -1,
//            transactionDate = if (this.settleDate == null) "" else (this.settleDate
//                ?: 0L).toDateTimeWithZoneId(
//                zoneId = ZoneId.systemDefault(),
//                format = DATE_FORMAT_11
//            ).orEmpty(),
            transactionDate = if(this.tradeDate != null) (this.tradeDate ?: 0L).toDateTimeWithZoneId(
                zoneId = ZoneId.systemDefault(),
                format = DATE_FORMAT_11
            ).orEmpty() else "-",
            transactionType = this.transactionType.orEmpty(),
            currency = this.currency.orEmpty(),
            amount = this.amount.orEmpty(),
            transactionStatus = OrderStatus.fromParams(this.status.orEmpty()),
            bankAcc = this.bankAccountInfo?.accountNumber.orEmpty(),
            proofImageUrl = image,
            proofPdfUrl = pdf
        )
    }
}