package com.siriustech.history.navigation

import android.content.Intent
import androidx.activity.compose.ManagedActivityResultLauncher
import androidx.activity.result.ActivityResult
import androidx.fragment.app.FragmentActivity
import androidx.navigation.NavController
import com.siriustech.history.component.HistoryItemDisplayData
import com.siriustech.merit.app_common.data.display.HistoryFilterModel
import com.siriustech.merit.app_common.navigation.argument.market.MarketProfileArgument
import com.siriustech.merit.app_common.screen.pdfviewer.PDFViewerArguments
import dagger.hilt.EntryPoint
import dagger.hilt.InstallIn
import dagger.hilt.components.SingletonComponent

interface HistoryNavigation {
    fun onNavigateToSelectHistoryCategoryActivity(
        resultCallback: ManagedActivityResultLauncher<Intent, ActivityResult>,
        activity: FragmentActivity,
    )

    fun onNavigateToInstrumentSearchActivity(activity: FragmentActivity)
    fun onNavigateToPDFViewerActivity(activity: FragmentActivity, arguments: PDFViewerArguments)
    fun onNavigateToMarketProfile(navController: NavController, args: MarketProfileArgument)
    fun onNavigateToNotification(activity: FragmentActivity)
    fun onNavigateToSettings(activity: FragmentActivity)
    fun onNavigateToRefineHistoryListActivity(
        activity: FragmentActivity,
        arguments: HistoryFilterModel? = null,
        refineAssetListResultCallback: ManagedActivityResultLauncher<Intent, ActivityResult>,
    )

    fun onNavigateToHistoryDetails(
        activity: FragmentActivity, item: HistoryItemDisplayData,
        callback: ManagedActivityResultLauncher<Intent, ActivityResult>,
    )

    fun onNavigateToPDFViewer(arguments: PDFViewerArguments,activity: FragmentActivity)

    fun onNavigateToMarketTab(navController: NavController)
    fun onNavigateToPortfolio(navController: NavController)
    fun onNavigateToChatActivity(activity: FragmentActivity)
}

@EntryPoint
@InstallIn(SingletonComponent::class)
interface HistoryNavigationEntryPoint {
    fun historyNavigation(): HistoryNavigation
}