package com.siriustech.history.component

import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.tooling.preview.Preview
import coil3.compose.AsyncImage
import coil3.request.ImageRequest
import coil3.request.crossfade
import com.siriustech.merit.app_common.Constants
import com.siriustech.merit.app_common.R
import com.siriustech.merit.app_common.component.container.PaddingStart
import com.siriustech.merit.app_common.component.container.PaddingTop
import com.siriustech.merit.app_common.component.text.BadgeText
import com.siriustech.merit.app_common.ext.capitalizeWords
import com.siriustech.merit.app_common.ext.colorTxtInactive
import com.siriustech.merit.app_common.ext.colorTxtParagraph
import com.siriustech.merit.app_common.ext.colorTxtTitle
import com.siriustech.merit.app_common.ext.noRippleClickable
import com.siriustech.merit.app_common.theme.LocalAppColor
import com.siriustech.merit.app_common.theme.LocalDimens
import com.siriustech.merit.app_common.theme.LocalTypography
import com.siriustech.merit.app_common.typeenum.HistoryFilterType
import com.siriustech.merit.app_common.typeenum.OrderSide
import com.siriustech.merit.app_common.typeenum.OrderStatus
import com.siriustech.merit.app_common.typeenum.RiskLevel
import kotlinx.serialization.Serializable
import timber.log.Timber

/**
 * Created by Hein Htet
 */
@Composable
fun HistoryItem(
    data: HistoryItemDisplayData,
    onItemClicked: (HistoryItemDisplayData) -> Unit = {},
    showTime : Boolean = false
) {
    val statusDisplay = when (data.historyFilterType.value.capitalizeWords()) {
        Constants.DEPOSIT -> stringResource(id = R.string.key0350)
        Constants.WITHDRAW -> stringResource(id = R.string.key0955)
        else -> data.historyFilterType.value.capitalizeWords()
    }
    Box(
        modifier = Modifier
            .fillMaxWidth()
            .background(LocalAppColor.current.bgAccent)
            .padding(
                horizontal = LocalDimens.current.dimen12,
                vertical = LocalDimens.current.dimen8
            )
            .noRippleClickable { onItemClicked(data) }
    ) {
        Row(verticalAlignment = Alignment.CenterVertically) {
            if (data.historyFilterType == HistoryFilterType.TRADE_HISTORY) {
                AsyncImage(
                    modifier = Modifier
                        .size(LocalDimens.current.dimen32)
                        .clip(RoundedCornerShape(50)),
                    model = ImageRequest.Builder(LocalContext.current)
                        .data(data.logo)
                        .crossfade(true)
                        .build(),
                    placeholder = painterResource(R.drawable.ic_product_category_placeholder),
                    error = painterResource(R.drawable.ic_product_category_placeholder),
                    contentDescription = "Product Category Placeholder",
                    contentScale = ContentScale.Crop,
                    onError = {
                        Timber.d("Image Loading error ${it.result}")
                    }
                )
                PaddingStart(value = LocalDimens.current.dimen8)
            }
            Column(
                modifier = Modifier
                    .weight(1f)
                    .fillMaxWidth()
            ) {
                if (data.historyFilterType == HistoryFilterType.TRADE_HISTORY) {
                    OrderHistoryTitle(data)
                } else {
                    TransactionHistoryTitle(data)
                }
                PaddingTop(value = LocalDimens.current.dimen2)
                Row(verticalAlignment = Alignment.CenterVertically) {
                    if (data.orderSide != null && data.historyFilterType == HistoryFilterType.TRADE_HISTORY) {
                        data.orderSide.OrderSideBox()
                    } else {
                        val color =
                            if (data.historyFilterType == HistoryFilterType.DEPOSIT_HISTORY) LocalAppColor.current.txtPositive else LocalAppColor.current.txtNegative
                        BadgeText(
                            label = statusDisplay,
                            bgColor = Color.Transparent,
                            textColor = color,
                            modifier = Modifier
                                .border(LocalDimens.current.dimen1, color)
                        )
                    }
                    if (showTime) {
                        PaddingStart(value = LocalDimens.current.dimen8)
                        Image(
                            painter = painterResource(id = R.drawable.ic_clock),
                            contentDescription = "Time Image Resource"
                        )
                        Text(
                            text = data.orderTime,
                            style = LocalTypography.current.text12.regular.colorTxtInactive()
                        )
                    }
                    Spacer(modifier = Modifier.weight(1f))
                    Row(verticalAlignment = Alignment.CenterVertically) {
                        data.orderStatus.Badge()
                        PaddingStart(value = LocalDimens.current.dimen8)
                        Text(
                            text = data.orderStatus.getDisplayName(),
                            style = LocalTypography.current.text12.regular.colorTxtParagraph()
                        )
                    }
                }
            }

        }
    }
}

@Composable
fun OrderHistoryTitle(data: HistoryItemDisplayData) {
    Row(
        verticalAlignment = Alignment.CenterVertically,
        modifier = Modifier
            .fillMaxWidth()
    ) {
        Row(modifier = Modifier.weight(1f, true)) {
            Text(
                text = data.name,
                style = LocalTypography.current.text14.semiBold.colorTxtTitle(),
                maxLines = 1,
                overflow = TextOverflow.Ellipsis,
                modifier = Modifier.weight(1f, false)
            )
            PaddingStart(value = LocalDimens.current.dimen8)
            if (data.exchange.isNotEmpty()) {
                BadgeText(
                    label = data.exchange,
                    bgColor = LocalAppColor.current.bgAccent,
                    textColor = LocalAppColor.current.txtLabel
                )
            }
            if (data.riskLevel != null) {
                PaddingStart(value = LocalDimens.current.dimen8)
                RiskLevel.HIGH.RiskLevelBadge()
            }
        }

        Text(
            text = data.id.toString(),
            style = LocalTypography.current.text14.regular.colorTxtTitle()
        )
    }
}

@Composable
fun TransactionHistoryTitle(data: HistoryItemDisplayData) {
    Row(
        verticalAlignment = Alignment.CenterVertically,
        modifier = Modifier
            .fillMaxWidth()
    ) {
        Row(modifier = Modifier.weight(1f, true), verticalAlignment = Alignment.CenterVertically) {
            Text(
                text = data.amount,
                style = LocalTypography.current.text14.semiBold.colorTxtTitle(),
                maxLines = 1,
                overflow = TextOverflow.Ellipsis,
                modifier = Modifier.weight(1f, false)
            )
            PaddingStart(value = LocalDimens.current.dimen4)
            Text(
                text = data.currency,
                style = LocalTypography.current.text10.light.colorTxtInactive()
            )
        }

        Text(
            text = data.id.toString(),
            style = LocalTypography.current.text14.regular.colorTxtTitle()
        )
    }
}


@Serializable
data class HistoryItemDisplayData(
    val isHeader: Boolean = false,
    val name: String = "",
    val orderSide: OrderSide? = null,
    val exchange: String = "",
    val riskLevel: RiskLevel? = null,
    val id: Int = -1,
    val orderStatus: OrderStatus = OrderStatus.PENDING,
    val orderTime: String = "",
    val logo: String = "",
    val datetime: Long? = null,
    val headerDate: String = "",
    val instrumentId: Int = -1,
    // transaction
    val amount: String = "",
    val bankName: String = "",
    val currency: String = "",
    val historyFilterType: HistoryFilterType,

    ) : java.io.Serializable


@Preview(showBackground = true)
@Composable
fun PreviewHistoryItem() {
    HistoryItem(
        data = HistoryItemDisplayData(
            name = "AssetAssetAs ",
            orderSide = OrderSide.BUY,
            orderStatus = OrderStatus.PENDING,
            orderTime = "14:56:23",
            id = 1234234,
            exchange = "EXC",
            riskLevel = RiskLevel.HIGH,
            currency = "USD",
            amount = "100000",
            historyFilterType = HistoryFilterType.WITHDRAWAL_HISTORY,
        )
    )
}