package com.siriustech.history.component

import androidx.compose.animation.core.tween
import androidx.compose.animation.fadeIn
import androidx.compose.animation.fadeOut
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.verticalScroll
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.tooling.preview.Preview
import com.siriustech.merit.app_common.component.common.portfolio.EmptyItem
import com.siriustech.merit.app_common.component.container.PaddingBottom
import com.siriustech.merit.app_common.component.container.PaddingTop
import com.siriustech.merit.app_common.ext.colorTxtTitle
import com.siriustech.merit.app_common.theme.LocalDimens
import com.siriustech.merit.app_common.theme.LocalTypography
import com.siriustech.merit.app_common.typeenum.HistoryFilterType
import com.siriustech.merit.app_common.R as AppCommonR

/**
 * Created by <PERSON><PERSON> Htet
 */
@Composable
fun HistoryList(
    modifier: Modifier = Modifier,
    historyFilterType: HistoryFilterType = HistoryFilterType.TRADE_HISTORY,
    items: List<HistoryItemDisplayData>,
    onItemClicked: (item: HistoryItemDisplayData) -> Unit = {},
    onNavigationToMarket: () -> Unit = {},
) {
    Column(
        modifier = Modifier
            .verticalScroll(rememberScrollState())
            .then(modifier)
    ) {
        Box(
            modifier = Modifier
                .fillMaxSize(), contentAlignment = Alignment.Center
        ) {
            Column {
                items.forEachIndexed { index, historyItemDisplayData ->
                    if (historyItemDisplayData.isHeader) {
                        Text(
                            text = historyItemDisplayData.headerDate,
                            style = LocalTypography.current.text14.semiBold.colorTxtTitle()
                        )
                        PaddingTop(value = LocalDimens.current.dimen8)
                    } else {
                        HistoryItem(data = historyItemDisplayData, onItemClicked)
                        PaddingBottom(value = LocalDimens.current.dimen8)
                    }
                }
            }
            androidx.compose.animation.AnimatedVisibility(
                visible = items.isEmpty(),
                enter = fadeIn(initialAlpha = 0.4f),
                exit = fadeOut(animationSpec = tween(durationMillis = 250)),
            ) {
                Column {
                    PaddingTop(value = LocalDimens.current.dimen32)
                    EmptyItem(
                        modifier = Modifier,
                        showButton = true,
                        emptyText = stringResource(
                            id = when (historyFilterType) {
                                HistoryFilterType.TRADE_HISTORY -> AppCommonR.string.key0700
                                HistoryFilterType.DEPOSIT_HISTORY -> AppCommonR.string.key0948
                                HistoryFilterType.WITHDRAWAL_HISTORY -> AppCommonR.string.key0949
                            }
                        ),
                        buttonText = stringResource(
                            id = when (historyFilterType) {
                                HistoryFilterType.TRADE_HISTORY -> AppCommonR.string.key0701
                                HistoryFilterType.DEPOSIT_HISTORY -> AppCommonR.string.key0950
                                HistoryFilterType.WITHDRAWAL_HISTORY -> AppCommonR.string.key0950
                            }
                        ),
                        onNavigateToMarket = onNavigationToMarket
                    )
                }
            }
        }
    }
}


@Preview(showBackground = true, showSystemUi = true)
@Composable
fun PreviewHistoryList() {
    HistoryList(items = emptyList())
}