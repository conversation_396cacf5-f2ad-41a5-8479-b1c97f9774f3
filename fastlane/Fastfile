# This file contains the fastlane.tools configuration
# You can find the documentation at https://docs.fastlane.tools
#
# For a list of all available actions, check out
#
#     https://docs.fastlane.tools/actions
#
# For a list of all available plugins, check out
#
#     https://docs.fastlane.tools/plugins/available-plugins
#

# Uncomment the line if you want fastlane to automatically update itself
# update_fastlane

default_platform(:android)

platform :android do
  desc "Runs all the tests"
  lane :test do
    gradle(task: "test")
  end

  desc "Submit a new Beta Build to Crashlytics Beta"
  lane :beta do
    gradle(task: "clean assembleRelease")
    crashlytics
  
    # sh "your_script.sh"
    # You can also use other beta testing services here
  end

  desc "Deploy a new version to the Google Play"
  lane :deploy do
    gradle(task: "clean assembleRelease")
    upload_to_play_store
  end

   desc "Build release APK"
    lane :build_release do
       gradle(
          task: "assemble",
          build_type: "ProdRelease",
          properties: {
            "storePassword" => "SiriusTech",
            "keyPassword" => "SiriusTech"
          }
        )
    end

    lane :upload_to_appcenter_uat do
      # Build the APK (if needed)
      gradle(task: "assembleUatRelease")

      # Path to the generated APK
      apk_path = "app/build/outputs/apk/prod/release/app-prod-release.apk"

      # Upload to App Center
      appcenter_upload(
        api_token: "5bbb88d0dfce0c74b7b989334a3ccf4d71b844de",
        owner_name: "Merit-AM",
        app_name: "Merit-Android",
        apk: apk_path, # or 'aab' for App Bundles
        release_notes: "Uploaded via Fastlane",
        destinations: "Collaborators" # Specify distribution groups
      )
    end

       lane :upload_to_appcenter do
          # Build the APK (if needed)
          gradle(task: "assembleProdRelease")

          # Path to the generated APK
          apk_path = "app/build/outputs/apk/prod/release/app-prod-release.apk"

          # Upload to App Center
          appcenter_upload(
            api_token: "5bbb88d0dfce0c74b7b989334a3ccf4d71b844de",
            owner_name: "Merit-AM",
            app_name: "Merit-Android",
            apk: apk_path, # or 'aab' for App Bundles
            release_notes: "Uploaded via Fastlane",
            destinations: "Collaborators" # Specify distribution groups
          )
        end

end
