package com.siriustech.merit.apilayer.service.market.exchangecurrency

import com.core.network.base.AppUseCaseFlow
import com.core.network.base.Repository
import com.core.network.model.BaseResponse
import dagger.hilt.android.scopes.ViewModelScoped
import javax.inject.Inject

// FLOW USE_CASE
@ViewModelScoped
class MarketExchangeCurrencyUseCase @Inject constructor(
    repository: Repository<MarketCurrencyRequest, BaseResponse<MarketCurrencyListResponse>>,
) : AppUseCaseFlow<MarketCurrencyRequest, MarketCurrencyListResponse>(repository)


 val defaultMarketExchangeCurrencyRequestList = listOf(
    "USD/HKD",
    "USD/EUR",
    "USD/CNY",
    "USD/JPY"
)