package com.siriustech.merit.apilayer.service.authentication.authresetverify

import com.core.network.model.BaseResponse
import kotlinx.serialization.Serializable
import retrofit2.Call
import retrofit2.http.Body
import retrofit2.http.POST

/**
 * Created by <PERSON><PERSON><PERSON>
 */

@Serializable
data class AuthResetVerifyResponse(
    val token: String,
)

@Serializable
data class AuthResetVerifyRequest(
    val username: String,
    val type: String, // FORGOT_PASSWORD,FORGOT_PIN
    val otpType: String, // EMAIL,MOBILE
    val otp: String,
    val refCode: String,
)

interface AuthResetVerifyApiService {


    @POST("user/auth/reset/verification")
    fun request(@Body request: AuthResetVerifyRequest?): Call<BaseResponse<AuthResetVerifyResponse>>
}