package com.siriustech.merit.apilayer.service.history.transactiondetails

import com.core.network.base.AppUseCaseFlow
import com.core.network.base.Repository
import com.core.network.model.BaseResponse
import dagger.hilt.android.scopes.ViewModelScoped
import javax.inject.Inject

// FLOW USE_CASE
@ViewModelScoped
class TransactionDetailsUseCase @Inject constructor(
    repository: Repository<TransactionDetailsRequest, BaseResponse<TransactionDetailsResponse>>,
) : AppUseCaseFlow<TransactionDetailsRequest, TransactionDetailsResponse>(repository)


