package com.siriustech.merit.apilayer.service.onboarding.getuserdocument

import com.core.network.base.AppUseCaseFlow
import com.core.network.base.Repository
import com.core.network.model.BaseResponse
import dagger.hilt.android.scopes.ViewModelScoped
import javax.inject.Inject

/**
 * Created by <PERSON><PERSON>
 */

// FLOW USE_CASE
@ViewModelScoped
class GetUserDocumentUseCase @Inject constructor(
    repository: Repository<GetUserDocumentRequest, BaseResponse<GetUserDocumentResponse>>,
) : AppUseCaseFlow<GetUserDocumentRequest, GetUserDocumentResponse>(repository)


