package com.siriustech.merit.apilayer.service.authentication.resetauth

import com.core.network.base.RetrofitAPICaller
import com.core.network.model.BaseResponse
import javax.inject.Inject
import retrofit2.Response

/**
 * Created by <PERSON><PERSON>t
 */
class AuthResetCaller @Inject constructor (
    private val apiService: AuthResetApiService
) : RetrofitAPICaller<AuthResetRequest,BaseResponse<AuthResetResponse>>() {
    override fun call(reqParam: AuthResetRequest?): Response<BaseResponse<AuthResetResponse>> {
        return apiService.request(reqParam).execute()
    }
}