package com.siriustech.merit.apilayer.service.home.watchlist

import com.core.network.model.BaseResponse
import kotlinx.serialization.Serializable
import retrofit2.Call
import retrofit2.http.Body
import retrofit2.http.POST

/**
 * Created by He<PERSON> <PERSON><PERSON>t
 */

@Serializable
data class MarketWatchListRequest(
    val allocationClass: String? = null,
    val topType: String,
)


@Serializable
data class MarketWatchListResponse(
    val assetList: List<MarketWatchListItemResponse>? = emptyList(),
)


@Serializable
data class MarketWatchListItemResponse(
    val instrumentId: Int?,
    val symbol: String? = null,
    val name: String? = null,
    val exchange: String? = null,
    val timeZone: String? = null,
    val market: String? = null,
    val instrumentName: String? = null,
    val currency: String? = null,
    val logo: String? = null,
    val riskLevel: String? = null,
    val riskRating: String? = null,
    val instrumentClass: String? = null,
    val instrumentType: String? = null,
    val instrumentCategory: String? = null,
    val allocation: String? = null,
    val assetClass: String? = null,
    val lastPrice: String? = null,
    val valueChange: String? = null,
    val valueChangeRate: String? = null,
    val favorite: Boolean? = null,
    val totalVolume: String? = null,
    val totalAmount: String? = null,
    val value : String? = null
)


interface MarketWatchListApiService {
    @POST("market/watch/list")
    fun request(
        @Body request: MarketWatchListRequest? = null,
    ): Call<BaseResponse<MarketWatchListResponse>>
}