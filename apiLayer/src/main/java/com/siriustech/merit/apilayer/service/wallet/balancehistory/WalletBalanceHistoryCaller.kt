package com.siriustech.merit.apilayer.service.wallet.balancehistory

import com.core.network.base.RetrofitAPICaller
import com.core.network.model.BaseResponse
import javax.inject.Inject
import retrofit2.Response

/**
 * Created by <PERSON><PERSON><PERSON>
 */
class WalletBalanceHistoryCaller @Inject constructor(
    private val apiService: WalletBalanceHistoryApiService,
) : RetrofitAPICaller<WalletBalanceHistoryRequest, BaseResponse<WalletBalanceHistoryResponse>>() {
    override fun call(reqParam: WalletBalanceHistoryRequest?): Response<BaseResponse<WalletBalanceHistoryResponse>> {
        return apiService.request(reqParam).execute()
    }
}