package com.siriustech.merit.apilayer.service.user.sendchat

import com.core.network.model.BaseResponse
import kotlinx.serialization.Serializable
import retrofit2.Call
import retrofit2.http.Body
import retrofit2.http.PUT

/**
 * Created by <PERSON><PERSON> <PERSON><PERSON><PERSON>
 */


@Serializable
data class SendChatMessageRequest(
    val message: String,
    val fileList: List<SendChatMessageFileRequest>? = emptyList(),
)

@Serializable
data class SendChatMessageFileRequest(
    val fileName: String,
    val fileType: String,
    val fileKey: String,
    val fileSize : Long
)

@Serializable
class SendChatMessageResponse()

interface SendChatMessageApiService {


    @PUT("user/chat/message/sent")
    fun request(@Body request: SendChatMessageRequest?): Call<BaseResponse<SendChatMessageResponse>>
}