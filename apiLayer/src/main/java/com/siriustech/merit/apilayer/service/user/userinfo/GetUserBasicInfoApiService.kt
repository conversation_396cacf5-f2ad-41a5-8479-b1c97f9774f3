package com.siriustech.merit.apilayer.service.user.userinfo

import com.core.network.model.BaseResponse
import kotlinx.serialization.Serializable
import retrofit2.Call
import retrofit2.http.POST

/**
 * Created by <PERSON><PERSON> <PERSON><PERSON><PERSON>
 */

@Serializable
data class UserBasicInfoResponse(
    val email: String? = null,
    val mobile: String? = null,
    val profilePicture: String? = null,
    val language: String? = null,
    val fullName : String? = null,
    val mobileRegion : String? = null
)


interface GetUserBasicInfoApiService {
    @POST("user/info")
    fun request() : Call<BaseResponse<UserBasicInfoResponse>>
}