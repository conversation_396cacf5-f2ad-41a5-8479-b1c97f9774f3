package com.siriustech.merit.apilayer.service.onboarding.updateagreement

import com.core.network.model.BaseResponse
import kotlinx.serialization.Serializable
import retrofit2.Call
import retrofit2.http.Body
import retrofit2.http.PUT

/**
 * Created by <PERSON><PERSON><PERSON><PERSON>
 */


@Serializable
data class UpdateAgreementRequest(
    val agreementList: List<UpdateAgreementData>,
)

@Serializable
data class UpdateAgreementData(
    val agreementCode: String,
    val isAgree: <PERSON>olean,
)

@Serializable
class UpdateAgreementResponse {}


interface UpdateAgreementApiService {

    @PUT("user/agreement")
    fun updateAgreement(@Body request: UpdateAgreementRequest?): Call<BaseResponse<UpdateAgreementResponse>>
}