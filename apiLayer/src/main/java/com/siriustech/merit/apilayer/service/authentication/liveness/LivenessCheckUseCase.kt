package com.siriustech.merit.apilayer.service.authentication.liveness

import com.core.network.base.AppUseCaseFlow
import com.core.network.base.Repository
import com.core.network.model.BaseResponse
import dagger.hilt.android.scopes.ViewModelScoped
import javax.inject.Inject

// FLOW USE_CASE
@ViewModelScoped
class LivenessCheckUseCase @Inject constructor(
    repository: Repository<LivenessCheckResquest, BaseResponse<LivenessCheckResponse>>,
) : AppUseCaseFlow<LivenessCheckResquest, LivenessCheckResponse>(repository)
