package com.siriustech.merit.apilayer.service.onboarding.updatecustomerprofile

import com.core.network.base.AppUseCaseFlow
import com.core.network.base.Repository
import com.core.network.model.BaseResponse
import dagger.hilt.android.scopes.ViewModelScoped
import javax.inject.Inject

/**
 * Created by <PERSON><PERSON>
 */

// FLOW USE_CASE
@ViewModelScoped
class UpdateCustomerProfileUseCase @Inject constructor(
    repository: Repository<CustomerProfileRequest, BaseResponse<Unit>>,
) : AppUseCaseFlow<CustomerProfileRequest, Unit>(repository)


