package com.siriustech.merit.apilayer.service.authentication.config2FA

import com.core.network.model.BaseResponse
import kotlinx.serialization.Serializable
import retrofit2.Call
import retrofit2.http.Body
import retrofit2.http.PUT

/**
 * Created by <PERSON><PERSON><PERSON><PERSON>
 */
@Serializable
data class Configure2FARequest(
    val action: String,
    val otpType: String,
    val otp: String,
    val refCode: String
)
interface Configure2FAApiService {

    @PUT("user/2fa")
     fun request(@Body request: Configure2FARequest?): Call<BaseResponse<Unit>>
}