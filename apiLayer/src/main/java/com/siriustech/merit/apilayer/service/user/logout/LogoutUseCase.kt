package com.siriustech.merit.apilayer.service.user.logout

import com.core.network.base.AppUseCaseFlow
import com.core.network.base.Repository
import com.core.network.model.BaseResponse
import dagger.hilt.android.scopes.ViewModelScoped
import javax.inject.Inject

/**
 * Created by <PERSON><PERSON>
 */

@ViewModelScoped
class LogoutUseCase @Inject constructor(
    repository: Repository<LogoutRequest, BaseResponse<LogoutResponse>>
) : AppUseCaseFlow<LogoutRequest, LogoutResponse>(repository){
}
