package com.siriustech.merit.apilayer.service.user.chat

import com.core.network.base.RetrofitAPICaller
import com.core.network.model.BaseResponse
import javax.inject.Inject
import retrofit2.Response

/**
 * Created by <PERSON><PERSON> <PERSON><PERSON><PERSON>
 */
class ChatListCaller @Inject constructor(private val apiService: ChatListApiService) :
    RetrofitAPICaller<ChatListRequest, BaseResponse<ChatListResponse>>() {

    override fun call(reqParam: ChatListRequest?): Response<BaseResponse<ChatListResponse>> {
        return apiService.request(reqParam).execute()
    }
}