package com.siriustech.merit.apilayer.service.authentication.updatejpush

import com.core.network.base.RetrofitAPICaller
import com.core.network.model.BaseResponse
import javax.inject.Inject
import retrofit2.Response

/**
 * Created by <PERSON><PERSON><PERSON>
 */
class UpdateJPushRegIDApiCaller @Inject constructor(val apiService: UpdateJPushRegIDApiService) :
    RetrofitAPICaller<UpdateJPushRegIDRequest, BaseResponse<UpdateJPushRegIDResponse>>() {
    override fun call(reqParam: UpdateJPushRegIDRequest?): Response<BaseResponse<UpdateJPushRegIDResponse>> {
        return apiService.request(reqParam).execute()
    }
}