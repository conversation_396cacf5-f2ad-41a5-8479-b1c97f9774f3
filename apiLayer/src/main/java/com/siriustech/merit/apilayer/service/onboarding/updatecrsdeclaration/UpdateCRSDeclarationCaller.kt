package com.siriustech.merit.apilayer.service.onboarding.updatecrsdeclaration

import com.core.network.base.RetrofitAPICaller
import com.core.network.model.BaseResponse
import javax.inject.Inject
import retrofit2.Response

/**
 * Created by <PERSON><PERSON> <PERSON><PERSON>
 */

/**
 * Created by <PERSON><PERSON>t
 */
class UpdateCRSDeclarationCaller @Inject constructor(private val apiService: UpdateCRSDeclarationApiService) :
    RetrofitAPICaller<UpdateCRSDeclarationListRequest, BaseResponse<UpdateCRSResponse>>() {
    override fun call(reqParam: UpdateCRSDeclarationListRequest?): Response<BaseResponse<UpdateCRSResponse>> {
        return apiService.updateCRSDeclaration(reqParam).execute()
    }
}