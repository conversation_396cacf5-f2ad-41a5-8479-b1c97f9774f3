package com.siriustech.merit.apilayer.service.onboarding.updatecrsdeclaration

import com.core.network.model.BaseResponse
import kotlinx.serialization.Serializable
import retrofit2.Call
import retrofit2.http.Body
import retrofit2.http.PUT

/**
 * Created by <PERSON><PERSON><PERSON>
 */


@Serializable
data class UpdateCRSDeclarationListRequest(
    val list: List<UpdateCRSDeclarationRequest>? = emptyList(),
)


@Serializable
data class UpdateCRSDeclarationRequest(
    val residence: String,
    val hasTIN: String,
    val TIN: String,
    val reason: String,
    val explain: String,
)

@Serializable
class UpdateCRSResponse()

interface UpdateCRSDeclarationApiService {

    @PUT("user/declaration-crs")
    fun updateCRSDeclaration(@Body param: UpdateCRSDeclarationListRequest?): Call<BaseResponse<UpdateCRSResponse>>
}