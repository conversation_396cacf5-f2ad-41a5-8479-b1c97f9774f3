package com.siriustech.merit.apilayer.service.user.modify

import com.core.network.model.BaseResponse
import kotlinx.serialization.Serializable
import retrofit2.Call
import retrofit2.http.Body
import retrofit2.http.PUT

/**
 * Created by <PERSON><PERSON> <PERSON><PERSON><PERSON>
 */
@Serializable
data class ModifyUserBankRequest(
    val bankAccountId: Int?,
    val accountType: String = "",
    val bankName: String = "",
    val accountName: String = "",
    val accountNumber: String = "",
    val swiftCode: String = "",
    val currency: List<String> = emptyList(),
    val bankAddress: BankAddress = BankAddress(),
    val remark: String = "",
    val isPrimary: String = "",
    val action: String = "",
)

@Serializable
data class BankAddress(
    val countryRegion: String = "",
    val address: String = "",
    val postCode: String = "",
)

interface ModifyBankApiService {
    @PUT("user/bank")
    fun request(@Body request: ModifyUserBankRequest?): Call<BaseResponse<Unit>>
}