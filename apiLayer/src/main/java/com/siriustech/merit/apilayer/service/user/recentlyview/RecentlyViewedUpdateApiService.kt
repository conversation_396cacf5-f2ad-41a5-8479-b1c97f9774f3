package com.siriustech.merit.apilayer.service.user.recentlyview

import com.core.network.model.BaseResponse
import kotlinx.serialization.Serializable
import retrofit2.Call
import retrofit2.http.Body
import retrofit2.http.PUT

@Serializable
data class RecentlyViewedRequest(
    val instrumentIdList: List<Int>,
)

@Serializable
data class RecentlyViewedItem(
    val exchange: String,
    val symbol: String,
)

interface RecentlyViewedUpdateApiService {

    @PUT("user/instrument/view")
    fun request(@Body request: RecentlyViewedRequest?): Call<BaseResponse<Unit>>

}