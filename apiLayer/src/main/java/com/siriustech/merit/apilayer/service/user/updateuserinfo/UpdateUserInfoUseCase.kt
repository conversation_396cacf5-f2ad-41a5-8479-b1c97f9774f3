package com.siriustech.merit.apilayer.service.user.updateuserinfo

import com.core.network.base.AppUseCaseFlow
import com.core.network.base.Repository
import com.core.network.model.BaseResponse
import com.siriustech.merit.apilayer.service.user.modify.ModifyUserBankRequest
import dagger.hilt.android.scopes.ViewModelScoped
import javax.inject.Inject

/**
 * Created by <PERSON><PERSON> <PERSON><PERSON><PERSON>
 */

@ViewModelScoped
class UpdateUserInfoUseCase @Inject constructor(
    repository: Repository<UpdateUserInfoRequest, BaseResponse<UpdateUserInfoResponse>>
) : AppUseCaseFlow<UpdateUserInfoRequest, UpdateUserInfoResponse>(repository){
}
