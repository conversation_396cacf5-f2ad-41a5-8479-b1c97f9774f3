package com.siriustech.merit.apilayer.service.market.favorite

import com.core.network.base.RetrofitAPICaller
import com.core.network.model.BaseResponse
import com.siriustech.merit.apilayer.service.market.marketlistdetails.MarketInstrumentListResponse
import javax.inject.Inject
import retrofit2.Response

/**
 * Created by <PERSON><PERSON>t
 */
class FavoriteListListDetailsCaller @Inject constructor(
    private val apiService: GetFavoriteAssetListApiService
)  : RetrofitAPICaller<Unit, BaseResponse<MarketInstrumentListResponse>>(){
    override fun call(reqParam: Unit?): Response<BaseResponse<MarketInstrumentListResponse>> {
        return apiService.request().execute()
    }
}
