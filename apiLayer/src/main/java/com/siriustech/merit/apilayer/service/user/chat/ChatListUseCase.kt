package com.siriustech.merit.apilayer.service.user.chat

import com.core.network.base.AppUseCaseFlow
import com.core.network.base.Repository
import com.core.network.model.BaseResponse
import dagger.hilt.android.scopes.ViewModelScoped
import javax.inject.Inject

/**
 * Created by <PERSON><PERSON>tet
 */

@ViewModelScoped
class ChatListUseCase @Inject constructor(
    repository: Repository<ChatListRequest, BaseResponse<ChatListResponse>>
) : AppUseCaseFlow<ChatListRequest, ChatListResponse>(repository){
}
