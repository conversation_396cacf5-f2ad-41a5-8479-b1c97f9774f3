package com.siriustech.merit.apilayer.service.wallet.deposit

import com.core.network.model.BaseResponse
import kotlinx.serialization.Serializable
import retrofit2.Call
import retrofit2.http.Body
import retrofit2.http.POST

/**
 * Created by <PERSON><PERSON> <PERSON><PERSON><PERSON>
 */

@Serializable
data class DepositRequest(
    val refCode: String = "",
    val otpCode: String = "",
    val otpType: String = "",
    val otpAddress: String,
    val currency: String,
    val amount: String,
    val proofFile: String,
    val bankAccountId: Int,
) : java.io.Serializable

@Serializable
class DepositResponse {

}

interface DepositApiService {
    @POST("account/deposit")
    fun request(@Body request: DepositRequest?): Call<BaseResponse<DepositResponse>>
}
