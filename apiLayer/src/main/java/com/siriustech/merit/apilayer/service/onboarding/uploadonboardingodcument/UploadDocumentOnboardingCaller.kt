package com.siriustech.merit.apilayer.service.onboarding.uploadonboardingodcument

import com.core.network.base.RetrofitAPICaller
import com.core.network.model.BaseResponse
import javax.inject.Inject
import retrofit2.Response

/**
 * Created by <PERSON><PERSON> H<PERSON>t
 */
class UploadDocumentOnboardingCaller @Inject constructor(private val apiService: UploadDocumentOnboardingApiService) :
    RetrofitAPICaller<UploadDocumentOnboardingRequest, BaseResponse<UploadDocumentOnboardingResponse>>() {
    override fun call(reqParam: UploadDocumentOnboardingRequest?): Response<BaseResponse<UploadDocumentOnboardingResponse>> {
        return apiService.uploadDocument(reqParam).execute()
    }
}