package com.siriustech.merit.apilayer.service.authentication.common.ocr

import com.core.network.base.AppUseCaseFlow
import com.core.network.base.Repository
import com.core.network.model.BaseResponse
import dagger.hilt.android.scopes.ViewModelScoped
import javax.inject.Inject
import okhttp3.RequestBody

// FLOW USE_CASE
@ViewModelScoped
class CheckOCRUseCase @Inject constructor(
    repository: Repository<OCRRequest, BaseResponse<OCRResponse>>,
) : AppUseCaseFlow<OCRRequest, OCRResponse>(repository)
