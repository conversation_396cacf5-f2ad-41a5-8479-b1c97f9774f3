package com.siriustech.merit.apilayer.service.authentication.signdocument

import com.core.network.base.RetrofitAPICaller
import com.core.network.model.BaseResponse
import javax.inject.Inject
import retrofit2.Response

/**
 * Created by <PERSON><PERSON>t
 */
class SignDocumentCaller @Inject constructor(private val apiService: SignDocumentApiService) : RetrofitAPICaller<SignDocumentRequest,BaseResponse<SignDocumentResponse>>() {
    override fun call(reqParam: SignDocumentRequest?): Response<BaseResponse<SignDocumentResponse>> {
        return apiService.request(reqParam).execute()
    }
}