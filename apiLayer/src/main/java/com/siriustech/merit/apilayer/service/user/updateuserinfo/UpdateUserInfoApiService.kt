package com.siriustech.merit.apilayer.service.user.updateuserinfo

import com.core.network.model.BaseResponse
import kotlinx.serialization.Serializable
import retrofit2.Call
import retrofit2.http.Body
import retrofit2.http.PUT

/**
 * Created by <PERSON><PERSON> <PERSON>tet
 */

@Serializable
data class UpdateUserInfoRequest(
    val deletePicture: Boolean = false,
    val email: String? = null,
    val mobile: String? = null,
    val otp: String? = null,
    val otpType: String? = null,
    val refCode: String? = null,
    val profilePicture: String? = null,
    val language: String? = null,
    val mobileRegion : String? = null
): java.io.Serializable

@Serializable
class UpdateUserInfoResponse {

}


interface UpdateUserInfoApiService {

    @PUT("user/info")
    fun request(@Body request: UpdateUserInfoRequest?): Call<BaseResponse<UpdateUserInfoResponse>>
}