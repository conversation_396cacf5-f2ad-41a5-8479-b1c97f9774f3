package com.siriustech.merit.apilayer.service.authentication.common

import com.core.network.base.AppUseCaseFlow
import com.core.network.base.Repository
import com.core.network.model.BaseResponse
import dagger.hilt.android.scopes.ViewModelScoped
import javax.inject.Inject

// FLOW USE_CASE
@ViewModelScoped
class GetOTPUseCase @Inject constructor(
    repository: Repository<GetOTPRequest, BaseResponse<GetOTPResponse>>,
) : AppUseCaseFlow<GetOTPRequest, GetOTPResponse>(repository)
