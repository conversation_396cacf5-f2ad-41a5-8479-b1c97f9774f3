package com.siriustech.merit.apilayer.service.user.modify

import com.core.network.base.AppUseCaseFlow
import com.core.network.base.Repository
import com.core.network.model.BaseResponse
import com.siriustech.merit.apilayer.service.user.banklist.BankAccountsResponse
import dagger.hilt.android.scopes.ViewModelScoped
import javax.inject.Inject

/**
 * Created by <PERSON><PERSON>
 */


@ViewModelScoped
class ModifyBankAccountUseCase @Inject constructor(
    repository: Repository<ModifyUserBankRequest, BaseResponse<Unit>>
) : AppUseCaseFlow<ModifyUserBankRequest, Unit>(repository){
}
