package com.siriustech.merit.apilayer.service.onboarding.getdeclarationcrs

import com.core.network.model.BaseResponse
import kotlinx.serialization.Serializable
import retrofit2.Call
import retrofit2.http.Body
import retrofit2.http.POST

/**
 * Created by <PERSON><PERSON><PERSON>
 */

@Serializable
class GetCRSDeclarationListRequest {}


@Serializable
data class GetCRSDeclarationListResponse(
    val list: List<GetCRSDeclarationResponse>? = emptyList(),
)


@Serializable
data class GetCRSDeclarationResponse(
    val residence: String,
    val hasTIN: String,
    val TIN: String,
    val reason: String,
    val explain: String,
)

interface GetCRSDeclarationApiService {

    @POST("user/declaration-crs")
    fun getCRSDeclarationList(@Body request: GetCRSDeclarationListRequest?): Call<BaseResponse<GetCRSDeclarationListResponse>>

}