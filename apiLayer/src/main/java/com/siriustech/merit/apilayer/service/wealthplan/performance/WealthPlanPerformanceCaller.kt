package com.siriustech.merit.apilayer.service.wealthplan.performance

import com.core.network.base.RetrofitAPICaller
import com.core.network.model.BaseResponse
import javax.inject.Inject
import retrofit2.Response

/**
 * Created by <PERSON><PERSON><PERSON>t
 */
class WealthPlanPerformanceCaller @Inject constructor(private val apiService: WealthPlanPerformanceApiService) :
    RetrofitAPICaller<WealthPlanPerformanceRequest, BaseResponse<WealthPlanPerformanceResponse>>() {
    override fun call(reqParam: WealthPlanPerformanceRequest?): Response<BaseResponse<WealthPlanPerformanceResponse>> {
        return apiService.request(reqParam).execute()
    }
}