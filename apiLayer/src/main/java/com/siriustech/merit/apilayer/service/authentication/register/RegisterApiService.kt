package com.siriustech.merit.apilayer.service.authentication.register

import com.core.network.model.BaseResponse
import kotlinx.serialization.Serializable
import retrofit2.Call
import retrofit2.http.Body
import retrofit2.http.PUT

/**
 * Created by <PERSON><PERSON> <PERSON><PERSON>t
 */

@Serializable
data class RegisterRequest(
    val chineseName: String,
    val englishName: String,
    val phoneNumber: String,
    val password: String,
    val email: String,
    val referenceNumber: String,
    val refCode: String,
    val otpBizType: String,
    val otpType: String,
    val otpValue: String,
    val mobileRegion : String
)

@Serializable
data class RegisterResponse(
    val customerId: Int? = null,
)

interface RegisterApiService {
    @PUT("user/register")
    fun request(@Body request: RegisterRequest?): Call<BaseResponse<RegisterResponse>>
}