package com.siriustech.merit.apilayer.service.authentication.common.eumeration

import com.core.network.model.BaseResponse
import kotlinx.serialization.Serializable
import retrofit2.Call
import retrofit2.http.Body
import retrofit2.http.POST

@Serializable
data class EnumerationResponse(
    val list: List<EnumerationList>,
)

@Serializable
data class EnumerationList(
    val code: String,
    val list: List<Enumeration>,
)

@Serializable
data class Enumeration(
    val value: String,
    val desc: String,
    val sort: Int,
)

@Serializable
data class EnumerationRequest(
    val codeList: List<String>,
)

enum class DropDownEnumeration {
    IDENTITY_REGION_CHINA,
    IDENTITY_REGION_HK,
    IDENTITY_REGION_OTHERS,
    BROKER,
    REGION,
    BANK_ACCOUNT_TYPE,
    PRODUCT_CATEGORY,
    ASSET_CLASS,
    INDUSTRY,
    EDUCATION,
    STATEMENT_DELIVERY_METHOD,
    CURRENCY,
    MO<PERSON>LE_REGION,
}


interface GetEnumerationService {
    @POST("common/enumeration")
    fun request(@Body request: EnumerationRequest?): Call<BaseResponse<EnumerationResponse>>
}