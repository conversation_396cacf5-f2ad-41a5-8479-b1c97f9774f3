package com.siriustech.merit.apilayer.service.onboarding.userbanklist

import com.core.network.base.RetrofitAPICaller
import com.core.network.model.BaseResponse
import javax.inject.Inject
import retrofit2.Response

/**
 * Created by <PERSON><PERSON>t
 */
class GetUserBankListCaller @Inject constructor(private val apiService: GetUserBankListApiService) :
    RetrofitAPICaller<Unit, BaseResponse<UserBankListResponse>>() {
    override fun call(reqParam: Unit?): Response<BaseResponse<UserBankListResponse>> {
        return apiService.getBankList().execute()
    }
}