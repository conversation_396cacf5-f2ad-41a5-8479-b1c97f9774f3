package com.siriustech.merit.apilayer.service.market.marketquote

import com.core.network.base.RetrofitAPICaller
import com.core.network.model.BaseResponse
import javax.inject.Inject
import retrofit2.Response

/**
 * Created by <PERSON><PERSON><PERSON>
 */
class MarketQuoteDataCaller @Inject constructor(private val apiService: MarketQuoteApiService) :
    RetrofitAPICaller<MarketQuoteRequest, BaseResponse<MarketQuoteResponse>>() {
    override fun call(reqParam: MarketQuoteRequest?): Response<BaseResponse<MarketQuoteResponse>> {
        return apiService.request(reqParam).execute()
    }
}