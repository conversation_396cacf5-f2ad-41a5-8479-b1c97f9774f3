package com.siriustech.merit.apilayer.service.onboarding.questionnaire

import com.core.network.base.AppUseCaseFlow
import com.core.network.base.Repository
import com.core.network.model.BaseResponse
import dagger.hilt.android.scopes.ViewModelScoped
import javax.inject.Inject

// FLOW USE_CASE
@ViewModelScoped
class GetQuestionnaireUseCase @Inject constructor(
    repository: Repository<GetQuestionnaireRequest, BaseResponse<QuestionnaireResponse>>,
) : AppUseCaseFlow<GetQuestionnaireRequest, QuestionnaireResponse>(repository)


