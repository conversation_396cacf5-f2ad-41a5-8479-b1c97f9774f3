package com.siriustech.merit.apilayer.service.authentication.common.uploadfile

import com.core.network.base.RetrofitAPICaller
import com.core.network.model.BaseResponse
import retrofit2.Response
import javax.inject.Inject
import okhttp3.RequestBody

class UploadFileCaller @Inject constructor(
    private val apiService: UploadFileApiService
) : RetrofitAPICaller<RequestBody, BaseResponse<UploadFileResponse>>() {
    override fun call(reqParam: RequestBody?): Response<BaseResponse<UploadFileResponse>> {
        return apiService.request(reqParam).execute()
    }
}