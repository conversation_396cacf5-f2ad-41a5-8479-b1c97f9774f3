package com.siriustech.merit.apilayer.di

import com.core.network.di.getService
import com.siriustech.merit.apilayer.service.authentication.auth.ConfigurePinPassApiService
import com.siriustech.merit.apilayer.service.authentication.authresetverify.AuthResetVerifyApiService
import com.siriustech.merit.apilayer.service.authentication.checkonboardingstep.CheckOnboardingStepApiService
import com.siriustech.merit.apilayer.service.authentication.common.GetOTPApiService
import com.siriustech.merit.apilayer.service.authentication.common.eumeration.GetEnumerationService
import com.siriustech.merit.apilayer.service.authentication.common.ocr.OCRApiService
import com.siriustech.merit.apilayer.service.authentication.common.uploadfile.UploadFileApiService
import com.siriustech.merit.apilayer.service.authentication.config2FA.Configure2FAApiService
import com.siriustech.merit.apilayer.service.authentication.liveness.LivenessApiService
import com.siriustech.merit.apilayer.service.authentication.login.LoginApiService
import com.siriustech.merit.apilayer.service.authentication.register.RegisterApiService
import com.siriustech.merit.apilayer.service.authentication.resetauth.AuthResetApiService
import com.siriustech.merit.apilayer.service.authentication.signdocument.SignDocumentApiService
import com.siriustech.merit.apilayer.service.authentication.updatejpush.UpdateJPushRegIDApiService
import com.siriustech.merit.apilayer.service.authentication.userinfo.GetUserInfoApiService
import com.siriustech.merit.apilayer.service.history.list.GetHistoryApiService
import com.siriustech.merit.apilayer.service.history.orderdetails.OrderDetailsApiService
import com.siriustech.merit.apilayer.service.history.transactiondetails.TransactionDetailsApiService
import com.siriustech.merit.apilayer.service.history.transactions.TransactionHistoryApiService
import com.siriustech.merit.apilayer.service.home.banner.GetBannerListApiService
import com.siriustech.merit.apilayer.service.home.deletenotification.DeleteNotificationApiService
import com.siriustech.merit.apilayer.service.home.modifynotification.ModifyNotificationApiService
import com.siriustech.merit.apilayer.service.home.notification.NotificationApiService
import com.siriustech.merit.apilayer.service.home.watchlist.MarketWatchListApiService
import com.siriustech.merit.apilayer.service.market.benchmarkchartdata.BenchMarkChartApiService
import com.siriustech.merit.apilayer.service.market.candle.CandleChartApiService
import com.siriustech.merit.apilayer.service.market.exchangecurrency.MarketCurrencyApiService
import com.siriustech.merit.apilayer.service.market.favorite.GetFavoriteAssetListApiService
import com.siriustech.merit.apilayer.service.market.instrumentsearch.InstrumentSearchApiService
import com.siriustech.merit.apilayer.service.market.marketlistdetails.MarketListDetailsApiService
import com.siriustech.merit.apilayer.service.market.marketprofile.MarketInstrumentDetailsApiService
import com.siriustech.merit.apilayer.service.market.marketquote.MarketQuoteApiService
import com.siriustech.merit.apilayer.service.market.recentlyviewed.GetRecentlyViewedListApiService
import com.siriustech.merit.apilayer.service.market.simplechart.SimpleChartApiService
import com.siriustech.merit.apilayer.service.market.togglefavorite.ToggleFavoriteApiService
import com.siriustech.merit.apilayer.service.onboarding.agreementterm.GetAgreementApiService
import com.siriustech.merit.apilayer.service.onboarding.config.GetCommonConfigApiService
import com.siriustech.merit.apilayer.service.onboarding.getdeclarationcrs.GetCRSDeclarationApiService
import com.siriustech.merit.apilayer.service.onboarding.getuserdocument.GetUserDocumentApiService
import com.siriustech.merit.apilayer.service.onboarding.getuseridentity.GetUserIdentityApiService
import com.siriustech.merit.apilayer.service.onboarding.questionnaire.GetQuestionnaireApiService
import com.siriustech.merit.apilayer.service.onboarding.updateagreement.UpdateAgreementApiService
import com.siriustech.merit.apilayer.service.onboarding.updatecrsdeclaration.UpdateCRSDeclarationApiService
import com.siriustech.merit.apilayer.service.onboarding.updatecustomerprofile.UpdateCustomerProfileApiService
import com.siriustech.merit.apilayer.service.onboarding.updateemployment.UpdateUserEmploymentApiService
import com.siriustech.merit.apilayer.service.onboarding.updateidentitycard.UpdateIdentityCardListApiService
import com.siriustech.merit.apilayer.service.onboarding.updatequestionnaire.UpdateQuestionnaireApiService
import com.siriustech.merit.apilayer.service.onboarding.uploadonboardingodcument.UploadDocumentOnboardingApiService
import com.siriustech.merit.apilayer.service.onboarding.userbanklist.GetUserBankListApiService
import com.siriustech.merit.apilayer.service.onboarding.usercheck.OnboardingUserCheckApiService
import com.siriustech.merit.apilayer.service.requestdocsign.RequestDocSignApiService
import com.siriustech.merit.apilayer.service.user.banklist.GetBankAccountsApiService
import com.siriustech.merit.apilayer.service.user.chat.ChatListApiService
import com.siriustech.merit.apilayer.service.user.infocheck.UserInfoCheckApiService
import com.siriustech.merit.apilayer.service.user.logout.LogoutApiService
import com.siriustech.merit.apilayer.service.user.modify.ModifyBankApiService
import com.siriustech.merit.apilayer.service.user.recentlyview.RecentlyViewedUpdateApiService
import com.siriustech.merit.apilayer.service.user.sendchat.SendChatMessageApiService
import com.siriustech.merit.apilayer.service.user.status.UpdateUserStatusApiService
import com.siriustech.merit.apilayer.service.user.updateuserinfo.UpdateUserInfoApiService
import com.siriustech.merit.apilayer.service.user.userinfo.GetUserBasicInfoApiService
import com.siriustech.merit.apilayer.service.wallet.balancehistory.WalletBalanceHistoryApiService
import com.siriustech.merit.apilayer.service.wallet.deposit.DepositApiService
import com.siriustech.merit.apilayer.service.wallet.gainlosshistory.GainLossHistoryApiService
import com.siriustech.merit.apilayer.service.wallet.report.GenerateReportApiService
import com.siriustech.merit.apilayer.service.wallet.statistic.WalletStatisticApiService
import com.siriustech.merit.apilayer.service.wallet.summary.WalletSummaryApiService
import com.siriustech.merit.apilayer.service.wallet.withdraw.WithdrawApiService
import com.siriustech.merit.apilayer.service.wealthplan.details.WealthPlanDetailsApiService
import com.siriustech.merit.apilayer.service.wealthplan.list.WealthPlanListApiService
import com.siriustech.merit.apilayer.service.wealthplan.modify.ModifyWealthPlanApiService
import com.siriustech.merit.apilayer.service.wealthplan.performance.WealthPlanPerformanceApiService
import dagger.Module
import dagger.Provides
import dagger.hilt.InstallIn
import dagger.hilt.components.SingletonComponent
import javax.inject.Singleton
import retrofit2.Retrofit

/**
 * Created by Hein Htet
 */
@Module
@InstallIn(SingletonComponent::class)
class ApiServiceModule {

    @Singleton
    @Provides
    fun provideLoginApiService(retrofit: Retrofit) = getService<LoginApiService>(retrofit)


    @Singleton
    @Provides
    fun provideUserInfoApiService(retrofit: Retrofit) = getService<GetUserInfoApiService>(retrofit)


    @Singleton
    @Provides
    fun provideGetOTPApiService(retrofit: Retrofit) = getService<GetOTPApiService>(retrofit)


    @Singleton
    @Provides
    fun provideConfigure2FAApiService(retrofit: Retrofit) =
        getService<Configure2FAApiService>(retrofit)


    @Singleton
    @Provides
    fun provideConfigureAuthPinPassApiService(retrofit: Retrofit) =
        getService<ConfigurePinPassApiService>(retrofit)


    @Singleton
    @Provides
    fun provideCheckOnBoardingStepApiService(retrofit: Retrofit) =
        getService<CheckOnboardingStepApiService>(retrofit)


    @Singleton
    @Provides
    fun provideRegisterApiService(retrofit: Retrofit) =
        getService<RegisterApiService>(retrofit)


    @Singleton
    @Provides
    fun provideEnumerationApiService(retrofit: Retrofit) =
        getService<GetEnumerationService>(retrofit)

    @Singleton
    @Provides
    fun provideUploadFileApiService(retrofit: Retrofit) =
        getService<UploadFileApiService>(retrofit)

    @Singleton
    @Provides
    fun provideCheckOCRApiService(retrofit: Retrofit) =
        getService<OCRApiService>(retrofit)


    @Singleton
    @Provides
    fun provideLivenessCheckApiService(retrofit: Retrofit) =
        getService<LivenessApiService>(retrofit)


    @Singleton
    @Provides
    fun provideMarketCurrencyApiService(retrofit: Retrofit) =
        getService<MarketCurrencyApiService>(retrofit)


    @Singleton
    @Provides
    fun provideWalletSummaryApiService(retrofit: Retrofit) =
        getService<WalletSummaryApiService>(retrofit)


    @Singleton
    @Provides
    fun provideWalletBalanceHistoryApiService(retrofit: Retrofit) =
        getService<WalletBalanceHistoryApiService>(retrofit)

    @Singleton
    @Provides
    fun provideWalletStatisticApiService(retrofit: Retrofit) =
        getService<WalletStatisticApiService>(retrofit)


    @Singleton
    @Provides
    fun provideMarketListInstrumentDetailsApiService(retrofit: Retrofit) =
        getService<MarketListDetailsApiService>(retrofit)

    @Singleton
    @Provides
    fun provideBenchMarkChartApiService(retrofit: Retrofit) =
        getService<BenchMarkChartApiService>(retrofit)

    @Singleton
    @Provides
    fun provideAuthResetApiService(retrofit: Retrofit) =
        getService<AuthResetApiService>(retrofit)


    @Singleton
    @Provides
    fun provideAuthResetVerifyApiService(retrofit: Retrofit) =
        getService<AuthResetVerifyApiService>(retrofit)


    @Singleton
    @Provides
    fun provideBankAccountsAiService(retrofit: Retrofit) =
        getService<GetBankAccountsApiService>(retrofit)

    @Singleton
    @Provides
    fun provideDepositApiService(retrofit: Retrofit) =
        getService<DepositApiService>(retrofit)

    @Singleton
    @Provides
    fun provideWithdrawService(retrofit: Retrofit) =
        getService<WithdrawApiService>(retrofit)

    @Singleton
    @Provides
    fun provideGainLossHistoryApiService(retrofit: Retrofit) =
        getService<GainLossHistoryApiService>(retrofit)


    @Singleton
    @Provides
    fun provideFavoriteListApiService(retrofit: Retrofit) =
        getService<GetFavoriteAssetListApiService>(retrofit)

    @Singleton
    @Provides
    fun provideRecentlyViewedListApiService(retrofit: Retrofit) =
        getService<GetRecentlyViewedListApiService>(retrofit)


    @Singleton
    @Provides
    fun provideToggleFavouriteApiService(retrofit: Retrofit) =
        getService<ToggleFavoriteApiService>(retrofit)

    @Singleton
    @Provides
    fun provideInstrumentSearchApiService(retrofit: Retrofit) =
        getService<InstrumentSearchApiService>(retrofit)


    @Singleton
    @Provides
    fun provideCandleChartDataApiService(retrofit: Retrofit) =
        getService<CandleChartApiService>(retrofit)


    @Singleton
    @Provides
    fun provideSimpleChartDataApiService(retrofit: Retrofit) =
        getService<SimpleChartApiService>(retrofit)


    @Singleton
    @Provides
    fun provideMarketQuoteApiService(retrofit: Retrofit) =
        getService<MarketQuoteApiService>(retrofit)

    @Singleton
    @Provides
    fun provideMarketInstrumentDetailsApiService(retrofit: Retrofit) =
        getService<MarketInstrumentDetailsApiService>(retrofit)

    @Singleton
    @Provides
    fun provideWealthPlanListApiService(retrofit: Retrofit) =
        getService<WealthPlanListApiService>(retrofit)


    @Singleton
    @Provides
    fun provideModifyWealthPlanApiService(retrofit: Retrofit) =
        getService<ModifyWealthPlanApiService>(retrofit)


    @Singleton
    @Provides
    fun provideWealthPlanDetailsApiService(retrofit: Retrofit) =
        getService<WealthPlanDetailsApiService>(retrofit)

    @Singleton
    @Provides
    fun provideSignDocumentApiService(retrofit: Retrofit) =
        getService<SignDocumentApiService>(retrofit)


    @Singleton
    @Provides
    fun provideWealthPlanPerformanceApiService(retrofit: Retrofit) =
        getService<WealthPlanPerformanceApiService>(retrofit)


    @Singleton
    @Provides
    fun provideWatchListApiService(retrofit: Retrofit) =
        getService<MarketWatchListApiService>(retrofit)


    @Singleton
    @Provides
    fun provideNotificationApiService(retrofit: Retrofit) =
        getService<NotificationApiService>(retrofit)


    @Singleton
    @Provides
    fun provideModifyNotificationApiService(retrofit: Retrofit) =
        getService<ModifyNotificationApiService>(retrofit)


    @Singleton
    @Provides
    fun provideGenerateReportApiService(retrofit: Retrofit) =
        getService<GenerateReportApiService>(retrofit)

    @Singleton
    @Provides
    fun provideHistoryApiService(retrofit: Retrofit) =
        getService<GetHistoryApiService>(retrofit)

    @Singleton
    @Provides
    fun provideTransactionHistoryApiService(retrofit: Retrofit) =
        getService<TransactionHistoryApiService>(retrofit)


    @Singleton
    @Provides
    fun provideDeleteNotificationApiService(retrofit: Retrofit) =
        getService<DeleteNotificationApiService>(retrofit)


    @Singleton
    @Provides
    fun provideUpdateJPushRegIDApiService(retrofit: Retrofit) =
        getService<UpdateJPushRegIDApiService>(retrofit)


    @Singleton
    @Provides
    fun provideOrderDetailsApiService(retrofit: Retrofit) =
        getService<OrderDetailsApiService>(retrofit)


    @Singleton
    @Provides
    fun provideTransactionDetailsApiService(retrofit: Retrofit) =
        getService<TransactionDetailsApiService>(retrofit)


    @Singleton
    @Provides
    fun provideModifyBankAccountApiService(retrofit: Retrofit) =
        getService<ModifyBankApiService>(retrofit)


    @Singleton
    @Provides
    fun provideUpdateUserInfoApiService(retrofit: Retrofit) =
        getService<UpdateUserInfoApiService>(retrofit)


    @Singleton
    @Provides
    fun provideUserInfoCheckApiService(retrofit: Retrofit) =
        getService<UserInfoCheckApiService>(retrofit)


    @Singleton
    @Provides
    fun provideGetUserInfoApiService(retrofit: Retrofit) =
        getService<GetUserBasicInfoApiService>(retrofit)

    @Singleton
    @Provides
    fun provideLogoutApiService(retrofit: Retrofit) =
        getService<LogoutApiService>(retrofit)

    @Singleton
    @Provides
    fun provideChatListApiService(retrofit: Retrofit) =
        getService<ChatListApiService>(retrofit)

    @Singleton
    @Provides
    fun provideUpdateUserStatusApiService(retrofit: Retrofit) =
        getService<UpdateUserStatusApiService>(retrofit)


    @Singleton
    @Provides
    fun provideSendChatMessageApiService(retrofit: Retrofit) =
        getService<SendChatMessageApiService>(retrofit)

    @Singleton
    @Provides
    fun provideRecentlyViewedUpdateApiService(retrofit: Retrofit) =
        getService<RecentlyViewedUpdateApiService>(retrofit)


    @Singleton
    @Provides
    fun provideGetQuestionnaireApiService(retrofit: Retrofit) =
        getService<GetQuestionnaireApiService>(retrofit)


    @Singleton
    @Provides
    fun provideGetAgreementTermApiService(retrofit: Retrofit) =
        getService<GetAgreementApiService>(retrofit)

    @Singleton
    @Provides
    fun provideGetUserBankListApiService(retrofit: Retrofit) =
        getService<GetUserBankListApiService>(retrofit)

    @Singleton
    @Provides
    fun provideUpdateQuestionnaireApiService(retrofit: Retrofit) =
        getService<UpdateQuestionnaireApiService>(retrofit)


    @Singleton
    @Provides
    fun provideUpdateAgreementApiService(retrofit: Retrofit) =
        getService<UpdateAgreementApiService>(retrofit)


    @Singleton
    @Provides
    fun provideUploadOnboardingDocumentApiService(retrofit: Retrofit) =
        getService<UploadDocumentOnboardingApiService>(retrofit)


    @Singleton
    @Provides
    fun provideUpdateCustomerProfileApiService(retrofit: Retrofit) =
        getService<UpdateCustomerProfileApiService>(retrofit)


    @Singleton
    @Provides
    fun provideUpdateEmploymentProfileApiService(retrofit: Retrofit) =
        getService<UpdateUserEmploymentApiService>(retrofit)


    @Singleton
    @Provides
    fun provideUpdateUserIdentityCardListApiService(retrofit: Retrofit) =
        getService<UpdateIdentityCardListApiService>(retrofit)


    @Singleton
    @Provides
    fun provideGetUserIdentityApiService(retrofit: Retrofit) =
        getService<GetUserIdentityApiService>(retrofit)


    @Singleton
    @Provides
    fun provideGetUserDocumentApiService(retrofit: Retrofit) =
        getService<GetUserDocumentApiService>(retrofit)


    @Singleton
    @Provides
    fun provideGetDeclarationCrsApiService(retrofit: Retrofit) =
        getService<GetCRSDeclarationApiService>(retrofit)

    @Singleton
    @Provides
    fun provideUpdateDeclarationCrsApiService(retrofit: Retrofit) =
        getService<UpdateCRSDeclarationApiService>(retrofit)

    @Singleton
    @Provides
    fun provideCheckUserOnboardingApiService(retrofit: Retrofit) =
        getService<OnboardingUserCheckApiService>(retrofit)

    @Singleton
    @Provides
    fun provideRequestDocSignApiService(retrofit: Retrofit) =
        getService<RequestDocSignApiService>(retrofit)


    @Singleton
    @Provides
    fun provideBannerListApiService(retrofit: Retrofit) =
        getService<GetBannerListApiService>(retrofit)

    @Singleton
    @Provides
    fun provideGetCommonConfigApiService(retrofit: Retrofit) =
        getService<GetCommonConfigApiService>(retrofit)

}