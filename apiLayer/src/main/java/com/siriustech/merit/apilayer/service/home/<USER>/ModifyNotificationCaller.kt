package com.siriustech.merit.apilayer.service.home.modifynotification

import com.core.network.base.RetrofitAPICaller
import com.core.network.model.BaseResponse
import com.siriustech.merit.apilayer.service.home.notification.NotificationListResponse
import javax.inject.Inject
import retrofit2.Response

/**
 * Created by <PERSON><PERSON>
 */
class ModifyNotificationCaller @Inject constructor(private val service: ModifyNotificationApiService) :
    RetrofitAPICaller<ModifyNotificationRequest, BaseResponse<ModifyNotificationResponse>>() {
    override fun call(reqParam: ModifyNotificationRequest?): Response<BaseResponse<ModifyNotificationResponse>> {
        return service.request(reqParam).execute()
    }
}