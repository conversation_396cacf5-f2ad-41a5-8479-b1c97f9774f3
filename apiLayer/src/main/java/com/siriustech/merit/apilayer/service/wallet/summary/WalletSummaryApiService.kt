package com.siriustech.merit.apilayer.service.wallet.summary

import com.core.network.model.BaseResponse
import kotlinx.serialization.Serializable
import retrofit2.Call
import retrofit2.http.Body
import retrofit2.http.POST

/**
 * Created by <PERSON><PERSON> <PERSON><PERSON>t
 */

@Serializable
data class WalletSummaryRequest(
    val summaryBy: String,
)

@Serializable
data class WalletSummaryResponse(
    val totalValue: String? = null,
    val cashBalance: String? = null,
    val marketValue: String? = null,
    val costValue: String? = null,
    val unrealizedGl: String? = null,
    val unrealizedGlRate: String? = null,
    val currency: String? = null,
    val allocationList: List<AllocationResponse>? = emptyList(), // remove it later,
    val summaryList: List<WalletSummaryListResponse>? = emptyList(),
    val riskLevelBreakdown: List<RiskLevelBreakdownResponse>? = null,
)


@Serializable
data class WalletSummaryListResponse(
    val summaryName: String? = null,
    val marketValue: String? = null,
    val unrealizedGl: String? = null,
    val unrealizedGlRate: String? = null,
    val currency: String? = null,
    val percentage: String? = null,
    val riskTag: String? = null,
    val costValue : String? = null,
    val assetList: List<CategoryAssetListResponse>? = null,
)

@Serializable
data class AllocationResponse(
    val allocationClass: String? = null,
    val marketValue: String? = null,
    val costValue: String? = null,
    val unrealizedGl: String? = null,
    val unrealizedGlRate: String? = null,
    val currency: String? = null,
    val percentage: String? = null,
    val categoryList: List<ProductCategoryListResponse>? = emptyList(),
)

@Serializable
data class ProductCategoryListResponse(
    val productCategory: String? = null,
    val assetList: List<CategoryAssetListResponse>? = emptyList(),
)

@Serializable
data class CategoryAssetListResponse(
    val instrumentId : Int? = null,
    val symbol: String? = null,
    val name: String? = null,
    val logo: String? = null,
    val instrumentClass: String? = null,
    val exchange: String? = null,
    val instrumentName: String? = null,
    val riskLevel: String? = null,
    val currency: String? = null,
    val unit: String? = null,
    val marketPrice: String? = null,
    val marketValue: String? = null,
    val costPrice: String? = null,
    val costValue: String? = null,
    val unrealizedGl: String? = null,
    val unrealizedGlRate: String? = null,
    val percentage: String? = null,

)

@Serializable
data class RiskLevelBreakdownResponse(
    val riskLevel: String? = null,
    val percentage: String? = null,
)

interface WalletSummaryApiService {

    @POST("wallet/summary")
    fun request(@Body request: WalletSummaryRequest?): Call<BaseResponse<WalletSummaryResponse>>
}

fun Double.format(digits: Int) = "%.${digits}f".format(this)
