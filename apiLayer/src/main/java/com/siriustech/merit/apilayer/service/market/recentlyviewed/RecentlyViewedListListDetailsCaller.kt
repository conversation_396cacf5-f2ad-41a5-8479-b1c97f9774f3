package com.siriustech.merit.apilayer.service.market.recentlyviewed

import com.core.network.base.RetrofitAPICaller
import com.core.network.model.BaseResponse
import com.siriustech.merit.apilayer.service.market.marketlistdetails.MarketInstrumentListResponse
import javax.inject.Inject
import retrofit2.Response

/**
 * Created by <PERSON><PERSON>
 */
class RecentlyViewedListListDetailsCaller @Inject constructor(
    private val apiService: GetRecentlyViewedListApiService
)  : RetrofitAPICaller<Unit, BaseResponse<MarketInstrumentListResponse>>(){
    override fun call(reqParam: Unit?): Response<BaseResponse<MarketInstrumentListResponse>> {
        return apiService.request().execute()
    }
}
