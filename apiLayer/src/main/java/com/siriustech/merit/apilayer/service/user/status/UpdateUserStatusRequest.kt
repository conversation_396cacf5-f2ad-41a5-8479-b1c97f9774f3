package com.siriustech.merit.apilayer.service.user.status

import com.core.network.model.BaseResponse
import kotlinx.serialization.Serializable
import retrofit2.Call
import retrofit2.http.Body
import retrofit2.http.PUT

@Serializable
data class UpdateUserStatusRequest(
    val status: String,
    val reason: String? = null,
    val password: String? = null,
)

@Serializable
class UpdateUserStatusResponse {

}

interface UpdateUserStatusApiService {
    @PUT("/user/status")
     fun request(@Body request: UpdateUserStatusRequest?): Call<BaseResponse<UpdateUserStatusResponse>>
}