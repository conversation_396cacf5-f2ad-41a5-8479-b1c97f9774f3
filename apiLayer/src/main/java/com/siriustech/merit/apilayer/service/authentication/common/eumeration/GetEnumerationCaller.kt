package com.siriustech.merit.apilayer.service.authentication.common.eumeration

import com.core.network.base.RetrofitAPICaller
import com.core.network.model.BaseResponse
import retrofit2.Response
import javax.inject.Inject

class GetEnumerationCaller @Inject constructor(
    private val apiService: GetEnumerationService
) : RetrofitAPICaller<EnumerationRequest, BaseResponse<EnumerationResponse>>() {
    override fun call(reqParam: EnumerationRequest?): Response<BaseResponse<EnumerationResponse>> {
        return apiService.request(reqParam).execute()
    }
}