package com.siriustech.merit.apilayer.service.user.sendchat

import com.core.network.base.AppUseCaseFlow
import com.core.network.base.Repository
import com.core.network.model.BaseResponse
import dagger.hilt.android.scopes.ViewModelScoped
import javax.inject.Inject

/**
 * Created by <PERSON><PERSON><PERSON>
 */


@ViewModelScoped
class SendChatMessageUseCase @Inject constructor(
    repository: Repository<SendChatMessageRequest, BaseResponse<SendChatMessageResponse>>
) : AppUseCaseFlow<SendChatMessageRequest, SendChatMessageResponse>(repository){
}
