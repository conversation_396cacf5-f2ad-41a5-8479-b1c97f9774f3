package com.siriustech.merit.apilayer.service.authentication.signdocument

import com.core.network.model.BaseResponse
import kotlinx.serialization.Serializable
import retrofit2.Call
import retrofit2.http.Body
import retrofit2.http.POST

@kotlinx.serialization.Serializable
class SignDocumentRequest()


@Serializable
data class SignDocumentResponse(
    val docuSignUrl : String,
    val status : String
)

interface SignDocumentApiService {

    @POST("user/docu-sign")
     fun request(@Body request: SignDocumentRequest?) :  Call< BaseResponse<SignDocumentResponse>>

}