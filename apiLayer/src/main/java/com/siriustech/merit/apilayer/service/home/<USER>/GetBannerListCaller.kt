package com.siriustech.merit.apilayer.service.home.banner

import com.core.network.base.RetrofitAPICaller
import com.core.network.model.BaseResponse
import javax.inject.Inject
import retrofit2.Response

/**
 * Created by <PERSON><PERSON><PERSON><PERSON>
 */
class GetBannerListCaller @Inject constructor(private val apiService: GetBannerListApiService) :
    RetrofitAPICaller<Unit, BaseResponse<BannerListResponse>>() {
    override fun call(reqParam: Unit?): Response<BaseResponse<BannerListResponse>> {
        return apiService.request().execute()
    }
}