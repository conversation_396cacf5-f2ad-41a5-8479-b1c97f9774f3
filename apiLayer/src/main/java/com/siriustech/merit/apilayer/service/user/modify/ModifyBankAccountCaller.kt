package com.siriustech.merit.apilayer.service.user.modify

import com.core.network.base.RetrofitAPICaller
import com.core.network.model.BaseResponse
import javax.inject.Inject
import retrofit2.Response

/**
 * Created by <PERSON><PERSON><PERSON>
 */
class ModifyBankAccountCaller @Inject constructor(
    private val apiService: ModifyBankApiService,
) : RetrofitAPICaller<ModifyUserBankRequest, BaseResponse<Unit>>() {
    override fun call(reqParam: ModifyUserBankRequest?): Response<BaseResponse<Unit>> {
        return apiService.request(reqParam).execute()
    }
}