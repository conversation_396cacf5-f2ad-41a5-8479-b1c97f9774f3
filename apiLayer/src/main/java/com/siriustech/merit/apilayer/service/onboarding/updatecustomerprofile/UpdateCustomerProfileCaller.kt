package com.siriustech.merit.apilayer.service.onboarding.updatecustomerprofile

import com.core.network.base.RetrofitAPICaller
import com.core.network.model.BaseResponse
import javax.inject.Inject
import retrofit2.Response

/**
 * Created by <PERSON><PERSON> H<PERSON>t
 */
class UpdateCustomerProfileCaller @Inject constructor(private val apiService: UpdateCustomerProfileApiService) :
    RetrofitAPICaller<CustomerProfileRequest, BaseResponse<Unit>>() {
    override fun call(reqParam: CustomerProfileRequest?): Response<BaseResponse<Unit>> {
        return apiService.getCustomerProfile(reqParam).execute()
    }
}