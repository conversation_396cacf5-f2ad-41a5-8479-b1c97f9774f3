package com.siriustech.merit.apilayer.service.onboarding.config

import com.core.network.base.RetrofitAPICaller
import com.core.network.model.BaseResponse
import javax.inject.Inject
import retrofit2.Response

/**
 * Created by <PERSON><PERSON><PERSON>t
 */
class GetCommonConfigCaller @Inject constructor(private val apiService: GetCommonConfigApiService) :
    RetrofitAPICaller<Unit, BaseResponse<CommonConfigResponse>>() {
    override fun call(reqParam: Unit?): Response<BaseResponse<CommonConfigResponse>> {
        return apiService.request().execute()
    }
}