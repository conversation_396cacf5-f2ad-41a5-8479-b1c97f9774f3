package com.siriustech.merit.apilayer.service.user.infocheck

import com.core.network.model.BaseResponse
import kotlinx.serialization.Serializable
import retrofit2.Call
import retrofit2.http.Body
import retrofit2.http.POST

/**
 * Created by <PERSON><PERSON> <PERSON><PERSON>t
 */

@Serializable
data class UserInfoCheckRequest(
    val email: String? = null,
    val mobile: String? = null,
)

@Serializable
class UserInfoCheckResponse {}

interface UserInfoCheckApiService {

    @POST("user/info/check")
    fun request(@Body request: UserInfoCheckRequest?): Call<BaseResponse<UserInfoCheckResponse>>
}