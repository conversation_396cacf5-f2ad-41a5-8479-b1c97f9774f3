package com.siriustech.merit.apilayer.service.market.instrumentsearch

import com.core.network.model.BaseResponse
import com.siriustech.merit.apilayer.service.market.marketlistdetails.MarketInstrumentListResponse
import kotlinx.serialization.Serializable
import retrofit2.Call
import retrofit2.http.Body
import retrofit2.http.POST

/**
 * Created by <PERSON><PERSON>
 */

@Serializable
data class InstrumentSearchRequest(
    val keyword: String,
)

interface InstrumentSearchApiService {

    @POST("market/instrument/search")
    fun request(@Body request: InstrumentSearchRequest?): Call<BaseResponse<MarketInstrumentListResponse>>

}