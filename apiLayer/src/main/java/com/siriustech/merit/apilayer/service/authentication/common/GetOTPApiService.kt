package com.siriustech.merit.apilayer.service.authentication.common

import com.core.network.model.BaseResponse
import kotlinx.serialization.Serializable
import retrofit2.Call
import retrofit2.http.Body
import retrofit2.http.POST

/**
 * Created by <PERSON><PERSON> <PERSON><PERSON>t
 */

@Serializable
data class GetOTPRequest(
    val bizType: String,
    val otpType: String,
    val otpAddress: String,
    val mobileRegion: String? = null
)

@Serializable
data class GetOTPResponse(
    val refCode: String? = null,
    val expireSeconds: Long? = 0L,
)

interface GetOTPApiService {

    @POST("common/otp")
    fun request(@Body body: GetOTPRequest?): Call<BaseResponse<GetOTPResponse>>
}