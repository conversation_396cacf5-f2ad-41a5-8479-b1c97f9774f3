package com.siriustech.merit.apilayer.service.wallet.summary

import com.core.network.base.RetrofitAPICaller
import com.core.network.model.BaseResponse
import javax.inject.Inject
import retrofit2.Response

/**
 * Created by <PERSON><PERSON><PERSON>
 */
class WalletSummaryCaller @Inject constructor(
    private val apiService: WalletSummaryApiService,
) : RetrofitAPICaller<WalletSummaryRequest, BaseResponse<WalletSummaryResponse>>() {
    override fun call(reqParam: WalletSummaryRequest?): Response<BaseResponse<WalletSummaryResponse>> {
        return apiService.request(reqParam).execute()
    }
}