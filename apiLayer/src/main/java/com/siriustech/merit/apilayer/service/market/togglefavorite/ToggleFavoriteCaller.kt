package com.siriustech.merit.apilayer.service.market.togglefavorite

import com.core.network.base.RetrofitAPICaller
import com.core.network.model.BaseResponse
import com.siriustech.merit.apilayer.service.market.favorite.GetFavoriteAssetListApiService
import com.siriustech.merit.apilayer.service.market.marketlistdetails.MarketInstrumentListResponse
import javax.inject.Inject
import kotlinx.serialization.Serializable
import retrofit2.Call
import retrofit2.Response
import retrofit2.http.Body
import retrofit2.http.POST

/**
 * Created by <PERSON><PERSON> Htet
 */

class ToggleFavoriteCaller @Inject constructor(
    private val apiService: ToggleFavoriteApiService
)  : RetrofitAPICaller<ToggleFavoriteRequest, BaseResponse<ToggleFavoriteResponse>>(){
    override fun call(reqParam: ToggleFavoriteRequest?): Response<BaseResponse<ToggleFavoriteResponse>> {
        return apiService.request(reqParam).execute()
    }
}