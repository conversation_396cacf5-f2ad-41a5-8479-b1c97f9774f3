package com.siriustech.merit.apilayer.service.authentication.common.uploadfile

import com.core.network.model.BaseResponse
import kotlinx.serialization.Serializable
import okhttp3.RequestBody
import retrofit2.Call
import retrofit2.http.Body
import retrofit2.http.POST

@Serializable
data class UploadFileResponse(
    val dosKey :String
)

interface UploadFileApiService {

    @POST("user/file")
     fun request(@Body body : RequestBody?) :Call<BaseResponse<UploadFileResponse>>
}