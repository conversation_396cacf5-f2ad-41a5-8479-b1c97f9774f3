package com.siriustech.merit.apilayer.service.market.benchmarkchartdata

import com.core.network.base.AppUseCaseFlow
import com.core.network.base.Repository
import com.core.network.model.BaseResponse
import dagger.hilt.android.scopes.ViewModelScoped
import javax.inject.Inject

// FLOW USE_CASE
@ViewModelScoped
class BenchMarkChartUseCase @Inject constructor(
    repository: Repository<BenchMarkChartRequest, BaseResponse<BenchMarkChartResponse>>,
) : AppUseCaseFlow<BenchMarkChartRequest, BenchMarkChartResponse>(repository)


