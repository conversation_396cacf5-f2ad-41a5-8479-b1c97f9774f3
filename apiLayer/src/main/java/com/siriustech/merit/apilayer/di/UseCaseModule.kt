package com.siriustech.merit.apilayer.di

import com.core.network.base.BaseRepository
import com.core.network.base.getRepository
import com.core.network.model.BaseResponse
import com.siriustech.merit.apilayer.service.authentication.auth.ConfigurePinPassApiService
import com.siriustech.merit.apilayer.service.authentication.auth.ConfigurePinPassCaller
import com.siriustech.merit.apilayer.service.authentication.auth.ConfigurePinPassRequest
import com.siriustech.merit.apilayer.service.authentication.auth.ConfigurePinPassUseCase
import com.siriustech.merit.apilayer.service.authentication.authresetverify.AuthResetVerifyApiService
import com.siriustech.merit.apilayer.service.authentication.authresetverify.AuthResetVerifyCaller
import com.siriustech.merit.apilayer.service.authentication.authresetverify.AuthResetVerifyRequest
import com.siriustech.merit.apilayer.service.authentication.authresetverify.AuthResetVerifyResponse
import com.siriustech.merit.apilayer.service.authentication.authresetverify.AuthResetVerifyUseCase
import com.siriustech.merit.apilayer.service.authentication.checkonboardingstep.CheckOnboardingStepApiService
import com.siriustech.merit.apilayer.service.authentication.checkonboardingstep.CheckOnboardingStepCaller
import com.siriustech.merit.apilayer.service.authentication.checkonboardingstep.CheckOnboardingStepRequest
import com.siriustech.merit.apilayer.service.authentication.checkonboardingstep.CheckOnboardingStepResponse
import com.siriustech.merit.apilayer.service.authentication.checkonboardingstep.CheckOnboardingStepUseCase
import com.siriustech.merit.apilayer.service.authentication.common.GetOTPApiService
import com.siriustech.merit.apilayer.service.authentication.common.GetOTPCaller
import com.siriustech.merit.apilayer.service.authentication.common.GetOTPRequest
import com.siriustech.merit.apilayer.service.authentication.common.GetOTPResponse
import com.siriustech.merit.apilayer.service.authentication.common.GetOTPUseCase
import com.siriustech.merit.apilayer.service.authentication.common.eumeration.EnumerationRequest
import com.siriustech.merit.apilayer.service.authentication.common.eumeration.EnumerationResponse
import com.siriustech.merit.apilayer.service.authentication.common.eumeration.GetEnumerationCaller
import com.siriustech.merit.apilayer.service.authentication.common.eumeration.GetEnumerationService
import com.siriustech.merit.apilayer.service.authentication.common.eumeration.GetEnumerationUseCase
import com.siriustech.merit.apilayer.service.authentication.common.ocr.CheckOCRCaller
import com.siriustech.merit.apilayer.service.authentication.common.ocr.CheckOCRUseCase
import com.siriustech.merit.apilayer.service.authentication.common.ocr.OCRApiService
import com.siriustech.merit.apilayer.service.authentication.common.ocr.OCRRequest
import com.siriustech.merit.apilayer.service.authentication.common.ocr.OCRResponse
import com.siriustech.merit.apilayer.service.authentication.common.uploadfile.UploadFileApiService
import com.siriustech.merit.apilayer.service.authentication.common.uploadfile.UploadFileCaller
import com.siriustech.merit.apilayer.service.authentication.common.uploadfile.UploadFileResponse
import com.siriustech.merit.apilayer.service.authentication.common.uploadfile.UploadFileUseCase
import com.siriustech.merit.apilayer.service.authentication.config2FA.Configure2FAApiService
import com.siriustech.merit.apilayer.service.authentication.config2FA.Configure2FACaller
import com.siriustech.merit.apilayer.service.authentication.config2FA.Configure2FARequest
import com.siriustech.merit.apilayer.service.authentication.config2FA.Configure2FAUseCase
import com.siriustech.merit.apilayer.service.authentication.liveness.LivenessApiService
import com.siriustech.merit.apilayer.service.authentication.liveness.LivenessCheckCaller
import com.siriustech.merit.apilayer.service.authentication.liveness.LivenessCheckResponse
import com.siriustech.merit.apilayer.service.authentication.liveness.LivenessCheckResquest
import com.siriustech.merit.apilayer.service.authentication.liveness.LivenessCheckUseCase
import com.siriustech.merit.apilayer.service.authentication.login.LoginApiService
import com.siriustech.merit.apilayer.service.authentication.login.LoginCaller
import com.siriustech.merit.apilayer.service.authentication.login.LoginUseCase
import com.siriustech.merit.apilayer.service.authentication.login.LoginUserRequest
import com.siriustech.merit.apilayer.service.authentication.login.LoginUserResponse
import com.siriustech.merit.apilayer.service.authentication.register.RegisterApiService
import com.siriustech.merit.apilayer.service.authentication.register.RegisterCaller
import com.siriustech.merit.apilayer.service.authentication.register.RegisterRequest
import com.siriustech.merit.apilayer.service.authentication.register.RegisterResponse
import com.siriustech.merit.apilayer.service.authentication.register.RegisterUseCase
import com.siriustech.merit.apilayer.service.authentication.resetauth.AuthResetApiService
import com.siriustech.merit.apilayer.service.authentication.resetauth.AuthResetCaller
import com.siriustech.merit.apilayer.service.authentication.resetauth.AuthResetRequest
import com.siriustech.merit.apilayer.service.authentication.resetauth.AuthResetResponse
import com.siriustech.merit.apilayer.service.authentication.resetauth.AuthResetUseCase
import com.siriustech.merit.apilayer.service.authentication.signdocument.SignDocumentApiService
import com.siriustech.merit.apilayer.service.authentication.signdocument.SignDocumentCaller
import com.siriustech.merit.apilayer.service.authentication.signdocument.SignDocumentRequest
import com.siriustech.merit.apilayer.service.authentication.signdocument.SignDocumentResponse
import com.siriustech.merit.apilayer.service.authentication.signdocument.SignDocumentUseCase
import com.siriustech.merit.apilayer.service.authentication.updatejpush.UpdateJPushRegIDApiCaller
import com.siriustech.merit.apilayer.service.authentication.updatejpush.UpdateJPushRegIDApiService
import com.siriustech.merit.apilayer.service.authentication.updatejpush.UpdateJPushRegIDRequest
import com.siriustech.merit.apilayer.service.authentication.updatejpush.UpdateJPushRegIDResponse
import com.siriustech.merit.apilayer.service.authentication.updatejpush.UpdateJPushRegIDUseCase
import com.siriustech.merit.apilayer.service.authentication.userinfo.GetUserInfoApiService
import com.siriustech.merit.apilayer.service.authentication.userinfo.GetUserInfoCaller
import com.siriustech.merit.apilayer.service.authentication.userinfo.GetUserInfoUseCase
import com.siriustech.merit.apilayer.service.authentication.userinfo.UserInfoResponse
import com.siriustech.merit.apilayer.service.history.list.GetHistoryApiService
import com.siriustech.merit.apilayer.service.history.list.GetHistoryListCaller
import com.siriustech.merit.apilayer.service.history.list.GetHistoryListUseCase
import com.siriustech.merit.apilayer.service.history.list.GetHistoryRequest
import com.siriustech.merit.apilayer.service.history.list.HistoryListResponse
import com.siriustech.merit.apilayer.service.history.orderdetails.OrderDetailsApiService
import com.siriustech.merit.apilayer.service.history.orderdetails.OrderDetailsCaller
import com.siriustech.merit.apilayer.service.history.orderdetails.OrderDetailsRequest
import com.siriustech.merit.apilayer.service.history.orderdetails.OrderDetailsResponse
import com.siriustech.merit.apilayer.service.history.orderdetails.OrderDetailsUseCase
import com.siriustech.merit.apilayer.service.history.transactiondetails.TransactionDetailsApiService
import com.siriustech.merit.apilayer.service.history.transactiondetails.TransactionDetailsCaller
import com.siriustech.merit.apilayer.service.history.transactiondetails.TransactionDetailsRequest
import com.siriustech.merit.apilayer.service.history.transactiondetails.TransactionDetailsResponse
import com.siriustech.merit.apilayer.service.history.transactiondetails.TransactionDetailsUseCase
import com.siriustech.merit.apilayer.service.history.transactions.TransactionHistoryApiService
import com.siriustech.merit.apilayer.service.history.transactions.TransactionHistoryCaller
import com.siriustech.merit.apilayer.service.history.transactions.TransactionHistoryListUseCase
import com.siriustech.merit.apilayer.service.history.transactions.TransactionHistoryRequest
import com.siriustech.merit.apilayer.service.history.transactions.TransactionListResponse
import com.siriustech.merit.apilayer.service.home.banner.BannerListResponse
import com.siriustech.merit.apilayer.service.home.banner.GetBannerListApiService
import com.siriustech.merit.apilayer.service.home.banner.GetBannerListCaller
import com.siriustech.merit.apilayer.service.home.banner.GetBannerListUseCase
import com.siriustech.merit.apilayer.service.home.deletenotification.DeleteNotificationApiService
import com.siriustech.merit.apilayer.service.home.deletenotification.DeleteNotificationCaller
import com.siriustech.merit.apilayer.service.home.deletenotification.DeleteNotificationRequest
import com.siriustech.merit.apilayer.service.home.deletenotification.DeleteNotificationResponse
import com.siriustech.merit.apilayer.service.home.deletenotification.DeleteNotificationUseCase
import com.siriustech.merit.apilayer.service.home.modifynotification.ModifyNotificationApiService
import com.siriustech.merit.apilayer.service.home.modifynotification.ModifyNotificationCaller
import com.siriustech.merit.apilayer.service.home.modifynotification.ModifyNotificationRequest
import com.siriustech.merit.apilayer.service.home.modifynotification.ModifyNotificationResponse
import com.siriustech.merit.apilayer.service.home.modifynotification.ModifyNotificationUseCase
import com.siriustech.merit.apilayer.service.home.notification.NotificationApiService
import com.siriustech.merit.apilayer.service.home.notification.NotificationCaller
import com.siriustech.merit.apilayer.service.home.notification.NotificationListResponse
import com.siriustech.merit.apilayer.service.home.notification.NotificationRequest
import com.siriustech.merit.apilayer.service.home.notification.NotificationUseCase
import com.siriustech.merit.apilayer.service.home.watchlist.MarketWatchListApiService
import com.siriustech.merit.apilayer.service.home.watchlist.MarketWatchListCaller
import com.siriustech.merit.apilayer.service.home.watchlist.MarketWatchListRequest
import com.siriustech.merit.apilayer.service.home.watchlist.MarketWatchListResponse
import com.siriustech.merit.apilayer.service.home.watchlist.MarketWatchListUseCase
import com.siriustech.merit.apilayer.service.market.benchmarkchartdata.BenchMarkChartApiService
import com.siriustech.merit.apilayer.service.market.benchmarkchartdata.BenchMarkChartCaller
import com.siriustech.merit.apilayer.service.market.benchmarkchartdata.BenchMarkChartRequest
import com.siriustech.merit.apilayer.service.market.benchmarkchartdata.BenchMarkChartResponse
import com.siriustech.merit.apilayer.service.market.benchmarkchartdata.BenchMarkChartUseCase
import com.siriustech.merit.apilayer.service.market.candle.CandleChartApiService
import com.siriustech.merit.apilayer.service.market.candle.CandleChartDataCaller
import com.siriustech.merit.apilayer.service.market.candle.CandleChartDataRequest
import com.siriustech.merit.apilayer.service.market.candle.CandleChartDataResponse
import com.siriustech.merit.apilayer.service.market.candle.CandleChartDataUseCase
import com.siriustech.merit.apilayer.service.market.exchangecurrency.MarketCurrencyApiService
import com.siriustech.merit.apilayer.service.market.exchangecurrency.MarketCurrencyListResponse
import com.siriustech.merit.apilayer.service.market.exchangecurrency.MarketCurrencyRequest
import com.siriustech.merit.apilayer.service.market.exchangecurrency.MarketExchangeCurrencyCaller
import com.siriustech.merit.apilayer.service.market.exchangecurrency.MarketExchangeCurrencyUseCase
import com.siriustech.merit.apilayer.service.market.favorite.FavoriteListDetailsUseCase
import com.siriustech.merit.apilayer.service.market.favorite.FavoriteListListDetailsCaller
import com.siriustech.merit.apilayer.service.market.favorite.GetFavoriteAssetListApiService
import com.siriustech.merit.apilayer.service.market.instrumentsearch.InstrumentSearchApiService
import com.siriustech.merit.apilayer.service.market.instrumentsearch.InstrumentSearchCaller
import com.siriustech.merit.apilayer.service.market.instrumentsearch.InstrumentSearchRequest
import com.siriustech.merit.apilayer.service.market.instrumentsearch.InstrumentSearchUseCase
import com.siriustech.merit.apilayer.service.market.marketlistdetails.MarketInstrumentListRequest
import com.siriustech.merit.apilayer.service.market.marketlistdetails.MarketInstrumentListResponse
import com.siriustech.merit.apilayer.service.market.marketlistdetails.MarketListDetailsApiService
import com.siriustech.merit.apilayer.service.market.marketlistdetails.MarketListDetailsCaller
import com.siriustech.merit.apilayer.service.market.marketlistdetails.MarketListDetailsUseCase
import com.siriustech.merit.apilayer.service.market.marketprofile.MarketInstrumentDetailsApiService
import com.siriustech.merit.apilayer.service.market.marketprofile.MarketInstrumentDetailsCaller
import com.siriustech.merit.apilayer.service.market.marketprofile.MarketInstrumentDetailsRequest
import com.siriustech.merit.apilayer.service.market.marketprofile.MarketInstrumentDetailsUseCase
import com.siriustech.merit.apilayer.service.market.marketquote.MarketQuoteApiService
import com.siriustech.merit.apilayer.service.market.marketquote.MarketQuoteDataCaller
import com.siriustech.merit.apilayer.service.market.marketquote.MarketQuoteDataUseCase
import com.siriustech.merit.apilayer.service.market.marketquote.MarketQuoteRequest
import com.siriustech.merit.apilayer.service.market.marketquote.MarketQuoteResponse
import com.siriustech.merit.apilayer.service.market.recentlyviewed.GetRecentlyViewedListApiService
import com.siriustech.merit.apilayer.service.market.recentlyviewed.RecentlyViewedListDetailsUseCase
import com.siriustech.merit.apilayer.service.market.recentlyviewed.RecentlyViewedListListDetailsCaller
import com.siriustech.merit.apilayer.service.market.simplechart.SimpleChartApiService
import com.siriustech.merit.apilayer.service.market.simplechart.SimpleChartDataCaller
import com.siriustech.merit.apilayer.service.market.simplechart.SimpleChartRequest
import com.siriustech.merit.apilayer.service.market.simplechart.SimpleChartResponse
import com.siriustech.merit.apilayer.service.market.simplechart.SimpleChartUseCase
import com.siriustech.merit.apilayer.service.market.togglefavorite.ToggleFavoriteApiService
import com.siriustech.merit.apilayer.service.market.togglefavorite.ToggleFavoriteCaller
import com.siriustech.merit.apilayer.service.market.togglefavorite.ToggleFavoriteRequest
import com.siriustech.merit.apilayer.service.market.togglefavorite.ToggleFavoriteResponse
import com.siriustech.merit.apilayer.service.market.togglefavorite.ToggleFavoriteUseCase
import com.siriustech.merit.apilayer.service.onboarding.agreementterm.AgreementRequest
import com.siriustech.merit.apilayer.service.onboarding.agreementterm.AgreementResponse
import com.siriustech.merit.apilayer.service.onboarding.agreementterm.GetAgreementApiService
import com.siriustech.merit.apilayer.service.onboarding.agreementterm.GetAgreementCaller
import com.siriustech.merit.apilayer.service.onboarding.agreementterm.GetAgreementUseCase
import com.siriustech.merit.apilayer.service.onboarding.config.CommonConfigResponse
import com.siriustech.merit.apilayer.service.onboarding.config.GetCommonConfigApiService
import com.siriustech.merit.apilayer.service.onboarding.config.GetCommonConfigCaller
import com.siriustech.merit.apilayer.service.onboarding.config.GetCommonConfigUseCase
import com.siriustech.merit.apilayer.service.onboarding.getdeclarationcrs.GetCRSDeclarationApiService
import com.siriustech.merit.apilayer.service.onboarding.getdeclarationcrs.GetCRSDeclarationCaller
import com.siriustech.merit.apilayer.service.onboarding.getdeclarationcrs.GetCRSDeclarationListRequest
import com.siriustech.merit.apilayer.service.onboarding.getdeclarationcrs.GetCRSDeclarationListResponse
import com.siriustech.merit.apilayer.service.onboarding.getdeclarationcrs.GetCRSDeclarationUseCase
import com.siriustech.merit.apilayer.service.onboarding.getuserdocument.GetUserDocumentApiService
import com.siriustech.merit.apilayer.service.onboarding.getuserdocument.GetUserDocumentCaller
import com.siriustech.merit.apilayer.service.onboarding.getuserdocument.GetUserDocumentRequest
import com.siriustech.merit.apilayer.service.onboarding.getuserdocument.GetUserDocumentResponse
import com.siriustech.merit.apilayer.service.onboarding.getuserdocument.GetUserDocumentUseCase
import com.siriustech.merit.apilayer.service.onboarding.getuseridentity.GetUserIdentityApiService
import com.siriustech.merit.apilayer.service.onboarding.getuseridentity.GetUserIdentityCaller
import com.siriustech.merit.apilayer.service.onboarding.getuseridentity.GetUserIdentityRequest
import com.siriustech.merit.apilayer.service.onboarding.getuseridentity.GetUserIdentityResponse
import com.siriustech.merit.apilayer.service.onboarding.getuseridentity.GetUserIdentityUseCase
import com.siriustech.merit.apilayer.service.onboarding.questionnaire.GetQuestionnaireApiService
import com.siriustech.merit.apilayer.service.onboarding.questionnaire.GetQuestionnaireCaller
import com.siriustech.merit.apilayer.service.onboarding.questionnaire.GetQuestionnaireRequest
import com.siriustech.merit.apilayer.service.onboarding.questionnaire.GetQuestionnaireUseCase
import com.siriustech.merit.apilayer.service.onboarding.questionnaire.QuestionnaireResponse
import com.siriustech.merit.apilayer.service.onboarding.updateagreement.UpdateAgreementApiService
import com.siriustech.merit.apilayer.service.onboarding.updateagreement.UpdateAgreementCaller
import com.siriustech.merit.apilayer.service.onboarding.updateagreement.UpdateAgreementRequest
import com.siriustech.merit.apilayer.service.onboarding.updateagreement.UpdateAgreementResponse
import com.siriustech.merit.apilayer.service.onboarding.updateagreement.UpdateAgreementUseCase
import com.siriustech.merit.apilayer.service.onboarding.updatecrsdeclaration.UpdateCRSDeclarationApiService
import com.siriustech.merit.apilayer.service.onboarding.updatecrsdeclaration.UpdateCRSDeclarationCaller
import com.siriustech.merit.apilayer.service.onboarding.updatecrsdeclaration.UpdateCRSDeclarationListRequest
import com.siriustech.merit.apilayer.service.onboarding.updatecrsdeclaration.UpdateCRSDeclarationUseCase
import com.siriustech.merit.apilayer.service.onboarding.updatecrsdeclaration.UpdateCRSResponse
import com.siriustech.merit.apilayer.service.onboarding.updatecustomerprofile.CustomerProfileRequest
import com.siriustech.merit.apilayer.service.onboarding.updatecustomerprofile.UpdateCustomerProfileApiService
import com.siriustech.merit.apilayer.service.onboarding.updatecustomerprofile.UpdateCustomerProfileCaller
import com.siriustech.merit.apilayer.service.onboarding.updatecustomerprofile.UpdateCustomerProfileUseCase
import com.siriustech.merit.apilayer.service.onboarding.updateemployment.UpdateUserEmploymentApiService
import com.siriustech.merit.apilayer.service.onboarding.updateemployment.UpdateUserEmploymentCaller
import com.siriustech.merit.apilayer.service.onboarding.updateemployment.UpdateUserEmploymentUseCase
import com.siriustech.merit.apilayer.service.onboarding.updateemployment.UserEmploymentRequest
import com.siriustech.merit.apilayer.service.onboarding.updateidentitycard.UpdateIdentityCardListApiService
import com.siriustech.merit.apilayer.service.onboarding.updateidentitycard.UpdateIdentityCardListCaller
import com.siriustech.merit.apilayer.service.onboarding.updateidentitycard.UpdateIdentityCardListRequest
import com.siriustech.merit.apilayer.service.onboarding.updateidentitycard.UpdateIdentityCardListResponse
import com.siriustech.merit.apilayer.service.onboarding.updateidentitycard.UpdateIdentityCardListUseCase
import com.siriustech.merit.apilayer.service.onboarding.updatequestionnaire.QuestionnaireUpdateRequest
import com.siriustech.merit.apilayer.service.onboarding.updatequestionnaire.QuestionnaireUpdateResponse
import com.siriustech.merit.apilayer.service.onboarding.updatequestionnaire.UpdateQuestionnaireApiService
import com.siriustech.merit.apilayer.service.onboarding.updatequestionnaire.UpdateQuestionnaireCaller
import com.siriustech.merit.apilayer.service.onboarding.updatequestionnaire.UpdateQuestionnaireUseCase
import com.siriustech.merit.apilayer.service.onboarding.uploadonboardingodcument.UploadDocumentOnboardingApiService
import com.siriustech.merit.apilayer.service.onboarding.uploadonboardingodcument.UploadDocumentOnboardingCaller
import com.siriustech.merit.apilayer.service.onboarding.uploadonboardingodcument.UploadDocumentOnboardingRequest
import com.siriustech.merit.apilayer.service.onboarding.uploadonboardingodcument.UploadDocumentOnboardingResponse
import com.siriustech.merit.apilayer.service.onboarding.uploadonboardingodcument.UploadDocumentOnboardingUseCase
import com.siriustech.merit.apilayer.service.onboarding.userbanklist.GetUserBankListApiService
import com.siriustech.merit.apilayer.service.onboarding.userbanklist.GetUserBankListCaller
import com.siriustech.merit.apilayer.service.onboarding.userbanklist.GetUserBankListUseCase
import com.siriustech.merit.apilayer.service.onboarding.userbanklist.UserBankListResponse
import com.siriustech.merit.apilayer.service.onboarding.usercheck.OnboardingUserCheckApiService
import com.siriustech.merit.apilayer.service.onboarding.usercheck.OnboardingUserCheckCaller
import com.siriustech.merit.apilayer.service.onboarding.usercheck.OnboardingUserCheckRequest
import com.siriustech.merit.apilayer.service.onboarding.usercheck.OnboardingUserCheckResponse
import com.siriustech.merit.apilayer.service.onboarding.usercheck.OnboardingUserCheckUseCase
import com.siriustech.merit.apilayer.service.requestdocsign.RequestDocSignApiService
import com.siriustech.merit.apilayer.service.requestdocsign.RequestDocSignCaller
import com.siriustech.merit.apilayer.service.requestdocsign.RequestDocSignResponse
import com.siriustech.merit.apilayer.service.requestdocsign.RequestDocSignUseCase
import com.siriustech.merit.apilayer.service.user.banklist.BankAccountsResponse
import com.siriustech.merit.apilayer.service.user.banklist.GetBankAccountListCaller
import com.siriustech.merit.apilayer.service.user.banklist.GetBankAccountListUseCase
import com.siriustech.merit.apilayer.service.user.banklist.GetBankAccountsApiService
import com.siriustech.merit.apilayer.service.user.chat.ChatListApiService
import com.siriustech.merit.apilayer.service.user.chat.ChatListCaller
import com.siriustech.merit.apilayer.service.user.chat.ChatListRequest
import com.siriustech.merit.apilayer.service.user.chat.ChatListResponse
import com.siriustech.merit.apilayer.service.user.chat.ChatListUseCase
import com.siriustech.merit.apilayer.service.user.infocheck.UserInfoCheckApiService
import com.siriustech.merit.apilayer.service.user.infocheck.UserInfoCheckCaller
import com.siriustech.merit.apilayer.service.user.infocheck.UserInfoCheckRequest
import com.siriustech.merit.apilayer.service.user.infocheck.UserInfoCheckResponse
import com.siriustech.merit.apilayer.service.user.infocheck.UserInfoCheckUseCase
import com.siriustech.merit.apilayer.service.user.logout.LogoutApiService
import com.siriustech.merit.apilayer.service.user.logout.LogoutCaller
import com.siriustech.merit.apilayer.service.user.logout.LogoutRequest
import com.siriustech.merit.apilayer.service.user.logout.LogoutResponse
import com.siriustech.merit.apilayer.service.user.logout.LogoutUseCase
import com.siriustech.merit.apilayer.service.user.modify.ModifyBankAccountCaller
import com.siriustech.merit.apilayer.service.user.modify.ModifyBankAccountUseCase
import com.siriustech.merit.apilayer.service.user.modify.ModifyBankApiService
import com.siriustech.merit.apilayer.service.user.modify.ModifyUserBankRequest
import com.siriustech.merit.apilayer.service.user.recentlyview.RecentlyViewedRequest
import com.siriustech.merit.apilayer.service.user.recentlyview.RecentlyViewedUpdateApiService
import com.siriustech.merit.apilayer.service.user.recentlyview.RecentlyViewedUpdateCaller
import com.siriustech.merit.apilayer.service.user.recentlyview.RecentlyViewedUpdateUseCase
import com.siriustech.merit.apilayer.service.user.sendchat.SendChatMessageApiService
import com.siriustech.merit.apilayer.service.user.sendchat.SendChatMessageCaller
import com.siriustech.merit.apilayer.service.user.sendchat.SendChatMessageRequest
import com.siriustech.merit.apilayer.service.user.sendchat.SendChatMessageResponse
import com.siriustech.merit.apilayer.service.user.sendchat.SendChatMessageUseCase
import com.siriustech.merit.apilayer.service.user.status.UpdateUserStatusApiService
import com.siriustech.merit.apilayer.service.user.status.UpdateUserStatusCaller
import com.siriustech.merit.apilayer.service.user.status.UpdateUserStatusRequest
import com.siriustech.merit.apilayer.service.user.status.UpdateUserStatusResponse
import com.siriustech.merit.apilayer.service.user.status.UpdateUserStatusUseCase
import com.siriustech.merit.apilayer.service.user.updateuserinfo.UpdateUserInfoApiService
import com.siriustech.merit.apilayer.service.user.updateuserinfo.UpdateUserInfoCaller
import com.siriustech.merit.apilayer.service.user.updateuserinfo.UpdateUserInfoRequest
import com.siriustech.merit.apilayer.service.user.updateuserinfo.UpdateUserInfoResponse
import com.siriustech.merit.apilayer.service.user.updateuserinfo.UpdateUserInfoUseCase
import com.siriustech.merit.apilayer.service.user.userinfo.GetUserBasicInfoApiService
import com.siriustech.merit.apilayer.service.user.userinfo.GetUserBasicInfoCaller
import com.siriustech.merit.apilayer.service.user.userinfo.GetUserBasicInfoUseCase
import com.siriustech.merit.apilayer.service.user.userinfo.UserBasicInfoResponse
import com.siriustech.merit.apilayer.service.wallet.balancehistory.WalletBalanceHistoryApiService
import com.siriustech.merit.apilayer.service.wallet.balancehistory.WalletBalanceHistoryCaller
import com.siriustech.merit.apilayer.service.wallet.balancehistory.WalletBalanceHistoryRequest
import com.siriustech.merit.apilayer.service.wallet.balancehistory.WalletBalanceHistoryResponse
import com.siriustech.merit.apilayer.service.wallet.balancehistory.WalletBalanceHistoryUseCase
import com.siriustech.merit.apilayer.service.wallet.deposit.DepositApiService
import com.siriustech.merit.apilayer.service.wallet.deposit.DepositCaller
import com.siriustech.merit.apilayer.service.wallet.deposit.DepositRequest
import com.siriustech.merit.apilayer.service.wallet.deposit.DepositResponse
import com.siriustech.merit.apilayer.service.wallet.deposit.DepositUseCase
import com.siriustech.merit.apilayer.service.wallet.gainlosshistory.GainLossHistoryApiService
import com.siriustech.merit.apilayer.service.wallet.gainlosshistory.GainLossHistoryCaller
import com.siriustech.merit.apilayer.service.wallet.gainlosshistory.GainLossHistoryRequest
import com.siriustech.merit.apilayer.service.wallet.gainlosshistory.GainLossHistoryResponse
import com.siriustech.merit.apilayer.service.wallet.gainlosshistory.GainLossHistoryUseCase
import com.siriustech.merit.apilayer.service.wallet.report.GenerateReportApiService
import com.siriustech.merit.apilayer.service.wallet.report.GenerateReportCaller
import com.siriustech.merit.apilayer.service.wallet.report.GenerateReportRequest
import com.siriustech.merit.apilayer.service.wallet.report.GenerateReportResponse
import com.siriustech.merit.apilayer.service.wallet.report.GenerateReportUseCase
import com.siriustech.merit.apilayer.service.wallet.statistic.WalletStatisticApiService
import com.siriustech.merit.apilayer.service.wallet.statistic.WalletStatisticCaller
import com.siriustech.merit.apilayer.service.wallet.statistic.WalletStatisticResponse
import com.siriustech.merit.apilayer.service.wallet.statistic.WalletStatisticUseCase
import com.siriustech.merit.apilayer.service.wallet.summary.WalletSummaryApiService
import com.siriustech.merit.apilayer.service.wallet.summary.WalletSummaryCaller
import com.siriustech.merit.apilayer.service.wallet.summary.WalletSummaryRequest
import com.siriustech.merit.apilayer.service.wallet.summary.WalletSummaryResponse
import com.siriustech.merit.apilayer.service.wallet.summary.WalletSummaryUseCase
import com.siriustech.merit.apilayer.service.wallet.withdraw.WithdrawApiService
import com.siriustech.merit.apilayer.service.wallet.withdraw.WithdrawCaller
import com.siriustech.merit.apilayer.service.wallet.withdraw.WithdrawRequest
import com.siriustech.merit.apilayer.service.wallet.withdraw.WithdrawResponse
import com.siriustech.merit.apilayer.service.wallet.withdraw.WithdrawUseCase
import com.siriustech.merit.apilayer.service.wealthplan.details.WealthPlanDetailsApiService
import com.siriustech.merit.apilayer.service.wealthplan.details.WealthPlanDetailsCaller
import com.siriustech.merit.apilayer.service.wealthplan.details.WealthPlanDetailsRequest
import com.siriustech.merit.apilayer.service.wealthplan.details.WealthPlanDetailsResponse
import com.siriustech.merit.apilayer.service.wealthplan.details.WealthPlanDetailsUseCase
import com.siriustech.merit.apilayer.service.wealthplan.list.WealthPlanListApiService
import com.siriustech.merit.apilayer.service.wealthplan.list.WealthPlanListCaller
import com.siriustech.merit.apilayer.service.wealthplan.list.WealthPlanListUseCase
import com.siriustech.merit.apilayer.service.wealthplan.list.WealthPlanResponse
import com.siriustech.merit.apilayer.service.wealthplan.modify.ModifyWealthPlanApiService
import com.siriustech.merit.apilayer.service.wealthplan.modify.ModifyWealthPlanCaller
import com.siriustech.merit.apilayer.service.wealthplan.modify.ModifyWealthPlanRequest
import com.siriustech.merit.apilayer.service.wealthplan.modify.ModifyWealthPlanResponse
import com.siriustech.merit.apilayer.service.wealthplan.modify.ModifyWealthPlanUseCase
import com.siriustech.merit.apilayer.service.wealthplan.performance.WealthPlanPerformanceApiService
import com.siriustech.merit.apilayer.service.wealthplan.performance.WealthPlanPerformanceCaller
import com.siriustech.merit.apilayer.service.wealthplan.performance.WealthPlanPerformanceRequest
import com.siriustech.merit.apilayer.service.wealthplan.performance.WealthPlanPerformanceResponse
import com.siriustech.merit.apilayer.service.wealthplan.performance.WealthPlanPerformanceUseCase
import dagger.Module
import dagger.Provides
import dagger.hilt.InstallIn
import dagger.hilt.android.components.ViewModelComponent
import javax.inject.Named
import okhttp3.RequestBody

/**
 * Created by Hein Htet
 */

@Module
@InstallIn(ViewModelComponent::class)
class UseCaseModule {

    @Provides
    fun provideSampleCaller(apiService: LoginApiService): LoginCaller {
        return LoginCaller(apiService)
    }

    @Provides
    fun provideSampleRepository(caller: LoginCaller): BaseRepository<LoginUserRequest, BaseResponse<LoginUserResponse>> {
        return getRepository(caller)
    }

    @Provides
    fun provideLoginUseCase(
        repository: BaseRepository<LoginUserRequest, BaseResponse<LoginUserResponse>>,
    ): LoginUseCase {
        return LoginUseCase(repository)
    }


    @Provides
    fun provideGetUserInfoCaller(apiService: GetUserInfoApiService): GetUserInfoCaller {
        return GetUserInfoCaller(apiService)
    }

    @Provides
    fun provideGetUserInfoRepository(caller: GetUserInfoCaller): BaseRepository<Unit, BaseResponse<UserInfoResponse>> {
        return getRepository(caller)
    }

    @Provides
    fun provideGetUserInfoUseCase(
        repository: BaseRepository<Unit, BaseResponse<UserInfoResponse>>,
    ): GetUserInfoUseCase {
        return GetUserInfoUseCase(repository)
    }


    @Provides
    fun provideGetOTPInfoCaller(apiService: GetOTPApiService): GetOTPCaller {
        return GetOTPCaller(apiService)
    }

    @Provides
    fun provideGetOTPInfoRepository(caller: GetOTPCaller): BaseRepository<GetOTPRequest, BaseResponse<GetOTPResponse>> {
        return getRepository(caller)
    }

    @Provides
    fun provideGetOTPInfoUseCase(
        repository: BaseRepository<GetOTPRequest, BaseResponse<GetOTPResponse>>,
    ): GetOTPUseCase {
        return GetOTPUseCase(repository)
    }

    //
    @Provides
    fun provideConfigure2FACaller(apiService: Configure2FAApiService): Configure2FACaller {
        return Configure2FACaller(apiService)
    }

    @Provides
    fun provideConfigure2FARepository(caller: Configure2FACaller): BaseRepository<Configure2FARequest, BaseResponse<Unit>> {
        return getRepository(caller)
    }

    @Provides
    fun provideConfigure2FAUseCase(
        repository: BaseRepository<Configure2FARequest, BaseResponse<Unit>>,
    ): Configure2FAUseCase {
        return Configure2FAUseCase(repository)
    }


    //
    @Provides
    fun provideConfigurePinPassCaller(apiService: ConfigurePinPassApiService): ConfigurePinPassCaller {
        return ConfigurePinPassCaller(apiService)
    }

    @Provides
    fun provideConfigurePinPassRepository(caller: ConfigurePinPassCaller): BaseRepository<ConfigurePinPassRequest, BaseResponse<Unit>> {
        return getRepository(caller)
    }

    @Provides
    fun provideConfigurePinPassUseCase(
        repository: BaseRepository<ConfigurePinPassRequest, BaseResponse<Unit>>,
    ): ConfigurePinPassUseCase {
        return ConfigurePinPassUseCase(repository)
    }


    //
    @Provides
    fun provideCheckOnboardingStepCaller(apiService: CheckOnboardingStepApiService): CheckOnboardingStepCaller {
        return CheckOnboardingStepCaller(apiService)
    }

    @Provides
    fun provideCheckOnboardingStepRepository(caller: CheckOnboardingStepCaller): BaseRepository<CheckOnboardingStepRequest, BaseResponse<CheckOnboardingStepResponse>> {
        return getRepository(caller)
    }

    @Provides
    fun provideCheckOnboardingStepUseCase(
        repository: BaseRepository<CheckOnboardingStepRequest, BaseResponse<CheckOnboardingStepResponse>>,
    ): CheckOnboardingStepUseCase {
        return CheckOnboardingStepUseCase(repository)
    }

    //
    @Provides
    fun provideRegisterStepCaller(apiService: RegisterApiService): RegisterCaller {
        return RegisterCaller(apiService)
    }

    @Provides
    fun provideRegisterRepository(caller: RegisterCaller): BaseRepository<RegisterRequest, BaseResponse<RegisterResponse>> {
        return getRepository(caller)
    }

    @Provides
    fun provideRegisterUseCase(
        repository: BaseRepository<RegisterRequest, BaseResponse<RegisterResponse>>,
    ): RegisterUseCase {
        return RegisterUseCase(repository)
    }


    //
    @Provides
    fun provideGetEnumerationStepCaller(apiService: GetEnumerationService): GetEnumerationCaller {
        return GetEnumerationCaller(apiService)
    }

    @Provides
    fun provideGetEnumerationRepository(caller: GetEnumerationCaller): BaseRepository<EnumerationRequest, BaseResponse<EnumerationResponse>> {
        return getRepository(caller)
    }

    @Provides
    fun provideGetEnumerationUseCase(
        repository: BaseRepository<EnumerationRequest, BaseResponse<EnumerationResponse>>,
    ): GetEnumerationUseCase {
        return GetEnumerationUseCase(repository)
    }


    //
    @Provides
    fun provideUploadFileCaller(apiService: UploadFileApiService): UploadFileCaller {
        return UploadFileCaller(apiService)
    }

    @Provides
    fun provideUploadFileRepository(caller: UploadFileCaller): BaseRepository<RequestBody, BaseResponse<UploadFileResponse>> {
        return getRepository(caller)
    }

    @Provides
    fun provideUploadFileUseCase(
        repository: BaseRepository<RequestBody, BaseResponse<UploadFileResponse>>,
    ): UploadFileUseCase {
        return UploadFileUseCase(repository)
    }


    //
    @Provides
    fun provideCheckOCRCaller(apiService: OCRApiService): CheckOCRCaller {
        return CheckOCRCaller(apiService)
    }

    @Provides
    fun provideCheckOCRRepository(caller: CheckOCRCaller): BaseRepository<OCRRequest, BaseResponse<OCRResponse>> {
        return getRepository(caller)
    }

    @Provides
    fun provideCheckOCRUseCase(
        repository: BaseRepository<OCRRequest, BaseResponse<OCRResponse>>,
    ): CheckOCRUseCase {
        return CheckOCRUseCase(repository)
    }


    //
    @Provides
    fun provideLivenessCheckCaller(apiService: LivenessApiService): LivenessCheckCaller {
        return LivenessCheckCaller(apiService)
    }

    @Provides
    fun provideLivenessCheckRepository(caller: LivenessCheckCaller): BaseRepository<LivenessCheckResquest, BaseResponse<LivenessCheckResponse>> {
        return getRepository(caller)
    }

    @Provides
    fun provideLivenessCheckRUseCase(
        repository: BaseRepository<LivenessCheckResquest, BaseResponse<LivenessCheckResponse>>,
    ): LivenessCheckUseCase {
        return LivenessCheckUseCase(repository)
    }


    //
    @Provides
    fun provideMarketExchangeCurrencyCheckCaller(apiService: MarketCurrencyApiService): MarketExchangeCurrencyCaller {
        return MarketExchangeCurrencyCaller(apiService)
    }

    @Provides
    fun provideMarketExchangeCurrencyRepository(caller: MarketExchangeCurrencyCaller): BaseRepository<MarketCurrencyRequest, BaseResponse<MarketCurrencyListResponse>> {
        return getRepository(caller)
    }

    @Provides
    fun provideMarketExchangeCurrencyUseCase(
        repository: BaseRepository<MarketCurrencyRequest, BaseResponse<MarketCurrencyListResponse>>,
    ): MarketExchangeCurrencyUseCase {
        return MarketExchangeCurrencyUseCase(repository)
    }


    //
    @Provides
    fun provideWallerSummaryCaller(apiService: WalletSummaryApiService): WalletSummaryCaller {
        return WalletSummaryCaller(apiService)
    }

    @Provides
    fun provideWallerSummaryRepository(caller: WalletSummaryCaller): BaseRepository<WalletSummaryRequest, BaseResponse<WalletSummaryResponse>> {
        return getRepository(caller)
    }

    @Provides
    fun provideWallerSummaryUseCase(
        repository: BaseRepository<WalletSummaryRequest, BaseResponse<WalletSummaryResponse>>,
    ): WalletSummaryUseCase {
        return WalletSummaryUseCase(repository)
    }

    //
    @Provides
    fun provideWalletBalanceHistorySummaryCaller(apiService: WalletBalanceHistoryApiService): WalletBalanceHistoryCaller {
        return WalletBalanceHistoryCaller(apiService)
    }

    @Provides
    fun provideWalletBalanceHistorySummaryRepository(caller: WalletBalanceHistoryCaller): BaseRepository<WalletBalanceHistoryRequest, BaseResponse<WalletBalanceHistoryResponse>> {
        return getRepository(caller)
    }

    @Provides
    fun provideWalletBalanceHistorySummaryUseCase(
        repository: BaseRepository<WalletBalanceHistoryRequest, BaseResponse<WalletBalanceHistoryResponse>>,
    ): WalletBalanceHistoryUseCase {
        return WalletBalanceHistoryUseCase(repository)
    }


    //
    @Provides
    fun provideWalletStatisticCaller(apiService: WalletStatisticApiService): WalletStatisticCaller {
        return WalletStatisticCaller(apiService)
    }

    @Provides
    fun provideWalletStatisticRepository(caller: WalletStatisticCaller): BaseRepository<Unit, BaseResponse<WalletStatisticResponse>> {
        return getRepository(caller)
    }

    @Provides
    fun provideWalletStatisticUseCase(
        repository: BaseRepository<Unit?, BaseResponse<WalletStatisticResponse>>,
    ): WalletStatisticUseCase {
        return WalletStatisticUseCase(repository)
    }


    //
    @Provides
    fun provideMarketListDetailsCaller(apiService: MarketListDetailsApiService): MarketListDetailsCaller {
        return MarketListDetailsCaller(apiService)
    }

    @Provides
    fun provideMarketListDetailsRepository(caller: MarketListDetailsCaller): BaseRepository<MarketInstrumentListRequest, BaseResponse<MarketInstrumentListResponse>> {
        return getRepository(caller)
    }

    @Provides
    fun provideMarketListDetailsUseCase(
        repository: BaseRepository<MarketInstrumentListRequest, BaseResponse<MarketInstrumentListResponse>>,
    ): MarketListDetailsUseCase {
        return MarketListDetailsUseCase(repository)
    }


    @Provides
    fun provideBenchMarkChartCaller(apiService: BenchMarkChartApiService): BenchMarkChartCaller {
        return BenchMarkChartCaller(apiService)
    }

    @Provides
    fun provideBenchMarkChartRepository(caller: BenchMarkChartCaller): BaseRepository<BenchMarkChartRequest, BaseResponse<BenchMarkChartResponse>> {
        return getRepository(caller)
    }

    @Provides
    fun provideBenchMarkChartUseCase(
        repository: BaseRepository<BenchMarkChartRequest, BaseResponse<BenchMarkChartResponse>>,
    ): BenchMarkChartUseCase {
        return BenchMarkChartUseCase(repository)
    }


    @Provides
    fun provideAuthResetCaller(apiService: AuthResetApiService): AuthResetCaller {
        return AuthResetCaller(apiService)
    }

    @Provides
    fun provideAuthResetRepository(caller: AuthResetCaller): BaseRepository<AuthResetRequest, BaseResponse<AuthResetResponse>> {
        return getRepository(caller)
    }

    @Provides
    fun provideAuthResetUseCase(
        repository: BaseRepository<AuthResetRequest, BaseResponse<AuthResetResponse>>,
    ): AuthResetUseCase {
        return AuthResetUseCase(repository)
    }


    @Provides
    fun provideAuthResetVerifyCaller(apiService: AuthResetVerifyApiService): AuthResetVerifyCaller {
        return AuthResetVerifyCaller(apiService)
    }

    @Provides
    fun provideAuthResetVerifyRepository(caller: AuthResetVerifyCaller): BaseRepository<AuthResetVerifyRequest, BaseResponse<AuthResetVerifyResponse>> {
        return getRepository(caller)
    }

    @Provides
    fun provideAuthResetVerifyUseCase(
        repository: BaseRepository<AuthResetVerifyRequest, BaseResponse<AuthResetVerifyResponse>>,
    ): AuthResetVerifyUseCase {
        return AuthResetVerifyUseCase(repository)
    }


    @Provides
    fun provideGetBankAccountListCaller(apiService: GetBankAccountsApiService): GetBankAccountListCaller {
        return GetBankAccountListCaller(apiService)
    }

    @Provides
    fun provideGetBankAccountListRepository(caller: GetBankAccountListCaller): BaseRepository<Unit, BaseResponse<BankAccountsResponse>> {
        return getRepository(caller)
    }

    @Provides
    fun provideGetBankAccountListUseCase(
        repository: BaseRepository<Unit, BaseResponse<BankAccountsResponse>>,
    ): GetBankAccountListUseCase {
        return GetBankAccountListUseCase(repository)
    }


    @Provides
    fun provideDepositCaller(apiService: DepositApiService): DepositCaller {
        return DepositCaller(apiService)
    }

    @Provides
    fun provideDepositRepository(caller: DepositCaller): BaseRepository<DepositRequest, BaseResponse<DepositResponse>> {
        return getRepository(caller)
    }

    @Provides
    fun provideDepositUseCase(
        repository: BaseRepository<DepositRequest, BaseResponse<DepositResponse>>,
    ): DepositUseCase {
        return DepositUseCase(repository)
    }


    @Provides
    fun provideWithdrawCaller(apiService: WithdrawApiService): WithdrawCaller {
        return WithdrawCaller(apiService)
    }

    @Provides
    fun provideWithdrawRepository(caller: WithdrawCaller): BaseRepository<WithdrawRequest, BaseResponse<WithdrawResponse>> {
        return getRepository(caller)
    }

    @Provides
    fun provideWithdrawUseCase(
        repository: BaseRepository<WithdrawRequest, BaseResponse<WithdrawResponse>>,
    ): WithdrawUseCase {
        return WithdrawUseCase(repository)
    }


    @Provides
    fun provideGainLossHistoryCaller(apiService: GainLossHistoryApiService): GainLossHistoryCaller {
        return GainLossHistoryCaller(apiService)
    }

    @Provides
    fun provideGainLossHistoryRepository(caller: GainLossHistoryCaller): BaseRepository<GainLossHistoryRequest, BaseResponse<GainLossHistoryResponse>> {
        return getRepository(caller)
    }

    @Provides
    fun provideGainLossHistoryUseCase(
        repository: BaseRepository<GainLossHistoryRequest, BaseResponse<GainLossHistoryResponse>>,
    ): GainLossHistoryUseCase {
        return GainLossHistoryUseCase(repository)
    }


    //
    @Provides
    fun provideFavoriteListDetailsCaller(apiService: GetFavoriteAssetListApiService): FavoriteListListDetailsCaller {
        return FavoriteListListDetailsCaller(apiService)
    }

    @Provides
    fun provideFavoriteListDetailsRepository(caller: FavoriteListListDetailsCaller): BaseRepository<Unit, BaseResponse<MarketInstrumentListResponse>> {
        return getRepository(caller)
    }

    @Provides
    fun provideFavoriteListDetailsUseCase(
        repository: BaseRepository<Unit, BaseResponse<MarketInstrumentListResponse>>,
    ): FavoriteListDetailsUseCase {
        return FavoriteListDetailsUseCase(repository)
    }


    //
    @Provides
    fun provideRecentlyViewedListDetailsCaller(apiService: GetRecentlyViewedListApiService): RecentlyViewedListListDetailsCaller {
        return RecentlyViewedListListDetailsCaller(apiService)
    }

    @Provides
    @Named("RECENTLY_VIEWED")
    fun provideRecentlyViewedListDetailsRepository(caller: RecentlyViewedListListDetailsCaller): BaseRepository<Unit, BaseResponse<MarketInstrumentListResponse>> {
        return getRepository(caller)
    }

    @Provides
    fun provideRecentlyViewedListDetailsUseCase(
        @Named("RECENTLY_VIEWED") repository: BaseRepository<Unit?, BaseResponse<MarketInstrumentListResponse>>,
    ): RecentlyViewedListDetailsUseCase {
        return RecentlyViewedListDetailsUseCase(repository)
    }


    //
    @Provides
    fun provideToggleFavouriteCaller(apiService: ToggleFavoriteApiService): ToggleFavoriteCaller {
        return ToggleFavoriteCaller(apiService)
    }

    @Provides
    fun provideToggleFavouriteRepository(caller: ToggleFavoriteCaller): BaseRepository<ToggleFavoriteRequest, BaseResponse<ToggleFavoriteResponse>> {
        return getRepository(caller)
    }

    @Provides
    fun provideToggleFavouriteUseCase(
        repository: BaseRepository<ToggleFavoriteRequest?, BaseResponse<ToggleFavoriteResponse>>,
    ): ToggleFavoriteUseCase {
        return ToggleFavoriteUseCase(repository)
    }


    //
    @Provides
    fun provideInstrumentSearchCaller(apiService: InstrumentSearchApiService): InstrumentSearchCaller {
        return InstrumentSearchCaller(apiService)
    }

    @Provides
    fun provideInstrumentSearchRepository(caller: InstrumentSearchCaller): BaseRepository<InstrumentSearchRequest, BaseResponse<MarketInstrumentListResponse>> {
        return getRepository(caller)
    }

    @Provides
    fun provideInstrumentSearchUseCase(
        repository: BaseRepository<InstrumentSearchRequest?, BaseResponse<MarketInstrumentListResponse>>,
    ): InstrumentSearchUseCase {
        return InstrumentSearchUseCase(repository)
    }


    //
    @Provides
    fun provideCandleChartDataCaller(apiService: CandleChartApiService): CandleChartDataCaller {
        return CandleChartDataCaller(apiService)
    }

    @Provides
    fun provideCandleChartDataRepository(caller: CandleChartDataCaller): BaseRepository<CandleChartDataRequest, BaseResponse<CandleChartDataResponse>> {
        return getRepository(caller)
    }

    @Provides
    fun provideCandleChartDataUseCase(
        repository: BaseRepository<CandleChartDataRequest?, BaseResponse<CandleChartDataResponse>>,
    ): CandleChartDataUseCase {
        return CandleChartDataUseCase(repository)
    }


    //
    @Provides
    fun provideSimpleChartDataCaller(apiService: SimpleChartApiService): SimpleChartDataCaller {
        return SimpleChartDataCaller(apiService)
    }

    @Provides
    fun provideSimpleChartDataRepository(caller: SimpleChartDataCaller): BaseRepository<SimpleChartRequest, BaseResponse<SimpleChartResponse>> {
        return getRepository(caller)
    }

    @Provides
    fun provideSimpleChartDataUseCase(
        repository: BaseRepository<SimpleChartRequest?, BaseResponse<SimpleChartResponse>>,
    ): SimpleChartUseCase {
        return SimpleChartUseCase(repository)
    }


    //
    @Provides
    fun provideMarketQuoteCaller(apiService: MarketQuoteApiService): MarketQuoteDataCaller {
        return MarketQuoteDataCaller(apiService)
    }

    @Provides
    fun provideMarketQuoteDataRepository(caller: MarketQuoteDataCaller): BaseRepository<MarketQuoteRequest, BaseResponse<MarketQuoteResponse>> {
        return getRepository(caller)
    }

    @Provides
    fun provideQuoteDataUseCase(
        repository: BaseRepository<MarketQuoteRequest?, BaseResponse<MarketQuoteResponse>>,
    ): MarketQuoteDataUseCase {
        return MarketQuoteDataUseCase(repository)
    }


    //
    @Provides
    fun provideMarketInstrumentDetailsCaller(apiService: MarketInstrumentDetailsApiService): MarketInstrumentDetailsCaller {
        return MarketInstrumentDetailsCaller(apiService)
    }

    @Provides
    fun provideMarketInstrumentDetailsRepository(caller: MarketInstrumentDetailsCaller): BaseRepository<MarketInstrumentDetailsRequest, BaseResponse<com.siriustech.merit.apilayer.service.market.marketprofile.MarketInstrumentDetailsResponse>> {
        return getRepository(caller)
    }

    @Provides
    fun provideMarketInstrumentDetailsUseCase(
        repository: BaseRepository<MarketInstrumentDetailsRequest?, BaseResponse<com.siriustech.merit.apilayer.service.market.marketprofile.MarketInstrumentDetailsResponse>>,
    ): MarketInstrumentDetailsUseCase {
        return MarketInstrumentDetailsUseCase(repository)
    }

    //
    @Provides
    fun provideWealthPlanListCaller(apiService: WealthPlanListApiService): WealthPlanListCaller {
        return WealthPlanListCaller(apiService)
    }

    @Provides
    fun provideWealthPlanListRepository(caller: WealthPlanListCaller): BaseRepository<Unit, BaseResponse<WealthPlanResponse>> {
        return getRepository(caller)
    }

    @Provides
    fun provideWealthPlanListUseCase(
        repository: BaseRepository<Unit, BaseResponse<WealthPlanResponse>>,
    ): WealthPlanListUseCase {
        return WealthPlanListUseCase(repository)
    }


    //
    @Provides
    fun provideModifyWealthPlanCaller(apiService: ModifyWealthPlanApiService): ModifyWealthPlanCaller {
        return ModifyWealthPlanCaller(apiService)
    }

    @Provides
    fun provideModifyWealthPlanRepository(caller: ModifyWealthPlanCaller): BaseRepository<ModifyWealthPlanRequest, BaseResponse<ModifyWealthPlanResponse>> {
        return getRepository(caller)
    }

    @Provides
    fun provideModifyWealthPlanUseCase(
        repository: BaseRepository<ModifyWealthPlanRequest, BaseResponse<ModifyWealthPlanResponse>>,
    ): ModifyWealthPlanUseCase {
        return ModifyWealthPlanUseCase(repository)
    }


    //
    @Provides
    fun provideWealthPlanDetailsCaller(apiService: WealthPlanDetailsApiService): WealthPlanDetailsCaller {
        return WealthPlanDetailsCaller(apiService)
    }

    @Provides
    fun provideWealthPlanDetailsRepository(caller: WealthPlanDetailsCaller): BaseRepository<WealthPlanDetailsRequest, BaseResponse<WealthPlanDetailsResponse>> {
        return getRepository(caller)
    }

    @Provides
    fun provideWealthPlanDetailsUseCase(
        repository: BaseRepository<WealthPlanDetailsRequest, BaseResponse<WealthPlanDetailsResponse>>,
    ): WealthPlanDetailsUseCase {
        return WealthPlanDetailsUseCase(repository)
    }


    //
    @Provides
    fun provideSignDocumentCaller(apiService: SignDocumentApiService): SignDocumentCaller {
        return SignDocumentCaller(apiService)
    }

    @Provides
    fun provideSignDocumentRepository(caller: SignDocumentCaller): BaseRepository<SignDocumentRequest, BaseResponse<SignDocumentResponse>> {
        return getRepository(caller)
    }

    @Provides
    fun provideSignDocumentUseCase(
        repository: BaseRepository<SignDocumentRequest, BaseResponse<SignDocumentResponse>>,
    ): SignDocumentUseCase {
        return SignDocumentUseCase(repository)
    }


    //
    @Provides
    fun provideWealthPlanHistoricalPerformanceCaller(apiService: WealthPlanPerformanceApiService): WealthPlanPerformanceCaller {
        return WealthPlanPerformanceCaller(apiService)
    }

    @Provides
    fun provideWealthPlanHistoricalPerformanceRepository(caller: WealthPlanPerformanceCaller): BaseRepository<WealthPlanPerformanceRequest, BaseResponse<WealthPlanPerformanceResponse>> {
        return getRepository(caller)
    }

    @Provides
    fun provideWealthPlanHistoricalPerformanceUseCase(
        repository: BaseRepository<WealthPlanPerformanceRequest, BaseResponse<WealthPlanPerformanceResponse>>,
    ): WealthPlanPerformanceUseCase {
        return WealthPlanPerformanceUseCase(repository)
    }


    //
    @Provides
    fun provideWatchListCaller(apiService: MarketWatchListApiService): MarketWatchListCaller {
        return MarketWatchListCaller(apiService)
    }

    @Provides
    fun provideMarketWatchListRepository(caller: MarketWatchListCaller): BaseRepository<MarketWatchListRequest, BaseResponse<MarketWatchListResponse>> {
        return getRepository(caller)
    }

    @Provides
    fun provideMarketWatchListUseCase(
        repository: BaseRepository<MarketWatchListRequest, BaseResponse<MarketWatchListResponse>>,
    ): MarketWatchListUseCase {
        return MarketWatchListUseCase(repository)
    }


    //
    @Provides
    fun provideNotificationCaller(apiService: NotificationApiService): NotificationCaller {
        return NotificationCaller(apiService)
    }

    @Provides
    fun provideNotificationRepository(caller: NotificationCaller): BaseRepository<NotificationRequest, BaseResponse<NotificationListResponse>> {
        return getRepository(caller)
    }

    @Provides
    fun provideNotificationUseCase(
        repository: BaseRepository<NotificationRequest, BaseResponse<NotificationListResponse>>,
    ): NotificationUseCase {
        return NotificationUseCase(repository)
    }


    //
    @Provides
    fun provideModifyNotificationCaller(apiService: ModifyNotificationApiService): ModifyNotificationCaller {
        return ModifyNotificationCaller(apiService)
    }

    @Provides
    fun provideModifyNotificationRepository(caller: ModifyNotificationCaller): BaseRepository<ModifyNotificationRequest, BaseResponse<ModifyNotificationResponse>> {
        return getRepository(caller)
    }

    @Provides
    fun provideModifyNotificationUseCase(
        repository: BaseRepository<ModifyNotificationRequest, BaseResponse<ModifyNotificationResponse>>,
    ): ModifyNotificationUseCase {
        return ModifyNotificationUseCase(repository)
    }


    //
    @Provides
    fun provideGenerateReportCaller(apiService: GenerateReportApiService): GenerateReportCaller {
        return GenerateReportCaller(apiService)
    }

    @Provides
    fun provideGenerateReportRepository(caller: GenerateReportCaller): BaseRepository<GenerateReportRequest, BaseResponse<GenerateReportResponse>> {
        return getRepository(caller)
    }

    @Provides
    fun provideGenerateReportUseCase(
        repository: BaseRepository<GenerateReportRequest, BaseResponse<GenerateReportResponse>>,
    ): GenerateReportUseCase {
        return GenerateReportUseCase(repository)
    }


    //
    @Provides
    fun provideHistoryListCaller(apiService: GetHistoryApiService): GetHistoryListCaller {
        return GetHistoryListCaller(apiService)
    }

    @Provides
    fun provideHistoryListRepository(caller: GetHistoryListCaller): BaseRepository<GetHistoryRequest, BaseResponse<HistoryListResponse>> {
        return getRepository(caller)
    }

    @Provides
    fun provideHistoryListUseCase(
        repository: BaseRepository<GetHistoryRequest, BaseResponse<HistoryListResponse>>,
    ): GetHistoryListUseCase {
        return GetHistoryListUseCase(repository)
    }


    //
    @Provides
    fun provideTransactionHistoryListCaller(apiService: TransactionHistoryApiService): TransactionHistoryCaller {
        return TransactionHistoryCaller(apiService)
    }

    @Provides
    fun provideTransactionHistoryListRepository(caller: TransactionHistoryCaller): BaseRepository<TransactionHistoryRequest, BaseResponse<TransactionListResponse>> {
        return getRepository(caller)
    }

    @Provides
    fun provideTransactionHistoryListUseCase(
        repository: BaseRepository<TransactionHistoryRequest, BaseResponse<TransactionListResponse>>,
    ): TransactionHistoryListUseCase {
        return TransactionHistoryListUseCase(repository)
    }


    //
    @Provides
    fun provideDeleteNotificationCaller(apiService: DeleteNotificationApiService): DeleteNotificationCaller {
        return DeleteNotificationCaller(apiService)
    }

    @Provides
    fun provideDeleteNotificationRepository(caller: DeleteNotificationCaller): BaseRepository<DeleteNotificationRequest, BaseResponse<DeleteNotificationResponse>> {
        return getRepository(caller)
    }

    @Provides
    fun provideDeleteNotificationUseCase(
        repository: BaseRepository<DeleteNotificationRequest, BaseResponse<DeleteNotificationResponse>>,
    ): DeleteNotificationUseCase {
        return DeleteNotificationUseCase(repository)
    }


    //
    @Provides
    fun provideUpdateJPushRegIDCaller(apiService: UpdateJPushRegIDApiService): UpdateJPushRegIDApiCaller {
        return UpdateJPushRegIDApiCaller(apiService)
    }

    @Provides
    fun provideUpdateJPushRegIDRepository(caller: UpdateJPushRegIDApiCaller): BaseRepository<UpdateJPushRegIDRequest, BaseResponse<UpdateJPushRegIDResponse>> {
        return getRepository(caller)
    }

    @Provides
    fun provideUpdateJPushRegIDUseCase(
        repository: BaseRepository<UpdateJPushRegIDRequest, BaseResponse<UpdateJPushRegIDResponse>>,
    ): UpdateJPushRegIDUseCase {
        return UpdateJPushRegIDUseCase(repository)
    }


    //
    @Provides
    fun provideOrderDetailsCaller(apiService: OrderDetailsApiService): OrderDetailsCaller {
        return OrderDetailsCaller(apiService)
    }

    @Provides
    fun provideOrderDetailsRepository(caller: OrderDetailsCaller): BaseRepository<OrderDetailsRequest, BaseResponse<OrderDetailsResponse>> {
        return getRepository(caller)
    }

    @Provides
    fun provideOrderDetailsUseCase(
        repository: BaseRepository<OrderDetailsRequest, BaseResponse<OrderDetailsResponse>>,
    ): OrderDetailsUseCase {
        return OrderDetailsUseCase(repository)
    }


    //
    @Provides
    fun provideTransactionDetailsCaller(apiService: TransactionDetailsApiService): TransactionDetailsCaller {
        return TransactionDetailsCaller(apiService)
    }

    @Provides
    fun provideTransactionDetailsRepository(caller: TransactionDetailsCaller): BaseRepository<TransactionDetailsRequest, BaseResponse<TransactionDetailsResponse>> {
        return getRepository(caller)
    }

    @Provides
    fun provideTransactionDetailsUseCase(
        repository: BaseRepository<TransactionDetailsRequest, BaseResponse<TransactionDetailsResponse>>,
    ): TransactionDetailsUseCase {
        return TransactionDetailsUseCase(repository)
    }

    //
    @Provides
    fun provideModifyBankAccountCaller(apiService: ModifyBankApiService): ModifyBankAccountCaller {
        return ModifyBankAccountCaller(apiService)
    }

    @Provides
    fun provideModifyBankAccountRepository(caller: ModifyBankAccountCaller): BaseRepository<ModifyUserBankRequest, BaseResponse<Unit>> {
        return getRepository(caller)
    }

    @Provides
    fun provideModifyBankAccountUseCase(
        repository: BaseRepository<ModifyUserBankRequest, BaseResponse<Unit>>,
    ): ModifyBankAccountUseCase {
        return ModifyBankAccountUseCase(repository)
    }

    //
    @Provides
    fun provideUpdateUserInfoCaller(apiService: UpdateUserInfoApiService): UpdateUserInfoCaller {
        return UpdateUserInfoCaller(apiService)
    }

    @Provides
    fun provideUpdateUserInfoRepository(caller: UpdateUserInfoCaller): BaseRepository<UpdateUserInfoRequest, BaseResponse<UpdateUserInfoResponse>> {
        return getRepository(caller)
    }

    @Provides
    fun provideUpdateUserInfoUseCase(
        repository: BaseRepository<UpdateUserInfoRequest, BaseResponse<UpdateUserInfoResponse>>,
    ): UpdateUserInfoUseCase {
        return UpdateUserInfoUseCase(repository)
    }


    //
    @Provides
    fun provideUserInfoCheckCaller(apiService: UserInfoCheckApiService): UserInfoCheckCaller {
        return UserInfoCheckCaller(apiService)
    }

    @Provides
    fun provideUserInfoCheckRepository(caller: UserInfoCheckCaller): BaseRepository<UserInfoCheckRequest, BaseResponse<UserInfoCheckResponse>> {
        return getRepository(caller)
    }

    @Provides
    fun provideUserInfoCheckUseCase(
        repository: BaseRepository<UserInfoCheckRequest, BaseResponse<UserInfoCheckResponse>>,
    ): UserInfoCheckUseCase {
        return UserInfoCheckUseCase(repository)
    }


    //
    @Provides
    fun provideGetUserBasicInfoCaller(apiService: GetUserBasicInfoApiService): GetUserBasicInfoCaller {
        return GetUserBasicInfoCaller(apiService)
    }

    @Provides
    fun provideGetUserBasicInfoRepository(
        caller:
        GetUserBasicInfoCaller,
    ): BaseRepository<Unit, BaseResponse<UserBasicInfoResponse>> {
        return getRepository(caller)
    }

    @Provides
    fun provideUserBasicInfoCheckUseCase(
        repository: BaseRepository<Unit, BaseResponse<UserBasicInfoResponse>>,
    ): GetUserBasicInfoUseCase {
        return GetUserBasicInfoUseCase(repository)
    }


    //
    @Provides
    fun provideLogoutCaller(apiService: LogoutApiService): LogoutCaller {
        return LogoutCaller(apiService)
    }

    @Provides
    fun provideLogoutRepository(
        caller:
        LogoutCaller,
    ): BaseRepository<LogoutRequest, BaseResponse<LogoutResponse>> {
        return getRepository(caller)
    }

    @Provides
    fun provideLogoutUseCase(
        repository: BaseRepository<LogoutRequest, BaseResponse<LogoutResponse>>,
    ): LogoutUseCase {
        return LogoutUseCase(repository)
    }


    //
    @Provides
    fun provideChatListCaller(apiService: ChatListApiService): ChatListCaller {
        return ChatListCaller(apiService)
    }

    @Provides
    fun provideChatListRepository(
        caller:
        ChatListCaller,
    ): BaseRepository<ChatListRequest, BaseResponse<ChatListResponse>> {
        return getRepository(caller)
    }

    @Provides
    fun provideChatListUseCase(
        repository: BaseRepository<ChatListRequest, BaseResponse<ChatListResponse>>,
    ): ChatListUseCase {
        return ChatListUseCase(repository)
    }

    //
    @Provides
    fun provideUpdateUserStatusCaller(apiService: UpdateUserStatusApiService): UpdateUserStatusCaller {
        return UpdateUserStatusCaller(apiService)
    }

    @Provides
    fun provideUpdateUserStatusRepository(
        caller:
        UpdateUserStatusCaller,
    ): BaseRepository<UpdateUserStatusRequest, BaseResponse<UpdateUserStatusResponse>> {
        return getRepository(caller)
    }

    @Provides
    fun provideUpdateUserStatusUseCase(
        repository: BaseRepository<UpdateUserStatusRequest, BaseResponse<UpdateUserStatusResponse>>,
    ): UpdateUserStatusUseCase {
        return UpdateUserStatusUseCase(repository)
    }


    //
    @Provides
    fun provideSendChatMessageCaller(apiService: SendChatMessageApiService): SendChatMessageCaller {
        return SendChatMessageCaller(apiService)
    }

    @Provides
    fun provideSendChatMessageRepository(
        caller:
        SendChatMessageCaller,
    ): BaseRepository<SendChatMessageRequest, BaseResponse<SendChatMessageResponse>> {
        return getRepository(caller)
    }

    @Provides
    fun provideSendChatMessageUseCase(
        repository: BaseRepository<SendChatMessageRequest, BaseResponse<SendChatMessageResponse>>,
    ): SendChatMessageUseCase {
        return SendChatMessageUseCase(repository)
    }


    //
    @Provides
    fun provideUpdateRecentlyViewedCaller(apiService: RecentlyViewedUpdateApiService): RecentlyViewedUpdateCaller {
        return RecentlyViewedUpdateCaller(apiService)
    }

    @Provides
    fun provideUpdateRecentlyViewedRepository(
        caller:
        RecentlyViewedUpdateCaller,
    ): BaseRepository<RecentlyViewedRequest, BaseResponse<Unit>> {
        return getRepository(caller)
    }

    @Provides
    fun provideRecentlyViewedUpdateUseCase(
        repository: BaseRepository<RecentlyViewedRequest, BaseResponse<Unit>>,
    ): RecentlyViewedUpdateUseCase {
        return RecentlyViewedUpdateUseCase(repository)
    }

    //
    @Provides
    fun provideGetQuestionnaireCaller(apiService: GetQuestionnaireApiService): GetQuestionnaireCaller {
        return GetQuestionnaireCaller(apiService)
    }

    @Provides
    fun provideGetQuestionnaireRepository(
        caller:
        GetQuestionnaireCaller,
    ): BaseRepository<GetQuestionnaireRequest, BaseResponse<QuestionnaireResponse>> {
        return getRepository(caller)
    }

    @Provides
    fun provideGetQuestionnaireUseCase(
        repository: BaseRepository<GetQuestionnaireRequest, BaseResponse<QuestionnaireResponse>>,
    ): GetQuestionnaireUseCase {
        return GetQuestionnaireUseCase(repository)
    }

    //
    @Provides
    fun provideAgreementTermCaller(apiService: GetAgreementApiService): GetAgreementCaller {
        return GetAgreementCaller(apiService)
    }

    @Provides
    fun provideAgreementTermRepository(
        caller:
        GetAgreementCaller,
    ): BaseRepository<AgreementRequest, BaseResponse<AgreementResponse>> {
        return getRepository(caller)
    }

    @Provides
    fun provideAgreementTermUseCase(
        repository: BaseRepository<AgreementRequest, BaseResponse<AgreementResponse>>,
    ): GetAgreementUseCase {
        return GetAgreementUseCase(repository)
    }


    //
    @Provides
    fun provideGetUserBankListCaller(apiService: GetUserBankListApiService): GetUserBankListCaller {
        return GetUserBankListCaller(apiService)
    }

    @Provides
    fun provideGetUserBankListRepository(
        caller:
        GetUserBankListCaller,
    ): BaseRepository<Unit, BaseResponse<UserBankListResponse>> {
        return getRepository(caller)
    }

    @Provides
    fun provideGetUserBankListUseCase(
        repository: BaseRepository<Unit, BaseResponse<UserBankListResponse>>,
    ): GetUserBankListUseCase {
        return GetUserBankListUseCase(repository)
    }

    //
    @Provides
    fun provideUpdateQuestionnaireCaller(apiService: UpdateQuestionnaireApiService): UpdateQuestionnaireCaller {
        return UpdateQuestionnaireCaller(apiService)
    }

    @Provides
    fun provideUpdateQuestionnaireRepository(
        caller:
        UpdateQuestionnaireCaller,
    ): BaseRepository<QuestionnaireUpdateRequest, BaseResponse<QuestionnaireUpdateResponse>> {
        return getRepository(caller)
    }

    @Provides
    fun provideUpdateQuestionnaireUseCase(
        repository: BaseRepository<QuestionnaireUpdateRequest, BaseResponse<QuestionnaireUpdateResponse>>,
    ): UpdateQuestionnaireUseCase {
        return UpdateQuestionnaireUseCase(repository)
    }

    //
    @Provides
    fun provideUpdateAgreementCaller(apiService: UpdateAgreementApiService): UpdateAgreementCaller {
        return UpdateAgreementCaller(apiService)
    }

    @Provides
    fun provideUpdateAgreementRepository(
        caller:
        UpdateAgreementCaller,
    ): BaseRepository<UpdateAgreementRequest, BaseResponse<UpdateAgreementResponse>> {
        return getRepository(caller)
    }

    @Provides
    fun provideUpdateAgreementUseCase(
        repository: BaseRepository<UpdateAgreementRequest, BaseResponse<UpdateAgreementResponse>>,
    ): UpdateAgreementUseCase {
        return UpdateAgreementUseCase(repository)
    }

    //
    @Provides
    fun provideUploadOnboardingDocumentCaller(apiService: UploadDocumentOnboardingApiService): UploadDocumentOnboardingCaller {
        return UploadDocumentOnboardingCaller(apiService)
    }

    @Provides
    fun provideUploadOnboardingDocumentRepository(
        caller:
        UploadDocumentOnboardingCaller,
    ): BaseRepository<UploadDocumentOnboardingRequest, BaseResponse<UploadDocumentOnboardingResponse>> {
        return getRepository(caller)
    }

    @Provides
    fun provideUploadOnboardingDocumentUseCase(
        repository: BaseRepository<UploadDocumentOnboardingRequest, BaseResponse<UploadDocumentOnboardingResponse>>,
    ): UploadDocumentOnboardingUseCase {
        return UploadDocumentOnboardingUseCase(repository)
    }


    //
    @Provides
    fun provideUpdateCustomerProfileCaller(apiService: UpdateCustomerProfileApiService): UpdateCustomerProfileCaller {
        return UpdateCustomerProfileCaller(apiService)
    }

    @Provides
    fun provideUpdateCustomerProfileRepository(
        caller:
        UpdateCustomerProfileCaller,
    ): BaseRepository<CustomerProfileRequest, BaseResponse<Unit>> {
        return getRepository(caller)
    }

    @Provides
    fun provideUpdateCustomerProfileUseCase(
        repository: BaseRepository<CustomerProfileRequest, BaseResponse<Unit>>,
    ): UpdateCustomerProfileUseCase {
        return UpdateCustomerProfileUseCase(repository)
    }


    //
    @Provides
    fun provideUpdateEmploymentProfileCaller(apiService: UpdateUserEmploymentApiService): UpdateUserEmploymentCaller {
        return UpdateUserEmploymentCaller(apiService)
    }

    @Provides
    fun provideUpdateEmploymentProfileRepository(
        caller:
        UpdateUserEmploymentCaller,
    ): BaseRepository<UserEmploymentRequest, BaseResponse<Unit>> {
        return getRepository(caller)
    }

    @Provides
    fun provideUpdateEmploymentProfileUseCase(
        repository: BaseRepository<UserEmploymentRequest, BaseResponse<Unit>>,
    ): UpdateUserEmploymentUseCase {
        return UpdateUserEmploymentUseCase(repository)
    }


    //
    @Provides
    fun provideUpdateIdentityCardListCaller(apiService: UpdateIdentityCardListApiService): UpdateIdentityCardListCaller {
        return UpdateIdentityCardListCaller(apiService)
    }

    @Provides
    fun provideUpdateIdentityCardListRepository(
        caller:
        UpdateIdentityCardListCaller,
    ): BaseRepository<UpdateIdentityCardListRequest, BaseResponse<UpdateIdentityCardListResponse>> {
        return getRepository(caller)
    }

    @Provides
    fun provideUpdateIdentityCardListUseCase(
        repository: BaseRepository<UpdateIdentityCardListRequest, BaseResponse<UpdateIdentityCardListResponse>>,
    ): UpdateIdentityCardListUseCase {
        return UpdateIdentityCardListUseCase(repository)
    }

    //
    @Provides
    fun provideGetIdentityCaller(apiService: GetUserIdentityApiService): GetUserIdentityCaller {
        return GetUserIdentityCaller(apiService)
    }

    @Provides
    fun provideGetIdentityRepository(
        caller:
        GetUserIdentityCaller,
    ): BaseRepository<GetUserIdentityRequest, BaseResponse<GetUserIdentityResponse>> {
        return getRepository(caller)
    }

    @Provides
    fun provideGetIdentityUseCase(
        repository: BaseRepository<GetUserIdentityRequest, BaseResponse<GetUserIdentityResponse>>,
    ): GetUserIdentityUseCase {
        return GetUserIdentityUseCase(repository)
    }

    //
    @Provides
    fun provideGetUserDocumentCaller(apiService: GetUserDocumentApiService): GetUserDocumentCaller {
        return GetUserDocumentCaller(apiService)
    }

    @Provides
    fun provideGetUserDocumentRepository(
        caller:
        GetUserDocumentCaller,
    ): BaseRepository<GetUserDocumentRequest, BaseResponse<GetUserDocumentResponse>> {
        return getRepository(caller)
    }

    @Provides
    fun provideGetUserDocumentUseCase(
        repository: BaseRepository<GetUserDocumentRequest, BaseResponse<GetUserDocumentResponse>>,
    ): GetUserDocumentUseCase {
        return GetUserDocumentUseCase(repository)
    }


    //
    @Provides
    fun provideGetCRSDeclarationCaller(apiService: GetCRSDeclarationApiService): GetCRSDeclarationCaller {
        return GetCRSDeclarationCaller(apiService)
    }

    @Provides
    fun provideGetCRSDeclarationRepository(
        caller:
        GetCRSDeclarationCaller,
    ): BaseRepository<GetCRSDeclarationListRequest, BaseResponse<GetCRSDeclarationListResponse>> {
        return getRepository(caller)
    }

    @Provides
    fun provideGetCRSDeclarationUseCase(
        repository: BaseRepository<GetCRSDeclarationListRequest, BaseResponse<GetCRSDeclarationListResponse>>,
    ): GetCRSDeclarationUseCase {
        return GetCRSDeclarationUseCase(repository)
    }

    //
    @Provides
    fun provideUpdateCRSDeclarationCaller(apiService: UpdateCRSDeclarationApiService): UpdateCRSDeclarationCaller {
        return UpdateCRSDeclarationCaller(apiService)
    }

    @Provides
    fun provideUpdateCRSDeclarationRepository(
        caller:
        UpdateCRSDeclarationCaller,
    ): BaseRepository<UpdateCRSDeclarationListRequest, BaseResponse<UpdateCRSResponse>> {
        return getRepository(caller)
    }

    @Provides
    fun provideUpdateCRSDeclarationUseCase(
        repository: BaseRepository<UpdateCRSDeclarationListRequest, BaseResponse<UpdateCRSResponse>>,
    ): UpdateCRSDeclarationUseCase {
        return UpdateCRSDeclarationUseCase(repository)
    }


    //
    @Provides
    fun provideCheckOnboardingUserCaller(apiService: OnboardingUserCheckApiService): OnboardingUserCheckCaller {
        return OnboardingUserCheckCaller(apiService)
    }

    @Provides
    fun provideCheckOnboardingUserRepository(
        caller:
        OnboardingUserCheckCaller,
    ): BaseRepository<OnboardingUserCheckRequest, BaseResponse<OnboardingUserCheckResponse>> {
        return getRepository(caller)
    }

    @Provides
    fun provideCheckOnboardingUserUseCase(
        repository: BaseRepository<OnboardingUserCheckRequest, BaseResponse<OnboardingUserCheckResponse>>,
    ): OnboardingUserCheckUseCase {
        return OnboardingUserCheckUseCase(repository)
    }


    //
    @Provides
    fun provideRequestDocSignCaller(apiService: RequestDocSignApiService): RequestDocSignCaller {
        return RequestDocSignCaller(apiService)
    }

    @Provides
    fun provideRequestDocSignRepository(
        caller:
        RequestDocSignCaller,
    ): BaseRepository<Unit, BaseResponse<RequestDocSignResponse>> {
        return getRepository(caller)
    }

    @Provides
    fun provideRequestDocSignUseCase(
        repository: BaseRepository<Unit, BaseResponse<RequestDocSignResponse>>,
    ): RequestDocSignUseCase {
        return RequestDocSignUseCase(repository)
    }


    //
    @Provides
    fun provideGetBannerListCaller(apiService: GetBannerListApiService): GetBannerListCaller {
        return GetBannerListCaller(apiService)
    }

    @Provides
    fun provideGetBannerListRepository(
        caller:
        GetBannerListCaller,
    ): BaseRepository<Unit, BaseResponse<BannerListResponse>> {
        return getRepository(caller)
    }

    @Provides
    fun provideGetBannerListUseCase(
        repository: BaseRepository<Unit, BaseResponse<BannerListResponse>>,
    ): GetBannerListUseCase {
        return GetBannerListUseCase(repository)
    }


    //
    @Provides
    fun provideGetCommonConfigCaller(apiService: GetCommonConfigApiService): GetCommonConfigCaller {
        return GetCommonConfigCaller(apiService)
    }

    @Provides
    fun provideGetCommonConfigRepository(
        caller:
        GetCommonConfigCaller,
    ): BaseRepository<Unit, BaseResponse<CommonConfigResponse>> {
        return getRepository(caller)
    }

    @Provides
    fun provideGetCommonConfigUseCase(
        repository: BaseRepository<Unit, BaseResponse<CommonConfigResponse>>,
    ): GetCommonConfigUseCase {
        return GetCommonConfigUseCase(repository)
    }
}