package com.siriustech.merit.apilayer.service.authentication.userinfo

import com.core.network.base.RetrofitAPICaller
import com.core.network.model.BaseResponse
import retrofit2.Response
import javax.inject.Inject

class GetUserInfoCaller @Inject constructor(
    private val apiService: GetUserInfoApiService
) : RetrofitAPICaller<Unit, BaseResponse<UserInfoResponse>>() {
    override fun call(reqParam: Unit?): Response<BaseResponse<UserInfoResponse>> {
        return apiService.request().execute()
    }
}