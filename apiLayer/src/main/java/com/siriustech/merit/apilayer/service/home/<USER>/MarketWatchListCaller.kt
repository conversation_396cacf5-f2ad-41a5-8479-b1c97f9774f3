package com.siriustech.merit.apilayer.service.home.watchlist

import com.core.network.base.RetrofitAPICaller
import com.core.network.model.BaseResponse
import javax.inject.Inject
import retrofit2.Response

/**
 * Created by <PERSON><PERSON><PERSON><PERSON>
 */
class MarketWatchListCaller @Inject constructor(
    private val watchListApiService: MarketWatchListApiService,
) : RetrofitAPICaller<MarketWatchListRequest, BaseResponse<MarketWatchListResponse>>() {
    override fun call(reqParam: MarketWatchListRequest?): Response<BaseResponse<MarketWatchListResponse>> {
        return watchListApiService.request(reqParam).execute()
    }
}