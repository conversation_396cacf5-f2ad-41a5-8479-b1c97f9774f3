package com.siriustech.merit.apilayer.service.authentication.checkonboardingstep

import com.core.network.model.BaseResponse
import kotlinx.serialization.Serializable
import retrofit2.Call
import retrofit2.http.Body
import retrofit2.http.POST

/**
 * Created by <PERSON><PERSON><PERSON>
 */

@Serializable
class CheckOnboardingStepRequest

@Serializable
data class CheckOnboardingStep(
    val code: String? = null,
    val desc: String? = null,
    val status: String,
)

@Serializable
data class CheckOnboardingStepResponse(
    val list: List<CheckOnboardingStep>,
)
interface CheckOnboardingStepApiService {

    @POST("user/register/steps")
     fun request(@Body request: CheckOnboardingStepRequest?): Call<BaseResponse<CheckOnboardingStepResponse>>
}