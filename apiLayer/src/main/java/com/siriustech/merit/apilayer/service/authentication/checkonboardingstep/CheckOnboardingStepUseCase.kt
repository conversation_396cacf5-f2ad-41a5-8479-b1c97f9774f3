package com.siriustech.merit.apilayer.service.authentication.checkonboardingstep

import com.core.network.base.AppUseCaseFlow
import com.core.network.base.Repository
import com.core.network.model.BaseResponse
import dagger.hilt.android.scopes.ViewModelScoped
import javax.inject.Inject

// FLOW USE_CASE
@ViewModelScoped
class CheckOnboardingStepUseCase @Inject constructor(
    repository: Repository<CheckOnboardingStepRequest, BaseResponse<CheckOnboardingStepResponse>>,
) : AppUseCaseFlow<CheckOnboardingStepRequest, CheckOnboardingStepResponse>(repository)