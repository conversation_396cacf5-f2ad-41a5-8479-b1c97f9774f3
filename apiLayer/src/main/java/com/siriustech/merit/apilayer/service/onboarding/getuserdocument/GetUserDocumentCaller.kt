package com.siriustech.merit.apilayer.service.onboarding.getuserdocument

import com.core.network.base.RetrofitAPICaller
import com.core.network.model.BaseResponse
import javax.inject.Inject
import retrofit2.Response

/**
 * Created by <PERSON><PERSON> Htet
 */

class GetUserDocumentCaller @Inject constructor(private val apiService: GetUserDocumentApiService) :
    RetrofitAPICaller<GetUserDocumentRequest, BaseResponse<GetUserDocumentResponse>>() {
    override fun call(reqParam: GetUserDocumentRequest?): Response<BaseResponse<GetUserDocumentResponse>> {
        return apiService.getUserDocument(reqParam).execute()
    }
}