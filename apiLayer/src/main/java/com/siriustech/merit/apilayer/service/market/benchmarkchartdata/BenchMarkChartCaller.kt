package com.siriustech.merit.apilayer.service.market.benchmarkchartdata

import com.core.network.base.RetrofitAPICaller
import com.core.network.model.BaseResponse
import javax.inject.Inject
import retrofit2.Response

/**
 * Created by <PERSON><PERSON>t
 */
class BenchMarkChartCaller @Inject constructor(
    private val apiService: BenchMarkChartApiService
)  : RetrofitAPICaller<BenchMarkChartRequest,BaseResponse<BenchMarkChartResponse>>(){
    override fun call(reqParam: BenchMarkChartRequest?): Response<BaseResponse<BenchMarkChartResponse>> {
        return apiService.request(reqParam).execute()
    }
}
