package com.siriustech.merit.apilayer.service.user.userinfo

import com.core.network.base.RetrofitAPICaller
import com.core.network.model.BaseResponse
import javax.inject.Inject
import retrofit2.Response

/**
 * Created by <PERSON><PERSON> <PERSON><PERSON>t
 */
class GetUserBasicInfoCaller @Inject constructor(val apiService: GetUserBasicInfoApiService) : RetrofitAPICaller<Unit,BaseResponse<UserBasicInfoResponse>>() {
    override fun call(reqParam: Unit?): Response<BaseResponse<UserBasicInfoResponse>> {
        return apiService.request().execute()
    }
}