package com.siriustech.merit.apilayer.service.onboarding.usercheck

import com.core.network.model.BaseResponse
import kotlinx.serialization.Serializable
import retrofit2.Call
import retrofit2.http.Body
import retrofit2.http.POST

/**
 * Created by <PERSON><PERSON><PERSON>
 */


@Serializable
data class OnboardingUserCheckRequest(
    val email : String,
    val mobile : String,
    val mobileRegion : String
)

@Serializable
class OnboardingUserCheckResponse {

}

interface OnboardingUserCheckApiService {

    @POST("user/onboarding/check")
     fun request(@Body request: OnboardingUserCheckRequest?): Call<BaseResponse<OnboardingUserCheckResponse>>
}