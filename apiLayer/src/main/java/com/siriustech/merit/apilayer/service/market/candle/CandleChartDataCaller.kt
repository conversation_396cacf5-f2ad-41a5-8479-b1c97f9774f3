package com.siriustech.merit.apilayer.service.market.candle

import com.core.network.base.RetrofitAPICaller
import com.core.network.model.BaseResponse
import javax.inject.Inject
import retrofit2.Response

/**
 * Created by <PERSON><PERSON><PERSON><PERSON>
 */
class CandleChartDataCaller @Inject constructor(
    private val apiService: CandleChartApiService
) : RetrofitAPICaller<CandleChartDataRequest,BaseResponse<CandleChartDataResponse>>(){
    override fun call(reqParam: CandleChartDataRequest?): Response<BaseResponse<CandleChartDataResponse>> {
        return apiService.request(reqParam).execute()
    }
}