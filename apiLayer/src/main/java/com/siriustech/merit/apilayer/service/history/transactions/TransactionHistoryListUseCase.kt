package com.siriustech.merit.apilayer.service.history.transactions

import com.core.network.base.AppUseCaseFlow
import com.core.network.base.Repository
import com.core.network.model.BaseResponse
import dagger.hilt.android.scopes.ViewModelScoped
import javax.inject.Inject

// FLOW USE_CASE
@ViewModelScoped
class TransactionHistoryListUseCase @Inject constructor(
    repository: Repository<TransactionHistoryRequest, BaseResponse<TransactionListResponse>>,
) : AppUseCaseFlow<TransactionHistoryRequest, TransactionListResponse>(repository)


