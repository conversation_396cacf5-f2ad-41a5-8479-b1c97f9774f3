package com.siriustech.merit.apilayer.service.onboarding.getuseridentity

import com.core.network.model.BaseResponse
import kotlinx.serialization.Serializable
import retrofit2.Call
import retrofit2.http.Body
import retrofit2.http.POST

@Serializable
class GetUserIdentityRequest()

@Serializable
data class GetUserIdentityResponse(
    val livenessApprovalStatus: String,
    val customerName: String,
    val similarityScore: Double,
    val region: String,
    val birthDay: Long,
    val idExpirationDate: Long,
    val idImage: String,
    val cardList: List<IdentityCardResponse> = emptyList(),
)

@Serializable
data class IdentityCardResponse(
    val cardNumber: String,
    val cardImage: String,
    val cardType: String,
)

interface GetUserIdentityApiService {
    @POST("user/liveness")
    fun getUserIdentityData(@Body params: GetUserIdentityRequest?): Call<BaseResponse<GetUserIdentityResponse>>
}
