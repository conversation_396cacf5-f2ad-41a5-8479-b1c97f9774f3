package com.siriustech.merit.apilayer.service.authentication.authresetverify

import com.core.network.base.RetrofitAPICaller
import com.core.network.model.BaseResponse
import javax.inject.Inject
import retrofit2.Response

/**
 * Created by <PERSON><PERSON> <PERSON>tet
 */
class AuthResetVerifyCaller @Inject constructor(private val apiService: AuthResetVerifyApiService) : RetrofitAPICaller<AuthResetVerifyRequest,BaseResponse<AuthResetVerifyResponse>>() {
    override fun call(reqParam: AuthResetVerifyRequest?): Response<BaseResponse<AuthResetVerifyResponse>> {
        return apiService.request(reqParam).execute()
    }
}