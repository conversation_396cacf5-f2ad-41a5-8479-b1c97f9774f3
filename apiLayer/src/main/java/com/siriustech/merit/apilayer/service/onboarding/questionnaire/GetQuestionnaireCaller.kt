package com.siriustech.merit.apilayer.service.onboarding.questionnaire

import com.core.network.base.RetrofitAPICaller
import com.core.network.model.BaseResponse
import javax.inject.Inject
import retrofit2.Response

/**
 * Created by <PERSON><PERSON><PERSON>
 */
class GetQuestionnaireCaller @Inject constructor(private val apiService: GetQuestionnaireApiService) : RetrofitAPICaller<GetQuestionnaireRequest,BaseResponse<QuestionnaireResponse>>() {
    override fun call(reqParam: GetQuestionnaireRequest?): Response<BaseResponse<QuestionnaireResponse>> {
        return apiService.onGetQuestionnaire(reqParam).execute()
    }
}