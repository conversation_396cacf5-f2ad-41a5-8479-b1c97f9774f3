package com.siriustech.merit.apilayer.service.onboarding.agreementterm

import com.core.network.model.BaseResponse
import kotlinx.serialization.Serializable
import retrofit2.Call
import retrofit2.http.Body
import retrofit2.http.POST

/**
 * Created by <PERSON><PERSON><PERSON><PERSON>
 */

@Serializable
data class AgreementRequest(
    val agreementCodeList: List<String>,
)

@Serializable
data class AgreementResponse(
    val list: List<AgreementData>? = emptyList(),
)

@Serializable
data class AgreementData(
    val agreementCode: String,
    val isAgree: String,
    val isRequired: <PERSON>olean,
)

enum class AgreementType(val type: String) {
    VULNERABLE_CLIENT_DECLARATION("VULNERABLE_CLIENT_DECLARATION")
}

interface GetAgreementApiService {
    @POST("user/agreement")
     fun getAgreement(@Body request: AgreementRequest?) : Call<BaseResponse<AgreementResponse>>
}

