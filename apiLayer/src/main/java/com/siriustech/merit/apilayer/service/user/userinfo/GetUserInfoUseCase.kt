package com.siriustech.merit.apilayer.service.user.userinfo

import com.core.network.base.AppUseCaseFlow
import com.core.network.base.Repository
import com.core.network.model.BaseResponse
import dagger.hilt.android.scopes.ViewModelScoped
import javax.inject.Inject

/**
 * Created by <PERSON><PERSON> <PERSON><PERSON>t
 */
@ViewModelScoped
class GetUserBasicInfoUseCase @Inject constructor(
    repository: Repository<Unit, BaseResponse<UserBasicInfoResponse>>
) : AppUseCaseFlow<Unit, UserBasicInfoResponse>(repository){
}
