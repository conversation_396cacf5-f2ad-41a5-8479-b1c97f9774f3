package com.siriustech.merit.apilayer.service.authentication.common.ocr

import com.core.network.base.RetrofitAPICaller
import com.core.network.model.BaseResponse
import retrofit2.Response
import javax.inject.Inject
import okhttp3.RequestBody

class CheckOCRCaller @Inject constructor(
    private val apiService: OCRApiService
) : RetrofitAPICaller<OCRRequest, BaseResponse<OCRResponse>>() {
    override fun call(reqParam: OCRRequest?): Response<BaseResponse<OCRResponse>> {
        return apiService.request(reqParam).execute()
    }
}