package com.siriustech.merit.apilayer.service.history.list

import com.core.network.base.AppUseCaseFlow
import com.core.network.base.Repository
import com.core.network.model.BaseResponse
import dagger.hilt.android.scopes.ViewModelScoped
import javax.inject.Inject

// FLOW USE_CASE
@ViewModelScoped
class GetHistoryListUseCase @Inject constructor(
    repository: Repository<GetHistoryRequest, BaseResponse<HistoryListResponse>>,
) : AppUseCaseFlow<GetHistoryRequest, HistoryListResponse>(repository)


