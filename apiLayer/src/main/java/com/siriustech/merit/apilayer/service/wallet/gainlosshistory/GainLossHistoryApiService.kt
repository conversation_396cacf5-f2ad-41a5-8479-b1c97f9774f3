package com.siriustech.merit.apilayer.service.wallet.gainlosshistory

import com.core.network.model.BaseResponse
import kotlinx.serialization.Serializable
import retrofit2.Call
import retrofit2.http.Body
import retrofit2.http.POST

/**
 * Created by <PERSON><PERSON><PERSON>t
 */

@Serializable
data class GainLossHistoryRequest(
    val period: String,
    val summaryBy: String,
)

@Serializable
data class GainLossHistoryResponse(
    val summaryList: List<GainLossHistorySummaryListResponse>? = null,
)

@Serializable
data class GainLossHistorySummaryListResponse(
    val summaryName: String? = null,
    val dataList: List<GainLossHistoryDataListResponse>? = null,
)

@Serializable
data class GainLossHistoryDataListResponse(
    val d: String? = null,
    val b: String? = null,
    val r: String? = null,
    val rp: String? = null,
)

interface GainLossHistoryApiService {

    @POST("wallet/gainLoss/history")
    fun request(@Body param : GainLossHistoryRequest?) : Call<BaseResponse<GainLossHistoryResponse>>
}