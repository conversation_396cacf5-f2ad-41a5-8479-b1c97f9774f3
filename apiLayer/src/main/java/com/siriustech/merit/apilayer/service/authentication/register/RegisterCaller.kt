package com.siriustech.merit.apilayer.service.authentication.register

import com.core.network.base.RetrofitAPICaller
import com.core.network.model.BaseResponse
import retrofit2.Response
import javax.inject.Inject

class RegisterCaller @Inject constructor(
    private val apiService: RegisterApiService
) : RetrofitAPICaller<RegisterRequest, BaseResponse<RegisterResponse>>() {
    override fun call(reqParam: RegisterRequest?): Response<BaseResponse<RegisterResponse>> {
        return apiService.request(reqParam).execute()
    }
}