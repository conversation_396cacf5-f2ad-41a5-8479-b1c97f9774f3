package com.siriustech.merit.apilayer.service.wallet.deposit

import com.core.network.base.AppUseCaseFlow
import com.core.network.base.Repository
import com.core.network.model.BaseResponse
import dagger.hilt.android.scopes.ViewModelScoped
import javax.inject.Inject

/**
 * Created by <PERSON><PERSON><PERSON>t
 */

@ViewModelScoped
class DepositUseCase @Inject constructor(
    repository: Repository<DepositRequest, BaseResponse<DepositResponse>>
) : AppUseCaseFlow<DepositRequest, DepositResponse>(repository){
}