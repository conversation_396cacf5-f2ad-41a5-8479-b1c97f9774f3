package com.siriustech.merit.apilayer.service.wealthplan.details

import com.core.network.base.AppUseCaseFlow
import com.core.network.base.Repository
import com.core.network.model.BaseResponse
import dagger.hilt.android.scopes.ViewModelScoped
import javax.inject.Inject

/**
 * Created by <PERSON><PERSON>
 */


@ViewModelScoped
class WealthPlanDetailsUseCase @Inject constructor(
    repository: Repository<WealthPlanDetailsRequest, BaseResponse<WealthPlanDetailsResponse>>
) : AppUseCaseFlow<WealthPlanDetailsRequest, WealthPlanDetailsResponse>(repository){
}