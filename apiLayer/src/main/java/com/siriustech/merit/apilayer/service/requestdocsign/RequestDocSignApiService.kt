package com.siriustech.merit.apilayer.service.requestdocsign

import com.core.network.model.BaseResponse
import kotlinx.serialization.Serializable
import retrofit2.Call
import retrofit2.http.POST

/**
 * Created by <PERSON><PERSON><PERSON><PERSON>
 */


@Serializable
class RequestDocSignResponse {

}

interface RequestDocSignApiService {
    @POST("/user/reg-pdf/generate")
    fun updateStatus(): Call<BaseResponse<RequestDocSignResponse>>
}