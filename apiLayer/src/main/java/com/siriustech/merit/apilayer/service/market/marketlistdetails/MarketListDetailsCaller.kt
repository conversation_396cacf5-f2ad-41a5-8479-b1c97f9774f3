package com.siriustech.merit.apilayer.service.market.marketlistdetails

import com.core.network.base.RetrofitAPICaller
import com.core.network.model.BaseResponse
import com.siriustech.merit.apilayer.service.market.exchangecurrency.MarketCurrencyApiService
import com.siriustech.merit.apilayer.service.market.exchangecurrency.MarketCurrencyListResponse
import com.siriustech.merit.apilayer.service.market.exchangecurrency.MarketCurrencyRequest
import javax.inject.Inject
import retrofit2.Response

/**
 * Created by <PERSON><PERSON>
 */
class MarketListDetailsCaller @Inject constructor(
    private val apiService: MarketListDetailsApiService
)  : RetrofitAPICaller<MarketInstrumentListRequest, BaseResponse<MarketInstrumentListResponse>>(){
    override fun call(reqParam: MarketInstrumentListRequest?): Response<BaseResponse<MarketInstrumentListResponse>> {
        return apiService.request(reqParam).execute()
    }
}
