package com.siriustech.merit.apilayer.service.onboarding.updatequestionnaire

import com.core.network.model.BaseResponse
import kotlinx.serialization.Serializable
import retrofit2.Call
import retrofit2.http.Body
import retrofit2.http.PUT

/**
 * Created by <PERSON><PERSON><PERSON><PERSON>
 */
@Serializable
data class QuestionnaireAnswer(
    val id: Int,
    val selected: <PERSON>olean,
    val value: String,
)

@Serializable
data class QuestionnaireQuestion(
    val id: Int,
    val questionType: String,
    val answerList: List<QuestionnaireAnswer>,
)

@Serializable
data class QuestionnaireUpdateRequest(
    val id: Int,
    val isDryRun: <PERSON><PERSON><PERSON>,
    val questionList: List<QuestionnaireQuestion>,
)

@Serializable
data class QuestionnaireUpdateResponse(
    val score: Int,
    val riskLevel: String,
)

interface UpdateQuestionnaireApiService {

    @PUT("user/questionnaire")
    fun updateQuestionnaire(@Body request: QuestionnaireUpdateRequest?): Call<BaseResponse<QuestionnaireUpdateResponse>>
}