package com.siriustech.merit.apilayer.service.user.recentlyview

import com.core.network.base.AppUseCaseFlow
import com.core.network.base.Repository
import com.core.network.model.BaseResponse
import dagger.hilt.android.scopes.ViewModelScoped
import javax.inject.Inject

/**
 * Created by <PERSON><PERSON>
 */
@ViewModelScoped
class RecentlyViewedUpdateUseCase @Inject constructor(
    repository: Repository<RecentlyViewedRequest, BaseResponse<Unit>>
) : AppUseCaseFlow<RecentlyViewedRequest, Unit>(repository){
}
