package com.siriustech.merit.apilayer.service.history.transactions

import com.core.network.base.RetrofitAPICaller
import com.core.network.model.BaseResponse
import javax.inject.Inject
import retrofit2.Response

/**
 * Created by <PERSON><PERSON><PERSON><PERSON>
 */
class TransactionHistoryCaller @Inject constructor(private val transactionHistoryApiService: TransactionHistoryApiService) : RetrofitAPICaller<TransactionHistoryRequest,BaseResponse<TransactionListResponse>>() {
    override fun call(reqParam: TransactionHistoryRequest?): Response<BaseResponse<TransactionListResponse>> {
        return transactionHistoryApiService.request(reqParam).execute()
    }
}