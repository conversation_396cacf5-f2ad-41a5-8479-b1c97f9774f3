package com.siriustech.merit.apilayer.service.home.deletenotification

import com.core.network.model.BaseResponse
import kotlinx.serialization.Serializable
import retrofit2.Call
import retrofit2.http.Body
import retrofit2.http.POST

/**
 * Created by <PERSON><PERSON><PERSON><PERSON>
 */

@Serializable
data class DeleteNotificationRequest(
    val idList: List<Int> = emptyList(),
)

@Serializable
class DeleteNotificationResponse {}

interface DeleteNotificationApiService {
    @POST("user/notification/delete")
    fun request(@Body request: DeleteNotificationRequest?): Call<BaseResponse<DeleteNotificationResponse>>
}