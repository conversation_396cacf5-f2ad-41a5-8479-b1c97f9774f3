package com.siriustech.merit.apilayer.service.requestdocsign

import com.core.network.base.AppUseCaseFlow
import com.core.network.base.Repository
import com.core.network.model.BaseResponse
import dagger.hilt.android.scopes.ViewModelScoped
import javax.inject.Inject

/**
 * Created by Hein Htet
 */


// FLOW USE_CASE
@ViewModelScoped
class RequestDocSignUseCase @Inject constructor(
    repository: Repository<Unit, BaseResponse<RequestDocSignResponse>>,
) : AppUseCaseFlow<Unit, RequestDocSignResponse>(repository)


