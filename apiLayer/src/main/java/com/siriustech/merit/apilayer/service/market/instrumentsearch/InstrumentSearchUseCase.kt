package com.siriustech.merit.apilayer.service.market.instrumentsearch

import com.core.network.base.AppUseCaseFlow
import com.core.network.base.Repository
import com.core.network.model.BaseResponse
import com.siriustech.merit.apilayer.service.market.marketlistdetails.MarketInstrumentListResponse
import dagger.hilt.android.scopes.ViewModelScoped
import javax.inject.Inject

/**
 * Created by <PERSON><PERSON>
 */
@ViewModelScoped
class InstrumentSearchUseCase @Inject constructor(
    repository: Repository<InstrumentSearchRequest, BaseResponse<MarketInstrumentListResponse>>,
) : AppUseCaseFlow<InstrumentSearchRequest, MarketInstrumentListResponse>(repository)
