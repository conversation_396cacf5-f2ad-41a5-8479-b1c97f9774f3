package com.siriustech.merit.apilayer.service.home.notification

import com.core.network.base.AppUseCaseFlow
import com.core.network.base.Repository
import com.core.network.model.BaseResponse
import dagger.hilt.android.scopes.ViewModelScoped
import javax.inject.Inject

// FLOW USE_CASE
@ViewModelScoped
class NotificationUseCase @Inject constructor(
    repository: Repository<NotificationRequest, BaseResponse<NotificationListResponse>>,
) : AppUseCaseFlow<NotificationRequest, NotificationListResponse>(repository)


