package com.siriustech.merit.apilayer.service.home.modifynotification

import com.core.network.base.AppUseCaseFlow
import com.core.network.base.Repository
import com.core.network.model.BaseResponse
import com.siriustech.merit.apilayer.service.home.notification.NotificationListResponse
import dagger.hilt.android.scopes.ViewModelScoped
import javax.inject.Inject

// FLOW USE_CASE
@ViewModelScoped
class ModifyNotificationUseCase @Inject constructor(
    repository: Repository<ModifyNotificationRequest, BaseResponse<ModifyNotificationResponse>>,
) : AppUseCaseFlow<ModifyNotificationRequest, ModifyNotificationResponse>(repository)


