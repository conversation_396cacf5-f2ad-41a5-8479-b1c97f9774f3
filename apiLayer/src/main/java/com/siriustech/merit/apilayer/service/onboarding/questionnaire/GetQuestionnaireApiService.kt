package com.siriustech.merit.apilayer.service.onboarding.questionnaire

import com.core.network.model.BaseResponse
import kotlinx.serialization.Serializable
import retrofit2.Call
import retrofit2.http.Body
import retrofit2.http.POST

/**
 * Created by <PERSON><PERSON> <PERSON><PERSON><PERSON>
 */

@Serializable
data class GetQuestionnaireRequest(
    val questionnaireCode: String,
)

@Serializable
data class QuestionnaireResponse(
    val score: String? = null,
    val riskLevel: String? = null,
    val code: String? = null,
    val name: String ? = null,
    val id : Int,
    val questionList: List<QuestionnaireQuestionResponse>? = emptyList(),
)

@Serializable
data class QuestionnaireQuestionResponse(
    val content: String? = null,
    var header:String? = null, // local mapping
    val id: Int? = null,
    val sort: Int? = null,
    val sortTitle: String?,
    val questionType: String? = null,
    val answerType: String? = null,
    val subQuestionList: List<QuestionnaireQuestionResponse>? = emptyList(),
    val answerList: List<QuestionnaireQuestionResponse>? = emptyList(),
    val subAnswerList: List<QuestionnaireQuestionResponse>? = emptyList(),
    val selected: Boolean? = null,
    val value : String? = null,
)
interface GetQuestionnaireApiService {
    @POST("user/questionnaire")
    fun onGetQuestionnaire(@Body request: GetQuestionnaireRequest?): Call<BaseResponse<QuestionnaireResponse>>
}


enum class QuestionnaireCode( val code: String) {
    RISK_PROFILING_ASSESSMENT("RISK_PROFILING_ASSESSMENT"),
    DISCLOSURE_OF_ASSOCIATED_ACCOUNTS("DISCLOSURE_OF_ASSOCIATED_ACCOUNTS"),
    DISCLOSURE_OF_IDENTITY("DISCLOSURE_OF_IDENTITY"),
    CRS_DECLARATION("CRS_DECLARATION"),
    US_INDICIA_QUESTIONNAIRE("US_INDICIA_QUESTIONNAIRE"),
    FATCA_IDENTITY_DECLARATION("FATCA_IDENTITY_DECLARATION"),
    PROFESSIONAL_INVESTOR_ASSESSMENT("PROFESSIONAL_INVESTOR_ASSESSMENT"),
}

