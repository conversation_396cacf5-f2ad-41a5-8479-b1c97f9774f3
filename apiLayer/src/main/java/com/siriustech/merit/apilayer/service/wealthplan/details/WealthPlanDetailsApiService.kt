package com.siriustech.merit.apilayer.service.wealthplan.details

import com.core.network.model.BaseResponse
import com.siriustech.merit.apilayer.service.wallet.summary.AllocationResponse
import com.siriustech.merit.apilayer.service.wallet.summary.CategoryAssetListResponse
import com.siriustech.merit.apilayer.service.wallet.summary.RiskLevelBreakdownResponse
import com.siriustech.merit.apilayer.service.wallet.summary.WalletSummaryListResponse
import kotlinx.serialization.Serializable
import retrofit2.Call
import retrofit2.http.Body
import retrofit2.http.POST

/**
 * Created by Hein Htet
 */

@Serializable
data class WealthPlanDetailsRequest(
    val wealthPlanId: Int,
    val summaryBy: String,
)

@Serializable
data class WealthPlanDetailsResponse(
    val id: Int? = null,
    val totalBalance: String? = null,
    val unrealizedGl: String? = null,
    val unrealizedGlRate: String? = null,
    val currency: String? = null,
    val name: String? = null,
    val asOf: Long? = null,
    val initialDate: Long? = null,
    val initialAmount: String? = null,
    val mtdReturn: Double? = null,
    val ytdReturn: Double? = null,
    val itdReturn: Double? = null,
    val beta: String? = null,
    val sharpe: String? = null,
    val annualizedReturn: String? = null,
    val annualizedVolatility: String? = null,
    val description: String? = null,
    val logo: String? = null,
    val summaryList: List<WealthPlanDetailsListSummaryResponse>? = emptyList(),
    val returnList: List<ReturnListResponse>? = null,
    val benchmarkList: List<BenchMarkResponse>? = null,
)

@Serializable
data class BenchMarkResponse(
    val instrumentId: Int? = null,
    val name: String? = null,
)

@Serializable
data class ReturnListResponse(
    val name: String? = null,
    val returnValue: String? = null,
    val volatilityValue: String? = null,
)

@Serializable
data class WealthPlanDetailsListSummaryResponse(
    val summaryName: String? = null,
    val marketValue: String? = null,
    val unrealizedGl: String? = null,
    val unrealizedGlRate: String? = null,
    val currency: String? = null,
    val percentage: String? = null,
    val riskTag: String? = null,
    val costValue: String? = null,
    val assetList: List<CategoryAssetListResponse>? = null,
)


interface WealthPlanDetailsApiService {

    @POST("user/wealth-plan")
    fun request(@Body request: WealthPlanDetailsRequest?): Call<BaseResponse<WealthPlanDetailsResponse>>
}