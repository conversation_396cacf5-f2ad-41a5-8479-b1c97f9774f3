package com.siriustech.merit.apilayer.service.onboarding.getuseridentity

import com.core.network.base.RetrofitAPICaller
import com.core.network.model.BaseResponse
import javax.inject.Inject
import retrofit2.Response

/**
 * Created by <PERSON><PERSON> Htet
 */
class GetUserIdentityCaller @Inject constructor(private val apiService: GetUserIdentityApiService) :
    RetrofitAPICaller<GetUserIdentityRequest, BaseResponse<GetUserIdentityResponse>>() {
    override fun call(reqParam: GetUserIdentityRequest?): Response<BaseResponse<GetUserIdentityResponse>> {
        return apiService.getUserIdentityData(reqParam).execute()
    }
}