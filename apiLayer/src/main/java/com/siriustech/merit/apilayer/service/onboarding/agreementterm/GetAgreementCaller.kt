package com.siriustech.merit.apilayer.service.onboarding.agreementterm

import com.core.network.base.RetrofitAPICaller
import com.core.network.model.BaseResponse
import javax.inject.Inject
import retrofit2.Response

/**
 * Created by <PERSON><PERSON><PERSON>t
 */
class GetAgreementCaller @Inject constructor(private val getAgreementApiService: GetAgreementApiService) :
    RetrofitAPICaller<AgreementRequest, BaseResponse<AgreementResponse>>() {
    override fun call(reqParam: AgreementRequest?): Response<BaseResponse<AgreementResponse>> {
        return getAgreementApiService.getAgreement(reqParam).execute()
    }
}