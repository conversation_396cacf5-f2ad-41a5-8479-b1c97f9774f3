package com.siriustech.merit.apilayer.service.wealthplan.performance

import com.core.network.base.AppUseCaseFlow
import com.core.network.base.Repository
import com.core.network.model.BaseResponse
import com.siriustech.merit.apilayer.service.wealthplan.list.WealthPlanResponse
import dagger.hilt.android.scopes.ViewModelScoped
import javax.inject.Inject

/**
 * Created by <PERSON><PERSON>
 */

@ViewModelScoped
class WealthPlanPerformanceUseCase @Inject constructor(
    repository: Repository<WealthPlanPerformanceRequest, BaseResponse<WealthPlanPerformanceResponse>>,
) : AppUseCaseFlow<WealthPlanPerformanceRequest, WealthPlanPerformanceResponse>(repository)