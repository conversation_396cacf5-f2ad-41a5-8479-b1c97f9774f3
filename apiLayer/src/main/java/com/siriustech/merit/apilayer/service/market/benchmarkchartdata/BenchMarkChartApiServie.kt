package com.siriustech.merit.apilayer.service.market.benchmarkchartdata

import com.core.network.model.BaseResponse
import kotlinx.serialization.Serializable
import retrofit2.Call
import retrofit2.http.Body
import retrofit2.http.POST

/**
 * Created by <PERSON><PERSON><PERSON><PERSON>
 */


@Serializable
data class BenchMarkChartRequest(
    val instrumentIdList: List<Int>,
    val period: String,
)

@Serializable
data class BenchMarkChartResponse(
    val list: List<BenchMarkInstrumentResponse>,
)

@Serializable
data class BenchMarkInstrumentResponse(
    val instrumentId: Int? = null,
    val name : String = "",
    val dataList: List<BenchMarkChartItemResponse>? = emptyList(),
)

@Serializable
data class BenchMarkChartItemResponse(
    val d: String? = null,
    val v: String? = null,
    val rp: String? = null,
    val r: String? = null,
)

interface BenchMarkChartApiService {
    @POST("/market/data/benchmark")
    fun request(@Body request: BenchMarkChartRequest?): Call<BaseResponse<BenchMarkChartResponse>>
}
