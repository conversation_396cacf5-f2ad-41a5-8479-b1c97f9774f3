package com.siriustech.merit.apilayer.service.home.notification

import com.core.network.base.RetrofitAPICaller
import com.core.network.model.BaseResponse
import javax.inject.Inject
import retrofit2.Response

/**
 * Created by <PERSON><PERSON><PERSON>t
 */
class NotificationCaller @Inject constructor(private val service: NotificationApiService) :
    RetrofitAPICaller<NotificationRequest, BaseResponse<NotificationListResponse>>() {
    override fun call(reqParam: NotificationRequest?): Response<BaseResponse<NotificationListResponse>> {
        return service.request(reqParam).execute()
    }
}