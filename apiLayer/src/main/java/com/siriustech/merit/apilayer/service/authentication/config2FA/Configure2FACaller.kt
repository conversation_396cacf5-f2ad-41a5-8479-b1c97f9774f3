package com.siriustech.merit.apilayer.service.authentication.config2FA

import com.core.network.base.RetrofitAPICaller
import com.core.network.model.BaseResponse
import retrofit2.Response
import javax.inject.Inject

class Configure2FACaller @Inject constructor(
    private val apiService: Configure2FAApiService
) : RetrofitAPICaller<Configure2FARequest, BaseResponse<Unit>>() {
    override fun call(reqParam: Configure2FARequest?): Response<BaseResponse<Unit>> {
        return apiService.request(reqParam).execute()
    }
}