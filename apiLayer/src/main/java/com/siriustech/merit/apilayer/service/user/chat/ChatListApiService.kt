package com.siriustech.merit.apilayer.service.user.chat

import com.core.network.model.BaseResponse
import kotlinx.serialization.Serializable
import retrofit2.Call
import retrofit2.http.Body
import retrofit2.http.POST

/**
 * Created by <PERSON><PERSON> <PERSON><PERSON>t
 */

@Serializable
data class ChatListRequest(
    val referenceTime: Long,
    val direction: String,
    val limit: Int,
)

@Serializable
data class ChatListResponse(
    val messageList: List<ChatItemResponse>? = emptyList(),
)

@Serializable
data class ChatItemResponse(
    val senderRole: String? = null,
    val senderId: Int? = null,
    val sentTime: Long? = null,
    val senderName: String? = null,
    val message: String? = null,
    val fileList: List<ChatItemFileResponse>? = null,
)

@Serializable
data class ChatItemFileResponse(
    val fileType: String? = null,
    val fileName: String? = null,
    val fileKey: String? = null,
    val fileSize : Long? = null
)


interface ChatListApiService {

    @POST("user/chat/message/list")
    fun request(@Body request: ChatListRequest?): Call<BaseResponse<ChatListResponse>>

}