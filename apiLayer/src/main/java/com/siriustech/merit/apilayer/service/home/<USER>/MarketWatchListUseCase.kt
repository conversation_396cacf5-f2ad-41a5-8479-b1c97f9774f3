package com.siriustech.merit.apilayer.service.home.watchlist

import com.core.network.base.AppUseCaseFlow
import com.core.network.base.Repository
import com.core.network.model.BaseResponse
import dagger.hilt.android.scopes.ViewModelScoped
import javax.inject.Inject

// FLOW USE_CASE
@ViewModelScoped
class MarketWatchListUseCase @Inject constructor(
    repository: Repository<MarketWatchListRequest, BaseResponse<MarketWatchListResponse>>,
) : AppUseCaseFlow<MarketWatchListRequest, MarketWatchListResponse>(repository)


