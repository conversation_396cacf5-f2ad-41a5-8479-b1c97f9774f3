package com.siriustech.merit.apilayer.service.onboarding.getuserdocument

import com.core.network.model.BaseResponse
import kotlinx.serialization.Serializable
import retrofit2.Call
import retrofit2.http.Body
import retrofit2.http.POST

/**
 * Created by <PERSON><PERSON><PERSON>
 */

@Serializable
class GetUserDocumentRequest(
    val documentTypes : List<String > = emptyList()
)


@Serializable
data class GetUserDocumentResponse(
    val customerDocumentList: List<GetUserDocumentData> = emptyList()
)

@Serializable
data class GetUserDocumentData(
    val id: Int,
    val documentType: String,
    val otherDocumentType: String,
    val status: String,
    val rejectReason: String,
    val documentFileKey: String,
    val documentFileType: String,
    val documentFileName: String,
)


interface GetUserDocumentApiService {

    @POST("user/document")
    fun getUserDocument(@Body request: GetUserDocumentRequest?) : Call<BaseResponse<GetUserDocumentResponse>>
}