package com.siriustech.merit.apilayer.service.authentication.updatejpush

import com.core.network.model.BaseResponse
import kotlinx.serialization.Serializable
import retrofit2.Call
import retrofit2.http.Body
import retrofit2.http.PUT

/**
 * Created by <PERSON><PERSON><PERSON><PERSON>
 */

@Serializable
data class UpdateJPushRegIDRequest(
    val deviceId: String,
    val jpushRid: String,
)

@Serializable
class UpdateJPushRegIDResponse {}

interface UpdateJPushRegIDApiService {

    @PUT("user/device")
    fun request(@Body request: UpdateJPushRegIDRequest?): Call<BaseResponse<UpdateJPushRegIDResponse>>
}