package com.siriustech.merit.apilayer.service.history.transactiondetails

import com.core.network.base.RetrofitAPICaller
import com.core.network.model.BaseResponse
import javax.inject.Inject
import retrofit2.Response

/**
 * Created by <PERSON><PERSON>t
 */
class TransactionDetailsCaller @Inject constructor(private val apiService: TransactionDetailsApiService) :
    RetrofitAPICaller<TransactionDetailsRequest, BaseResponse<TransactionDetailsResponse>>() {
    override fun call(reqParam: TransactionDetailsRequest?): Response<BaseResponse<TransactionDetailsResponse>> {
        return apiService.request(reqParam).execute()
    }
}