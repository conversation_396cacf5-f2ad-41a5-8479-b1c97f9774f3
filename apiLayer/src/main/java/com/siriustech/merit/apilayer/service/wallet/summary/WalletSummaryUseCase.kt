package com.siriustech.merit.apilayer.service.wallet.summary

import com.core.network.base.AppUseCaseFlow
import com.core.network.base.Repository
import com.core.network.model.BaseResponse
import com.siriustech.merit.apilayer.service.market.exchangecurrency.MarketCurrencyListResponse
import com.siriustech.merit.apilayer.service.market.exchangecurrency.MarketCurrencyRequest
import dagger.hilt.android.scopes.ViewModelScoped
import javax.inject.Inject

/**
 * Created by <PERSON><PERSON>
 */
@ViewModelScoped
class WalletSummaryUseCase @Inject constructor(
    repository: Repository<WalletSummaryRequest, BaseResponse<WalletSummaryResponse>>
) : AppUseCaseFlow<WalletSummaryRequest,WalletSummaryResponse>(repository){
}

