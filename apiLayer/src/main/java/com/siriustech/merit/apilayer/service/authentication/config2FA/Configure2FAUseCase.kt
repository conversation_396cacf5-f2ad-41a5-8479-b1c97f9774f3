package com.siriustech.merit.apilayer.service.authentication.config2FA

import com.core.network.base.AppUseCaseFlow
import com.core.network.base.Repository
import com.core.network.model.BaseResponse
import dagger.hilt.android.scopes.ViewModelScoped
import javax.inject.Inject

// FLOW USE_CASE
@ViewModelScoped
class Configure2FAUseCase @Inject constructor(
    repository: Repository<Configure2FARequest, BaseResponse<Unit>>,
) : AppUseCaseFlow<Configure2FARequest, Unit>(repository)
