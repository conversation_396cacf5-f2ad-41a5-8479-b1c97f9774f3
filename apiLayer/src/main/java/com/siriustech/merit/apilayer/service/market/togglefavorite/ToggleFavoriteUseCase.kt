package com.siriustech.merit.apilayer.service.market.togglefavorite

import com.core.network.base.AppUseCaseFlow
import com.core.network.base.Repository
import com.core.network.base.RetrofitAPICaller
import com.core.network.model.BaseResponse
import com.siriustech.merit.apilayer.service.market.favorite.GetFavoriteAssetListApiService
import com.siriustech.merit.apilayer.service.market.marketlistdetails.MarketInstrumentListResponse
import dagger.hilt.android.scopes.ViewModelScoped
import javax.inject.Inject
import kotlinx.serialization.Serializable
import retrofit2.Call
import retrofit2.Response
import retrofit2.http.Body
import retrofit2.http.POST

/**
 * Created by <PERSON><PERSON><PERSON>
 */

// FLOW USE_CASE
@ViewModelScoped
class ToggleFavoriteUseCase @Inject constructor(
    repository: Repository<ToggleFavoriteRequest?, BaseResponse<ToggleFavoriteResponse>>,
) : AppUseCaseFlow<ToggleFavoriteRequest, ToggleFavoriteResponse>(repository)
