package com.siriustech.merit.apilayer.service.onboarding.updateagreement

import com.core.network.base.RetrofitAPICaller
import com.core.network.model.BaseResponse
import javax.inject.Inject
import retrofit2.Response

/**
 * Created by <PERSON><PERSON>t
 */
class UpdateAgreementCaller @Inject constructor(private val apiService: UpdateAgreementApiService) :
    RetrofitAPICaller<UpdateAgreementRequest, BaseResponse<UpdateAgreementResponse>>() {
    override fun call(reqParam: UpdateAgreementRequest?): Response<BaseResponse<UpdateAgreementResponse>> {
        return apiService.updateAgreement(reqParam).execute()
    }
}