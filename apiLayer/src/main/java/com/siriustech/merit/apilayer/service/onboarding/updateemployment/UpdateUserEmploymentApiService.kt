package com.siriustech.merit.apilayer.service.onboarding.updateemployment

import com.core.network.model.BaseResponse
import kotlinx.serialization.SerialName
import kotlinx.serialization.Serializable
import retrofit2.Call
import retrofit2.http.Body
import retrofit2.http.PUT

@Serializable
data class UserEmploymentRequest(
    val employmentStatus: String,
    val companyName: String,
    val position: String,
    val yearsInService: Int,
    val industry: String,
    val companyRegion: String,
    val companyAddress: String,
    @SerialName("companyPostalCode") val companyAddressPostalCode: String,
    val employmentStatusText: String? = null,
)

interface UpdateUserEmploymentApiService {
    @PUT("user/employment")
     fun updateUserEmployment(@Body request: UserEmploymentRequest?): Call<BaseResponse<Unit>>
}