package com.siriustech.merit.apilayer.service.authentication.authresetverify

import com.core.network.base.AppUseCaseFlow
import com.core.network.base.Repository
import com.core.network.model.BaseResponse
import com.siriustech.merit.apilayer.service.authentication.auth.ConfigurePinPassRequest
import dagger.hilt.android.scopes.ViewModelScoped
import javax.inject.Inject

/**
 * Created by <PERSON><PERSON> <PERSON>tet
 */

@ViewModelScoped
class AuthResetVerifyUseCase @Inject constructor(
    repository: Repository<AuthResetVerifyRequest, BaseResponse<AuthResetVerifyResponse>>,
) : AppUseCaseFlow<AuthResetVerifyRequest, AuthResetVerifyResponse>(repository)