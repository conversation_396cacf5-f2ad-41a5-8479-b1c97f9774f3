package com.siriustech.merit.apilayer.service.onboarding.updateemployment

import com.core.network.base.RetrofitAPICaller
import com.core.network.model.BaseResponse
import javax.inject.Inject
import retrofit2.Response

/**
 * Created by <PERSON><PERSON><PERSON>t
 */
class UpdateUserEmploymentCaller @Inject constructor(private val apiService: UpdateUserEmploymentApiService) :
    RetrofitAPICaller<UserEmploymentRequest, BaseResponse<Unit>>() {
    override fun call(reqParam: UserEmploymentRequest?): Response<BaseResponse<Unit>> {
        return apiService.updateUserEmployment(reqParam).execute()
    }
}