package com.siriustech.merit.apilayer.service.user.logout

import com.core.network.model.BaseResponse
import kotlinx.serialization.Serializable
import retrofit2.Call
import retrofit2.http.Body
import retrofit2.http.POST

/**
 * Created by <PERSON><PERSON><PERSON>t
 */

@Serializable
class LogoutRequest()

@Serializable
class LogoutResponse()




interface LogoutApiService {
    @POST("user/logout")
    fun request(@Body request: LogoutRequest?) : Call<BaseResponse<LogoutResponse>>
}