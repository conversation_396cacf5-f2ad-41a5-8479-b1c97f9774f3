package com.siriustech.merit.apilayer.service.authentication.resetauth

import com.core.network.base.AppUseCaseFlow
import com.core.network.base.Repository
import com.core.network.model.BaseResponse
import dagger.hilt.android.scopes.ViewModelScoped
import javax.inject.Inject

// FLOW USE_CASE
@ViewModelScoped
class AuthResetUseCase @Inject constructor(
    repository: Repository<AuthResetRequest, BaseResponse<AuthResetResponse>>,
) : AppUseCaseFlow<AuthResetRequest, AuthResetResponse>(repository)
