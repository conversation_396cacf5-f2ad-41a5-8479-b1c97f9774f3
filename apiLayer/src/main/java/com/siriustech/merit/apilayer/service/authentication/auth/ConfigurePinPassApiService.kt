package com.siriustech.merit.apilayer.service.authentication.auth

import com.core.network.model.BaseResponse
import kotlinx.serialization.Serializable
import retrofit2.Call
import retrofit2.http.Body
import retrofit2.http.POST
import retrofit2.http.PUT

/**
 * Created by <PERSON><PERSON> <PERSON>tet
 */

@Serializable
data class ConfigurePinPassRequest(
    val password: String,
    val newPassword: String? = null,
    val newPin: String? = null,
    val deviceId: String? = null,
)

interface ConfigurePinPassApiService {
    @PUT("user/auth")
    fun request(@Body request: ConfigurePinPassRequest?) : Call<BaseResponse<Unit>>
}