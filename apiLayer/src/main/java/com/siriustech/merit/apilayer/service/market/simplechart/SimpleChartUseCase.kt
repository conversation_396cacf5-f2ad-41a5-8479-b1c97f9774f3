package com.siriustech.merit.apilayer.service.market.simplechart

import com.core.network.base.AppUseCaseFlow
import com.core.network.base.Repository
import com.core.network.model.BaseResponse
import dagger.hilt.android.scopes.ViewModelScoped
import javax.inject.Inject

/**
 * Created by <PERSON><PERSON>
 */
@ViewModelScoped
class SimpleChartUseCase @Inject
constructor(
    repository: Repository<SimpleChartRequest, BaseResponse<SimpleChartResponse>>,
) : AppUseCaseFlow<SimpleChartRequest, SimpleChartResponse>(repository) {}