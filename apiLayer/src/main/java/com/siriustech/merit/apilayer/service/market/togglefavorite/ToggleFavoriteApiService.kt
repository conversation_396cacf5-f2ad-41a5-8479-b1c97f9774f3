package com.siriustech.merit.apilayer.service.market.togglefavorite

import com.core.network.model.BaseResponse
import kotlinx.serialization.Serializable
import retrofit2.Call
import retrofit2.http.Body
import retrofit2.http.POST

/**
 * Created by <PERSON><PERSON>
 */

@Serializable
data class ToggleFavoriteRequest(
    val instrumentId: Int,
)

@Serializable
class ToggleFavoriteResponse {}

interface ToggleFavoriteApiService {

    @POST("user/instrument/favorite/update")
    fun request(@Body request: ToggleFavoriteRequest?): Call<BaseResponse<ToggleFavoriteResponse>>
}