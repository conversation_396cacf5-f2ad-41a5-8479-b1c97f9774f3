package com.siriustech.merit.apilayer.service.wallet.withdraw

import com.core.network.base.AppUseCaseFlow
import com.core.network.base.Repository
import com.core.network.model.BaseResponse
import dagger.hilt.android.scopes.ViewModelScoped
import javax.inject.Inject

/**
 * Created by <PERSON><PERSON>
 */

@ViewModelScoped
class WithdrawUseCase @Inject constructor(
    repository: Repository<WithdrawRequest, BaseResponse<WithdrawResponse>>
) : AppUseCaseFlow<WithdrawRequest, WithdrawResponse>(repository){
}