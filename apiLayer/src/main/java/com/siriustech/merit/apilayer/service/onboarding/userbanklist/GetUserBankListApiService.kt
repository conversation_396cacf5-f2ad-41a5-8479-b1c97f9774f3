package com.siriustech.merit.apilayer.service.onboarding.userbanklist

import com.core.network.model.BaseResponse
import kotlinx.serialization.Serializable
import retrofit2.Call
import retrofit2.http.POST

@Serializable
data class UserBankListResponse(
    val bankAccountList: List<UserBank>?,
)

@Serializable
data class UserBank(
    val id: Int,
    val customerId: Int,
    val bankName: String,
    val accountName: String,
    val accountNumber: String,
    val swiftCode: String,
    var accountType: String,
    val bankAddress: UserBankAddress? = null,
    val remark: String? = null,
    val currency: List<String>? = emptyList(),
    val isPrimary: Boolean,
)

@Serializable
data class UserBankAddress(
    val address: String,
    val countryRegion: String,
    val postCode: String,
)


interface GetUserBankListApiService {
    @POST("user/bank/list")
    fun getBankList(): Call<BaseResponse<UserBankListResponse>>
}