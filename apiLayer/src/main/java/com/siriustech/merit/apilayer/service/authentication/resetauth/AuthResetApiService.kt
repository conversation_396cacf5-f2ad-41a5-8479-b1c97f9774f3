package com.siriustech.merit.apilayer.service.authentication.resetauth

import com.core.network.model.BaseResponse
import kotlinx.serialization.Serializable
import retrofit2.Call
import retrofit2.http.Body
import retrofit2.http.PUT

@Serializable
data class AuthResetRequest(
    val token: String? = null,
    val password: String? = null,
    val pin: String? = null,
    val deviceId: String,
)

@Serializable
class AuthResetResponse {}

interface AuthResetApiService {

    @PUT("user/auth/reset")
    fun request(@Body request: AuthResetRequest?): Call<BaseResponse<AuthResetResponse>>
}