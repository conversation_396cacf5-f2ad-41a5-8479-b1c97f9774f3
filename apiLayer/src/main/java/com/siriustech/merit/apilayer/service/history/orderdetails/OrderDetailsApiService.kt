package com.siriustech.merit.apilayer.service.history.orderdetails

import com.core.network.model.BaseResponse
import kotlinx.serialization.Serializable
import retrofit2.Call
import retrofit2.http.Body
import retrofit2.http.POST

/**
 * Created by Hein Htet
 */

@Serializable
data class OrderDetailsRequest(
    val orderId: Int,
)

@Serializable
data class OrderDetailsResponse(
    val orderId: Int? = null,
    val assetLogo: String? = null,
    val assetSymbol: String? = null,
    val assetName: String? = null,
    val currency: String? = null,
    val exchange: String? = null,
    val price: String? = null,
    val unitPrice: String? = null,
    val priceChange: String? = null,
    val priceChangeRate: String? = null,
    val orderStatus: String? = null,
    val orderDate: Long? = null,
    val settleDate: Long? = null,
    val tradeDate: Long? = null,
    val orderSide: String? = null,
    val quantity: String? = null,
    val brokerageAmount: String? = null,
    val accruedInterest: String? = null,
    val otherFee: String? = null,
    val transactionCost: String? = null,
    val settleAmountWithBroker: String? = null,
    val actualAmount: String? = null,
    val instrumentId: Int? = null,
    val riskLevel: String? = null,
)

interface OrderDetailsApiService {

    @POST("account/order/detail")
    fun request(@Body request: OrderDetailsRequest?): Call<BaseResponse<OrderDetailsResponse>>
}