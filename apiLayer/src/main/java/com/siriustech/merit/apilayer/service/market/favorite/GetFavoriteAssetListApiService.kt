package com.siriustech.merit.apilayer.service.market.favorite

import com.core.network.model.BaseResponse
import com.siriustech.merit.apilayer.service.market.marketlistdetails.MarketInstrumentListResponse
import retrofit2.Call
import retrofit2.http.POST

/**
 * Created by <PERSON><PERSON><PERSON>
 */


interface GetFavoriteAssetListApiService {

    @POST("user/instrument/favorite")
    fun request(): Call<BaseResponse<MarketInstrumentListResponse>>

}