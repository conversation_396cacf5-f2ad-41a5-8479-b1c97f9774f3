package com.siriustech.merit.apilayer.service.onboarding.getdeclarationcrs

import com.core.network.base.RetrofitAPICaller
import com.core.network.model.BaseResponse
import javax.inject.Inject
import retrofit2.Response

/**
 * Created by <PERSON><PERSON> Htet
 */

class GetCRSDeclarationCaller @Inject constructor(private val apiService: GetCRSDeclarationApiService) :
    RetrofitAPICaller<GetCRSDeclarationListRequest, BaseResponse<GetCRSDeclarationListResponse>>() {
    override fun call(reqParam: GetCRSDeclarationListRequest?): Response<BaseResponse<GetCRSDeclarationListResponse>> {
        return apiService.getCRSDeclarationList(reqParam).execute()
    }
}