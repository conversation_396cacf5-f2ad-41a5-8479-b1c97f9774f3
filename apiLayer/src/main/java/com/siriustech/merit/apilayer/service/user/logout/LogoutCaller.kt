package com.siriustech.merit.apilayer.service.user.logout

import com.core.network.base.RetrofitAPICaller
import com.core.network.model.BaseResponse
import javax.inject.Inject
import retrofit2.Response

/**
 * Created by <PERSON><PERSON><PERSON>
 */
class LogoutCaller @Inject constructor(private val apiService: LogoutApiService) : RetrofitAPICaller<LogoutRequest,BaseResponse<LogoutResponse>>() {
    override fun call(reqParam: LogoutRequest?): Response<BaseResponse<LogoutResponse>> {
        return apiService.request(reqParam).execute()
    }
}