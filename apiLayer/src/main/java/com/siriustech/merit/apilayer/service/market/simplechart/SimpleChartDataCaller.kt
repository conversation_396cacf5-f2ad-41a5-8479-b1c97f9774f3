package com.siriustech.merit.apilayer.service.market.simplechart

import com.core.network.base.RetrofitAPICaller
import com.core.network.model.BaseResponse
import javax.inject.Inject
import retrofit2.Response

/**
 * Created by <PERSON><PERSON><PERSON>
 */
class SimpleChartDataCaller @Inject constructor(
    private val apiService: SimpleChartApiService
) : RetrofitAPICaller<SimpleChartRequest,BaseResponse<SimpleChartResponse>>(){
    override fun call(reqParam: SimpleChartRequest?): Response<BaseResponse<SimpleChartResponse>> {
        return apiService.request(reqParam).execute()
    }
}