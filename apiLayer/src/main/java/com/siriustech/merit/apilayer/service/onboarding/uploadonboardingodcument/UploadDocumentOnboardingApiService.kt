package com.siriustech.merit.apilayer.service.onboarding.uploadonboardingodcument

import com.core.network.model.BaseResponse
import kotlinx.serialization.Serializable
import retrofit2.Call
import retrofit2.http.Body
import retrofit2.http.PUT

/**
 * Created by <PERSON><PERSON><PERSON><PERSON>
 */

@Serializable
data class UploadDocumentOnboardingRequest(
    val scenario: String,
    val addressProof : String? = null,
    val assetProof : String? = null,
    val fatca : String? = null
)

@Serializable
 class UploadDocumentOnboardingResponse{}

interface UploadDocumentOnboardingApiService {

    @PUT("user/document/onboarding")
     fun uploadDocument(@Body request: UploadDocumentOnboardingRequest?) : Call<BaseResponse<UploadDocumentOnboardingResponse>>
}