package com.siriustech.merit.apilayer.service.home.banner

import com.core.network.base.AppUseCaseFlow
import com.core.network.base.Repository
import com.core.network.model.BaseResponse
import dagger.hilt.android.scopes.ViewModelScoped
import javax.inject.Inject

/**
 * Created by Hein Htet
 */
// FLOW USE_CASE
@ViewModelScoped
class GetBannerListUseCase @Inject constructor(
    repository: Repository<Unit, BaseResponse<BannerListResponse>>,
) : AppUseCaseFlow<Unit, BannerListResponse>(repository)
