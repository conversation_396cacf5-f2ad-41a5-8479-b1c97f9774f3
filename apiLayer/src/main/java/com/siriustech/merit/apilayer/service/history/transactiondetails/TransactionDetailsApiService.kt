package com.siriustech.merit.apilayer.service.history.transactiondetails

import com.core.network.model.BaseResponse
import kotlinx.serialization.Serializable
import retrofit2.Call
import retrofit2.http.Body
import retrofit2.http.POST

/**
 * Created by Hein <PERSON>tet
 */

@Serializable
data class TransactionDetailsRequest(
    val transactionId: Int,
)

@Serializable
data class TransactionDetailsResponse(
    val transactionId: Int? = null,
    val createdAt: Long? = null,
    val settleDate: Long? = null,
    val tradeDate: Long? = null,
    val transactionType: String? = null,
    val status: String? = null,
    val amount: String? = null,
    val currency: String? = null,
    val bankAccountType: String? = null,
    val bankAccountName: String? = null,
    val bankAccountNumber: String? = null,
    val proofFile: ProofFileResponse? = null,
    val bankAccountInfo: BankAccountInfoResponse? = null,
)

@Serializable
data class ProofFileResponse(
    val fileKey: String? = null,
    val documentFileType: String? = null,
    val documentFileName: String? = null,
)

@Serializable
data class BankAccountInfoResponse(
    val accountType: String? = null,
    val bankName: String? = null,
    val accountName: String? = null,
    val accountNumber: String? = null,
    val swiftCode: String? = null,
    val currency: List<String>? = null,
    val remark: String? = null,
    val isPrimary: String? = null,
    val bankAddress: BankAddressResponse? = null,
)

@Serializable
data class BankAddressResponse(
    val countryRegion: String? = null,
    val address: String? = null,
    val postCode: String? = null,
)

interface TransactionDetailsApiService {
    @POST("account/transaction/detail")
    fun request(@Body request: TransactionDetailsRequest?): Call<BaseResponse<TransactionDetailsResponse>>
}