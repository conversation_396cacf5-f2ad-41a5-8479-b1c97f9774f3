package com.siriustech.merit.apilayer.service.onboarding.updatequestionnaire

import com.core.network.base.RetrofitAPICaller
import com.core.network.model.BaseResponse
import javax.inject.Inject
import retrofit2.Response

/**
 * Created by <PERSON><PERSON><PERSON>
 */
class UpdateQuestionnaireCaller @Inject constructor(
    private val apiService: UpdateQuestionnaireApiService,
) : RetrofitAPICaller<QuestionnaireUpdateRequest, BaseResponse<QuestionnaireUpdateResponse>>() {
    override fun call(reqParam: QuestionnaireUpdateRequest?): Response<BaseResponse<QuestionnaireUpdateResponse>> {
        return apiService.updateQuestionnaire(reqParam).execute()
    }
}