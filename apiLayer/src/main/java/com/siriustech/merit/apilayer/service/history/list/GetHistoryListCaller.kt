package com.siriustech.merit.apilayer.service.history.list

import com.core.network.base.RetrofitAPICaller
import com.core.network.model.BaseResponse
import javax.inject.Inject
import retrofit2.Response

/**
 * Created by <PERSON><PERSON><PERSON><PERSON>
 */
class GetHistoryListCaller @Inject constructor(
    private val apiService: GetHistoryApiService,
) : RetrofitAPICaller<GetHistoryRequest, BaseResponse<HistoryListResponse>>() {
    override fun call(reqParam: GetHistoryRequest?): Response<BaseResponse<HistoryListResponse>> {
        return apiService.request(reqParam).execute()
    }
}