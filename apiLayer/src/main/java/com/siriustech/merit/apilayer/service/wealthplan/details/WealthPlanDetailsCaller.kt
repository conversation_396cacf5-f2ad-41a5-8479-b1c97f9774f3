package com.siriustech.merit.apilayer.service.wealthplan.details

import com.core.network.base.RetrofitAPICaller
import com.core.network.model.BaseResponse
import javax.inject.Inject
import retrofit2.Response

/**
 * Created by <PERSON><PERSON><PERSON>t
 */
class WealthPlanDetailsCaller @Inject constructor(private val apiService: WealthPlanDetailsApiService) :
    RetrofitAPICaller<WealthPlanDetailsRequest, BaseResponse<WealthPlanDetailsResponse>>() {
    override fun call(reqParam: WealthPlanDetailsRequest?): Response<BaseResponse<WealthPlanDetailsResponse>> {
        return apiService.request(reqParam).execute()
    }
}