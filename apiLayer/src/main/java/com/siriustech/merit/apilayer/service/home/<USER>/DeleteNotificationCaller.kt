package com.siriustech.merit.apilayer.service.home.deletenotification

import com.core.network.base.RetrofitAPICaller
import com.core.network.model.BaseResponse
import javax.inject.Inject
import retrofit2.Response

/**
 * Created by <PERSON><PERSON>t
 */
class DeleteNotificationCaller @Inject constructor(private val apiService: DeleteNotificationApiService) : RetrofitAPICaller<DeleteNotificationRequest,BaseResponse<DeleteNotificationResponse>>() {
    override fun call(reqParam: DeleteNotificationRequest?): Response<BaseResponse<DeleteNotificationResponse>> {
        return apiService.request(reqParam).execute()
    }
}