package com.siriustech.merit.apilayer.service.market.marketlistdetails

import com.core.network.model.BaseResponse
import kotlinx.serialization.Serializable
import retrofit2.Call
import retrofit2.http.Body
import retrofit2.http.POST

/**
 * Created by <PERSON><PERSON> <PERSON><PERSON>t
 */

@Serializable
data class MarketInstrumentDetailsResponse(
    val id: Int? = null,
    val symbol: String? = null,
    val exchange: String? = null,
    val timeZone: String? = null,
    val market: String? = null,
    val instrumentName: String? = null,
    val currency: String? = null,
    val logo: String? = null,
    val riskLevel: String? = null,
    val riskRating: String? = null,
    val instrumentClass: String? = null,
    val instrumentType: String? = null,
    val instrumentCategory: String? = null,
    val allocation: String? = null,
    val assetClass: String? = null,
    val lastPrice: String? = null,
    val priceChange: String? = null,
    val priceChangePercentage: String? = null,
    val favorite: Boolean? = null,
    val totalVolume: String? = null,
    val totalAmount: String? = null,
)


@Serializable
data class MarketInstrumentListResponse(
    val instruments: List<MarketInstrumentDetailsResponse>? = null,
    val instrumentList: List<MarketInstrumentDetailsResponse>? = null, // for instrument view API
)


@Serializable
data class MarketInstrumentListRequest(
    val listCode: String? = null,
    val allocationList: List<String>? = emptyList(), //CASH_EQUIVALENT FIX_INCOME STRUCTURED_PRODUCTS PRIVATE_CREDIT MULTI-ASSET EQUITY PRIVATE_EQUITY
    val riskLevelList: List<String>? = emptyList(), // LOW MEDIUM HIGH
    val productTypeList: List<String>? = emptyList(), // HK_EQUITY US_EQUITY MUTUAL_FUNDS PRIVATE_EQUITY_FUNDS BONDS STRUCTURED_PRODUCTS
    val productCategoryList: List<String>? = emptyList(),
)

interface MarketListDetailsApiService {

    @POST("market/list/detail")
    fun request(@Body request: MarketInstrumentListRequest?): Call<BaseResponse<MarketInstrumentListResponse>>

}