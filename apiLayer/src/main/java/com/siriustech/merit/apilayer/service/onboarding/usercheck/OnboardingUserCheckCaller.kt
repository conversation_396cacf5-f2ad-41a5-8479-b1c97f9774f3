package com.siriustech.merit.apilayer.service.onboarding.usercheck

import com.core.network.base.RetrofitAPICaller
import com.core.network.model.BaseResponse
import javax.inject.Inject
import retrofit2.Response

/**
 * Created by <PERSON><PERSON><PERSON>
 */

class OnboardingUserCheckCaller @Inject constructor(private val apiService: OnboardingUserCheckApiService) :
    RetrofitAPICaller<OnboardingUserCheckRequest, BaseResponse<OnboardingUserCheckResponse>>() {
    override fun call(reqParam: OnboardingUserCheckRequest?): Response<BaseResponse<OnboardingUserCheckResponse>> {
        return apiService.request(reqParam).execute()
    }
}