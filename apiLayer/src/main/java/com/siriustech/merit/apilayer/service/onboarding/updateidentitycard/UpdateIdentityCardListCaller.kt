package com.siriustech.merit.apilayer.service.onboarding.updateidentitycard

import com.core.network.base.RetrofitAPICaller
import com.core.network.model.BaseResponse
import javax.inject.Inject
import retrofit2.Response

/**
 * Created by <PERSON><PERSON> <PERSON><PERSON><PERSON>
 */
class UpdateIdentityCardListCaller @Inject constructor(private val apiService: UpdateIdentityCardListApiService) : RetrofitAPICaller<UpdateIdentityCardListRequest,BaseResponse<UpdateIdentityCardListResponse>>() {
    override fun call(reqParam: UpdateIdentityCardListRequest?): Response<BaseResponse<UpdateIdentityCardListResponse>> {
        return apiService.updateIdentityCardList(reqParam).execute()
    }
}