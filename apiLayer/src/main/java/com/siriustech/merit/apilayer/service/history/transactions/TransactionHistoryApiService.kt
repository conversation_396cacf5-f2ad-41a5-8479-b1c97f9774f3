package com.siriustech.merit.apilayer.service.history.transactions

import com.core.network.model.BaseResponse
import kotlinx.serialization.Serializable
import retrofit2.Call
import retrofit2.http.Body
import retrofit2.http.POST

/**
 * Created by <PERSON><PERSON> <PERSON><PERSON>t
 */


@Serializable
data class TransactionHistoryRequest(
    val transactionType: String,
    val transactionStatus: String,
    val startTime: Long = 0,
    val endTime: Long = 0,
    val currency: String = "USD",
)

@Serializable
data class TransactionListResponse(
    val transactionList: List<TransactionHistoryResponse>? = emptyList(),
)

@Serializable
data class TransactionHistoryResponse(
    val transactionId: Int? = null,
    val transactionType: String? = null,
    val transactionStatus: String? = null,
    val amount: String? = null,
    val currency: String? = null,
    val createTime: Long? = null,
    val tradeDate: Long? = null,
    val settDate: Long? = null,
    val bankName: String? = null,
    val bankAccountType: String? = null,
    val bankAccountName: String? = null,
    val bankAccountNumber: String? = null,
)

interface TransactionHistoryApiService {
    @POST("account/transaction/list")
    fun request(@Body request: TransactionHistoryRequest?): Call<BaseResponse<TransactionListResponse>>
}