package com.siriustech.merit.apilayer.service.user.status

import com.core.network.base.RetrofitAPICaller
import com.core.network.model.BaseResponse
import javax.inject.Inject
import retrofit2.Response

/**
 * Created by <PERSON><PERSON><PERSON>
 */
class UpdateUserStatusCaller @Inject constructor(private val userStatusApiService: UpdateUserStatusApiService) : RetrofitAPICaller<UpdateUserStatusRequest,BaseResponse<UpdateUserStatusResponse>>() {
    override fun call(reqParam: UpdateUserStatusRequest?): Response<BaseResponse<UpdateUserStatusResponse>> {
        return userStatusApiService.request(reqParam).execute()
    }
}