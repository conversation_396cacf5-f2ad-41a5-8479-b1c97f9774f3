package com.siriustech.merit.apilayer.service.wallet.withdraw

import com.core.network.model.BaseResponse
import kotlinx.serialization.Serializable
import retrofit2.Call
import retrofit2.http.Body
import retrofit2.http.POST

/**
 * Created by <PERSON><PERSON> <PERSON><PERSON><PERSON>
 */

@Serializable
data class WithdrawRequest(
    val refCode: String = "",
    val otpCode: String = "",
    val otpType: String = "",
    val otpAddress: String,
    val currency: String,
    val amount: String,
    val bankAccountId: Int,
) : java.io.Serializable

@Serializable
class WithdrawResponse {

}

interface WithdrawApiService {
    @POST("account/withdraw")
    fun request(@Body request: WithdrawRequest?): Call<BaseResponse<WithdrawResponse>>
}
