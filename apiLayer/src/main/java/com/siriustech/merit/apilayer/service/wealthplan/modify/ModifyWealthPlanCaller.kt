package com.siriustech.merit.apilayer.service.wealthplan.modify

import com.core.network.base.RetrofitAPICaller
import com.core.network.model.BaseResponse
import com.siriustech.merit.apilayer.service.wealthplan.list.WealthPlanResponse
import javax.inject.Inject
import retrofit2.Response

/**
 * Created by <PERSON><PERSON>
 */
class ModifyWealthPlanCaller @Inject constructor(
    private val apiService: ModifyWealthPlanApiService
) : RetrofitAPICaller<ModifyWealthPlanRequest,BaseResponse<ModifyWealthPlanResponse>>(){
    override fun call(reqParam: ModifyWealthPlanRequest?): Response<BaseResponse<ModifyWealthPlanResponse>> {
        return apiService.request(reqParam).execute()
    }
}