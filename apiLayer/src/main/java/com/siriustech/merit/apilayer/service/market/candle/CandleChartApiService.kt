package com.siriustech.merit.apilayer.service.market.candle

import com.core.network.model.BaseResponse
import kotlinx.serialization.Serializable
import retrofit2.Call
import retrofit2.http.Body
import retrofit2.http.POST

/**
 * Created by <PERSON><PERSON> <PERSON><PERSON><PERSON>
 */

@Serializable
data class CandleChartDataRequest(
    val candleType: String, // 1m,5m,15m,30m,1H,2H,4H,1D,1W,1M,
    val instrumentId: Int,
    val limit: Int,
    val toTime: Long? = null,
    val fromTime: Long? = null,
)

@Serializable
data class CandleChartDataResponse(
    val candles: List<List<String>>? = emptyList(),
)


interface CandleChartApiService {

    @POST("market/candles")
    fun request(@Body request: CandleChartDataRequest?): Call<BaseResponse<CandleChartDataResponse>>
}