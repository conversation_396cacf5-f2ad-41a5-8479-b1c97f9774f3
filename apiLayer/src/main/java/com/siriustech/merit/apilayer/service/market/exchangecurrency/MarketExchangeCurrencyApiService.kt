package com.siriustech.merit.apilayer.service.market.exchangecurrency

import com.core.network.model.BaseResponse
import kotlinx.serialization.Serializable
import retrofit2.Call
import retrofit2.http.Body
import retrofit2.http.POST

/**
 * Created by <PERSON><PERSON><PERSON>
 */

@Serializable
data class MarketCurrencyRequest(
    val currencyList: List<String> = emptyList(),
)

@Serializable
data class MarketCurrencyListResponse(
    val list: List<MarketCurrencyResponse>? = emptyList(),
)

@Serializable
data class MarketCurrencyResponse(
    val exchangeRate: String? = null,
    val exchange: String? = null,
)


interface MarketCurrencyApiService {
    @POST("market/currency")
    fun request(@Body request: MarketCurrencyRequest?) : Call<BaseResponse<MarketCurrencyListResponse>>
}