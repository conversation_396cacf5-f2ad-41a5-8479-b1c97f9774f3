package com.siriustech.merit.apilayer.service.wealthplan.list

import com.core.network.model.BaseResponse
import kotlinx.serialization.Serializable
import retrofit2.Call
import retrofit2.http.POST

/**
 * Created by <PERSON><PERSON> <PERSON><PERSON><PERSON>
 */


@Serializable
data class WealthPlanResponse(
    val wealthPlanList: List<WealthPlanListResponse>? = emptyList(),
)

@Serializable
data class WealthPlanListResponse(
    val asOf: Long? = null,
    val id: Int? = null,
    val name: String? = null,
    val initialAmount: String? = null,
    val initialDate: Long? = null,
    val mtdReturn : String? = null,
    val ytdReturn : String? = null,
    val sharpe : String? = null,
    val annualizedReturn : String? = null,
    val annualizedVolatility : String? = null,
    val beta : String? = null,
    val logo : String? = null,
    val description : String?= null
)

interface WealthPlanListApiService {


    @POST("user/wealth-plan/list")
    fun request() : Call<BaseResponse<WealthPlanResponse>>
}