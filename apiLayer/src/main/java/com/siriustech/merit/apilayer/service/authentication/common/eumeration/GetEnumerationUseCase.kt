package com.siriustech.merit.apilayer.service.authentication.common.eumeration

import com.core.network.base.AppUseCaseFlow
import com.core.network.base.Repository
import com.core.network.model.BaseResponse
import dagger.hilt.android.scopes.ViewModelScoped
import javax.inject.Inject

// FLOW USE_CASE
@ViewModelScoped
class GetEnumerationUseCase @Inject constructor(
    repository: Repository<EnumerationRequest, BaseResponse<EnumerationResponse>>,
) : AppUseCaseFlow<EnumerationRequest, EnumerationResponse>(repository)
