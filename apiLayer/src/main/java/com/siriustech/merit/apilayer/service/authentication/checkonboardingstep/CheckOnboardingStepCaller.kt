package com.siriustech.merit.apilayer.service.authentication.checkonboardingstep

import com.core.network.base.RetrofitAPICaller
import com.core.network.model.BaseResponse
import retrofit2.Response
import javax.inject.Inject

class CheckOnboardingStepCaller @Inject constructor(
    private val apiService: CheckOnboardingStepApiService
) : RetrofitAPICaller<CheckOnboardingStepRequest, BaseResponse<CheckOnboardingStepResponse>>() {
    override fun call(reqParam: CheckOnboardingStepRequest?): Response<BaseResponse<CheckOnboardingStepResponse>> {
        return apiService.request(reqParam).execute()
    }
}