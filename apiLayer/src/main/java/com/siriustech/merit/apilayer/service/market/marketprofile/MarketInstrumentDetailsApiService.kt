package com.siriustech.merit.apilayer.service.market.marketprofile

import com.core.network.model.BaseResponse
import kotlinx.serialization.Serializable
import retrofit2.Call
import retrofit2.http.Body
import retrofit2.http.POST

/**
 * Created by Hein <PERSON>tet
 */

@Serializable
data class MarketInstrumentDetailsRequest(
    val instrumentId: Int,
)

@Serializable
data class MarketInstrumentDetailsResponse(
    val instrumentStatus: String? = null,
    val isHighlight: Boolean? = null,
    val allocation: String? = null,
    val profileHKEquity: MarketProfileResponse? = null,
    val instrumentClass: String? = null,
    val profileUSEquity: MarketProfileResponse? = null,
    val region: String? = null,
    val currency: String? = null,
    val createdAt: String? = null,
    val exchange: String? = null,
    val tagList: List<String>? = null,
    val profileStructure: ProfileStructureResponse? = null,
    val updatedAt: String? = null,
    val createdBy: String? = null,
    val symbol: String? = null,
    val updatedBy: String? = null,
    val profilePrivateEquityFund: ProfilePrivateEquityFundResponse? = null,
    val profileMutualFund: ProfileMutualFundResponse? = null,
    val logo: String? = null,
    val riskLevel: String? = null,
    val instrumentCategory: String? = null,
    val instrumentName: String? = null,
    val description: String? = null,
    val profileBond: ProfileBondResponse? = null,
    val country: String? = null,
)

@Serializable
data class MarketProfileResponse(
    val mICCode: String? = null,
    val investment: InvestmentResponse? = null,
    val financial: FinancialResponse? = null,
)

@Serializable
data class InvestmentResponse(
    val sector: String? = null,
    val priceEarnings: String? = null,
    val priceSales: String? = null,
    val priceBook: String? = null,
    val marketCapitalization: String? = null,
    val industry: String? = null,
    val dividendYield: String? = null,
    val exDividendDate: String? = null,

    // mutual fund
    val investmentObjective: String? = null,
    val investmentFocus: String? = null,
    val dividend: String? = null,
    val minimumInvestment: Int? = null,
    val minimumAdditional: Int? = null,
    val rating: RatingResponse? = null,
    val performanceRating: RatingResponse? = null,
    val returnRating: RatingResponse? = null,
    val fundFactSheet: String? = null,

    // private fund
    val geographicFocus: String? = null,
    val investmentStrategy: String? = null,


    // bond
    val couponRate: String? = null,
    val yield: String? = null,
    val maturityDate: Long? = null,
    val creditRating: String? = null,

    // structure product
    val underlyingAssets: String? = null,
    val structureDescription: String? = null,
    val principalProtection: String? = null,
    val couponYield: String? = null,
    val participateRate: String? = null,
    val redemptionTerm: String? = null,
    val barrierLevel: String? = null,
)

@Serializable
data class RatingResponse(
    val total: Int? = null,
    val score: Int? = null,
)

@Serializable
data class FinancialResponse(
    val period: String? = null,
    val totalLiabilities: FinancialItemResponse? = null,
    val sales: FinancialItemResponse? = null,
    val operatingIncome:FinancialItemResponse? = null,
    val operatingMargin: FinancialItemResponse? = null,
    val earningsPerShare: FinancialItemResponse? = null,
    val grossProfit: FinancialItemResponse? = null,
    val periodDate: String? = null,
    val netIncome: FinancialItemResponse? = null,
    val totalAssets: FinancialItemResponse? = null,

    )

@Serializable
data class ProfileStructureResponse(
    val productType: String? = null,
    val investment: InvestmentResponse? = null,
    val financial: StructuredProductFinancialInfoResponse? = null,
    val issuer: String? = null,

)

@Serializable
data class StructuredProductFinancialInfoResponse(
    val strikePrice: String? = null,
    val initialPrice: String? = null,
    val issuePrice: String? = null,
    val returnCap: String? = null,
    val principalReturnAtMaturity: String? = null,
)

@Serializable
data class FinancialItemResponse(
    val yy: String? = null,
    val list: List<String>? = null,
)

@Serializable
data class ProfilePrivateEquityFundResponse(
    val assetAllocation: List<PerformanceItemResponse>? = null,
    val performance: List<PerformanceItemResponse>? = null,
    val fundSize: String? = null,
    val investment: InvestmentResponse? = null,
    val fundType: String? = null,
    val fundManager: String? = null,
)

@Serializable
data class ProfileMutualFundResponse(
    val inceptionDate: Long? = null,
    val performance: List<PerformanceItemResponse>? = null,
    val assetAllocation: List<PerformanceItemResponse>? = null,
    val fundType: String? = null,
    val fundSize: String? = null,
    val fundManager: String? = null,
    val investment: InvestmentResponse? = null,
)

@Serializable
data class ProfileBondResponse(
    val bondType: String? = null,
    val investment: InvestmentResponse? = null,
    val issuer: String? = null,
)

@Serializable
data class PerformanceItemResponse(
    val key: String? = null,
    val value: String? = null,
)

interface MarketInstrumentDetailsApiService {
    @POST("market/instrument/detail")
    fun request(@Body request: MarketInstrumentDetailsRequest?): Call<BaseResponse<MarketInstrumentDetailsResponse>>
}