package com.siriustech.merit.apilayer.service.authentication.userinfo

import com.core.network.model.BaseResponse
import kotlinx.serialization.Serializable
import retrofit2.Call
import retrofit2.http.POST

/**
 * Created by <PERSON><PERSON> <PERSON><PERSON>t
 */

@Serializable
data class UserInfoResponse(
    val accountStatus: String? = null,
    val registrationChannel: String? = null,
    val detectionAML: String? = null,
    val referenceNumber: String? = null,
    val basic: UserBasicResponse? = null,
    val tagList: List<String>? = null,
    val employment: UserEmploymentInfoResponse? = null,
)

@Serializable
data class UserBasicResponse(
    val region: String,
    val chineseName: String,
    val englishName: String,
    val gender: String,
    val DateOfBirth: String,
    val maritalStatus: String,
    val educationLevel: String,
    val deliveryMethod: String,
    val accountStatementLanguage: String,
    val additionalPhoneNumber: String,
    val residentialAddress: String,
    val residentialPostalCode: String? = null,
    val mainlingPostalCode: String? = null,
    val mainRegion: String,
    val mainlingAddress: String,
    val base: UserBaseResponse,
    val tagList: List<String?>,
)

@Serializable
data class UserBaseResponse(
    val phoneNumber: String? = null,
    val email: String? = null,
    val mobileRegion :String? = null
)

@Serializable
data class UserEmploymentInfoResponse(
    val employmentStatus: String,
    val companyName: String,
    val position: String,
    val yearsInService: Int,
    val industry: String,
    val companyRegion: String,
    val companyAddress: String,
    val companyPostalCode: String? = null,
    val employmentStatusText: String? = null,
)


interface GetUserInfoApiService {
    @POST("user/information")
    fun request(): Call<BaseResponse<UserInfoResponse>>
}