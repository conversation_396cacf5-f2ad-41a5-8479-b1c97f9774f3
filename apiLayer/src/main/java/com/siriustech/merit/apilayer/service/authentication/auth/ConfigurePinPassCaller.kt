package com.siriustech.merit.apilayer.service.authentication.auth

import com.core.network.base.RetrofitAPICaller
import com.core.network.model.BaseResponse
import com.siriustech.merit.apilayer.service.authentication.config2FA.Configure2FAApiService
import com.siriustech.merit.apilayer.service.authentication.config2FA.Configure2FARequest
import retrofit2.Response
import javax.inject.Inject

class ConfigurePinPassCaller @Inject constructor(
    private val apiService: ConfigurePinPassApiService
) : RetrofitAPICaller<ConfigurePinPassRequest, BaseResponse<Unit>>() {
    override fun call(reqParam: ConfigurePinPassRequest?): Response<BaseResponse<Unit>> {
        return apiService.request(reqParam).execute()
    }
}