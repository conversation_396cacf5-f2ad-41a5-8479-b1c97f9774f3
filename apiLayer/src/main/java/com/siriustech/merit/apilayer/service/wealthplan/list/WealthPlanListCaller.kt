package com.siriustech.merit.apilayer.service.wealthplan.list

import com.core.network.base.RetrofitAPICaller
import com.core.network.model.BaseResponse
import javax.inject.Inject
import retrofit2.Response

/**
 * Created by <PERSON><PERSON><PERSON>
 */
class WealthPlanListCaller @Inject constructor(private val apiService: WealthPlanListApiService) : RetrofitAPICaller<Unit,BaseResponse<WealthPlanResponse>>() {
    override fun call(reqParam: Unit?): Response<BaseResponse<WealthPlanResponse>> {
        return apiService.request().execute()
    }
}