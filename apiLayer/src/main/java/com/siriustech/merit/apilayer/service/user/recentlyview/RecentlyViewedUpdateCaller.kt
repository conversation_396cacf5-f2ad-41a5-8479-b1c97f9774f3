package com.siriustech.merit.apilayer.service.user.recentlyview

import com.core.network.base.RetrofitAPICaller
import com.core.network.model.BaseResponse
import javax.inject.Inject
import retrofit2.Response

/**
 * Created by <PERSON><PERSON><PERSON>
 */
class RecentlyViewedUpdateCaller @Inject constructor(
    private val apiService: RecentlyViewedUpdateApiService,
) : RetrofitAPICaller<RecentlyViewedRequest, BaseResponse<Unit>>() {
    override fun call(reqParam: RecentlyViewedRequest?): Response<BaseResponse<Unit>> {
        return apiService.request(reqParam).execute()
    }
}