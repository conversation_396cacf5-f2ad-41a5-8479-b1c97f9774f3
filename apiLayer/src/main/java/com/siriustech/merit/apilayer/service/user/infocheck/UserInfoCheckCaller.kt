package com.siriustech.merit.apilayer.service.user.infocheck

import com.core.network.base.RetrofitAPICaller
import com.core.network.model.BaseResponse
import com.siriustech.merit.apilayer.service.authentication.register.RegisterCaller
import javax.inject.Inject
import retrofit2.Response

/**
 * Created by He<PERSON> Htet
 */
class UserInfoCheckCaller @Inject constructor(
    private val apiService: UserInfoCheckApiService,
) : RetrofitAPICaller<UserInfoCheckRequest, BaseResponse<UserInfoCheckResponse>>() {
    override fun call(reqParam: UserInfoCheckRequest?): Response<BaseResponse<UserInfoCheckResponse>> {
        return apiService.request(reqParam).execute()
    }
}