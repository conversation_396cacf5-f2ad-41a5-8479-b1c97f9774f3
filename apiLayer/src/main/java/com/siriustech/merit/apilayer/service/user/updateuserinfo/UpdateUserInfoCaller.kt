package com.siriustech.merit.apilayer.service.user.updateuserinfo

import com.core.network.base.RetrofitAPICaller
import com.core.network.model.BaseResponse
import javax.inject.Inject
import retrofit2.Response

/**
 * Created by <PERSON><PERSON> <PERSON><PERSON>t
 */
class UpdateUserInfoCaller @Inject constructor(
    private val apiService: UpdateUserInfoApiService,
) : RetrofitAPICaller<UpdateUserInfoRequest, BaseResponse<UpdateUserInfoResponse>>() {
    override fun call(reqParam: UpdateUserInfoRequest?): Response<BaseResponse<UpdateUserInfoResponse>> {
        return apiService.
        request(reqParam).execute()
    }
}