package com.siriustech.merit.apilayer.service.wallet.deposit

import com.core.network.base.RetrofitAPICaller
import com.core.network.model.BaseResponse
import javax.inject.Inject
import retrofit2.Response

/**
 * Created by <PERSON><PERSON><PERSON>
 */
class DepositCaller @Inject constructor(
    private val apiService: DepositApiService
) : RetrofitAPICaller<DepositRequest,BaseResponse<DepositResponse>>() {
    override fun call(reqParam: DepositRequest?): Response<BaseResponse<DepositResponse>> {
        return apiService.request(reqParam).execute()
    }
}