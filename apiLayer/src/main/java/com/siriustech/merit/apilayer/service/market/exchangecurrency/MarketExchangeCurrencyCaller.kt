package com.siriustech.merit.apilayer.service.market.exchangecurrency

import com.core.network.base.RetrofitAPICaller
import com.core.network.model.BaseResponse
import javax.inject.Inject
import retrofit2.Response

/**
 * Created by <PERSON><PERSON><PERSON>t
 */
class MarketExchangeCurrencyCaller @Inject constructor(
    private val apiService: MarketCurrencyApiService
)  : RetrofitAPICaller<MarketCurrencyRequest,BaseResponse<MarketCurrencyListResponse>>(){
    override fun call(reqParam: MarketCurrencyRequest?): Response<BaseResponse<MarketCurrencyListResponse>> {
        return apiService.request(reqParam).execute()
    }
}
