package com.siriustech.merit.apilayer.service.history.list

import com.core.network.model.BaseResponse
import kotlinx.serialization.Serializable
import retrofit2.Call
import retrofit2.http.Body
import retrofit2.http.POST

/**
 * Created by <PERSON><PERSON> <PERSON><PERSON><PERSON>
 */

@Serializable
data class GetHistoryRequest(
    val orderSide: String,
    val orderStatus: String,
    val productCategoryList: List<String> = emptyList(),
    val orderDateFrom: Long = 0,
    val orderDateTo: Long = 0,
    val searchType: String = "",
    val keyword: String = "",
)

@Serializable
data class HistoryListResponse(
    val list : List<HistoryResponse>? = emptyList()
)

@Serializable
data class HistoryResponse(
    val orderId: Int,
    val orderDate: Long? = null,
    val orderSide: String? = null,
    val settleAmount: String? = null,
    val exchange: String? = null,
    val riskLevel: String? = null,
    val orderStatus: String? = null,
    val assetLogo: String? = null,
    val assetSymbol: String? = null,
    val instrumentId: Int? = null,
    val tradeDate : Long? = null
)


interface GetHistoryApiService {
    @POST("account/order/list")
    fun request(@Body request: GetHistoryRequest?): Call<BaseResponse<HistoryListResponse>>
}