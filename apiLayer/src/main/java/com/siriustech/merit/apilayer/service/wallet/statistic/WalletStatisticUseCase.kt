package com.siriustech.merit.apilayer.service.wallet.statistic

import com.core.network.base.AppUseCaseFlow
import com.core.network.base.Repository
import com.core.network.model.BaseResponse
import com.siriustech.merit.apilayer.service.market.exchangecurrency.MarketCurrencyListResponse
import com.siriustech.merit.apilayer.service.market.exchangecurrency.MarketCurrencyRequest
import dagger.hilt.android.scopes.ViewModelScoped
import javax.inject.Inject

/**
 * Created by <PERSON><PERSON><PERSON>
 */
@ViewModelScoped
class WalletStatisticUseCase @Inject constructor(
    repository: Repository<Unit?, BaseResponse<WalletStatisticResponse>>
) : AppUseCaseFlow<Unit?,WalletStatisticResponse>(repository){
}

