package com.siriustech.merit.apilayer.service.authentication.login

import com.core.network.model.BaseResponse
import kotlinx.serialization.Serializable
import retrofit2.Call
import retrofit2.http.Body
import retrofit2.http.POST

/**
 * Created by Hein Htet
 */
@Serializable
data class LoginUserRequest(
    val username: String,
    val password: String? = null, // PASSWORD
    val deviceId: String? = null,
    val otpType: String? = null,
    val token: String? = null, // 2FA
    val otp : String? = null, // 2FA
    val refCode : String? = null, // 2FA
    val pin : String? = null, // PIN
)

@Serializable
data class LoginUserResponse(
    val sessionId: String? = null,
    val token: String? = null,
    val require2Fa: Boolean? = false,
    val newDevice: Boolean? = false,
    val boOnboarding: Boolean? = false,
    val configured2Fa: Boolean? = false,
    val configuredPin: Boolean? = false,
    val email: String? = null,
)

interface LoginApiService {

    @POST("user/login")
    fun loginRequest(@Body request: LoginUserRequest?): Call<BaseResponse<LoginUserResponse>>

}