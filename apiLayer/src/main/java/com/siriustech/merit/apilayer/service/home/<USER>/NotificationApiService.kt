package com.siriustech.merit.apilayer.service.home.notification

import com.core.network.model.BaseResponse
import kotlinx.serialization.SerialName
import kotlinx.serialization.Serializable
import retrofit2.Call
import retrofit2.http.Body
import retrofit2.http.POST

/**
 * Created by <PERSON><PERSON><PERSON>t
 */

@Serializable
data class NotificationRequest(
    val limit: Int = 3,
     val messageType: String,
)


@Serializable
data class NotificationListResponse(
    val list: List<NotificationItemResponse>? = emptyList(),
)

@Serializable
data class NotificationItemResponse(
    val id: Int? = null,
    val title: String? = null,
    val isRead: Boolean = false,
    val datetime: Long? = null,
    val subTitle: String? = null,
    val content: String? = null,
    val messageType: String? = null,
    val businessType: String? = null,
    val tag: String? = null,
    val relateId: String? = null,
    val relateType: String? = null,
)

interface NotificationApiService {

    @POST("user/notification")
    fun request(@Body request: NotificationRequest?): Call<BaseResponse<NotificationListResponse>>
}