package com.siriustech.merit.apilayer.service.wallet.balancehistory

import com.core.network.base.AppUseCaseFlow
import com.core.network.base.Repository
import com.core.network.model.BaseResponse
import com.siriustech.merit.apilayer.service.market.exchangecurrency.MarketCurrencyListResponse
import com.siriustech.merit.apilayer.service.market.exchangecurrency.MarketCurrencyRequest
import dagger.hilt.android.scopes.ViewModelScoped
import javax.inject.Inject

/**
 * Created by <PERSON><PERSON>
 */
@ViewModelScoped
class WalletBalanceHistoryUseCase @Inject constructor(
    repository: Repository<WalletBalanceHistoryRequest, BaseResponse<WalletBalanceHistoryResponse>>
) : AppUseCaseFlow<WalletBalanceHistoryRequest,WalletBalanceHistoryResponse>(repository){
}

