package com.siriustech.merit.apilayer.service.requestdocsign

import com.core.network.base.RetrofitAPICaller
import com.core.network.model.BaseResponse
import javax.inject.Inject
import retrofit2.Response

/**
 * Created by <PERSON><PERSON><PERSON>t
 */

class RequestDocSignCaller @Inject constructor(private val apiService: RequestDocSignApiService) :
    RetrofitAPICaller<Unit, BaseResponse<RequestDocSignResponse>>() {
    override fun call(reqParam: Unit?): Response<BaseResponse<RequestDocSignResponse>> {
        return apiService.updateStatus().execute()
    }
}