package com.siriustech.merit.apilayer.service.user.sendchat

import com.core.network.base.RetrofitAPICaller
import com.core.network.model.BaseResponse
import javax.inject.Inject
import retrofit2.Response

/**
 * Created by <PERSON><PERSON><PERSON><PERSON>
 */
class SendChatMessageCaller @Inject constructor(private val apiService: SendChatMessageApiService) :RetrofitAPICaller<SendChatMessageRequest,BaseResponse<SendChatMessageResponse>>() {
    override fun call(reqParam: SendChatMessageRequest?): Response<BaseResponse<SendChatMessageResponse>> {
        return apiService.request(reqParam).execute()
    }
}