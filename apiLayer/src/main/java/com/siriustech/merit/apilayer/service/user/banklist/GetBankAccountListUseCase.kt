package com.siriustech.merit.apilayer.service.user.banklist

import com.core.network.base.AppUseCaseFlow
import com.core.network.base.Repository
import com.core.network.model.BaseResponse
import com.siriustech.merit.apilayer.service.wallet.balancehistory.WalletBalanceHistoryRequest
import com.siriustech.merit.apilayer.service.wallet.balancehistory.WalletBalanceHistoryResponse
import dagger.hilt.android.scopes.ViewModelScoped
import javax.inject.Inject

/**
 * Created by <PERSON><PERSON> <PERSON><PERSON><PERSON>
 */

@ViewModelScoped
class GetBankAccountListUseCase @Inject constructor(
    repository: Repository<Unit, BaseResponse<BankAccountsResponse>>
) : AppUseCaseFlow<Unit, BankAccountsResponse>(repository){
}
