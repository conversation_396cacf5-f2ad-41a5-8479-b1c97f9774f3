package com.siriustech.merit.apilayer.service.wallet.statistic

import com.core.network.base.RetrofitAPICaller
import com.core.network.model.BaseResponse
import javax.inject.Inject
import retrofit2.Response

/**
 * Created by <PERSON><PERSON><PERSON>
 */

class WalletStatisticCaller @Inject constructor(
    private val apiService: WalletStatisticApiService,
) : RetrofitAPICaller<Unit, BaseResponse<WalletStatisticResponse>>() {
    override fun call(reqParam: Unit?): Response<BaseResponse<WalletStatisticResponse>> {
        return apiService.request().execute()
    }
}