package com.siriustech.merit.apilayer.service.onboarding.updateagreement

import com.core.network.base.AppUseCaseFlow
import com.core.network.base.Repository
import com.core.network.model.BaseResponse
import dagger.hilt.android.scopes.ViewModelScoped
import javax.inject.Inject

// FLOW USE_CASE
@ViewModelScoped
class UpdateAgreementUseCase @Inject constructor(
    repository: Repository<UpdateAgreementRequest, BaseResponse<UpdateAgreementResponse>>,
) : AppUseCaseFlow<UpdateAgreementRequest, UpdateAgreementResponse>(repository)


