package com.siriustech.merit.apilayer.service.user.status

import com.core.network.base.AppUseCaseFlow
import com.core.network.base.Repository
import com.core.network.model.BaseResponse
import dagger.hilt.android.scopes.ViewModelScoped
import javax.inject.Inject

/**
 * Created by <PERSON><PERSON>
 */

@ViewModelScoped
class UpdateUserStatusUseCase @Inject constructor(
    repository: Repository<UpdateUserStatusRequest, BaseResponse<UpdateUserStatusResponse>>
) : AppUseCaseFlow<UpdateUserStatusRequest, UpdateUserStatusResponse>(repository){
}
