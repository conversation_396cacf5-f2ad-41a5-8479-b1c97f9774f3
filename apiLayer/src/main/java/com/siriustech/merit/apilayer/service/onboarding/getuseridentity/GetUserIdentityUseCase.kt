package com.siriustech.merit.apilayer.service.onboarding.getuseridentity

import com.core.network.base.AppUseCaseFlow
import com.core.network.base.Repository
import com.core.network.model.BaseResponse
import dagger.hilt.android.scopes.ViewModelScoped
import javax.inject.Inject

/**
 * Created by <PERSON><PERSON>
 */
// FLOW USE_CASE
@ViewModelScoped
class GetUserIdentityUseCase @Inject constructor(
    repository: Repository<GetUserIdentityRequest, BaseResponse<GetUserIdentityResponse>>,
) : AppUseCaseFlow<GetUserIdentityRequest, GetUserIdentityResponse>(repository)


