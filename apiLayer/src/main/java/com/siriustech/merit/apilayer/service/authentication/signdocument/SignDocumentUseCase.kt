package com.siriustech.merit.apilayer.service.authentication.signdocument

import com.core.network.base.AppUseCaseFlow
import com.core.network.base.Repository
import com.core.network.model.BaseResponse
import com.siriustech.merit.apilayer.service.authentication.resetauth.AuthResetRequest
import com.siriustech.merit.apilayer.service.authentication.resetauth.AuthResetResponse
import dagger.hilt.android.scopes.ViewModelScoped
import javax.inject.Inject

/**
 * Created by <PERSON><PERSON><PERSON>
 */

@ViewModelScoped
class SignDocumentUseCase @Inject constructor(
    repository: Repository<SignDocumentRequest, BaseResponse<SignDocumentResponse>>,
) : AppUseCaseFlow<SignDocumentRequest, SignDocumentResponse>(repository)