package com.siriustech.merit.apilayer.service.market.marketlistdetails

import com.core.network.base.AppUseCaseFlow
import com.core.network.base.Repository
import com.core.network.model.BaseResponse
import dagger.hilt.android.scopes.ViewModelScoped
import javax.inject.Inject

// FLOW USE_CASE
@ViewModelScoped
class MarketListDetailsUseCase @Inject constructor(
    repository: Repository<MarketInstrumentListRequest, BaseResponse<MarketInstrumentListResponse>>,
) : AppUseCaseFlow<MarketInstrumentListRequest, MarketInstrumentListResponse>(repository)
