package com.siriustech.merit.apilayer.service.wealthplan.modify

import com.core.network.model.BaseRequest
import com.core.network.model.BaseResponse
import kotlinx.serialization.Serializable
import retrofit2.Call
import retrofit2.http.Body
import retrofit2.http.PUT

/**
 * Created by <PERSON><PERSON><PERSON>t
 */

@Serializable
data class ModifyWealthPlanRequest(
    val id : Int? = null,
    val name: String? = null,
    val initialAmount: String? = null,
    val initialDate: Long? = null,
    val description: String? = null,
    val portfolio: List<ModifyWealthPlanPortfolioRequest> = emptyList(),
    val benchmarkList: List<Int> = emptyList(),
    val action: String, // ADD, EDIT, DUPLICATE, DELETE, RECALCULATE
)

@Serializable
data class ModifyWealthPlanPortfolioRequest(
    val instrumentId: Int,
    val allocation: String,
)

@Serializable
 class ModifyWealthPlanResponse{}


interface ModifyWealthPlanApiService {

    @PUT("user/wealth-plan")
    fun request(@Body request: ModifyWealthPlanRequest?): Call<BaseResponse<ModifyWealthPlanResponse>>
}