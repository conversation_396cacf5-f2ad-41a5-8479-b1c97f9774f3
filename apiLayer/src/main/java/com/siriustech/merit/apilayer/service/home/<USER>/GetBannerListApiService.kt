package com.siriustech.merit.apilayer.service.home.banner

import com.core.network.model.BaseResponse
import kotlinx.serialization.SerialName
import kotlinx.serialization.Serializable
import retrofit2.Call
import retrofit2.http.POST

/**
 * Created by <PERSON><PERSON><PERSON><PERSON>
 */

@Serializable
data class BannerListResponse(
    @SerialName("list")
    val list: List<BannerResponse>? = emptyList(),
)

@Serializable
data class BannerResponse(
    val id: String? = null,
    val title: String? = null,
    val imageFileKey: String? = null,
    val targetUrl: String? = null,
    val sortOrder: Int? = null,
    val isActive: Boolean? = null,
    val createdAt: Long? = null,
    val updatedAt: Long? = null,
    val startTime: Long? = null,
    val endTime: Long? = null,
)

interface GetBannerListApiService {

    @POST("content/banner/list")
    fun request(): Call<BaseResponse<BannerListResponse>>

}