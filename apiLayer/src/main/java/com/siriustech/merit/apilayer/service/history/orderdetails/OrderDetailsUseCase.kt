package com.siriustech.merit.apilayer.service.history.orderdetails

import com.core.network.base.AppUseCaseFlow
import com.core.network.base.Repository
import com.core.network.model.BaseResponse
import dagger.hilt.android.scopes.ViewModelScoped
import javax.inject.Inject

// FLOW USE_CASE
@ViewModelScoped
class OrderDetailsUseCase @Inject constructor(
    repository: Repository<OrderDetailsRequest, BaseResponse<OrderDetailsResponse>>,
) : AppUseCaseFlow<OrderDetailsRequest, OrderDetailsResponse>(repository)


