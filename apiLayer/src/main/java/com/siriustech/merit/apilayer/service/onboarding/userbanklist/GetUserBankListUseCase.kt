package com.siriustech.merit.apilayer.service.onboarding.userbanklist

import com.core.network.base.AppUseCaseFlow
import com.core.network.base.Repository
import com.core.network.model.BaseResponse
import dagger.hilt.android.scopes.ViewModelScoped
import javax.inject.Inject

// FLOW USE_CASE
@ViewModelScoped
class GetUserBankListUseCase @Inject constructor(
    repository: Repository<Unit, BaseResponse<UserBankListResponse>>,
) : AppUseCaseFlow<Unit, UserBankListResponse>(repository)


