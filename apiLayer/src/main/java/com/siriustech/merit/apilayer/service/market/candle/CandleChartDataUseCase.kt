package com.siriustech.merit.apilayer.service.market.candle

import com.core.network.base.AppUseCaseFlow
import com.core.network.base.BaseUseCaseFlow
import com.core.network.base.Repository
import com.core.network.model.BaseResponse
import dagger.hilt.android.scopes.ViewModelScoped
import javax.inject.Inject

/**
 * Created by <PERSON><PERSON>
 */
@ViewModelScoped
class CandleChartDataUseCase @Inject
constructor(
    repository: Repository<CandleChartDataRequest, BaseResponse<CandleChartDataResponse>>,
) : AppUseCaseFlow<CandleChartDataRequest, CandleChartDataResponse>(repository) {}