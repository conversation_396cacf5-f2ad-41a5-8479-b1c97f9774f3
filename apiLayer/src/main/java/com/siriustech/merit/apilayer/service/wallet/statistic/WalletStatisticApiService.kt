package com.siriustech.merit.apilayer.service.wallet.statistic

import com.core.network.model.BaseResponse
import kotlinx.serialization.Serializable
import retrofit2.Call
import retrofit2.http.POST

/**
 * Created by <PERSON><PERSON><PERSON><PERSON>
 */


@Serializable
data class WalletStatisticResponse(
    val mtdReturn : String? = null,
    val ytdReturn : String? = null,
    val itdReturn : String? = null,
    val beta : String? = null,
    val sharp : String? = null,
    val annualizedReturn : String? = null,
    val annualizedVolatility : String? = null,
    val asOf : Long? = null,
)

interface WalletStatisticApiService {

    @POST("wallet/statistic")
    fun request() : Call<BaseResponse<WalletStatisticResponse>>

}