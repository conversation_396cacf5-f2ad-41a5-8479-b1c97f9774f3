package com.siriustech.merit.apilayer.service.home.modifynotification

import com.core.network.model.BaseResponse
import com.siriustech.merit.apilayer.service.home.notification.NotificationListResponse
import kotlinx.serialization.Serializable
import retrofit2.Call
import retrofit2.http.Body
import retrofit2.http.POST

/**
 * Created by <PERSON><PERSON>t
 */


@Serializable
data class ModifyNotificationRequest(
    val idList: List<Int> = emptyList(),
    val isReadAll: Boolean = false,
    val messageType: String
)

@Serializable
 class ModifyNotificationResponse{

}

interface ModifyNotificationApiService {

    @POST("user/notification/read")
    fun request( @Body request: ModifyNotificationRequest?) : Call<BaseResponse<ModifyNotificationResponse>>
}