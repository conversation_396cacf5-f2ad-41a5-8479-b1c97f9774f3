package com.siriustech.merit.apilayer.service.onboarding.updatequestionnaire

import com.core.network.base.AppUseCaseFlow
import com.core.network.base.Repository
import com.core.network.model.BaseResponse
import dagger.hilt.android.scopes.ViewModelScoped
import javax.inject.Inject

/**
 * Created by <PERSON><PERSON>
 */
// FLOW USE_CASE
@ViewModelScoped
class UpdateQuestionnaireUseCase @Inject constructor(
    repository: Repository<QuestionnaireUpdateRequest, BaseResponse<QuestionnaireUpdateResponse>>,
) : AppUseCaseFlow<QuestionnaireUpdateRequest, QuestionnaireUpdateResponse>(repository)


