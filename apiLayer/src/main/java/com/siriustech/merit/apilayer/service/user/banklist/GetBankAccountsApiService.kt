package com.siriustech.merit.apilayer.service.user.banklist

import com.core.network.model.BaseResponse
import retrofit2.Call
import retrofit2.http.POST

/**
 * Created by Hein Htet
 */

data class BankAccountsResponse(
    val bankAccountList: List<BankAccountResponse>? = emptyList(),
)

data class BankAccountResponse(
    val id: Int? = null,
    val customerId: Int? = null,
    val accountType: String? = null,
    val bankName: String? = null,
    val accountName: String? = null,
    val accountNumber: String? = null,
    val swiftCode: String? = null,
    val currency: List<String>? = null,
    val bankAddress: BankAddressResponse? = null,
    val remark: String? = null,
    val isPrimary: Boolean? = null,
    val status: String? = null,
    val createdAt: Long? = null,
    val updateAt: Long? = null,
)

data class BankAddressResponse(
    val countryRegion: String? = null,
    val address: String? = null,
    val postCode: String? = null,
)


interface GetBankAccountsApiService {

    @POST("user/bank/list")
    fun request(): Call<BaseResponse<BankAccountsResponse>>
}