package com.siriustech.merit.apilayer.service.wealthplan.modify

import com.core.network.base.AppUseCaseFlow
import com.core.network.base.Repository
import com.core.network.model.BaseResponse
import com.siriustech.merit.apilayer.service.wealthplan.list.WealthPlanResponse
import dagger.hilt.android.scopes.ViewModelScoped
import javax.inject.Inject

/**
 * Created by <PERSON><PERSON>
 */

@ViewModelScoped
class ModifyWealthPlanUseCase @Inject constructor(
    repository: Repository<ModifyWealthPlanRequest, BaseResponse<ModifyWealthPlanResponse>>,
) : AppUseCaseFlow<ModifyWealthPlanRequest, ModifyWealthPlanResponse>(repository)