package com.siriustech.merit.apilayer.service.wallet.balancehistory

import com.core.network.model.BaseResponse
import kotlinx.serialization.Serializable
import retrofit2.Call
import retrofit2.http.Body
import retrofit2.http.POST

/**
 * Created by <PERSON><PERSON><PERSON><PERSON>
 */

@Serializable
data class WalletBalanceHistoryRequest(
    val period: String,
)

@Serializable
data class WalletBalanceHistoryResponse(
    val balances: List<WalletBalanceHistoryItemResponse>? = emptyList(),
)


@Serializable
data class WalletBalanceHistoryItemResponse(
    val d: String? = null,
    val b: String? = null,
    val r: String? = null,
    val rp: String? = null,
)

interface WalletBalanceHistoryApiService {


    @POST("wallet/balance/history")
    fun request(@Body request : WalletBalanceHistoryRequest?) : Call<BaseResponse<WalletBalanceHistoryResponse>>

}