package com.siriustech.merit.apilayer.service.wealthplan.performance

import com.core.network.model.BaseResponse
import kotlinx.serialization.Serializable
import retrofit2.Call
import retrofit2.http.Body
import retrofit2.http.POST

/**
 * Created by He<PERSON> <PERSON>tet
 */

@Serializable
data class WealthPlanPerformanceRequest(
    val wealthPlanId: Int,
    val period: String,
    val summaryBy: String,
)

@Serializable
data class WealthPlanPerformanceResponse(
    val overallData: List<WealthPlanPerformanceOverallResponse>? = emptyList(),
    val summaryList: List<WealthPlanSummaryListResponse>? = emptyList()
)

@Serializable
data class WealthPlanSummaryListResponse(
    val summaryName: String? = null,
    val assetList: List<WealthPlanSummaryResponse>? = emptyList(),
)


@Serializable
data class WealthPlanSummaryResponse(
    val symbol: String? = null,
    val name: String? = null,
    val logo: String? = null,
    val exchange: String? = null,
    val riskLevel: String? = null,
    val currency: String? = null,
    val dataList: List<WealthPlanPerformanceOverallResponse>? = null,
)

@Serializable
data class WealthPlanPerformanceOverallResponse(
    val d: String? = null,
    val v: String? = null,
    val r: String? = null,
)

interface WealthPlanPerformanceApiService {


    @POST("user/wealth-plan/performance")
    fun request(@Body request: WealthPlanPerformanceRequest?): Call<BaseResponse<WealthPlanPerformanceResponse>>
}