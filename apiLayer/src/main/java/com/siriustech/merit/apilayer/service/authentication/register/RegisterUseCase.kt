package com.siriustech.merit.apilayer.service.authentication.register

import com.core.network.base.AppUseCaseFlow
import com.core.network.base.Repository
import com.core.network.model.BaseResponse
import dagger.hilt.android.scopes.ViewModelScoped
import javax.inject.Inject

// FLOW USE_CASE
@ViewModelScoped
class RegisterUseCase @Inject constructor(
    repository: Repository<RegisterRequest, BaseResponse<RegisterResponse>>,
) : AppUseCaseFlow<RegisterRequest, RegisterResponse>(repository)
