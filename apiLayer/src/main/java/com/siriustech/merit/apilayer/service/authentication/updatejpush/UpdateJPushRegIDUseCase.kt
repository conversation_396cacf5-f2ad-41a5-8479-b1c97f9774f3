package com.siriustech.merit.apilayer.service.authentication.updatejpush

import com.core.network.base.AppUseCaseFlow
import com.core.network.base.Repository
import com.core.network.model.BaseResponse
import com.siriustech.merit.apilayer.service.authentication.resetauth.AuthResetRequest
import com.siriustech.merit.apilayer.service.authentication.resetauth.AuthResetResponse
import dagger.hilt.android.scopes.ViewModelScoped
import javax.inject.Inject

/**
 * Created by <PERSON><PERSON><PERSON>
 */

@ViewModelScoped
class UpdateJPushRegIDUseCase @Inject constructor(
    repository: Repository<UpdateJPushRegIDRequest, BaseResponse<UpdateJPushRegIDResponse>>,
) : AppUseCaseFlow<UpdateJPushRegIDRequest, UpdateJPushRegIDResponse>(repository)