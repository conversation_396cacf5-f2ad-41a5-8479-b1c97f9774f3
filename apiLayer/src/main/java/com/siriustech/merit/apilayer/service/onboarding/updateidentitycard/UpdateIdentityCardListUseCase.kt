package com.siriustech.merit.apilayer.service.onboarding.updateidentitycard

import com.core.network.base.AppUseCaseFlow
import com.core.network.base.Repository
import com.core.network.model.BaseResponse
import dagger.hilt.android.scopes.ViewModelScoped
import javax.inject.Inject

/**
 * Created by <PERSON><PERSON>
 */

// FLOW USE_CASE
@ViewModelScoped
class UpdateIdentityCardListUseCase @Inject constructor(
    repository: Repository<UpdateIdentityCardListRequest, BaseResponse<UpdateIdentityCardListResponse>>,
) : AppUseCaseFlow<UpdateIdentityCardListRequest, UpdateIdentityCardListResponse>(repository)

