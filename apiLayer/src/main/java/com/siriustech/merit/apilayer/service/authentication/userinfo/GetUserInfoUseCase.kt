package com.siriustech.merit.apilayer.service.authentication.userinfo

import com.core.network.base.AppUseCaseFlow
import com.core.network.base.Repository
import com.core.network.model.BaseResponse
import dagger.hilt.android.scopes.ViewModelScoped
import javax.inject.Inject

// FLOW USE_CASE
@ViewModelScoped
class GetUserInfoUseCase @Inject constructor(
    repository: Repository<Unit, BaseResponse<UserInfoResponse>>,
) : AppUseCaseFlow<Unit, UserInfoResponse>(repository)
