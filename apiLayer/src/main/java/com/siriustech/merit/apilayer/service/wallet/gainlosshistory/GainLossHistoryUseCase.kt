package com.siriustech.merit.apilayer.service.wallet.gainlosshistory

import com.core.network.base.AppUseCaseFlow
import com.core.network.base.Repository
import com.core.network.model.BaseResponse
import com.siriustech.merit.apilayer.service.wallet.deposit.DepositRequest
import com.siriustech.merit.apilayer.service.wallet.deposit.DepositResponse
import dagger.hilt.android.scopes.ViewModelScoped
import javax.inject.Inject

/**
 * Created by <PERSON><PERSON>
 */

@ViewModelScoped
class GainLossHistoryUseCase @Inject constructor(
    repository: Repository<GainLossHistoryRequest, BaseResponse<GainLossHistoryResponse>>,
) : AppUseCaseFlow<GainLossHistoryRequest, GainLossHistoryResponse>(repository) {
}