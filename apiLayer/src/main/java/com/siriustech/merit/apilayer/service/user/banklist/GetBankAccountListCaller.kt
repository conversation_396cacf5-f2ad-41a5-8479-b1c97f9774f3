package com.siriustech.merit.apilayer.service.user.banklist

import com.core.network.base.RetrofitAPICaller
import com.core.network.model.BaseResponse
import javax.inject.Inject
import retrofit2.Response

/**
 * Created by <PERSON><PERSON><PERSON><PERSON>
 */
class GetBankAccountListCaller @Inject constructor(private val apiService: GetBankAccountsApiService) : RetrofitAPICaller<Unit,BaseResponse<BankAccountsResponse>>() {
    override fun call(reqParam: Unit?): Response<BaseResponse<BankAccountsResponse>> {
        return apiService.request().execute()
    }
}