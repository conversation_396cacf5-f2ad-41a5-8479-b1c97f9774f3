package com.siriustech.merit.apilayer.service.home.deletenotification

import com.core.network.base.AppUseCaseFlow
import com.core.network.base.Repository
import com.core.network.model.BaseResponse
import com.siriustech.merit.apilayer.service.home.notification.NotificationListResponse
import dagger.hilt.android.scopes.ViewModelScoped
import javax.inject.Inject

// FLOW USE_CASE
@ViewModelScoped
class DeleteNotificationUseCase @Inject constructor(
    repository: Repository<DeleteNotificationRequest, BaseResponse<DeleteNotificationResponse>>,
) : AppUseCaseFlow<DeleteNotificationRequest, DeleteNotificationResponse>(repository)


