package com.siriustech.merit.apilayer.service.authentication.auth

import com.core.network.base.AppUseCaseFlow
import com.core.network.base.Repository
import com.core.network.model.BaseResponse
import dagger.hilt.android.scopes.ViewModelScoped
import javax.inject.Inject

// FLOW USE_CASE
@ViewModelScoped
class ConfigurePinPassUseCase @Inject constructor(
    repository: Repository<ConfigurePinPassRequest, BaseResponse<Unit>>,
) : AppUseCaseFlow<ConfigurePinPassRequest, Unit>(repository)
