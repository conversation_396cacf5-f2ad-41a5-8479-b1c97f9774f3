package com.siriustech.merit.apilayer.service.authentication.common.uploadfile

import com.core.network.base.AppUseCaseFlow
import com.core.network.base.Repository
import com.core.network.model.BaseResponse
import dagger.hilt.android.scopes.ViewModelScoped
import javax.inject.Inject
import okhttp3.RequestBody

// FLOW USE_CASE
@ViewModelScoped
class UploadFileUseCase @Inject constructor(
    repository: Repository<RequestBody, BaseResponse<UploadFileResponse>>,
) : AppUseCaseFlow<RequestBody, UploadFileResponse>(repository)
