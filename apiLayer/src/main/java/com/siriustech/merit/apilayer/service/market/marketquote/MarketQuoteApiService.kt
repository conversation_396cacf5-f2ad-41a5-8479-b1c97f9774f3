package com.siriustech.merit.apilayer.service.market.marketquote

import com.core.network.model.BaseResponse
import kotlinx.serialization.Serializable
import retrofit2.Call
import retrofit2.http.Body
import retrofit2.http.POST

/**
 * Created by <PERSON><PERSON> <PERSON>tet
 */

@Serializable
data class MarketQuoteRequest(
    val instrumentId : Int
)

@Serializable
data class MarketQuoteResponse(
    val averagePrice: String? = null,
    val highPrice: String? = null,
    val totalVolume: String? = null,
    val averageBuyPrice: String? = null,
    val averageSellPrice: String? = null,
    val lastMatchedTime: Long? = null,
    val priceChange: String? = null,
    val minimumOrder: String? = null,
    val totalAmount: String? = null,
    val ceilingPrice: String? = null,
    val floorPrice: String? = null,
    val lowPrice: String? = null,
    val marketStatus: String? = null,
    val openPrice: String? = null,
    val price: String? = null,
    val priceChangeRate: String? = null,
    val priorClosePrice: String? = null
)



interface MarketQuoteApiService {
    @POST("market/quote")
    fun request(@Body request : MarketQuoteRequest?) : Call<BaseResponse<MarketQuoteResponse>>
}