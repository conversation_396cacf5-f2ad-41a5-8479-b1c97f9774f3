package com.siriustech.merit.apilayer.service.authentication.liveness

import com.core.network.model.BaseResponse
import kotlinx.serialization.Serializable
import retrofit2.Call
import retrofit2.http.Body
import retrofit2.http.POST

@Serializable
data class LivenessCheckResquest(
    val videoDosKey: String,
    val imageDosKey: String,
    val idType: String,
    val idCode: String,
)

@Serializable
data class LivenessCheckResponse(
    val pass: Boolean,
)

interface LivenessApiService {

    @POST("common/liveness")
     fun request(@Body request: LivenessCheckResquest?): Call<BaseResponse<LivenessCheckResponse>>
}
