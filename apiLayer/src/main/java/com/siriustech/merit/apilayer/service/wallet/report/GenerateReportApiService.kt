package com.siriustech.merit.apilayer.service.wallet.report

import com.core.network.model.BaseResponse
import kotlinx.serialization.Serializable
import retrofit2.Call
import retrofit2.http.Body
import retrofit2.http.POST

/**
 * Created by <PERSON><PERSON> <PERSON><PERSON><PERSON>
 */

@Serializable
data class GenerateReportRequest(
    val statementDate: Long,
    val statementType: String,
    val currency: String,
    val exchangeRate: String,
    val reportType: String,
)


@Serializable
data class GenerateReportResponse(
    val message : String? = null,
    val fileKey: String? = null,
)

interface GenerateReportApiService {

    @POST("user/statement/generate")
    fun request(@Body request: GenerateReportRequest?): Call<BaseResponse<GenerateReportResponse>>
}