package com.siriustech.merit.apilayer.service.wallet.withdraw

import com.core.network.base.RetrofitAPICaller
import com.core.network.model.BaseResponse
import javax.inject.Inject
import retrofit2.Response

/**
 * Created by <PERSON><PERSON><PERSON>
 */
class WithdrawCaller @Inject constructor(
    private val apiService: WithdrawApiService
) : RetrofitAPICaller<WithdrawRequest,BaseResponse<WithdrawResponse>>() {
    override fun call(reqParam: WithdrawRequest?): Response<BaseResponse<WithdrawResponse>> {
        return apiService.request(reqParam).execute()
    }
}