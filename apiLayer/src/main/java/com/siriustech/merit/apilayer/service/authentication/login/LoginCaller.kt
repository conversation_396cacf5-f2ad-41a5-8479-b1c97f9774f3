package com.siriustech.merit.apilayer.service.authentication.login

import com.core.network.base.RetrofitAPICaller
import com.core.network.model.BaseResponse
import retrofit2.Response
import javax.inject.Inject

class LoginCaller @Inject constructor(
    private val apiService: LoginApiService
) : RetrofitAPICaller<LoginUserRequest, BaseResponse<LoginUserResponse>>() {
    override fun call(reqParam: LoginUserRequest?): Response<BaseResponse<LoginUserResponse>> {
        return apiService.loginRequest(reqParam).execute()
    }
}