package com.siriustech.merit.apilayer.service.authentication.common.ocr

import com.core.network.model.BaseResponse
import kotlinx.serialization.Serializable
import retrofit2.Call
import retrofit2.http.Body
import retrofit2.http.POST

/**
 * Created by <PERSON><PERSON><PERSON>
 */

@Serializable
data class OCRRequest(
    val idType: String,
    val idDosKey: String,
)

@Serializable
data class OCRResponse(
    val idNumber: String,
)


interface OCRApiService {

    @POST("common/ocr")
    fun request(@Body request: OCRRequest?): Call<BaseResponse<OCRResponse>>
}