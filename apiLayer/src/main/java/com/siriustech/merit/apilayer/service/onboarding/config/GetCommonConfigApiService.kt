package com.siriustech.merit.apilayer.service.onboarding.config

import com.core.network.model.BaseResponse
import kotlinx.serialization.Serializable
import retrofit2.Call
import retrofit2.http.POST

/**
 * Created by <PERSON><PERSON><PERSON>
 */

@Serializable
data class CommonConfigResponse(
    val supportMobile: String? = "",
    val supportEmail: String? = "",
)


interface GetCommonConfigApiService {
    @POST("common/config")
    fun request(): Call<BaseResponse<CommonConfigResponse>>
}