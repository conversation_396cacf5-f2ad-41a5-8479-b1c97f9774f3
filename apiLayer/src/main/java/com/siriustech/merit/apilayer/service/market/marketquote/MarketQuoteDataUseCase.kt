package com.siriustech.merit.apilayer.service.market.marketquote

import com.core.network.base.AppUseCaseFlow
import com.core.network.base.Repository
import com.core.network.model.BaseResponse
import dagger.hilt.android.scopes.ViewModelScoped
import javax.inject.Inject

/**
 * Created by <PERSON><PERSON>
 */

@ViewModelScoped
class MarketQuoteDataUseCase @Inject constructor(
    repository: Repository<MarketQuoteRequest, BaseResponse<MarketQuoteResponse>>,
) : AppUseCaseFlow<MarketQuoteRequest, MarketQuoteResponse>(repository)
