package com.siriustech.merit.apilayer.service.market.marketprofile

import com.core.network.base.RetrofitAPICaller
import com.core.network.model.BaseResponse
import javax.inject.Inject
import retrofit2.Response

/**
 * Created by <PERSON><PERSON><PERSON>t
 */

class MarketInstrumentDetailsCaller @Inject constructor(private val apiService: MarketInstrumentDetailsApiService) : RetrofitAPICaller<MarketInstrumentDetailsRequest,BaseResponse<MarketInstrumentDetailsResponse>>() {
    override fun call(reqParam: MarketInstrumentDetailsRequest?): Response<BaseResponse<MarketInstrumentDetailsResponse>> {
        return apiService.request(reqParam).execute()
    }
}