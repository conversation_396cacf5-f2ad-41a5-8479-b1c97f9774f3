package com.siriustech.merit.apilayer.service.user.infocheck

import com.core.network.base.AppUseCaseFlow
import com.core.network.base.Repository
import com.core.network.model.BaseResponse
import com.siriustech.merit.apilayer.service.wallet.balancehistory.WalletBalanceHistoryRequest
import com.siriustech.merit.apilayer.service.wallet.balancehistory.WalletBalanceHistoryResponse
import dagger.hilt.android.scopes.ViewModelScoped
import javax.inject.Inject

/**
 * Created by <PERSON><PERSON><PERSON>
 */

@ViewModelScoped
class UserInfoCheckUseCase @Inject constructor(
    repository: Repository<UserInfoCheckRequest, BaseResponse<UserInfoCheckResponse>>
) : AppUseCaseFlow<UserInfoCheckRequest, UserInfoCheckResponse>(repository){
}
