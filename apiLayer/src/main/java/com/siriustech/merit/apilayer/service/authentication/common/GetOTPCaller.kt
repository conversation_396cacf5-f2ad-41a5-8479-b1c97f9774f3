package com.siriustech.merit.apilayer.service.authentication.common

import com.core.network.base.RetrofitAPICaller
import com.core.network.model.BaseResponse
import retrofit2.Response
import javax.inject.Inject

class GetOTPCaller @Inject constructor(
    private val apiService: GetOTPApiService
) : RetrofitAPICaller<GetOTPRequest, BaseResponse<GetOTPResponse>>() {
    override fun call(reqParam: GetOTPRequest?): Response<BaseResponse<GetOTPResponse>> {
        return apiService.request(reqParam).execute()
    }
}