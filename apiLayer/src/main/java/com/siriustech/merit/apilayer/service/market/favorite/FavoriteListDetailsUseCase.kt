package com.siriustech.merit.apilayer.service.market.favorite

import com.core.network.base.AppUseCaseFlow
import com.core.network.base.Repository
import com.core.network.model.BaseResponse
import com.siriustech.merit.apilayer.service.market.marketlistdetails.MarketInstrumentListResponse
import dagger.hilt.android.scopes.ViewModelScoped
import javax.inject.Inject

// FLOW USE_CASE
@ViewModelScoped
class FavoriteListDetailsUseCase @Inject constructor(
    repository: Repository<Unit, BaseResponse<MarketInstrumentListResponse>>,
) : AppUseCaseFlow<Unit, MarketInstrumentListResponse>(repository)
