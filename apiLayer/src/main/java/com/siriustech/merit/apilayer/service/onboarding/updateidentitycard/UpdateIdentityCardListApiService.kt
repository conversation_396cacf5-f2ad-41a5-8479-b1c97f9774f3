package com.siriustech.merit.apilayer.service.onboarding.updateidentitycard

import com.core.network.model.BaseResponse
import kotlinx.serialization.Serializable
import retrofit2.Call
import retrofit2.http.Body
import retrofit2.http.PUT

/**
 * Created by <PERSON><PERSON><PERSON><PERSON>
 */

@Serializable
data class UpdateIdentityCardListRequest(
    val region : String,
    val cardList : List<IdentityCardRequest>
)

@Serializable
 class UpdateIdentityCardListResponse{}


@Serializable
data class IdentityCardRequest(
    val cardNumber : String,
    val cardImage : String,
    val cardType : String
)

interface UpdateIdentityCardListApiService {
    @PUT("user/identity")
    fun updateIdentityCardList(@Body request: UpdateIdentityCardListRequest?) : Call<BaseResponse<UpdateIdentityCardListResponse>>
}