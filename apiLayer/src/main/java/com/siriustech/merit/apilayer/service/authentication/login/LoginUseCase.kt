package com.siriustech.merit.apilayer.service.authentication.login

import com.core.network.base.AppUseCaseFlow
import com.core.network.base.Repository
import com.core.network.model.BaseResponse
import dagger.hilt.android.scopes.ViewModelScoped
import javax.inject.Inject

// FLOW USE_CASE
@ViewModelScoped
class LoginUseCase @Inject constructor(
    repository: Repository<LoginUserRequest, BaseResponse<LoginUserResponse>>,
) : AppUseCaseFlow<LoginUserRequest, LoginUserResponse>(repository)
