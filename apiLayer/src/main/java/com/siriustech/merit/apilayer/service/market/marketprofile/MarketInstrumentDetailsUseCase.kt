package com.siriustech.merit.apilayer.service.market.marketprofile

import com.core.network.base.AppUseCaseFlow
import com.core.network.base.Repository
import com.core.network.model.BaseResponse
import dagger.hilt.android.scopes.ViewModelScoped
import javax.inject.Inject

/**
 * Created by <PERSON><PERSON>
 */
@ViewModelScoped
class MarketInstrumentDetailsUseCase @Inject constructor(
    repository: Repository<MarketInstrumentDetailsRequest, BaseResponse<MarketInstrumentDetailsResponse>>,
) : AppUseCaseFlow<MarketInstrumentDetailsRequest, MarketInstrumentDetailsResponse>(repository)
