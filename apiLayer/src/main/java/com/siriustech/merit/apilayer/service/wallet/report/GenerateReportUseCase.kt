package com.siriustech.merit.apilayer.service.wallet.report

import com.core.network.base.AppUseCaseFlow
import com.core.network.base.Repository
import com.core.network.model.BaseResponse
import dagger.hilt.android.scopes.ViewModelScoped
import javax.inject.Inject

/**
 * Created by <PERSON><PERSON><PERSON>
 */

@ViewModelScoped
class GenerateReportUseCase @Inject constructor(
    repository: Repository<GenerateReportRequest, BaseResponse<GenerateReportResponse>>,
) : AppUseCaseFlow<GenerateReportRequest, GenerateReportResponse>(repository) {
}