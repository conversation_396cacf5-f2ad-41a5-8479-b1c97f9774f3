package com.siriustech.merit.apilayer.service.history.orderdetails

import com.core.network.base.RetrofitAPICaller
import com.core.network.model.BaseResponse
import javax.inject.Inject
import retrofit2.Response

/**
 * Created by <PERSON><PERSON><PERSON><PERSON>
 */
class OrderDetailsCaller @Inject constructor(private val apiService: OrderDetailsApiService) : RetrofitAPICaller<OrderDetailsRequest,BaseResponse<OrderDetailsResponse>>() {
    override fun call(reqParam: OrderDetailsRequest?): Response<BaseResponse<OrderDetailsResponse>> {
        return apiService.request(reqParam).execute()
    }
}

