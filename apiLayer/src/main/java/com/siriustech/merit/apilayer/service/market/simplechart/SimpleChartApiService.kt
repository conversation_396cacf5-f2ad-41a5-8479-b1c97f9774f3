package com.siriustech.merit.apilayer.service.market.simplechart

import com.core.network.model.BaseResponse
import kotlinx.serialization.Serializable
import retrofit2.Call
import retrofit2.http.Body
import retrofit2.http.POST

/**
 * Created by <PERSON><PERSON><PERSON><PERSON>
 */


@Serializable
data class SimpleChartRequest(
    val instrumentId: Int,
    val interval: String,
)

@Serializable
data class SimpleChartResponse(
    val candles: List<List<String>>? = emptyList(),
)

interface SimpleChartApiService {
    @POST("market/chart/simple")
    fun request(@Body request: SimpleChartRequest?) : Call<BaseResponse<SimpleChartResponse>>
}