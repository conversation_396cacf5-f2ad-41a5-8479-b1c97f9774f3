package com.siriustech.merit.apilayer.service.market.recentlyviewed

import com.core.network.model.BaseResponse
import com.siriustech.merit.apilayer.service.market.marketlistdetails.MarketInstrumentListResponse
import retrofit2.Call
import retrofit2.http.POST

/**
 * Created by <PERSON><PERSON>
 */


interface GetRecentlyViewedListApiService {

    @POST("user/instrument/view")
    fun request(): Call<BaseResponse<MarketInstrumentListResponse>>

}