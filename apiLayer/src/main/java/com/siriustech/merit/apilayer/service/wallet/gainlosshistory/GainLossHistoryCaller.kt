package com.siriustech.merit.apilayer.service.wallet.gainlosshistory

import com.core.network.base.RetrofitAPICaller
import com.core.network.model.BaseResponse
import javax.inject.Inject
import retrofit2.Response

/**
 * Created by <PERSON><PERSON><PERSON>t
 */
class GainLossHistoryCaller @Inject constructor(
    private val apiService: GainLossHistoryApiService,
) : RetrofitAPICaller<GainLossHistoryRequest, BaseResponse<GainLossHistoryResponse>>() {
    override fun call(reqParam: GainLossHistoryRequest?): Response<BaseResponse<GainLossHistoryResponse>> {
        return apiService.request(reqParam).execute()
    }
}