package com.siriustech.merit.apilayer.service.wallet.report

import com.core.network.base.RetrofitAPICaller
import com.core.network.model.BaseResponse
import javax.inject.Inject
import retrofit2.Response

/**
 * Created by <PERSON><PERSON><PERSON><PERSON>
 */
class GenerateReportCaller @Inject constructor(private val apiService: GenerateReportApiService) :
    RetrofitAPICaller<GenerateReportRequest, BaseResponse<GenerateReportResponse>>() {
    override fun call(reqParam: GenerateReportRequest?): Response<BaseResponse<GenerateReportResponse>> {
        return apiService.request(reqParam).execute()
    }
}