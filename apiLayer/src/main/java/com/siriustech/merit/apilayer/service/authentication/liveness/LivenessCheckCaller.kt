package com.siriustech.merit.apilayer.service.authentication.liveness

import com.core.network.base.RetrofitAPICaller
import com.core.network.model.BaseResponse
import retrofit2.Response
import javax.inject.Inject

class LivenessCheckCaller @Inject constructor(
    private val apiService: LivenessApiService
) : RetrofitAPICaller<LivenessCheckResquest, BaseResponse<LivenessCheckResponse>>() {
    override fun call(reqParam: LivenessCheckResquest?): Response<BaseResponse<LivenessCheckResponse>> {
        return apiService.request(reqParam).execute()
    }
}