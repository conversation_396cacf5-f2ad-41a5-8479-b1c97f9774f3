package com.siriustech.merit.apilayer.service.market.instrumentsearch

import com.core.network.base.RetrofitAPICaller
import com.core.network.model.BaseResponse
import com.siriustech.merit.apilayer.service.market.marketlistdetails.MarketInstrumentListResponse
import javax.inject.Inject
import retrofit2.Response

/**
 * Created by <PERSON><PERSON>
 */
class InstrumentSearchCaller @Inject constructor(
    private val apiService : InstrumentSearchApiService
) : RetrofitAPICaller<InstrumentSearchRequest,BaseResponse<MarketInstrumentListResponse>>(){
    override fun call(reqParam: InstrumentSearchRequest?): Response<BaseResponse<MarketInstrumentListResponse>> {
        return apiService.request(reqParam).execute()
    }
}