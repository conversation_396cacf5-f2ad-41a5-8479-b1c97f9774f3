package com.siriustech.merit.apilayer.service.onboarding.updatecustomerprofile

import com.core.network.model.BaseResponse
import kotlinx.serialization.Serializable
import retrofit2.Call
import retrofit2.http.Body
import retrofit2.http.PUT

@Serializable
data class CustomerProfileRequest(
    val region: String,
    val chineseName: String,
    val englishName: String,
    val gender: String,
    val dateOfBirth: String,
    val maritalStatus: String,
    val educationLevel: String,
    val additionalPhoneNumber: String,
    val deliveryMethod: String,
    val residentialAddress: String,
    val residentialAddressRegion: String,
    val residentialAddressPostalCode: String,
    val mainlingAddressRegion: String,
    val mainlingAddress: String,
    val mainlingAddressPostalCode: String,
    val accountStatementLanguage: String,
//    val base: CustomerProfileBase,
    val tagList: List<String>,
    val mainRegion: String,
)

@Serializable
data class CustomerProfileBase(
    val phoneNumber: String? = null,
    val email: String,
)

interface UpdateCustomerProfileApiService {
    @PUT("user/profile")
    fun getCustomerProfile(@Body request: CustomerProfileRequest?): Call<BaseResponse<Unit>>
}