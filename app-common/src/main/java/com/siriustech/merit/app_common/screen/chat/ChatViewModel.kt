package com.siriustech.merit.app_common.screen.chat

import android.content.Context
import android.net.Uri
import androidx.compose.runtime.mutableStateListOf
import androidx.compose.runtime.snapshots.SnapshotStateList
import com.core.network.base.getError
import com.siriustech.merit.apilayer.service.authentication.common.uploadfile.UploadFileUseCase
import com.siriustech.merit.apilayer.service.user.chat.ChatListRequest
import com.siriustech.merit.apilayer.service.user.chat.ChatListUseCase
import com.siriustech.merit.apilayer.service.user.sendchat.SendChatMessageFileRequest
import com.siriustech.merit.apilayer.service.user.sendchat.SendChatMessageRequest
import com.siriustech.merit.apilayer.service.user.sendchat.SendChatMessageUseCase
import com.siriustech.merit.apilayer.service.user.userinfo.GetUserBasicInfoUseCase
import com.siriustech.merit.app_common.Constants
import com.siriustech.merit.app_common.Constants.CHAT_STATUS_BEFORE
import com.siriustech.merit.app_common.Constants.INTERVAL_API_CALL_IN_SEC
import com.siriustech.merit.app_common.data.CommonSharedPreferences
import com.siriustech.merit.app_common.data.display.UserBasicInfoDisplay
import com.siriustech.merit.app_common.ext.toEpochMilli
import com.siriustech.merit.app_common.mapper.ChatMapper.mapToChatMessageListDisplay
import com.siriustech.merit.app_common.screen.chat.component.AttachmentDisplayModel
import com.siriustech.merit.app_common.screen.chat.component.ChatMessageDisplayModel
import com.siriustech.merit.app_common.screen.chat.component.MessageType
import com.siriustech.merit.app_common.screen.chat.component.SenderRoleType
import com.siriustech.merit.app_common.theme.AppAction
import com.siriustech.merit.app_common.theme.AppViewModel
import com.siriustech.merit.app_common.utils.ErrorCode.mapToErrorDisplay
import com.siriustech.merit.app_common.utils.formatFileSize
import com.siriustech.merit.app_common.utils.getFileSize
import dagger.hilt.android.lifecycle.HiltViewModel
import java.io.File
import javax.inject.Inject
import javax.inject.Named
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.delay
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.catch
import kotlinx.coroutines.flow.collectLatest
import kotlinx.coroutines.flow.onCompletion
import kotlinx.coroutines.flow.onStart
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import okhttp3.RequestBody
import org.threeten.bp.LocalDate
import org.threeten.bp.LocalDateTime
import org.threeten.bp.ZoneOffset
import timber.log.Timber

/**
 * Created by Hein Htet
 */
@HiltViewModel
class ChatViewModel @Inject constructor(
    private val getUserBasicInfoUseCase: GetUserBasicInfoUseCase,
    private val chatListUseCase: ChatListUseCase,
    private val uploadFileUseCase: UploadFileUseCase,
    @Named("BaseUrl") private val baseUrl: String,
    private val commonSharedPreferences: CommonSharedPreferences,
    private val sendChatMessageUseCase: SendChatMessageUseCase,
) : AppViewModel() {


    private var _showLoading = MutableStateFlow(false)
    private var _message = MutableStateFlow("")
    private val _chatMessages = mutableStateListOf<ChatMessageDisplayModel>().apply {
//        addAll(ChatMapper.generateMockChatData())
    }
    private val _userInfo = MutableStateFlow(UserBasicInfoDisplay())
    private val _uploadedFiles = mutableStateListOf<AttachmentDisplayModel>()
    private val _noMoreData = MutableStateFlow(false)

    private val _isLoadingMore = MutableStateFlow(false)

    override fun onTriggerActions(action: AppAction) {
        when (action) {
            is ChatAction.ChatInputTextChanged -> {
                if (_message.value.length < 500) {
                    _message.value = action.text
                }
            }

            is ChatAction.UserInfoLoaded -> {
                onListenChatMessage()
            }

            is ChatAction.OnUploadFile -> {
                inputs.onUploadFile(action.context, action.file, action.uri, action.requestBody)
            }

            is ChatAction.OnRemoveUploadedFile -> {
                _uploadedFiles.remove(action.chatMessageDisplayModel)
            }

            is ChatAction.OnSendMessage -> {
                inputs.onSendMessage()
            }

            is ChatAction.OnLoadPreviousChatHistory -> {
                val firstMessage = _chatMessages.firstOrNull()
                if (firstMessage != null && !_isLoadingMore.value && !_noMoreData.value) {
                    _isLoadingMore.value = true
                    scope.launch {
                        getChatList(
                            time = firstMessage.createdAt,
                            isLoadMore = true
                        )
                    }
                    Timber.d("LOAD_MORE_IS_FIRST_ITEM $firstMessage")
                }
            }
        }
        super.onTriggerActions(action)
    }

    override val inputs = ChatInputs()
    override val outputs = ChatOutputs()


    inner class ChatInputs : BaseInputs() {

        init {
            scope.launch {
                getChatList(
                    time = LocalDateTime.now().toInstant(ZoneOffset.UTC).toEpochMilli()
                )
            }
        }

        fun onGetUserInfo() = getUserInfo()

        fun onSendMessage() {
            if (_message.value.isNotEmpty() || _uploadedFiles.isNotEmpty()) {
                sendMessage()
            }
        }

        fun onUploadFile(context: Context, file: File, uri: Uri, requestBody: RequestBody) {
            uploadFile(context, file, uri, requestBody)
        }
    }

    inner class ChatOutputs : BaseOutputs() {

        val userInfo: StateFlow<UserBasicInfoDisplay>
            get() = _userInfo

        val messages: SnapshotStateList<ChatMessageDisplayModel>
            get() = _chatMessages

        val message: StateFlow<String>
            get() = _message

        val showLoading: StateFlow<Boolean>
            get() = _showLoading

        val loadingMore: StateFlow<Boolean>
            get() = _isLoadingMore

        val uploadedFiles: SnapshotStateList<AttachmentDisplayModel>
            get() = _uploadedFiles
    }

    private fun onListenChatMessage() {
        scope.launch(Dispatchers.IO) {
            while (true) {
                if (!_showLoading.value) {
//                    getChatList(true)
                }
                delay(INTERVAL_API_CALL_IN_SEC * 1000)
            }
        }
    }


    fun getUserInfo() {
        scope.launch {
            getUserBasicInfoUseCase(param = Unit)
                .onStart { inputs.emitLoading(true) }
                .onCompletion { inputs.emitLoading(false) }
                .catch { emitError(it.getError().mapToErrorDisplay()) }
                .collectLatest {
                    val image =
                        "$baseUrl${Constants.COMMON_FILE_BASE_URL}${it.profilePicture.orEmpty()}"
                    _userInfo.value = UserBasicInfoDisplay(
                        name = it.fullName.orEmpty(),
                        email = it.email.orEmpty(),
                        mobile = it.mobile.orEmpty(),
                        profileImage = image,
                        sid = commonSharedPreferences.sessionId
                    )
                    onTriggerActions(ChatAction.UserInfoLoaded)
                }
        }
    }

    private suspend fun getChatList(
        autoCalled: Boolean = false,
        action: String = "",
        time: Long = 0L,
        isLoadMore: Boolean = false,
    ) {
        chatListUseCase(
            param = ChatListRequest(
                referenceTime = time,
                limit = 10,
                direction = CHAT_STATUS_BEFORE
            )
        )
            .onStart { _showLoading.value = true }
            .onCompletion {
                if (isLoadMore) {
                    _showLoading.value = false
                    _isLoadingMore.value = false
                } else {
                    _showLoading.value = false
                }
            }
            .catch { inputs.emitError(it.getError().mapToErrorDisplay()) }
            .collectLatest {
                withContext(Dispatchers.Main) {
                    val data = it.mapToChatMessageListDisplay(baseUrl)
                    val latestMessageTime = _chatMessages.firstOrNull()?.createdAt
//                    val newMessage = data.filter { it.createdAt > (latestMessageTime ?: 0L) }
                    if (isLoadMore) {
                        _chatMessages.addAll(0, data)
                        _noMoreData.value = data.isEmpty()
                    } else {
                        _chatMessages.clear()
                        _chatMessages.addAll(data)
                    }
                }
            }
    }

    private fun uploadFile(context: Context, file: File, uri: Uri, requestBody: RequestBody) {
        scope.launch {
            val fileSize = uri.getFileSize(context) ?: 0L
            uploadFileUseCase(param = requestBody)
                .onStart { inputs.emitLoading(true) }
                .onCompletion { inputs.emitLoading(false) }
                .catch { emitError(it.getError().mapToErrorDisplay()) }
                .collectLatest {
                    _uploadedFiles.add(
                        AttachmentDisplayModel(
                            name = file.name.orEmpty(),
                            type = file.absolutePath.substringAfterLast("."),
                            url = it.dosKey,
                            fileSize = formatFileSize(uri.getFileSize(context) ?: 0L),
                            originalFileSize = fileSize
                        )
                    )
                }
        }
    }

    private fun sendMessage() {
        val files = _uploadedFiles.map {
            val docUrl = "$baseUrl${Constants.COMMON_FILE_BASE_URL}${it.url}"
            it.copy(url = docUrl)
        }
        val request = SendChatMessageRequest(
            message = _message.value,
            fileList = _uploadedFiles.map {
                SendChatMessageFileRequest(
                    fileName = it.name,
                    fileType = it.type,
                    fileKey = it.url,
                    fileSize = it.originalFileSize
                )
            }
        )
        scope.launch {
            sendChatMessageUseCase(param = request)
                .onStart { inputs.emitLoading(true) }
                .onCompletion { inputs.emitLoading(false) }
                .catch { emitError(it.getError().mapToErrorDisplay()) }
                .collectLatest {
                    _chatMessages.add(
                        ChatMessageDisplayModel(
                            text = _message.value,
                            senderRole = SenderRoleType.USER,
                            messageType = MessageType.TEXT,
                            imageUrl = "",
                            createdAt = LocalDate.now().toEpochMilli(),
                            attachments = files.toList()
                        )
                    )
                    Timber.d("CHAT_MESSAGE ${_chatMessages}")
                    _message.value = ""
                    _uploadedFiles.clear()
                }
        }
    }

}