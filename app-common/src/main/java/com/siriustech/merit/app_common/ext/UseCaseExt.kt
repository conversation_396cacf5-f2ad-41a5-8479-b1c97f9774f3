package com.siriustech.merit.app_common.ext

import com.core.network.base.getError
import com.core.network.model.Error
import com.siriustech.core_ui_compose.model.ErrorDisplay


/**
 * Created by <PERSON><PERSON><PERSON>
 */

fun Throwable.mapToErrorDisplay(): ErrorDisplay {
    return when (this) {
        is ErrorDisplay -> {
            this
        }

        is Error -> {
            ErrorDisplay(
                code = this.errorResponse.status,
                title = "Oops",
                message = this.errorResponse.message
            )
        }

        else -> {
            ErrorDisplay(
                code = COMMON_ERROR_DISPLAY_CODE,
                title = "Oops",
                message = this.message.orEmpty()
            )
        }
    }
}

const val COMMON_ERROR_DISPLAY_CODE = "1"