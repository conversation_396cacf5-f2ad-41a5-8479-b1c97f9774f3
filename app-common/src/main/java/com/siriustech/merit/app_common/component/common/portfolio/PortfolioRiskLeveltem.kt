package com.siriustech.merit.app_common.component.common.portfolio

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.tooling.preview.Preview
import com.core.util.toAmount
import com.siriustech.merit.app_common.component.common.MarketPriceChange
import com.siriustech.merit.app_common.component.common.MarketPriceChangeDisplayData
import com.siriustech.merit.app_common.data.display.PortfolioItemDisplayData
import com.siriustech.merit.app_common.ext.colorTxtParagraph
import com.siriustech.merit.app_common.ext.colorTxtTitle
import com.siriustech.merit.app_common.ext.displayPriceChange
import com.siriustech.merit.app_common.ext.displayPriceChangeRate
import com.siriustech.merit.app_common.theme.LocalDimens
import com.siriustech.merit.app_common.theme.LocalTypography
import com.siriustech.merit.app_common.typeenum.PortfolioAssetType
import com.siriustech.merit.app_common.typeenum.RiskLevel

/**
 * Created by Hein Htet
 */
@Composable
fun PortfolioRiskLevelItem(
    modifier: Modifier = Modifier,
    data: PortfolioItemDisplayData = PortfolioItemDisplayData(type = PortfolioAssetType.EQUITY),
) {
    Row(
        modifier = modifier
            .fillMaxWidth()
            .padding(vertical = LocalDimens.current.dimen12),
        horizontalArrangement = Arrangement.SpaceBetween
    ) {
        Column {
            Row(verticalAlignment = Alignment.CenterVertically) {
                Box(
                    modifier = Modifier
                        .size(LocalDimens.current.dimen10)
                        .clip(RoundedCornerShape(LocalDimens.current.dimen1))
                        .background(data.riskLevel?.getRiskLevelFillColor() ?: Color.Transparent)
                )
                Text(
                    text = data.riskLevel?.getDisplayVolatilityName().orEmpty(),
                    modifier = Modifier
                        .padding(start = LocalDimens.current.dimen4),
                    style = LocalTypography.current.text14.medium.colorTxtTitle()
                )
                Spacer(modifier = Modifier.width(LocalDimens.current.dimen8))
            }
            Text(
                data.percentage.plus("%"),
                style = LocalTypography.current.text12.medium.colorTxtParagraph()
            )
        }
        MarketPriceChange(
            data = MarketPriceChangeDisplayData(
                marketValue = data.marketValue,
                currency = data.currency,
                unrealizedGL = data.unrealizedGL,
                unrealizedGLRate = data.unrealizedGLRate
            )
        )
    }
}

@Preview(showBackground = true, showSystemUi = false)
@Composable
fun PreviewPortfolioRiskLevelItem() {
    PortfolioRiskLevelItem(
        data = PortfolioItemDisplayData(
            type = PortfolioAssetType.EQUITY,
            percentage = "33.33%",
            marketValue = "1400343",
            riskLevel = RiskLevel.LOW,
            unrealizedGL = "52",
            unrealizedGLRate = "20",
            currency = "USD"
        ),
    )
}

@Preview(showBackground = true, showSystemUi = false)
@Composable
fun PreviewPortfolioRiskLevelItemMedium() {
    PortfolioRiskLevelItem(
        data = PortfolioItemDisplayData(
            type = PortfolioAssetType.EQUITY,
            percentage = "33.33%",
            marketValue = "1400343",
            riskLevel = RiskLevel.MEDIUM,
            unrealizedGL = "52",
            unrealizedGLRate = "20",
            currency = "USD"
        ),
    )
}


@Preview(showBackground = true, showSystemUi = false)
@Composable
fun PreviewPortfolioRiskLevelItemHigh() {
    PortfolioRiskLevelItem(
        data = PortfolioItemDisplayData(
            type = PortfolioAssetType.EQUITY,
            percentage = "33.33%",
            marketValue = "1400343",
            riskLevel = RiskLevel.HIGH,
            unrealizedGL = "52",
            unrealizedGLRate = "20",
            currency = "USD"
        ),
    )
}