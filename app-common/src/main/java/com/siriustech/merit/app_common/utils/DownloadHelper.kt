package com.siriustech.merit.app_common.utils

import android.app.DownloadManager
import android.content.BroadcastReceiver
import android.content.Context
import android.content.Intent
import android.content.IntentFilter
import android.net.Uri
import android.os.Environment
import android.widget.Toast
import androidx.core.content.ContextCompat
import java.io.File

/**
 * Created by <PERSON><PERSON>
 */
class DownloadHelper(private val context: Context, onDownloaded: (uri: Uri) -> Unit = {}) {

    private var downloadId: Long = -1
    fun downloadFile(url: String, fileName: String, sid: String) {
        try {
            val customFolder = File(
                Environment.getExternalStoragePublicDirectory(Environment.DIRECTORY_DOWNLOADS),
                "MERIT"
            )

            val downloadManager =
                context.getSystemService(Context.DOWNLOAD_SERVICE) as DownloadManager
            if (!customFolder.exists()) {
                customFolder.mkdirs()
            }
            val request = DownloadManager.Request(Uri.parse(url))
                .setTitle("Download File $fileName")
                .setDescription("Downloading...")
                .addRequestHeader("sid", sid)
                .setNotificationVisibility(DownloadManager.Request.VISIBILITY_VISIBLE_NOTIFY_COMPLETED)
                .setDestinationUri(Uri.fromFile(File(customFolder, fileName)))

            downloadId = downloadManager.enqueue(request)
            ContextCompat.registerReceiver(
                context,
                onDownloadComplete,
                IntentFilter(DownloadManager.ACTION_DOWNLOAD_COMPLETE),
                ContextCompat.RECEIVER_EXPORTED
            )

        } catch (e: Exception) {
            e.printStackTrace()
            Toast.makeText(context, "Error: ${e.message}", Toast.LENGTH_LONG).show()
        }
    }

    private val onDownloadComplete = object : BroadcastReceiver() {
        override fun onReceive(context: Context, intent: Intent) {
            val id = intent.getLongExtra(DownloadManager.EXTRA_DOWNLOAD_ID, -1)
            if (id == downloadId) {

                val downloadManager =
                    context.getSystemService(Context.DOWNLOAD_SERVICE) as DownloadManager
                val query = DownloadManager.Query().setFilterById(downloadId)
                val cursor = downloadManager.query(query)

                if (cursor != null && cursor.moveToFirst()) {
                    val uriIndex = cursor.getColumnIndex(DownloadManager.COLUMN_LOCAL_URI)
                    val fileIndex = cursor.getColumnIndex(DownloadManager.COLUMN_LOCAL_FILENAME)

                    val fileUri = if (uriIndex != -1) {
                        Uri.parse(cursor.getString(uriIndex))
                    } else if (fileIndex != -1) {
                        Uri.fromFile(File(cursor.getString(fileIndex)))
                    } else {
                        null
                    }
                    cursor.close()
                    if (fileUri != null) {
                        onDownloaded(fileUri)
                    } else {
                        Toast.makeText(context, "Unable to retrieve file location", Toast.LENGTH_SHORT).show()
                    }
                }
                context.unregisterReceiver(this)
            }
        }
    }
}

