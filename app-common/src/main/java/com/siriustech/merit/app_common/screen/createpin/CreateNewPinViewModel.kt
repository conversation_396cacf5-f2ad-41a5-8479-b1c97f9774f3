package com.siriustech.merit.app_common.screen.createpin

import com.core.network.base.getError
import com.siriustech.merit.apilayer.service.authentication.auth.ConfigurePinPassRequest
import com.siriustech.merit.apilayer.service.authentication.auth.ConfigurePinPassUseCase
import com.siriustech.merit.app_common.component.alert.BannerAlertProperties
import com.siriustech.merit.app_common.component.alert.BannerAlertType
import com.siriustech.merit.app_common.data.CommonSharedPreferences
import com.siriustech.merit.app_common.theme.AppViewModel
import com.siriustech.merit.app_common.typeenum.CreateNewPinStep
import dagger.hilt.android.lifecycle.HiltViewModel
import javax.inject.Inject
import javax.inject.Named
import kotlinx.coroutines.channels.Channel
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.catch
import kotlinx.coroutines.flow.collectLatest
import kotlinx.coroutines.flow.onCompletion
import kotlinx.coroutines.flow.onStart
import kotlinx.coroutines.flow.receiveAsFlow
import kotlinx.coroutines.launch

/**
 * Created by Hein Htet
 */
@HiltViewModel
class CreateNewPinViewModel @Inject constructor(
    private val commonSharedPreferences: CommonSharedPreferences,
    private val configurePinPassUseCase: ConfigurePinPassUseCase,
    @Named("DeviceID") private val deviceID: String,
) : AppViewModel() {
    private val _confirmPin = MutableStateFlow("")
    private val _createPin = MutableStateFlow("")
    private val _currentStep = MutableStateFlow(0)
    private val _createPinCodeEvent = Channel<CreatePinCodeEvent>(capacity = Channel.BUFFERED)
    private val _pinUnMatchCount = MutableStateFlow(0)

    inner class CreateNewPinInputs : BaseInputs() {
        fun onConfirmPinChange(value: String) {
            _confirmPin.value = value
        }

        fun onCreateNewPinChange(value: String) {
            _createPin.value = value
        }

        private fun onResetCreateNewPin() {
            _createPin.value = ""
        }

        private fun onResetConfirmPin() {
            _confirmPin.value = ""
        }

        fun updateCurrentStep(step: Int) {
            _currentStep.value = step
        }

        fun onResetPin() {
            if (_currentStep.value == CreateNewPinStep.CREATE_PIN.step) {
                onResetCreateNewPin()
            } else {
                onResetConfirmPin()
            }
        }

        fun onComparePinCode() {
            if (_createPin.value != _confirmPin.value) {
                if (_pinUnMatchCount.value == 4) {
                    _pinUnMatchCount.value = 0
                    _createPin.value = ""
                    scope.launch {
                        _createPinCodeEvent.send(CreatePinCodeEvent.PinUnMatchExceed)
                    }
                } else {
                    _pinUnMatchCount.value += 1
                    scope.launch {
                        _createPinCodeEvent.send(CreatePinCodeEvent.PinUnMatched(5 - _pinUnMatchCount.value))
                    }
                }
                _confirmPin.value = ""
            } else {
                scope.launch {
                    _createPinCodeEvent.send(CreatePinCodeEvent.OnPinCodeMatched)
                }
            }
        }


//        fun onComparePinCode() {
//            if (_createPin.value != _confirmPin.value) {
//                if (_pinUnMatchCount.value == 4) {
//                    _pinUnMatchCount.value = 0
//                    _createPin.value = ""
//                    scope.launch {
//                        _createPinCodeEvent.send(CreatePinCodeEvent.PinUnMatchExceed)
//                    }
//                } else {
//                    _pinUnMatchCount.value += 1
//                }
//                emitBannerAlert(
//                    BannerAlertProperties(
//                        "Error",
//                        description = "Pin code does not match",
//                        type = BannerAlertType.ALERT_ERROR
//                    )
//                )
//                _confirmPin.value = ""
//            } else {
//                scope.launch {
//                    _createPinCodeEvent.send(CreatePinCodeEvent.OnPinCodeMatched)
//                }
//            }
//        }

        fun onConfigurePin() = configurePin()
        fun onBiometricSetupSuccess() {
            commonSharedPreferences.setBiometricSetUp(true)
        }
    }

    inner class CreateNewPinOutputs : BaseOutputs() {

        val createPin: StateFlow<String>
            get() = _createPin

        val confirmPin: StateFlow<String>
            get() = _confirmPin

        val currentStep: StateFlow<Int>
            get() = _currentStep

        val createPinCodeEvent: Flow<CreatePinCodeEvent>
            get() = _createPinCodeEvent.receiveAsFlow()
    }


    override val inputs = CreateNewPinInputs()
    override val outputs = CreateNewPinOutputs()

    private fun configurePin() {

        val request = ConfigurePinPassRequest(
            password = commonSharedPreferences.userLoginPassword,
            newPin = _confirmPin.value,
            deviceId = deviceID
        )

        scope.launch {
            configurePinPassUseCase(param = request)
                .onStart { inputs.emitLoading(true) }
                .onCompletion { inputs.emitLoading(false) }
                .catch {
                    emitBannerAlert(
                        BannerAlertProperties(
                            description = it.getError().message.orEmpty(),
                            type = BannerAlertType.ALERT_ERROR
                        )
                    )
                }
                .collectLatest {
                    commonSharedPreferences.setUserLoginPin(_confirmPin.value)
                    commonSharedPreferences.setPinSetUp(true)
                    _createPinCodeEvent.send(CreatePinCodeEvent.OnConfiguredPinCode)
                }
        }
    }
}