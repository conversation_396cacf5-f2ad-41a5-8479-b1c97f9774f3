package com.siriustech.merit.app_common.network

import com.core.network.base.ApiHeaderInterceptor
import com.core.network.base.DefaultHttpConfiguration
import com.siriustech.merit.app_common.Constants
import okhttp3.Interceptor

class AppHttpConfiguration(
    private val interceptor: <PERSON><PERSON>HeaderInterceptor,
    private val authInterceptor: AuthInterceptor,
    private val flavor: String,
) : DefaultHttpConfiguration() {

    override fun getOkhttpInterceptors(): List<Interceptor> {
        val interceptors = ArrayList<Interceptor>()
        interceptors.add(interceptor)
        interceptors.add(authInterceptor)
        interceptors.addAll(super.getOkhttpInterceptors())
        return interceptors
    }

    override fun chuckEnable() = flavor == Constants.FLAVOR_UAT

}