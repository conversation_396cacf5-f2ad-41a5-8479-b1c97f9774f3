package com.siriustech.merit.app_common.component.pin

import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.DisposableEffect
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.fragment.app.FragmentActivity
import com.siriustech.merit.app_common.R
import com.siriustech.merit.app_common.ext.colorTxtParagraph
import com.siriustech.merit.app_common.ext.colorTxtTitle
import com.siriustech.merit.app_common.navigation.CreateNewPinSuccess
import com.siriustech.merit.app_common.theme.LocalAppColor
import com.siriustech.merit.app_common.theme.LocalDimens
import com.siriustech.merit.app_common.theme.LocalTypography
import com.siriustech.merit.app_common.utils.BiometricHelper

/**
 * Created by Hein Htet
 */


@Composable
fun PinView(
    modifier: Modifier = Modifier,
    pinCodeResetCount: Int = 0,
    pinCodeDefaultValue: String = "",
    properties: PinViewProperties = PinViewProperties(),
    onPinCodeChanged: (code: String) -> Unit = {},
    onBiometricAuthSuccess: () -> Unit = {},
) {

    var pinCode by remember {
        mutableStateOf(pinCodeDefaultValue)
    }

    LaunchedEffect(pinCodeResetCount) {
        pinCode = pinCodeDefaultValue
    }

    DisposableEffect(pinCodeDefaultValue) {
        pinCode = pinCodeDefaultValue
        onDispose {}
    }

    val fragmentActivity = LocalContext.current as FragmentActivity

    Column(
        modifier = Modifier
            .fillMaxWidth()
            .then(modifier)
            .background(LocalAppColor.current.bgDefault),
        horizontalAlignment = Alignment.CenterHorizontally
    ) {
        Text(
            text = properties.title,
            style = LocalTypography.current.text24.semiBold.colorTxtTitle()
        )
        Spacer(modifier = Modifier.height(LocalDimens.current.dimen8))
        Text(
            text = properties.subTitle,
            style = LocalTypography.current.text14.regular.colorTxtParagraph()
        )
        PinCodeCircle(value = pinCode)
        Spacer(modifier = Modifier.height(LocalDimens.current.dimen24))
        NumberPad(
            showBiometricButton = properties.showBiometricButton,
            onBiometricClicked = {
                BiometricHelper.getBiometricPrompt(fragmentActivity, onAuthSucceed = {
                    onBiometricAuthSuccess()
                }).authenticate(BiometricHelper.promptInfo)

            },
            onPinCodeClicked = {
                if (it == "x") {
                    if (it.isNotEmpty()) {
                        pinCode = pinCode.dropLast(1)
                    }
                } else {
                    if (pinCode.length == 6) return@NumberPad
                    pinCode += it
                }
                onPinCodeChanged(pinCode)
            })
    }
}

@Composable
fun PinCodeCircle(value: String) {
    Row(
        modifier = Modifier
            .fillMaxWidth()
            .padding(vertical = LocalDimens.current.dimen32),
        horizontalArrangement = Arrangement.spacedBy(
            LocalDimens.current.dimen16,
            Alignment.CenterHorizontally
        ),

        ) {
        for (i in 0..5) {
            Image(
                painter = painterResource(id = if (i < value.length) R.drawable.ic_pin_code_filled else R.drawable.ic_pin_code_default),
                contentDescription = "Pin Code"
            )
        }

    }
}

@Composable
fun NumberPad(
    showBiometricButton: Boolean = false,
    onPinCodeClicked: (code: String) -> Unit = {},
    onBiometricClicked: () -> Unit = {},
) {
    Column(horizontalAlignment = Alignment.CenterHorizontally) {
        Row(horizontalArrangement = Arrangement.spacedBy(LocalDimens.current.dimen30)) {
            NumberPadButton(text = "1", onClicked = { onPinCodeClicked.invoke("1") })
            NumberPadButton(text = "2", onClicked = { onPinCodeClicked.invoke("2") })
            NumberPadButton(text = "3", onClicked = { onPinCodeClicked.invoke("3") })
        }
        Spacer(modifier = Modifier.height(LocalDimens.current.dimen20))
        Row(horizontalArrangement = Arrangement.spacedBy(LocalDimens.current.dimen30)) {
            NumberPadButton(text = "4", onClicked = { onPinCodeClicked.invoke("4") })
            NumberPadButton(text = "5", onClicked = { onPinCodeClicked.invoke("5") })
            NumberPadButton(text = "6", onClicked = { onPinCodeClicked.invoke("6") })
        }
        Spacer(modifier = Modifier.height(LocalDimens.current.dimen20))
        Row(horizontalArrangement = Arrangement.spacedBy(LocalDimens.current.dimen30)) {
            NumberPadButton(text = "7", onClicked = { onPinCodeClicked.invoke("7") })
            NumberPadButton(text = "8", onClicked = { onPinCodeClicked.invoke("8") })
            NumberPadButton(text = "9", onClicked = { onPinCodeClicked.invoke("9") })
        }
        Spacer(modifier = Modifier.height(LocalDimens.current.dimen20))
        Row(horizontalArrangement = Arrangement.spacedBy(LocalDimens.current.dimen30)) {
            NumberPadButton(text = "", showBiometricButton = showBiometricButton, onClicked = {
                onBiometricClicked()
            })
            NumberPadButton(text = "0", onClicked = { onPinCodeClicked.invoke("0") })
            NumberPadButton(text = "x", onClicked = { onPinCodeClicked.invoke("x") })
        }
    }
}


@Composable
fun NumberPadButton(
    showBiometricButton: Boolean = false,
    text: String, onClicked: () -> Unit = {},
) {
    var modifier = Modifier
        .size(LocalDimens.current.dimen72)
        .clip(RoundedCornerShape(36.dp))
        .background(if (!showBiometricButton && text.isEmpty()) LocalAppColor.current.bgDefault else LocalAppColor.current.bgTone)
    if (text.isNotEmpty() || showBiometricButton) {
        modifier = modifier
            .clickable { onClicked() }
    }
    Box(
        modifier = modifier,
        contentAlignment = Alignment.Center
    ) {
        if (showBiometricButton) {
            Image(
                painter = painterResource(id = R.drawable.ic_biometric_login),
                contentDescription = "Biometric Login"
            )
        } else {
            Text(text = text, style = LocalTypography.current.text28.semiBold.colorTxtTitle())
        }
    }
}


@Preview(showBackground = true, showSystemUi = true)
@Composable
fun PreviewPinView() {
    PinView()
}

data class PinViewProperties(
    val title: String = "",
    val subTitle: String = "",
    val showBiometricButton: Boolean = false,
)