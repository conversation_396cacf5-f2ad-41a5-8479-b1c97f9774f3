package com.siriustech.merit.app_common.typeenum

import android.content.Context
import androidx.compose.runtime.Composable
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.platform.LocalContext
import com.siriustech.merit.app_common.component.text.BadgeText
import com.siriustech.merit.app_common.theme.LocalAppColor
import com.siriustech.merit.app_common.R as AppCommonR


/**
 * Created by Hein Htet
 */
enum class HomeMarketListType(val value: String) {
    GAINER("GAINER"),
    LOSER("LOSER");

    @Composable
    fun getColor(): Color {
        return when (this) {
            GAINER -> LocalAppColor.current.txtPositive
            LOSER -> LocalAppColor.current.txtNegative
        }
    }

    fun displayValue(context: Context): String {
        return when (this) {
            GAINER -> context.getString(AppCommonR.string.key0637)
            LOSER -> context.getString(AppCommonR.string.key0638)
        }
    }

    @Composable
    fun DisplayBadge() {
        val context = LocalContext.current
        var bgColor = LocalAppColor.current.bgPositive
        var textColor = LocalAppColor.current.txtPositive

        when (this) {
            GAINER -> {
                bgColor = LocalAppColor.current.bgPositive
                textColor = LocalAppColor.current.txtPositive
            }

            LOSER -> {
                bgColor = LocalAppColor.current.bgNegative
                textColor = LocalAppColor.current.txtNegative
            }
        }

        BadgeText(label = this.displayValue(context), bgColor = bgColor, textColor = textColor)
    }

    companion object {

        fun fromParam(value: String): HomeMarketListType {
            return when (value) {
                GAINER.value -> GAINER
                LOSER.value -> LOSER
                else -> GAINER
            }
        }
    }
}