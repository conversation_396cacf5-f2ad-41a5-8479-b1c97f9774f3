package com.siriustech.merit.app_common.mapper

import com.core.util.toAmount
import com.siriustech.merit.apilayer.service.wallet.summary.AllocationResponse
import com.siriustech.merit.apilayer.service.wallet.summary.CategoryAssetListResponse
import com.siriustech.merit.apilayer.service.wallet.summary.ProductCategoryListResponse
import com.siriustech.merit.apilayer.service.wallet.summary.RiskLevelBreakdownResponse
import com.siriustech.merit.apilayer.service.wallet.summary.WalletSummaryListResponse
import com.siriustech.merit.app_common.component.modalbts.ProductCategoryDetailsModalDisplayData
import com.siriustech.merit.app_common.data.AppCache
import com.siriustech.merit.app_common.data.display.MarketAssetDisplayData
import com.siriustech.merit.app_common.data.display.PortfolioItemDisplayData
import com.siriustech.merit.app_common.data.display.ProductCategoryDisplayData
import com.siriustech.merit.app_common.data.display.RiskLevelBreakdownDisplayData
import com.siriustech.merit.app_common.ext.capitalizeWords
import com.siriustech.merit.app_common.typeenum.PortfolioAssetARRType
import com.siriustech.merit.app_common.typeenum.PortfolioAssetType
import com.siriustech.merit.app_common.typeenum.RiskLevel

/**
 * Created by Hein Htet
 */
object WalletSummaryMapper {


    fun List<WalletSummaryListResponse>?.mapToPortfolioItemDisplay(appCache: AppCache): List<PortfolioItemDisplayData> {
        return this.orEmpty().map {
            val allocationClass = PortfolioAssetType.fromParam(it.summaryName.orEmpty())
            val arrType = PortfolioAssetARRType.fromParam(it.summaryName.orEmpty())
            val riskLevel = RiskLevel.fromParam(it.summaryName.orEmpty())
            PortfolioItemDisplayData(
                aarType = arrType,
                rawSummaryName = it.summaryName.orEmpty().capitalizeWords(),
                type = allocationClass ?: PortfolioAssetType.UNKNOWN_TYPE,
                riskLevel = riskLevel,
                riskTag = it.riskTag.orEmpty(),
                percentage = it.percentage.orEmpty(),
                marketValue = it.marketValue.orEmpty(),
                costValue = it.costValue.orEmpty(),
                unrealizedGL = it.unrealizedGl.orEmpty(),
                unrealizedGLRate = it.unrealizedGlRate.orEmpty(),
                currency = it.currency.orEmpty(),
                categoryAssetList = it.assetList.orEmpty()
                    .mapToPortfolioListDisplayData(it),
                displaySummaryName = appCache.allocationClass.findLast { assetClass -> assetClass.id == it.summaryName }?.title
                    ?: it.summaryName.orEmpty().capitalizeWords()
            )
        }
    }

    private fun List<CategoryAssetListResponse>.mapToPortfolioListDisplayData(
        allocationResponse: WalletSummaryListResponse,
    ): ArrayList<ProductCategoryDisplayData> {
        val items = ArrayList<ProductCategoryDisplayData>()
        forEach { asset ->
            items.add(
                ProductCategoryDisplayData(
                    id = asset.instrumentId.toString(),
                    symbol = asset.symbol.orEmpty(),
                    name = asset.name.orEmpty(),
                    exchange = asset.exchange.orEmpty(),
                    currency = asset.currency.orEmpty(),
                    marketValue = asset.marketValue.orEmpty(),
                    costPrice = asset.costPrice.orEmpty(),
                    costValue = asset.costValue.orEmpty(),
                    marketPrice = asset.marketPrice.orEmpty(),
                    riskLevel = RiskLevel.fromParam(asset.riskLevel.orEmpty()),
                    unit = asset.unit?.toAmount(0).orEmpty(),
                    logo = asset.logo.orEmpty(),
                    unrealizedGl = asset.unrealizedGl.orEmpty(),
                    unrealizedGlRate = asset.unrealizedGlRate.orEmpty(),
                    assetAllocationClass = allocationResponse.summaryName.orEmpty(),
                    percentage = allocationResponse.percentage ?: "0.0",
                )
            )
        }
        return items
    }


    private fun List<ProductCategoryListResponse>.mapToProductAssetListDisplayData(
        allocationResponse: AllocationResponse,
    ): ArrayList<ProductCategoryDisplayData> {
        val items = ArrayList<ProductCategoryDisplayData>()
        this.forEach { category ->
            val isNotInSection =
                items.findLast { it.categoryName == category.productCategory } == null
            if (isNotInSection) {
                items.add(
                    ProductCategoryDisplayData(
                        categoryName = category.productCategory,
                        isSection = true
                    )
                )
            }
            category.assetList.orEmpty().forEach { asset ->
                items.add(
                    ProductCategoryDisplayData(
                        name = asset.name.orEmpty(),
                        exchange = asset.exchange.orEmpty(),
                        currency = asset.currency.orEmpty(),
                        marketValue = asset.marketValue.orEmpty(),
                        costPrice = asset.costPrice.orEmpty(),
                        marketPrice = asset.marketPrice.orEmpty(),
                        riskLevel = RiskLevel.fromParam(asset.riskLevel.orEmpty()),
                        categoryName = category.productCategory,
                        unit = asset.unit.orEmpty(),
                        logo = asset.logo.orEmpty(),
                        unrealizedGl = asset.unrealizedGl.orEmpty(),
                        unrealizedGlRate = asset.unrealizedGlRate.orEmpty(),
                        assetAllocationClass = allocationResponse.allocationClass.orEmpty(),
                        percentage = allocationResponse.percentage ?: "0.0"
                    )
                )
            }
        }
        return items
    }


    fun ProductCategoryDisplayData.mapToProductCategoryDetailsDisplay(): ProductCategoryDetailsModalDisplayData {
        val asset = this
        return ProductCategoryDetailsModalDisplayData(
            id = asset.id,
            name = asset.name,
            symbol = asset.symbol,
            exchange = asset.exchange,
            currency = asset.currency,
            marketValue = asset.marketValue.toAmount(4) ?: "0.0000",
            costPrice = asset.costPrice.toAmount(4) ?: "0.0000",
            marketPrice = asset.marketPrice.toAmount(4) ?: "0.0000",
            riskLevel = asset.riskLevel,
            unit = asset.unit,
            logo = asset.logo,
            unrealizedGl = asset.unrealizedGl.toAmount(4) ?: "0.0000",
            unrealizedGlRate = asset.unrealizedGlRate,
            asOfDate = "",
            country = asset.currency,
            costValue = asset.costValue.toAmount(4) ?: "0.0000",
            categoryName = this.categoryName,
        )
    }

    fun List<RiskLevelBreakdownResponse>.mapToRiskLevelPieChartDisplay(): List<RiskLevelBreakdownDisplayData> {
        return this.map {
            RiskLevelBreakdownDisplayData(
                RiskLevel.fromParam(it.riskLevel.orEmpty()),
                it.percentage ?: "0.0"
            )
        }
    }

    fun ProductCategoryDetailsModalDisplayData.mapToMarketAssetDetails(): MarketAssetDisplayData {
        return this.let {
            return@let MarketAssetDisplayData(
                id = it.id,
                costPrice = it.costPrice,
                marketValue = it.marketValue,
                marketPrice = it.marketPrice,
                name = it.name,
                symbol = it.symbol,
                exchange = it.exchange,
                currency = it.currency,
                riskLevel = it.riskLevel,
                unit = it.unit,
                logo = it.logo,
                unrealizedGl = it.unrealizedGl,
                unrealizedGlRate = it.unrealizedGlRate,
                costValue = it.costValue,
                categoryName = it.categoryName,
                region = it.country,
            )
        }
    }
}