package com.siriustech.merit.app_common.typeenum

/**
 * Created by <PERSON><PERSON>
 */
enum class UserStatus(val value: String) {
    ONBOARDING("ONBOARDING"),
    NORMAL("NORMAL"),
    PENDING_REVIEW("PENDING_REVIEW"),
    PENDING_APPROVAL("PENDING_APPROVAL"),
    PENDING_CLOSURE("PENDING_CLOSURE"),
    CLOSED("CLOSED");


    companion object {


        fun fromParam(value: String): UserStatus {
            return when (value) {
                ONBOARDING.value -> ONBOARDING
                PENDING_REVIEW.value -> PENDING_REVIEW
                PENDING_APPROVAL.value -> PENDING_APPROVAL
                PENDING_CLOSURE.value -> PENDING_CLOSURE
                CLOSED.value -> CLOSED
                else -> NORMAL
            }
        }

        fun UserStatus.isLimitedAccess(): Boolean {
            val limitedAccessRoutes = listOf(
                UserStatus.PENDING_REVIEW, UserStatus.PENDING_APPROVAL,UserStatus.ONBOARDING,
            )
            return limitedAccessRoutes.contains(this)
        }

        fun UserStatus.isAccountIsPendingStatus(): Boolean {
            val limitedAccessRoutes = listOf(
                UserStatus.PENDING_REVIEW, UserStatus.PENDING_APPROVAL,
            )
            return limitedAccessRoutes.contains(this)
        }
    }
}