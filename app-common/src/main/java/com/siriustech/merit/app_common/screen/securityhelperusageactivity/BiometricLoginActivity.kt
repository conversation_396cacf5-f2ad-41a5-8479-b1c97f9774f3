package com.siriustech.merit.app_common.screen.securityhelperusageactivity

/**
 * Created by <PERSON><PERSON>tet
 */
import android.content.Context
import android.os.Bundle
import android.widget.Button
import android.widget.EditText
import android.widget.TextView
import android.widget.Toast
import androidx.biometric.BiometricPrompt
import androidx.core.content.ContextCompat
import androidx.fragment.app.FragmentActivity
import com.core.util.singleClick
import com.siriustech.merit.app_common.R
import com.siriustech.merit.app_common.rsa.SecurityHelper
import java.security.KeyPair
import timber.log.Timber

class BiometricLoginActivity : FragmentActivity() {

    private lateinit var securityHelper: SecurityHelper

    private lateinit var button: Button
    private lateinit var editText: EditText
    private lateinit var tvEncryptedText: TextView
    private lateinit var tvDecryptedText: TextView

    private var userId = "123456"
    private var keyVersion = "version_1"

    private var encryptedText = ""
    private lateinit var keyPairMap: KeyPair

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(R.layout.activity_biometric_main)
        securityHelper = SecurityHelper(this)

        button = findViewById<Button>(R.id.loginButton)
        editText = findViewById<EditText>(R.id.edInput)
        tvEncryptedText = findViewById<TextView>(R.id.tvEncrypted)
        tvDecryptedText = findViewById<TextView>(R.id.tvDecrypted)
        keyPairMap = securityHelper.generateKeyPair()

        button.singleClick {
            val plainText = editText.text.toString() // BE keyVersion + userId
            val encryptedText = securityHelper.encrypt(userId + keyVersion, keyPairMap.public)
            Timber.d("EncryptedText $encryptedText")
            tvEncryptedText.text = encryptedText
            tvDecryptedText.text = securityHelper.decrypt(encryptedText, keyPairMap.private)
            createBiometricPrompt()
        }
    }

    private fun createBiometricPrompt(): BiometricPrompt {
        val executor = ContextCompat.getMainExecutor(this)

        val callback = object : BiometricPrompt.AuthenticationCallback() {
            override fun onAuthenticationSucceeded(result: BiometricPrompt.AuthenticationResult) {
                super.onAuthenticationSucceeded(result)
                val decryptedString = securityHelper.decrypt(
                    encryptedText,
                    keyPairMap.private
                )
                tvDecryptedText.text = decryptedString
            }

            override fun onAuthenticationError(errorCode: Int, errString: CharSequence) {
                super.onAuthenticationError(errorCode, errString)
                <EMAIL>("Biometric authentication error: $errString")
            }

            override fun onAuthenticationFailed() {
                super.onAuthenticationFailed()
                <EMAIL>("Biometric authentication failed.")
            }
        }
        return BiometricPrompt(this, executor, callback)
    }

    fun Context.showToast(text: String) {
        Toast.makeText(this, text, Toast.LENGTH_SHORT).show()
    }


}
