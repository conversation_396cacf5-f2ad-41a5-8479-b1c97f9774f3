package com.siriustech.merit.app_common.screen.setup2FA

import android.util.Log
import androidx.lifecycle.SavedStateHandle
import androidx.lifecycle.viewModelScope
import com.core.network.base.getError
import com.siriustech.merit.apilayer.service.authentication.common.GetOTPRequest
import com.siriustech.merit.apilayer.service.authentication.common.GetOTPUseCase
import com.siriustech.merit.apilayer.service.authentication.config2FA.Configure2FARequest
import com.siriustech.merit.apilayer.service.authentication.config2FA.Configure2FAUseCase
import com.siriustech.merit.apilayer.service.authentication.login.LoginUseCase
import com.siriustech.merit.apilayer.service.authentication.login.LoginUserRequest
import com.siriustech.merit.apilayer.service.authentication.login.LoginUserResponse
import com.siriustech.merit.app_common.Constants.ACTION_ENABLE
import com.siriustech.merit.app_common.component.otp.VerificationEvent
import com.siriustech.merit.app_common.data.CommonSharedPreferences
import com.siriustech.merit.app_common.ext.mapToErrorDisplay
import com.siriustech.merit.app_common.navigation.argument.authentication.Verification2FAArguments
import com.siriustech.merit.app_common.theme.AppViewModel
import com.siriustech.merit.app_common.typeenum.Auth2FAType
import com.siriustech.merit.app_common.typeenum.BizType
import com.siriustech.merit.app_common.typeenum.OtpType
import com.siriustech.merit.app_common.utils.ErrorCode.mapToErrorDisplay
import dagger.hilt.android.lifecycle.HiltViewModel
import java.util.concurrent.TimeUnit
import javax.inject.Inject
import javax.inject.Named
import kotlinx.coroutines.channels.Channel
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.catch
import kotlinx.coroutines.flow.collectLatest
import kotlinx.coroutines.flow.onCompletion
import kotlinx.coroutines.flow.onStart
import kotlinx.coroutines.flow.receiveAsFlow
import kotlinx.coroutines.launch

/**
 * Created by Hein Htet
 */
@HiltViewModel
class Verification2FAViewModel @Inject constructor(
    private val savedStateHandle: SavedStateHandle,
    @Named("DeviceID") private val deviceID: String,
    private val getOTPUseCase: GetOTPUseCase,
    private val loginUseCase: LoginUseCase,
    private val configure2FAUseCase: Configure2FAUseCase,
    private val commonSharedPreferences: CommonSharedPreferences,
) : AppViewModel() {

    private val _otpCode = MutableStateFlow("")
    private val _authType = MutableStateFlow(Auth2FAType.EMAIL)
    private val _userAuthValue = MutableStateFlow("") //  email or phone number
    private val _verificationEvent = Channel<VerificationEvent>(capacity = Channel.BUFFERED)
    private val _otpExpiredTime = MutableStateFlow(0L)
    private val _otpResendCount = MutableStateFlow(0)
    private val _refNumber = MutableStateFlow("-")
    private val _token = MutableStateFlow("")
    private val _argument = MutableStateFlow(Verification2FAArguments())

    inner class VerificationInputs : BaseInputs() {

        fun init() {
            val args = Verification2FAArguments.from(savedStateHandle = savedStateHandle).args
            Log.d("Verification2FAViewModel", "init: ${args}")
            _argument.value = args
            _authType.value = args.authType
            _userAuthValue.value = args.email.orEmpty()
            _token.value = args.token.orEmpty()
            onRequestOTPApiCall()
        }

        fun updateOtpCode(code: String) {
            _otpCode.value = code
        }

        fun onOTPVerificationEventChanged(event: VerificationEvent) {
            viewModelScope.launch {
                _verificationEvent.send(event)
            }
        }

        fun onResendOTPCode() {
            onRequestOTPApiCall()
        }

        fun onSubmitVerificationCode(code: String) {
            _otpCode.value = code
            if (_argument.value.userBizType == BizType.LOGIN_2FA) {
                on2FALogin()
            } else {
                onConfigure2FA()
            }
        }

    }

    inner class VerificationOutputs : BaseOutputs() {
        val otpCode: StateFlow<String>
            get() = _otpCode

        val authType: StateFlow<Auth2FAType>
            get() = _authType

        val userAuthValue: StateFlow<String>
            get() = _userAuthValue

        val verificationEvent: Flow<VerificationEvent>
            get() = _verificationEvent.receiveAsFlow()

        val otpExpiredTime: StateFlow<Long>
            get() = _otpExpiredTime

        val refNumber: StateFlow<String>
            get() = _refNumber

        val otpResendCount: StateFlow<Int>
            get() = _otpResendCount
    }

    override val inputs = VerificationInputs()
    override val outputs = VerificationOutputs()


    private fun onRequestOTPApiCall() {
        scope.launch {
            getOTPUseCase(
                param = GetOTPRequest(
                    bizType = _argument.value.userBizType.type,
                    otpAddress = _userAuthValue.value,
                    otpType = OtpType.EMAIL.value
                )
            )
                .onStart { inputs.emitLoading(true) }
                .catch { emitError(it.getError().mapToErrorDisplay()) }
                .onCompletion { inputs.emitLoading(false) }
                .collectLatest {
                    _otpResendCount.value += 1
                    _refNumber.value = it.refCode.orEmpty()
                    _otpExpiredTime.value = TimeUnit.SECONDS.toMillis(it.expireSeconds ?: 0)
                    _verificationEvent.send(VerificationEvent.OnOTPSent)
                }
        }
    }

    private fun onConfigure2FA() {
        val loginRequest = Configure2FARequest(
            otpType = OtpType.EMAIL.value,
            otp = _otpCode.value,
            refCode = _refNumber.value,
            action = ACTION_ENABLE,
        )
        viewModelScope.launch {
            configure2FAUseCase(param = loginRequest)
                .onStart {
                    inputs.emitLoading(true)
                }
                .onCompletion {
                    inputs.emitLoading(false)
                }
                .catch {
                    _verificationEvent.send(VerificationEvent.OnVerificationFailed(it.mapToErrorDisplay().message))
                }
                .collectLatest {
                    onApiSuccessConfigure2FA()
                }
        }
    }

    // first time setup 2FA and force allow to setup PIN
    private fun onApiSuccessConfigure2FA() {
        Log.d("Verification2FAViewModel", "onHandleConfigure2FA")
        onHandleNavigateToNextStep(configurePinSetup = false)
    }

    private fun on2FALogin() {
        val loginRequest = LoginUserRequest(
            otpType = OtpType.EMAIL.value,
            otp = _otpCode.value,
            refCode = _refNumber.value,
            token = _token.value,
            deviceId = deviceID,
            username = _userAuthValue.value
        )
        viewModelScope.launch {
            loginUseCase(param = loginRequest)
                .onStart {
                    inputs.emitLoading(true)
                }
                .onCompletion {
                    inputs.emitLoading(false)
                }
                .catch {
                    inputs.emitError(it.getError().mapToErrorDisplay())
                }
                .collectLatest {
                    onLogin2FAApiSuccess(it)
                }
        }
    }

    private fun onLogin2FAApiSuccess(response: LoginUserResponse) {
        viewModelScope.launch {
            Log.d("Verification2FAViewModel", "onLogin2FAApiSuccess: $response")
            commonSharedPreferences.setSessionID(response.sessionId.orEmpty())
            // it's could be new device and force to 2FALogin and check if setup pin or not
            onHandleNavigateToNextStep(response.configuredPin ?: false)
        }
    }

    private fun onHandleNavigateToNextStep(configurePinSetup: Boolean) {
        scope.launch {
            if (!configurePinSetup) {
                _verificationEvent.send(VerificationEvent.OnConfigurePin)
            } else {
                _verificationEvent.send(VerificationEvent.VerificationSuccess)
            }
        }
    }
}