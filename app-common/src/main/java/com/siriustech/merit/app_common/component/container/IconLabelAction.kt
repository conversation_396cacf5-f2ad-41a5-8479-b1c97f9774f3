package com.siriustech.merit.app_common.component.container

import androidx.compose.foundation.Image
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.tooling.preview.Preview
import com.siriustech.merit.app_common.R
import com.siriustech.merit.app_common.ext.colorTxtTitle
import com.siriustech.merit.app_common.ext.noRippleClickable
import com.siriustech.merit.app_common.theme.LocalDimens
import com.siriustech.merit.app_common.theme.LocalTypography

/**
 * Created by <PERSON><PERSON>tet
 */

@Composable
fun IconLabelAction(
    onClick: () -> Unit = {},
    properties: IconLabelActionProperties = IconLabelActionProperties(),
) {
    Row(
        verticalAlignment = Alignment.CenterVertically, modifier = Modifier
            .padding(
                horizontal = LocalDimens.current.dimen12,
                vertical = LocalDimens.current.dimen14
            )
            .noRippleClickable { onClick() }
    ) {
        Image(
            painter = painterResource(id = properties.leftIconResId),
            contentDescription = "Icon Image Resource",
            modifier = Modifier.size(LocalDimens.current.dimen12)
        )
        PaddingStart(value = LocalDimens.current.dimen8)
        Text(
            text = properties.title,
            modifier = Modifier.weight(1f),
            style = LocalTypography.current.text14.regular.colorTxtTitle()
                .merge(properties.titleTextStyle)
        )
        if (properties.rightActionResId != null) {

            Image(
                painter = painterResource(id = properties.rightActionResId),
                contentDescription = "Icon Image Resource"
            )
        }
    }
}

data class IconLabelActionProperties(
    val title: String = "",
    val leftIconResId: Int = 0,
    val rightActionResId: Int? = null,
    val titleTextStyle: TextStyle = TextStyle(),
)


@Preview(showBackground = true)
@Composable
fun PreviewIconLabelAction() {
    IconLabelAction(
        properties = IconLabelActionProperties(
            title = "Title",
            leftIconResId = R.drawable.ic_chart_time_frame,
            rightActionResId = R.drawable.ic_right_arrow,
            titleTextStyle = LocalTypography.current.text14.regular.colorTxtTitle()
        )
    )
}