package com.siriustech.merit.app_common.theme

import androidx.compose.ui.graphics.Color


abstract class AppColor {
    abstract val bgDefault: Color
    abstract val bgPale: Color
    abstract val bgTone: Color
    abstract val bgAccent: Color
    abstract val bgDisabled: Color
    abstract val bgCaution: Color
    abstract val bgPositive: Color
    abstract val bgNegative: Color
    abstract val bgInfo: Color
    abstract val btnPrimary: Color
    abstract val btn2nd: Color
    abstract val btn3rd: Color
    abstract val btn4th: Color
    abstract val btnCaution: Color
    abstract val btnPositive: Color
    abstract val btnNegative: Color
    abstract val btnInfo: Color
    abstract val btnDisabled: Color
    abstract val txtParagraph: Color
    abstract val txtTitle: Color
    abstract val txtLabel: Color
    abstract val txtCaution: Color
    abstract val txtPositive: Color
    abstract val txtNegative: Color
    abstract val txtInfo: Color
    abstract val txtDisabled: Color
    abstract val txtInactive: Color
    abstract val txtInverted: Color
    abstract val chartEmeraldGreen: Color
    abstract val chartPurple: Color
    abstract val chartOrange: Color
    abstract val chartBlue: Color
    abstract val chartRed: Color
    abstract val chartYellow: Color
    abstract val chartGreen: Color
    abstract val chartPink: Color
    abstract val riskLevelBgHigh: Color
    abstract val riskLevelBgMedium: Color
    abstract val riskLevelBgLow: Color
    abstract val riskLevelFillLow1: Color
    abstract val riskLevelFillLow2: Color
    abstract val riskLevelFillLow3: Color
    abstract val riskLevelFillMedium4: Color
    abstract val riskLevelFillMedium5: Color
    abstract val riskLevelFillMedium6: Color
    abstract val riskLevelFillHigh7: Color
    abstract val riskLevelFillHigh8: Color
    abstract val riskLevelFillHigh9: Color
    abstract val riskLevelFillHigh10: Color
    abstract val riskLevelTextHigh: Color
    abstract val riskLevelTextMedium: Color
    abstract val riskLevelTextLow: Color
    abstract val chartLightRed: Color
    abstract val chartLightBlue: Color



}

data class LightColor(
    override val bgDefault: Color = Color(255, 255, 255),
    override val bgPale: Color = Color(251, 251, 251),
    override val bgTone: Color = Color(249, 249, 249),
    override val bgAccent: Color = Color(245, 245, 245),
    override val bgDisabled: Color = Color(237, 237, 237),
    override val bgCaution: Color = Color(253, 252, 239),
    override val bgPositive: Color = Color(246, 255, 253),
    override val bgNegative: Color = Color(255, 245, 245),
    override val bgInfo: Color = Color(245, 251, 255),
    override val btnPrimary: Color = Color(68, 84, 162),
    override val btn2nd: Color = Color(0, 0, 0),
    override val btn3rd: Color = Color(243, 246, 255),
    override val btn4th: Color = Color(245, 245, 245),
    override val btnCaution: Color = Color(248, 209, 69),
    override val btnPositive: Color = Color(43, 208, 179),
    override val btnNegative: Color = Color(221, 96, 96),
    override val btnInfo: Color = Color(91, 165, 210),
    override val btnDisabled: Color = Color(237, 237, 237),
    override val txtParagraph: Color = Color(88, 88, 88),
    override val txtTitle: Color = Color(0, 0, 0),
    override val txtLabel: Color = Color(68, 84, 162),
    override val txtCaution: Color = Color(165, 127, 29),
    override val txtPositive: Color = Color(33, 157, 135),
    override val txtNegative: Color = Color(199, 89, 89),
    override val txtInfo: Color = Color(28, 95, 135),
    override val txtDisabled: Color = Color(194, 194, 194),
    override val txtInactive: Color = Color(159, 159, 159),
    override val txtInverted: Color = Color(255, 255, 255),
    override val chartEmeraldGreen: Color = Color(119, 208, 214),
    override val chartPurple: Color = Color(143, 119, 214),
    override val chartOrange: Color = Color(216, 179, 124),
    override val chartBlue: Color = Color(125, 150, 217),
    override val chartRed: Color = Color(221, 118, 148),
    override val chartGreen: Color = Color(169, 209, 119),
    override val chartYellow: Color = Color(216, 212, 124, 255),
    override val chartPink: Color = Color(244, 132, 226, 255),
    override val chartLightRed: Color = Color.parse("#E08C65"),
    override val chartLightBlue : Color = Color.parse("#6CBDE6"),
    override val riskLevelBgLow: Color = Color.parse("#EBF9E7"),
    override val riskLevelBgMedium: Color = Color.parse("#F0F2FF"),
    override val riskLevelBgHigh: Color = Color.parse("#F7EAFD"),
    override val riskLevelFillLow1: Color = Color.parse("#3F9726"),
    override val riskLevelFillLow2: Color = Color.parse("#337A48"),
    override val riskLevelFillLow3: Color = Color.parse("#29626A"),
    override val riskLevelFillMedium4: Color = Color.parse("#2A4978"),
    override val riskLevelFillMedium5: Color = Color.parse("#244C88"),
    override val riskLevelFillMedium6: Color = Color.parse("#2F3F90"),
    override val riskLevelFillHigh7: Color = Color.parse("#522283"),
    override val riskLevelFillHigh8: Color = Color.parse("#7B2B9E"),
    override val riskLevelFillHigh9: Color = Color.parse("#922B9E"),
    override val riskLevelFillHigh10: Color = Color.parse("#522283"),
    override val riskLevelTextHigh: Color = Color.parse("#522283"),
    override val riskLevelTextMedium: Color = Color.parse("#2F3F90"),
    override val riskLevelTextLow: Color = Color.parse("#337A48"),
) : AppColor()

//data class DarkColor(override val bgDefault: Color = Color.Black) : AppColor()


fun Color.Companion.parse(colorString: String): Color =
    Color(color = android.graphics.Color.parseColor(colorString))