package com.siriustech.merit.app_common.component.common

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxHeight
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.sp
import com.siriustech.merit.app_common.component.container.PaddingStart
import com.siriustech.merit.app_common.component.container.PaddingTop
import com.siriustech.merit.app_common.ext.colorTxtInactive
import com.siriustech.merit.app_common.ext.colorTxtParagraph
import com.siriustech.merit.app_common.theme.LocalDimens
import com.siriustech.merit.app_common.theme.LocalTypography
import com.siriustech.merit.app_common.typeenum.PortfolioAssetType

/**
 * Created by Hein Htet
 */

@Composable
fun SummaryAllocationPercentage(
    modifier: Modifier = Modifier,
    allocations: List<WalletSummaryAllocationPercentageDisplay> = emptyList(),
) {
    val middle = allocations.size / 2
    val firstHalf = allocations.subList(0, middle)
    val secondHalf = allocations.subList(middle, allocations.size)

    Column(modifier) {
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .height(LocalDimens.current.dimen10)
                .clip(RoundedCornerShape(LocalDimens.current.dimen8)),
        ) {
            allocations.forEach {
                val weight = it.percentage / 100
                if (weight > 0) {
                    Box(
                        modifier = Modifier
                            .weight(it.percentage / 100)
                            .fillMaxHeight()
                            .background(it.type.color)
                    )
                }
            }
        }
        PaddingTop(value = LocalDimens.current.dimen12)
        Row(modifier = Modifier.fillMaxWidth(), horizontalArrangement = Arrangement.Start) {
            Column() {
                firstHalf.forEach {
                    AllocationPercentItem(item = it)
                }
            }
            PaddingStart(value = LocalDimens.current.dimen24)
            Column() {
                secondHalf.forEach {
                    AllocationPercentItem(item = it)
                }
            }
        }
    }
}

@Composable
fun AllocationPercentItem(item: WalletSummaryAllocationPercentageDisplay) {
    Row(modifier = Modifier, verticalAlignment = Alignment.CenterVertically) {
        Box(
            modifier = Modifier
                .size(LocalDimens.current.dimen6)
                .background(item.type.color)
        )
        PaddingStart(value = LocalDimens.current.dimen4)
        Text(
            text = (item.displaySummaryName ?: item.type.displayName).plus(": "),
            style = LocalTypography.current.text12.regular.colorTxtInactive().copy(fontSize = 11.sp)
        )
        Text(
            text = item.percentage.toString().plus("%"),
            style = LocalTypography.current.text12.regular.colorTxtParagraph()
                .copy(fontSize = 11.sp)
        )
    }
}

data class WalletSummaryAllocationPercentageDisplay(
    val type: PortfolioAssetType,
    val rawSummaryName: String = "",
    val percentage: Float,
    val displaySummaryName: String? = null,
)


@Preview(showBackground = true)
@Composable
fun PreviewSummaryAllocationPercentage() {
    SummaryAllocationPercentage(
        allocations = listOf(
            WalletSummaryAllocationPercentageDisplay(
                type = PortfolioAssetType.EQUITY,
                percentage = 18.2f,
            ), WalletSummaryAllocationPercentageDisplay(
                type = PortfolioAssetType.CASH_EQUIVALENT,
                percentage = 20f,
            ),
            WalletSummaryAllocationPercentageDisplay(
                type = PortfolioAssetType.VIRTUAL_ASSET,
                percentage = 31.8f,
            ),
            WalletSummaryAllocationPercentageDisplay(
                type = PortfolioAssetType.COMMODITIES,
                percentage = 30f,
            )
        )
    )
}