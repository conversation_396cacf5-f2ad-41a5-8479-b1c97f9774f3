package com.siriustech.merit.app_common.screen.pinlogin

import com.siriustech.merit.apilayer.service.authentication.login.LoginUseCase
import com.siriustech.merit.apilayer.service.authentication.login.LoginUserRequest
import com.siriustech.merit.app_common.Constants
import com.siriustech.merit.app_common.data.CommonSharedPreferences
import com.siriustech.merit.app_common.theme.AppViewModel
import dagger.hilt.android.lifecycle.HiltViewModel
import javax.inject.Inject
import javax.inject.Named
import kotlinx.coroutines.channels.Channel
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.catch
import kotlinx.coroutines.flow.collectLatest
import kotlinx.coroutines.flow.onCompletion
import kotlinx.coroutines.flow.onStart
import kotlinx.coroutines.flow.receiveAsFlow
import kotlinx.coroutines.launch

/**
 * Created by <PERSON><PERSON> Htet
 */
@HiltViewModel
class PinLoginViewModel @Inject constructor(
    val commonSharedPreferences: CommonSharedPreferences,
    private val loginUseCase: LoginUseCase,
    @Named("DeviceID") private val deviceId: String,
) : AppViewModel() {

    private val _pinLoginEvent = Channel<PinLoginEvent>(capacity = Channel.BUFFERED)
    private val _pinCode = MutableStateFlow("")
    private val _unMatchedPinCount = MutableStateFlow(5)
    private val _pinResetCount = MutableStateFlow(4)

    inner class PinLoginInputs : BaseInputs() {
        fun onPinChanged(pin: String) {
            _pinCode.value = pin
            scope.launch {
                if (pin.length == Constants.DEFAULT_OTP_COUNT) {
                    pinLogin()
                }
            }
        }

        fun onBiometricLogin() {
            _pinCode.value = commonSharedPreferences.loginPinCode
            // need to update with Proper API
            pinLogin()
        }

        fun clearPin() {
            commonSharedPreferences.setUserLoginPin("")
        }
    }

    inner class PinLoginOutputs : BaseOutputs() {
        val pinLoginEvent: Flow<PinLoginEvent>
            get() = _pinLoginEvent.receiveAsFlow()

        val pinCode: StateFlow<String>
            get() = _pinCode
        val pinResetCount: StateFlow<Int>
            get() = _pinResetCount

        val alreadyPinSetup: Boolean
            get() = commonSharedPreferences.alreadyPinSetup

        val alreadySetupBiometric: Boolean
            get() = commonSharedPreferences.alreadyBiometricSetup

    }

    override val inputs: PinLoginInputs = PinLoginInputs()

    override val outputs: PinLoginOutputs = PinLoginOutputs()


    private fun pinLogin() {
        scope.launch {
            val request = LoginUserRequest(
                pin = _pinCode.value,
                deviceId = deviceId,
                username = commonSharedPreferences.userEmail
            )
            loginUseCase(param = request)
                .onStart { inputs.emitLoading(true) }
                .onCompletion { inputs.emitLoading(false) }
                .catch {
                    _pinResetCount.value += 1
                    _pinCode.value = ""
                    if (_unMatchedPinCount.value == 1) {
                        commonSharedPreferences.clearData()
                        _pinLoginEvent.send(PinLoginEvent.ReachMaxPinLoginAttempt)
                    } else {
                        _unMatchedPinCount.value -= 1
                        _pinLoginEvent.send(PinLoginEvent.OnPinUnMatched(_unMatchedPinCount.value))
                    }
                }
                .collectLatest {
                    commonSharedPreferences.setSessionID(it.sessionId.orEmpty())
                    _pinLoginEvent.send(PinLoginEvent.OnPinMatched)
                }
        }
    }
}