package com.siriustech.merit.app_common.screen.pdfviewer

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.platform.LocalContext
import androidx.fragment.app.FragmentActivity
import androidx.hilt.navigation.compose.hiltViewModel
import com.rizzi.bouquet.ResourceType
import com.rizzi.bouquet.VerticalPDFReader
import com.rizzi.bouquet.VerticalPdfReaderState
import com.siriustech.merit.app_common.component.header.CommonToolbarWithBackMenu
import com.siriustech.merit.app_common.theme.LocalAppColor

/**
 * Created by Hein Htet
 */
@Composable
fun PDFViewerScreen(
    arguments: PDFViewerArguments?,
    viewModel: PdfViewerViewModel = hiltViewModel(),
) {

    val activity = LocalContext.current as FragmentActivity

    val pdfVerticallReaderState = VerticalPdfReaderState(
        resource = ResourceType.Remote(
            url = arguments?.url.orEmpty(),
            headers = hashMapOf("sid" to viewModel.outputs.sessionId)
        ),
        isZoomEnable = true
    )

    Column(modifier = Modifier.fillMaxSize()) {
        if (arguments?.showToolbar == true) {
            CommonToolbarWithBackMenu(
                title = arguments?.title.orEmpty(),
                onBackPressed = {
                    activity.finish()
                }
            )
        }
        VerticalPDFReader(
            state = pdfVerticallReaderState,
            modifier = Modifier
                .weight(1f)
                .background(LocalAppColor.current.bgDefault)
        )
    }
}