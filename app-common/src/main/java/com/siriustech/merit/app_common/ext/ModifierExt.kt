package com.siriustech.merit.app_common.ext

import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.interaction.MutableInteractionSource
import androidx.compose.runtime.Composable
import androidx.compose.runtime.remember
import androidx.compose.ui.Modifier
import androidx.compose.ui.composed
import androidx.compose.ui.draw.drawBehind
import androidx.compose.ui.geometry.Offset
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.unit.Dp
import androidx.compose.ui.unit.dp
import com.siriustech.merit.app_common.theme.LocalAppColor

/**
 * Created by <PERSON><PERSON><PERSON>
 */


@Composable
fun Modifier.backgroundBgDefault() = this.background(LocalAppColor.current.bgDefault)


/**
 * Creates a Modifier that adds a bottom stroke to a Composable.
 *
 * @param color The color of the stroke.
 * @param strokeWidth The thickness of the stroke.
 * @return A Modifier that draws a bottom stroke.
 */
fun Modifier.bottomStroke(color: Color, strokeWidth: Dp = 2.dp): Modifier = this.then(
    Modifier.drawBehind {
        val strokePx = strokeWidth.toPx()
        // Draw a line at the bottom
        drawLine(
            color = color,
            start = Offset(x = 0f, y = size.height - strokePx / 2),
            end = Offset(x = size.width, y = size.height - strokePx / 2),
            strokeWidth = strokePx
        )
    }
)

fun Modifier.noRippleClickable(onClick: () -> Unit): Modifier = composed {
    this.clickable(
        indication = null,
        interactionSource = remember { MutableInteractionSource() }) {
        onClick()
    }
}