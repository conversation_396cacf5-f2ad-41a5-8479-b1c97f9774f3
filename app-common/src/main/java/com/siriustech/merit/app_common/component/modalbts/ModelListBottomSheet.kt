package com.siriustech.merit.app_common.component.modalbts

import androidx.compose.foundation.ExperimentalFoundationApi
import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.ModalBottomSheet
import androidx.compose.material3.SheetState
import androidx.compose.material3.Text
import androidx.compose.material3.rememberModalBottomSheetState
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.painter.Painter
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.tooling.preview.Preview
import com.siriustech.merit.app_common.R
import com.siriustech.merit.app_common.component.button.ButtonProperties
import com.siriustech.merit.app_common.component.button.SecondaryBorderButton
import com.siriustech.merit.app_common.component.button.SecondaryButton
import com.siriustech.merit.app_common.component.container.PaddingStart
import com.siriustech.merit.app_common.component.textfield.InputBox
import com.siriustech.merit.app_common.component.textfield.InputProperties
import com.siriustech.merit.app_common.ext.colorTxtParagraph
import com.siriustech.merit.app_common.ext.colorTxtTitle
import com.siriustech.merit.app_common.ext.noRippleClickable
import com.siriustech.merit.app_common.theme.LocalAppColor
import com.siriustech.merit.app_common.theme.LocalDimens
import com.siriustech.merit.app_common.theme.LocalTypography
import kotlinx.coroutines.launch
import kotlinx.serialization.Serializable

/**
 * Created by Hein Htet
 */

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun ModelListBottomSheet(
    modifier: Modifier = Modifier,
    properties: ModelListBottomSheetProperties,
    sheetState: SheetState = rememberModalBottomSheetState(skipPartiallyExpanded = false),
    onItemClicked: (item: ModalListDataContent) -> Unit = {},
    onItemSelected: (items : List<ModalListDataContent>) -> Unit = {},
    onDismissed: () -> Unit = {},
) {
    val scope = rememberCoroutineScope()

    fun onDismiss() {
        scope.launch {
            sheetState.hide()
            onDismissed()
        }
    }


    ModalBottomSheet(
        dragHandle = {},
        shape = RoundedCornerShape(LocalDimens.current.dimen4),
        containerColor = LocalAppColor.current.bgDefault,
        onDismissRequest = {
            onDismiss()
        }, sheetState = sheetState,
        modifier = Modifier
    ) {
        Box(
            modifier = Modifier
//            .fillMaxHeight(0.8f)
                .then(modifier)
        ) {
            ModelListContent(
                properties = properties,
                onDismissed = {
                    onDismiss()
                }, onItemClicked = { item ->
                    onItemClicked(item)
                    if (properties.allowMultipleSelect.not()) {
                        onDismiss()
                    }
                }, onItemSelected = {
                    onItemSelected(it)
                    onDismiss()
                })
        }
    }
}

@OptIn(ExperimentalFoundationApi::class)
@Composable
fun ModelListContent(
    properties: ModelListBottomSheetProperties,
    onItemClicked: (item: ModalListDataContent) -> Unit = {},
    onItemSelected: (item: List<ModalListDataContent>) -> Unit = {},
    onDismissed: () -> Unit = {},
) {
    var items by remember {
        mutableStateOf(
            properties.items
        )
    }

    var originalItems by remember {
        mutableStateOf(properties.items)
    }

    var searchKeyword by remember {
        mutableStateOf("")
    }

    LaunchedEffect(searchKeyword) {
        items = if (searchKeyword.isEmpty()) {
            originalItems
        } else {
            originalItems.filter { it.title.contains(searchKeyword, ignoreCase = true) }
        }
    }

    Column(
        modifier = Modifier
            .fillMaxWidth()
            .padding(
                horizontal = LocalDimens.current.dimen14,
                vertical = LocalDimens.current.dimen16
            )
    ) {
        ModalListTitle(properties, onDismissed = onDismissed)
        Spacer(modifier = Modifier.height(LocalDimens.current.dimen16))
        if (properties.searchEnable) {
            InputBox(
                onTextChange = {
                    searchKeyword = it
                },
                properties =
                InputProperties(
                    inputPrefixIcon = painterResource(id = R.drawable.ic_search),
                    placeholder = properties.searchPlaceholder,
                ),
            )
            Spacer(modifier = Modifier.height(LocalDimens.current.dimen16))
        }
        LazyColumn(modifier =Modifier.weight(1f)) {
            items(items, key = { it.id }) {
                ModelItemContent(modifier = Modifier.animateItem(
                    fadeInSpec = null,
                    fadeOutSpec = null
                ), type = properties.backgroundType, it, onClicked = { content ->
                    items = items.mapIndexed { i, it ->
                        if (it.id == content.id) it.copy(isSelected = !it.isSelected) else it.copy(
                            isSelected = if (properties.allowMultipleSelect) it.isSelected else false
                        )
                    }
                    onItemClicked(content)
                })
            }
        }
        if(properties.showAction) {
            Row(
                modifier = Modifier.padding(top = LocalDimens.current.dimen16, bottom = LocalDimens.current.dimen16)
            ) {
                SecondaryBorderButton(
                    modifier = Modifier.weight(1f),
                    properties = ButtonProperties(text = stringResource(id = R.string.key0273)),
                    onClicked = {
                        onDismissed()
                    }
                )
                Spacer(modifier = Modifier.width(LocalDimens.current.dimen8))
                SecondaryButton(
                    modifier = Modifier.weight(1f),
                    properties = ButtonProperties(text = stringResource(id = R.string.key0411),
                        enabled = items.any { it.isSelected }),
                    onClicked = {
                        onItemSelected(items.filter { it.isSelected })
                    }
                )
            }
        }
    }
}


@Composable
fun ModalListTitle(properties: ModelListBottomSheetProperties, onDismissed: () -> Unit = {}) {
    Row(
        modifier = Modifier.fillMaxWidth(),
        verticalAlignment = Alignment.CenterVertically
    ) {
        Spacer(modifier = Modifier.width(LocalDimens.current.dimen4))
        if (properties.prefixTitleIcon != null) {
            Image(
                painter = properties.prefixTitleIcon,
                contentDescription = "Prefix Icon"
            )
            Spacer(modifier = Modifier.width(LocalDimens.current.dimen4))
        }
        Text(
            text = properties.prefixTitle,
            style = LocalTypography.current.text14.semiBold.colorTxtTitle()
        )
        Text(
            text = properties.title,
            style = LocalTypography.current.text14.regular.colorTxtParagraph()
        )
        Spacer(modifier = Modifier.weight(1f))
        Image(
            painter = painterResource(id = R.drawable.ic_action_close),
            contentDescription = "Close Image Resource",
            modifier = Modifier.noRippleClickable { onDismissed() }
        )
    }
}

@Composable
fun ModelItemContent(
    modifier: Modifier = Modifier,
    type: BackgroundType = BackgroundType.BTN3RD,
    item: ModalListDataContent,
    onClicked: (item: ModalListDataContent) -> Unit = {},
) {

    @Composable
    fun onGetBackgroundColor() = when (type) {
        BackgroundType.BTN3RD -> LocalAppColor.current.btn3rd
        BackgroundType.SECONDARY -> LocalAppColor.current.btn2nd
    }

    @Composable
    fun onGetTextColor() = when (type) {
        BackgroundType.BTN3RD -> LocalAppColor.current.txtTitle
        BackgroundType.SECONDARY -> LocalAppColor.current.txtInverted
    }

    Box(
        modifier = modifier
            .fillMaxWidth()
            .noRippleClickable { onClicked(item) }
            .clip(RoundedCornerShape(LocalDimens.current.dimen8))
            .background(if (item.isSelected) onGetBackgroundColor() else Color.Transparent)
            .padding(
                vertical = LocalDimens.current.dimen12,
                horizontal = LocalDimens.current.dimen12
            )
    ) {
        Row(verticalAlignment = Alignment.CenterVertically) {
            if (item.iconResId != null) {
                Image(
                    painter = painterResource(id = item.iconResId),
                    contentDescription = "Icon Image Resource"
                )
                PaddingStart(value = LocalDimens.current.dimen8)
            }
            Text(
                text = item.title,
                style = if (item.isSelected) LocalTypography.current.text14.medium.copy(color = onGetTextColor()) else LocalTypography.current.text14.light.colorTxtParagraph()
            )
            if (!item.suffixTitle.isNullOrEmpty()) {
                PaddingStart(value = LocalDimens.current.dimen8)
                Text(
                    text = item.suffixTitle.orEmpty(),
                    style = if (item.isSelected) LocalTypography.current.text14.medium.copy(color = onGetTextColor()) else LocalTypography.current.text14.light.colorTxtParagraph()
                )
            }
        }
    }
}

data class ModelListBottomSheetProperties(
    val prefixTitleIcon: Painter? = null,
    val prefixTitle: String = "",
    val title: String = "",
    val iconTitle: Painter? = null,
    val description: String = "",
    val buttonText: String = "",
    val items: List<ModalListDataContent> = emptyList(),
    val searchEnable: Boolean = false,
    var backgroundType: BackgroundType = BackgroundType.BTN3RD,
    val searchPlaceholder: String = "",
    val allowMultipleSelect: Boolean = false,
    val showAction : Boolean = false,
)


@Serializable
data class ModalListDataContent(
    val id: String = "",
    var title: String = "",
    val suffixTitle: String? = null,
    val value: String = "",
    var isSelected: Boolean = false,
    var items: List<ModalListDataContent> = emptyList(),
    val iconResId: Int? = null,
) : java.io.Serializable

enum class BackgroundType {
    SECONDARY,
    BTN3RD,
}


@Preview(showBackground = true, showSystemUi = true)
@Composable
fun PreviewCommonModelListBottomSheet() {
    ModelListContent(
        properties = ModelListBottomSheetProperties(
            searchEnable = false,
            prefixTitle = "Action: ",
            title = "Select Country/Region",
            searchPlaceholder = "Search country/region",
            items = listOf(
                ModalListDataContent(
                    id = "1",
                    title = "Thailand",
                    value = "TH",
                    isSelected = true,
                    iconResId = R.drawable.ic_camera
                ),
                ModalListDataContent(
                    id = "2",
                    title = "United State",
                    value = "US",
                    suffixTitle = "this is suffix title"
                ),
            )
        )
    )
}
