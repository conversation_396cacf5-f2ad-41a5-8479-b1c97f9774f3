package com.siriustech.merit.app_common.component.separator

import androidx.compose.foundation.Image
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.res.painterResource
import com.siriustech.merit.app_common.R

/**
 * Created by <PERSON><PERSON>
 */

@Composable
fun SeparatorLine(modifier: Modifier = Modifier) {
    Image(
        painter = painterResource(id = R.drawable.ic_separatoer),
        contentDescription = "Separator Line",
        modifier = Modifier.then(modifier)
    )
}