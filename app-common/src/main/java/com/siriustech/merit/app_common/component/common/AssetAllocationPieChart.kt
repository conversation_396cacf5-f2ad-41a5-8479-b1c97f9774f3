package com.siriustech.merit.app_common.component.common

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxHeight
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.remember
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.toArgb
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.tooling.preview.Preview
import com.github.mikephil.charting.charts.PieChart
import com.github.mikephil.charting.data.PieData
import com.github.mikephil.charting.data.PieDataSet
import com.github.mikephil.charting.data.PieEntry
import com.siriustech.merit.app_common.R
import com.siriustech.merit.app_common.component.chart.AppPieChart
import com.siriustech.merit.app_common.data.display.PortfolioItemDisplayData
import com.siriustech.merit.app_common.ext.colorTxtInactive
import com.siriustech.merit.app_common.ext.colorTxtParagraph
import com.siriustech.merit.app_common.theme.LocalDimens
import com.siriustech.merit.app_common.theme.LocalTypography
import com.siriustech.merit.app_common.typeenum.PortfolioAssetType

/**
 * Created by Hein Htet
 */

@Composable
fun AssetAllocationPieChart(
    modifier: Modifier = Modifier,
    items: List<PortfolioItemDisplayData>,
    onInfoClicked: () -> Unit = {},
) {

    val middle = items.size / 2
    val firstHalf = items.subList(0, middle)
    val secondHalf = items.subList(middle, items.size)
    val context = LocalContext.current
    val pieChart = remember {
        PieChart(context)
    }

    Column(
        modifier = Modifier
            .fillMaxSize()
            .then(modifier)
    ) {
//        Row(verticalAlignment = Alignment.CenterVertically) {
//            Text(
//                text = stringResource(id = R.string.key0401),
//                style = LocalTypography.current.text14.medium.colorTxtTitle(),
//                modifier = Modifier
//                    .padding(
//                        top = LocalDimens.current.dimen12,
//                        start = LocalDimens.current.dimen12,
//                        end = LocalDimens.current.dimen12,
//                        bottom = LocalDimens.current.dimen12
//                    )
//            )
//            Image(
//                modifier = Modifier.clickable { onInfoClicked() },
//                painter = painterResource(id = R.drawable.ic_info_big),
//                contentDescription = "Info Image Resource"
//            )
//        }

        val entries = items.map { PieEntry(it.percentage.toFloat()) }
        val dataSet = PieDataSet(entries, "AssetAllocation Pie Chart")
            .apply {
                colors = items.map { it.type.color.toArgb() }
                this.setAutomaticallyDisableSliceSpacing(false)
                this.setDrawValues(false)
            }

        val pieData = PieData(dataSet)

        Box(modifier = Modifier.fillMaxSize(), contentAlignment = Alignment.Center) {
            Row(modifier = Modifier.fillMaxSize()) {
                Column(
                    modifier = Modifier
                        .weight(0.6f)
                        .fillMaxHeight(),
                    verticalArrangement = Arrangement.Center
                ) {
                    secondHalf.forEach {
                        DisplayAllocationLabel(isLeft = true, it)
                    }
                }
                AppPieChart(
                    pieChart = pieChart,
                    pieData = pieData,
                    modifier = Modifier
                        .weight(0.8f)
                        .fillMaxHeight()
                )
                Column(
                    modifier = Modifier
                        .weight(0.6f)
                        .fillMaxHeight(),
                    verticalArrangement = Arrangement.Center
                ) {
                    firstHalf.forEach {
                        DisplayAllocationLabel(isLeft = false, it)
                    }
                }
            }
            if (items.isEmpty()) {
                Text(
                    text = stringResource(id = R.string.key0572),
                    textAlign = TextAlign.Center,
                    style = LocalTypography.current.text12.regular.colorTxtInactive()
                )
            }
        }
    }
}

@Composable
fun DisplayAllocationLabel(isLeft: Boolean, item: PortfolioItemDisplayData) {
    Row(
        verticalAlignment = Alignment.CenterVertically,
        horizontalArrangement = if (isLeft) Arrangement.End else Arrangement.Start,
        modifier = Modifier
            .fillMaxWidth()
            .padding(
                start = LocalDimens.current.dimen8,
                end = LocalDimens.current.dimen8,
                top = LocalDimens.current.dimen4,
                bottom = LocalDimens.current.dimen4,
            )
    ) {
        if (!isLeft) {
            Box(
                modifier = Modifier
                    .size(LocalDimens.current.dimen6)
                    .clip(RoundedCornerShape(50))
                    .background(item.type.color)
            )
            Spacer(modifier = Modifier.width(LocalDimens.current.dimen6))
        }
        Text(
            text = item.displaySummaryName,
//            text = if(item.type == PortfolioAssetType.UNKNOWN_TYPE) item.rawSummaryName.orEmpty() else item.type.displayName,
            modifier = Modifier.weight(1f, fill = false),
            style = LocalTypography.current.text14.regular.colorTxtParagraph()
        )
        if (isLeft) {
            Spacer(modifier = Modifier.width(LocalDimens.current.dimen6))
            Box(
                modifier = Modifier
                    .size(LocalDimens.current.dimen6)
                    .clip(RoundedCornerShape(50))
                    .background(item.type.color)
            )
        }
    }
}

@Preview(showBackground = true)
@Composable
fun PreviewAssetAllocationPieChart() {
    AssetAllocationPieChart(
        items = listOf(
            PortfolioItemDisplayData(
                type = PortfolioAssetType.VIRTUAL_ASSET,
                percentage = "20",
            ),
            PortfolioItemDisplayData(
                type = PortfolioAssetType.CASH_EQUIVALENT,
                percentage = "20",
            ),
        )
    )
}