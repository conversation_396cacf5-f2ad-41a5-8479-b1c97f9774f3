package com.siriustech.merit.app_common.screen.chat.component

import android.graphics.Color
import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.ColumnScope
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.tooling.preview.Preview
import coil3.compose.AsyncImage
import coil3.network.NetworkHeaders
import coil3.network.httpHeaders
import coil3.request.CachePolicy
import coil3.request.ImageRequest
import coil3.request.crossfade
import com.siriustech.merit.app_common.R
import com.siriustech.merit.app_common.data.display.UserBasicInfoDisplay
import com.siriustech.merit.app_common.ext.colorTxtTitle
import com.siriustech.merit.app_common.ext.noRippleClickable
import com.siriustech.merit.app_common.theme.LocalAppColor
import com.siriustech.merit.app_common.theme.LocalDimens
import com.siriustech.merit.app_common.theme.LocalTypography
import timber.log.Timber

/**
 * Created by Hein Htet
 */

@Composable
fun ColumnScope.ChatUserMessageItem(
    message: ChatMessageDisplayModel = ChatMessageDisplayModel(),
    userInfo: UserBasicInfoDisplay = UserBasicInfoDisplay(),
    showAvatar: Boolean = false,
    onAttachmentClicked: (AttachmentDisplayModel) -> Unit = {},
) {
    Row(
        modifier = Modifier
            .fillMaxWidth(0.95f)
            .align(Alignment.End),
    ) {
        Column(
            modifier = Modifier.weight(0.8f),
            horizontalAlignment = Alignment.End
        ) {
            if (message.text.isNotEmpty()) {
                Bubble(showShape = showAvatar) {
                    Text(
                        text = message.text,
                        style = LocalTypography.current.text12.light.colorTxtTitle(),
                        modifier = Modifier.padding(LocalDimens.current.dimen8)
                    )
                }
            }
            Attachment(showAvatar, message.attachments,onAttachmentClicked)
        }
        if (showAvatar) {
            Box(
                modifier = Modifier.size(LocalDimens.current.dimen44),
                contentAlignment = Alignment.Center
            ) {
                AsyncImage(
                    modifier =
                    Modifier
                        .size(LocalDimens.current.dimen32)
                        .clip(CircleShape),
                    model = ImageRequest.Builder(LocalContext.current)
                        .data(userInfo.profileImage)
                        .crossfade(true)
                        .httpHeaders(
                            headers = NetworkHeaders.Builder()
                                .set("sid", userInfo.sid)
                                .build()
                        )
                        .diskCachePolicy(CachePolicy.ENABLED)
                        .build(),
                    placeholder = painterResource(R.drawable.user_default_profile),
                    error = painterResource(R.drawable.user_default_profile),
                    contentDescription = "Product Category Placeholder",
                    contentScale = ContentScale.Crop,
                    onError = {
                        Timber.d("Image Loading error ${it.result.throwable.message}")
                    }
                )
            }
        } else {
            Box(modifier = Modifier.size(LocalDimens.current.dimen40))
        }
    }
}

@Composable
fun Attachment(
    showAvatar: Boolean,
    attachments: List<AttachmentDisplayModel>,
    onAttachmentClicked : (AttachmentDisplayModel) -> Unit ={}
) {
    if (attachments.isNotEmpty()) {
        Column(modifier = Modifier) {
            attachments.forEachIndexed { index, attachmentDisplayModel ->
                Bubble(showShape = showAvatar && index == 0) {
                    ChatAttachment(
                        attachmentDisplayModel = attachmentDisplayModel,
                        showBorder = false,
                        showRemoveButton = false,
                        modifier = Modifier
                            .clip(RoundedCornerShape(LocalDimens.current.dimen2))
                            .background(LocalAppColor.current.bgDefault)
                            .padding(LocalDimens.current.dimen8)
                            .noRippleClickable {
                                onAttachmentClicked(attachmentDisplayModel)
                            }
                    )
                }
            }
        }
    }
}


@Composable
fun Bubble(showShape: Boolean = false, content: @Composable () -> Unit = {}) {
    Row(
        modifier = Modifier
            .padding(vertical = LocalDimens.current.dimen4)
    ) {
        Box(
            modifier = Modifier
                .weight(1f, fill = false)
                .background(LocalAppColor.current.bgInfo)
                .clip(RoundedCornerShape(LocalDimens.current.dimen2))
        ) {
            content()
        }
        if (showShape) {
            Image(
                modifier = Modifier,
                painter = painterResource(id = R.drawable.ic_chat_bubble_shape),
                contentDescription = "Shape Image Resource",
            )
        }
    }
}

@Preview(showBackground = true, showSystemUi = true, backgroundColor = Color.WHITE.toLong())
@Composable
fun PreviewChatUserMessageItem() {
    Column(
        modifier = Modifier
            .fillMaxSize()
    ) {
        ChatUserMessageItem(
            showAvatar = true,
            message = ChatMessageDisplayModel(
                attachments = listOf(
                    AttachmentDisplayModel(
                        type = "PDF",
                        name = "Attachment01.pdf"
                    ),
                    AttachmentDisplayModel(
                        type = "PDF",
                        name = "Attachment02.pdf"
                    )
                ),
                text = "Lorem Ipsum is simply dummy t ambled it to make a type specimen book. It has survived not only five centuries, but also the leap into electronic typesetting, remaining essentially unchanged. It was popularised in the 1960s with the release of Letraset sheets containing Lorem Ipsum passages, and more recently with desktop publishing software like Aldus PageMaker including versions of Lorem Ipsum"
            )
        )
        ChatUserMessageItem(
            message = ChatMessageDisplayModel(
                text = "Hello"
            )
        )
        ChatUserMessageItem(
            message = ChatMessageDisplayModel(
                attachments = listOf(
                    AttachmentDisplayModel(
                        type = "PDF",
                        name = "Attachment01.pdf"
                    ),
                    AttachmentDisplayModel(
                        type = "PDF",
                        name = "Attachment02.pdf"
                    )
                ),
                text = "Lorem Ipsum is simply dummy t ambled it to make a type specimen book. It has survived not only five centuries, but also the leap into electronic typesetting, remaining essentially unchanged. It was popularised in the 1960s with the release of Letraset sheets containing Lorem Ipsum passages, and more recently with desktop publishing software like Aldus PageMaker including versions of Lorem Ipsum"
            )
        )
    }
}