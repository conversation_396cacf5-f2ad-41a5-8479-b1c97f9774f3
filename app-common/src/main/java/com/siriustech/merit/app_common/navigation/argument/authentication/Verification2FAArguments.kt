package com.siriustech.merit.app_common.navigation.argument.authentication

import androidx.lifecycle.SavedStateHandle
import androidx.navigation.toRoute
import com.siriustech.merit.app_common.ext.serializableType
import com.siriustech.merit.app_common.navigation.Verification2FA
import com.siriustech.merit.app_common.typeenum.Auth2FAType
import com.siriustech.merit.app_common.typeenum.BizType
import kotlin.reflect.typeOf
import kotlinx.serialization.Serializable

@Serializable
data class Verification2FAArguments(
    val authType: Auth2FAType = Auth2FAType.EMAIL,
    val token: String? = null,
    val email: String? = null,
    val phone : String? = null,
    val userBizType: BizType = BizType.CONFIG_2FA,
) {
    companion object {
        val typeMap =
            mapOf(typeOf<Verification2FAArguments>() to serializableType<Verification2FAArguments>())

        fun from(savedStateHandle: SavedStateHandle) =
            savedStateHandle.toRoute<Verification2FA>(typeMap)
    }
}
