package com.siriustech.merit.app_common.component.common

import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.width
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.tooling.preview.Preview
import com.siriustech.merit.app_common.data.display.WalletSummaryDisplayData
import com.siriustech.merit.app_common.ext.colorTxtInactive
import com.siriustech.merit.app_common.ext.colorTxtLabel
import com.siriustech.merit.app_common.ext.colorTxtTitle
import com.siriustech.merit.app_common.theme.LocalAppColor
import com.siriustech.merit.app_common.theme.LocalDimens
import com.siriustech.merit.app_common.theme.LocalTypography
import com.siriustech.merit.app_common.R as AppCommonR

/**
 * Created by Hein Htet
 */

@Composable
fun WalletSummary(
    modifier: Modifier = Modifier,
    showDetailsInfoIcon: Boolean = true,
    showTotalBalanceText: Boolean = true,
    data: WalletSummaryDisplayData = WalletSummaryDisplayData(),
    onInfoDetailsClicked: () -> Unit = {},
) {
    if (data.totalBalance.isEmpty()) {
        return
    }
    Box(
        modifier = Modifier
            .fillMaxWidth()
            .background(LocalAppColor.current.bgDefault)
            .then(modifier)
    ) {
        Row(
            modifier = Modifier.fillMaxWidth(),
            horizontalArrangement = Arrangement.SpaceBetween,
            verticalAlignment = Alignment.Bottom
        ) {
            WalletSummaryAmount(
                amount = data.totalBalance,
                currency = data.currency,
                showDetailsInfoIcon = showDetailsInfoIcon,
                showTotalBalanceText = showTotalBalanceText,
                onInfoDetailsClicked
            )
            PriceChangeRate(
                modifier = Modifier,
                priceChange = data.priceChange,
                priceChangeRate = data.priceChangeRate,
            )
        }
    }
}

@Composable
fun WalletSummaryAmount(
    amount: String = "",
    currency: String = "",
    showDetailsInfoIcon: Boolean = true,
    showTotalBalanceText: Boolean = true,

    onInfoDetailsClicked: () -> Unit,
) {
    Column {
        if (showTotalBalanceText) {
            Text(
                text = stringResource(id = AppCommonR.string.key0348),
                style = LocalTypography.current.text14.regular.colorTxtLabel(),
            )
        }

        Row(verticalAlignment = Alignment.CenterVertically) {
            Text(
                text = amount,
                style = LocalTypography.current.text22.bold.colorTxtTitle()
            )
            Text(
                modifier = Modifier.padding(
                    start = LocalDimens.current.dimen4,
                    top = LocalDimens.current.dimen4
                ),
                text = currency,
                style = LocalTypography.current.text16.regular.colorTxtInactive()
            )
            Spacer(modifier = Modifier.width(LocalDimens.current.dimen4))
            if (showDetailsInfoIcon) {
                Image(
                    modifier = Modifier
                        .clickable { onInfoDetailsClicked() },
                    painter = painterResource(id = AppCommonR.drawable.ic_info_big),
                    contentDescription = "Info Image Resource"
                )
            }
        }
    }
}

@Preview
@Composable
fun PreviewWalletSummary() {
    WalletSummary(
        data = WalletSummaryDisplayData(
            totalBalance = "999999999",
            currency = "USD",
            priceChange = "5.55",
            priceChangeRate = "5.55"
        )
    )
}