package com.siriustech.merit.app_common.screen.pdfviewer

import com.siriustech.merit.app_common.data.CommonSharedPreferences
import com.siriustech.merit.app_common.theme.AppViewModel
import dagger.hilt.android.lifecycle.HiltViewModel
import javax.inject.Inject

/**
 * Created by <PERSON><PERSON>
 */
@HiltViewModel
class PdfViewerViewModel @Inject constructor(
    private val commonSharedPreferences: CommonSharedPreferences,
) : AppViewModel() {

    override val inputs = PdfViewerInputs()
    override val outputs = PdfViewerOutputs()


    inner class PdfViewerInputs : BaseInputs()
    inner class PdfViewerOutputs : BaseOutputs() {
        val sessionId: String
            get() = commonSharedPreferences.sessionId
    }
}