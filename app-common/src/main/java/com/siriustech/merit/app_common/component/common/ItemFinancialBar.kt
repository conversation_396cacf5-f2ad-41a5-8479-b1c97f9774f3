package com.siriustech.merit.app_common.component.common

import android.content.Context
import android.content.res.Resources
import android.graphics.Canvas
import android.graphics.Color
import android.graphics.LinearGradient
import android.graphics.Paint
import android.graphics.Path
import android.graphics.RectF
import android.util.AttributeSet
import android.util.Log
import android.util.TypedValue
import android.view.View
import androidx.core.content.ContextCompat
import com.siriustech.merit.app_common.R
import kotlin.math.abs


/**
 * Created by He<PERSON> Htet
 */


data class FinancialBarModel(
    val price: Float,
    val width: Float = 24f,
    val height: Float,
    val barType: FinancialBarType = FinancialBarType.GREEN,
    val isNegativeBar: Boolean = false,
)


enum class FinancialBarType {
    GREEN,
    GRAY,
    RED,
    DARK_GRAY
}

@Suppress("TooManyFunctions")
class ItemFinancialBar @JvmOverloads constructor(
    context: Context,
    attributeSet: AttributeSet?,
    defAttrStyle: Int = 0,
) : View(context, attributeSet, defAttrStyle) {

    private var mWidthDp = 14f.dpToPx()
    private var mMaxDisplay = 5
    private var mIsUnderlineChart = false
    private var mMaxHeight = 45f.dpToPx()
    private var mPaddingDp = DEFAULT_PADDING.getPxToDp()
    private var mPaddingBarDp = DEFAULT_BAR_PADDING.getPxToDp()
    private var items = mutableListOf<FinancialBarModel>()
    private var lastBarType: FinancialBarType? = null


    private val linePaint = Paint().apply {
        color = ContextCompat.getColor(context, R.color.black)
        strokeWidth = 0.5f.getPxToDp()
    }

    private val grayPaint = Paint().apply {
        color = context.getColor(R.color.financialBarGrayColor)
        isAntiAlias = true
    }

    private val darkGrayPaint = Paint().apply {
        isAntiAlias = true
    }

    private val redPaint = Paint().apply {
        isAntiAlias = true
        color = context.getColor(R.color.txtNegative)
    }

    private val greenPaint = Paint().apply {
        isAntiAlias = true
        color = context.getColor(R.color.txtPositive)
    }

    init {
        val ta = context.obtainStyledAttributes(attributeSet, R.styleable.ItemFinancialBar)
        mWidthDp = ta.getDimension(R.styleable.ItemFinancialBar_ifb_width, mWidthDp)
        mMaxHeight = ta.getDimension(R.styleable.ItemFinancialBar_ifb_height, mMaxHeight)
        ta.recycle()
    }

    fun setMaxDisplay(maxDisplay: Int) {
        mMaxDisplay = maxDisplay
    }

    fun setBarWidth(width: Float) {
        this.mWidthDp = width
    }

    fun isUnderlineChart(underlineChart: Boolean) {
        mIsUnderlineChart = underlineChart
    }

    fun setMaxHeight(height: Float) {
        mMaxHeight = height.dpToPx()
    }

    fun setBarPadding(padding: Float) {
        mPaddingBarDp = padding.dpToPx()
    }

    fun updateLastBarType(barType: FinancialBarType) {
        lastBarType = barType
    }

    fun updateBars(prices: List<Double>, isNegativeColoringBarType: Boolean = false,) {
        if (prices.isEmpty()) {
            return
        }
        this.items.clear()
        val maxPrice = prices.getHighestValue()
        val minPrice = prices.getLowestValue()
        val maxHeight = mMaxHeight
        val barItems = mutableListOf<FinancialBarModel>()
        val last5Prices = ArrayList(prices.takeLast(mMaxDisplay))
        val count = mMaxDisplay - last5Prices.count()
        for (i in 0 until count) {
            last5Prices.add(i, 0.0)
        }
        last5Prices.forEachIndexed { index, d ->
            val price = last5Prices[index]
            val absPriceValue = abs(price)
            val percentage = maxPrice.getPercentageOfValue(absPriceValue)
            val valueFromPercentage = maxHeight.percentageFromValue(percentage).toFloat()
            val height: Float = if (absPriceValue >= maxPrice) {
                maxHeight
            } else if(valueFromPercentage <= 0) {
                5f
            }
            else {
                valueFromPercentage
            }
            barItems.add(
                FinancialBarModel(
                    price.toFloat(),
                    height = height,
                    barType = if (index == last5Prices.count() - 1) lastBarType
                        ?: last5Prices.getBarType(isNegativeColoringBarType) else FinancialBarType.GRAY,
                    isNegativeBar = price < 0
                )
            )
        }
        this.items.addAll(barItems)
        invalidate()
    }


    override fun onDraw(canvas: Canvas) {
        super.onDraw(canvas)
        var xPos = 0f
        items.forEachIndexed { index, _ ->
            // draw Rectangle bar
            drawBar(canvas, xPos, index)
            xPos = (xPos + mWidthDp + mPaddingBarDp)
        }
        if (mIsUnderlineChart) {
            drawLine(canvas, xPos)
        }
    }

    private fun getBarWidthValue(maxItem: Int): Float {
        val allPadding = DEFAULT_PADDING.times(maxItem)
        return measuredWidth.div(maxItem).minus(allPadding)
    }

    private fun drawBar(canvas: Canvas, xPos: Float, index: Int) {
        val item = items[index]

        val left = xPos + mPaddingBarDp
        val hasNegativeBar = hasNegativeBar()
        val halfHeight = getBarHeight(hasNegativeBar)

        val itemBarHeight = when {
            isAllNegativeBar() || isAllPositiveBar() -> item.height
            else -> item.height / 2
        }

        val itemBarHeightAbs =
            if (abs(itemBarHeight) > 0 && abs(itemBarHeight) < MIN_ITEM_BAR_HEIGHT) {
                2.5f
            } else {
                itemBarHeight
            }

        val top = when {
            isAllNegativeBar() -> itemBarHeightAbs
            item.isNegativeBar -> halfHeight + itemBarHeightAbs
            else -> halfHeight - itemBarHeightAbs
        }

        val heightDp = halfHeight
        val bottom = when {
            isAllNegativeBar() -> 0f
            item.isNegativeBar -> heightDp + 1f
            else -> heightDp
        }
        val right = (xPos + mWidthDp)

        val rect = RectF(left, top, right, bottom)
        val cornerRadius =
            DEFAULT_BAR_RADIUS
        val path = if (item.isNegativeBar) {
            Path().apply {
                addRoundRect(
                    rect,
                    floatArrayOf(
                        0f,
                        0f,
                        0f,
                        0f,
                        cornerRadius,
                        cornerRadius,
                        cornerRadius,
                        cornerRadius
                    ),
                    Path.Direction.CW
                )
            }
        } else {
            Path().apply {
                addRoundRect(
                    rect,
                    floatArrayOf(
                        cornerRadius,
                        cornerRadius,
                        cornerRadius,
                        cornerRadius,
                        0f,
                        0f,
                        0f,
                        0f
                    ),
                    Path.Direction.CW
                )
            }
        }

        canvas.drawPath(path, item.barType.mapBarTypeToPaint())
    }

//    private fun getGradientShape(item: FinancialBarModel): LinearGradient {
//
//        val gray = Colar(175, 175, 175)
//        val grayAlpha = Colar(255, 255, 255)
//
//        val colors = (gray toColor grayAlpha).run {
//            gradient0 { 0 upTo item.height.toInt() }
//        }
//        return LinearGradient(
//            0f,
//            0f,
//            item.width,
//            item.height,
//            colors,
//            null, Shader.TileMode.CLAMP
//        )
//        return when (item.barType) {
//            FinancialBarType.GREEN -> {
//                val pink = Colar(245, 9, 253)
//                val lime = Colar(0, 253, 32)
//
//                val colors = (pink toColor lime).run {
//                    gradient0 { 0 upTo 3 }
//                }
//                LinearGradient(
//                    0f,
//                    0f,
//                    item.width,
//                    item.height,
//                    colors,
//                    null, Shader.TileMode.CLAMP
//                )
//            }
//
//            FinancialBarType.GRAY -> {
//                LinearGradient(
//                    0f,
//                    0f,
//                    item.width,
//                    item.height,
//                    intArrayOf(0xFFAFAFAF.toInt(), 0x80D7D7D7.toInt()),
//                    null, Shader.TileMode.CLAMP
//                )
//            }
//
//            FinancialBarType.RED -> {
//                LinearGradient(
//                    0f,
//                    0f,
//                    item.width,
//                    item.height,
//                    intArrayOf(0x00D2B343.toInt(), 0x00D2B343.toInt(), 0xFFDC0303.toInt()),
//                    floatArrayOf(0.2f, 0.6f, 1f), Shader.TileMode.CLAMP
//                )
//            }
//
//            else -> {
//                LinearGradient(
//                    0f,
//                    0f,
//                    item.width,
//                    item.height,
//                    intArrayOf(0xFFAFAFAF.toInt(), 0x80D7D7D7.toInt()),
//                    null, Shader.TileMode.CLAMP
//                )
//            }
//        }
//    }

    /**
     * @param canvas : Canvas
     * @param xPos : Float
     * Draw the black line after rectangle bar
     */
    private fun drawLine(canvas: Canvas, xPos: Float) {
        if (items.isEmpty()) {
            return
        }
        val lineY = (getBarHeight(hasNegativeBar()) + 1f)
        canvas.drawLine(0f, lineY, xPos + (mPaddingBarDp), lineY, linePaint)
    }

    override fun onMeasure(widthMeasureSpec: Int, heightMeasureSpec: Int) {
        val desiredWidth =
            ((items.count()) * (mWidthDp.toInt() + paddingLeft + paddingRight + (mPaddingDp.toInt() * 2)))
        val desiredHeight = mMaxHeight.toInt() + paddingTop + paddingBottom
        setMeasuredDimension(
            measureDimension(desiredWidth, widthMeasureSpec),
            measureDimension(desiredHeight, heightMeasureSpec)
        )
    }


    private fun isAllPositiveBar(): Boolean {
        return items.filter { !it.isNegativeBar }.size == items.size
    }

    private fun isAllNegativeBar(): Boolean {
        return items.filter { it.isNegativeBar }.size == items.size
    }

    private fun measureDimension(desiredSize: Int, measureSpec: Int): Int {
        var result: Int
        val specMode = MeasureSpec.getMode(measureSpec)
        val specSize = MeasureSpec.getSize(measureSpec)
        if (specMode == MeasureSpec.EXACTLY) {
            result = specSize
        } else {
            result = desiredSize
            if (specMode == MeasureSpec.AT_MOST) {
                result = result.coerceAtMost(specSize)
            }
        }
        if (result < desiredSize) {
            Log.e("ItemFinancialBar", "The view is too small, the content might get cut")
        }
        return result
    }


    private fun FinancialBarType.mapBarTypeToPaint(): Paint {
        return when (this) {
            FinancialBarType.GREEN -> greenPaint
            FinancialBarType.GRAY -> grayPaint
            FinancialBarType.DARK_GRAY -> darkGrayPaint
            FinancialBarType.RED -> redPaint
        }
    }

    private fun Float.getPxToDp(): Float {
        val r: Resources = resources
        val px = TypedValue.applyDimension(
            TypedValue.COMPLEX_UNIT_DIP,
            this,
            r.displayMetrics
        )
        return px
    }

    private fun Float.dpToPx(): Float {
        val res = resources
        val metric = res.displayMetrics
        return this * metric.density
    }


    private fun getBarHeight(hasNegativeBar: Boolean): Float {
        return when {
            isAllNegativeBar() || isAllPositiveBar() -> mMaxHeight
            hasNegativeBar -> mMaxHeight / 2
            else -> mMaxHeight
        }
    }


    private fun hasNegativeBar() = items.find { it.isNegativeBar } != null

    companion object {
        const val DEFAULT_PADDING = 4f
        const val DEFAULT_BAR_PADDING = 0.5f
        const val MIN_ITEM_BAR_HEIGHT = 1f
        const val DEFAULT_BAR_RADIUS = 20f

        private fun List<Double>.getHighestValue(): Double {
            return this.maxOf { abs(it) }
        }

        private fun List<Double>.getLowestValue(): Double {
            return this.minOf { it }
        }

        private fun Float.percentageFromValue(percent: Double): Double {
            return (percent / 100) * this
        }

        private fun Double.getPercentageOfValue(value: Double): Double {
            return (value * 100) / this
        }


        private fun List<Double>.getBarType(isNegativeColoringBarType: Boolean): FinancialBarType {
            return if (isNegativeColoringBarType) getNegativeBarType() else getPositiveBarType()
        }

        private fun List<Double>.getPositiveBarType(): FinancialBarType {
            return if (this.count() >= 2) {
                val lastItem = this[this.count() - 1]
                val previousItem = this[this.count() - 2]
                return when {
                    lastItem == previousItem -> FinancialBarType.DARK_GRAY
                    lastItem < previousItem -> FinancialBarType.RED
                    lastItem > previousItem -> FinancialBarType.GREEN
                    else -> FinancialBarType.GRAY
                }
            } else {
                FinancialBarType.GRAY
            }
        }

        private fun List<Double>.getNegativeBarType(): FinancialBarType {
            return if (this.count() >= 2) {
                val lastItem = this[this.count() - 1]
                val previousItem = this[this.count() - 2]
                return when {
                    lastItem == previousItem -> FinancialBarType.DARK_GRAY
                    lastItem > previousItem -> FinancialBarType.RED
                    lastItem < previousItem -> FinancialBarType.GREEN
                    else -> FinancialBarType.GRAY
                }
            } else {
                FinancialBarType.GRAY
            }
        }
    }
}

/**
 * <AUTHOR>
 * @since 21.02.2019
 */
class StepGradientUtil(private var colar1: Colar?, private var colar2: Colar?) {

    private var mSteps: Int = 0

    infix fun StepGradientUtil.gradient0(f: () -> IntRange): IntArray {
        val result = f.invoke().map {
            it.colorStep()
        }.toIntArray()
        recycler()
        return result
    }

    infix fun Int.upTo(steps: Int): IntRange {
        mSteps = steps
        return (this until steps)
    }

    private fun recycler() {
        mSteps = 0
        colar1 = null
        colar2 = null
    }

    private fun Int.colorStep() = Color.argb(
        colar1!!.alpha,
        (colar1!!.r * (mSteps - this) + colar2!!.r * this) / mSteps,
        (colar1!!.g * (mSteps - this) + colar2!!.g * this) / mSteps,
        (colar1!!.b * (mSteps - this) + colar2!!.b * this) / mSteps
    )
}

data class Colar(
    val r: Int,
    val g: Int,
    val b: Int,
    val alpha: Int = 100,
)

infix fun Colar.toColor(colar: Colar) = StepGradientUtil(colar1 = this, colar2 = colar)

fun Int.gradientBySteps() = (Colar(35, 164, 237) toColor Colar(6, 65, 191)).run {
    gradient0 { 0 upTo this@gradientBySteps }
}