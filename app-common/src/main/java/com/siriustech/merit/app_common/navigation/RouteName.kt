package com.siriustech.merit.app_common.navigation

import com.siriustech.merit.app_common.navigation.argument.authentication.AgentSignupLandingArguments
import com.siriustech.merit.app_common.navigation.argument.authentication.Verification2FAArguments
import com.siriustech.merit.app_common.navigation.argument.history.OrderHistoryListArgument
import com.siriustech.merit.app_common.navigation.argument.market.MarketProfileArgument
import com.siriustech.merit.app_common.navigation.argument.notification.MessageCenterDetailsArgument
import kotlinx.serialization.Serializable

// authentication navigation
@Serializable
object Authentication

@Serializable
object Dashboard


// authentication route
@Serializable
object Login

@Serializable
data class Setup2FA(val args: Verification2FAArguments)

@Serializable
data class Verification2FA(val args: Verification2FAArguments)

@Serializable
object SuccessfullySetup2FA

@Serializable
object SetupPin

@Serializable
object CreateNewPin

@Serializable
object SetupBiometric


@Serializable
object CreateNewPinSuccess

@Serializable
object PinLogin

@Serializable
data class AgentSignupLanding(val args: AgentSignupLandingArguments)

@Serializable
data class AgentSignUp(val args: AgentSignupLandingArguments)

@Serializable
object RegisterAgentSignupSuccess

@Serializable
object ForgotPin

@Serializable
object ForgotPassword

@Serializable
object ForgotPinUpdatedSuccess

@Serializable
object ForgotPasswordUpdatedSuccess

@Serializable
data class MarketProfile(val args : MarketProfileArgument)

@Serializable
data object UserProfile

@Serializable
data object ChangePinLanding

@Serializable
data object ChangePasswordLanding

@Serializable
data object NotificationListing


@Serializable
data object NotificationLanding

@Serializable
data class MessageCenterDetails(val argument: MessageCenterDetailsArgument)

@Serializable
data class OrderHistoryList(val argument: OrderHistoryListArgument)

@Serializable
data object InstrumentSearch




// main dashboard bottom navigation routes


object RouteName {
    const val LOGIN_ROUTE = "/login_screen"
    const val DASHBOARD = "/dashboard"
    const val PIN_LOGIN = "/pinLogin"
}