package com.siriustech.merit.app_common.screen.docpreview

import com.siriustech.merit.app_common.data.CommonSharedPreferences
import com.siriustech.merit.app_common.theme.AppViewModel
import dagger.hilt.android.lifecycle.HiltViewModel
import javax.inject.Inject
import javax.inject.Named

/**
 * Created by <PERSON><PERSON>
 */
@HiltViewModel
class DocPreviewViewModel @Inject constructor(
    private val commonSharedPreferences: CommonSharedPreferences,
    @Named("BaseUrl") url : String,
) : AppViewModel() {

    val sessionId : String
        get() = commonSharedPreferences.sessionId


}