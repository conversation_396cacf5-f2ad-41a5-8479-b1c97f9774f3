package com.siriustech.merit.app_common.screen.chat.component

import androidx.compose.foundation.ExperimentalFoundationApi
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.ColumnScope
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.lazy.rememberLazyListState
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.runtime.setValue
import androidx.compose.runtime.snapshotFlow
import androidx.compose.runtime.snapshots.SnapshotStateList
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import com.siriustech.merit.app_common.data.display.UserBasicInfoDisplay
import com.siriustech.merit.app_common.ext.colorTxtInactive
import com.siriustech.merit.app_common.mapper.ChatMapper.groupMessagesByDay
import com.siriustech.merit.app_common.screen.chat.ChatAction
import com.siriustech.merit.app_common.theme.LocalAppColor
import com.siriustech.merit.app_common.theme.LocalDimens
import com.siriustech.merit.app_common.theme.LocalTypography
import java.io.File
import kotlinx.coroutines.launch

/**
 * Created by Hein Htet
 */

@OptIn(ExperimentalFoundationApi::class)
@Composable
fun ColumnScope.ChatList(
    messages: SnapshotStateList<ChatMessageDisplayModel>,
    userInfo: UserBasicInfoDisplay,
    onTriggerAction: (ChatAction) -> Unit = {},
) {
    val context = LocalContext.current
    val groupedMessages = groupMessagesByDay(messages,context)
    val scrollState = rememberLazyListState()
    val coroutineScope = rememberCoroutineScope()
    var isFirstTime by remember {
        mutableStateOf(true)
    }

    LaunchedEffect(messages.size) {
        if (messages.isNotEmpty() && isFirstTime) {
            coroutineScope.launch {
                scrollState.scrollToItem(messages.size)
                isFirstTime = false
            }
        }
    }

    LaunchedEffect(scrollState) {
        snapshotFlow { scrollState.firstVisibleItemIndex }
            .collect { firstVisibleItemIndex ->
                if (firstVisibleItemIndex <= 4) {
                    onTriggerAction(ChatAction.OnLoadPreviousChatHistory)
                }
            }
    }

    LazyColumn(
        state = scrollState,
        modifier = Modifier
            .weight(1f)
            .padding(horizontal = LocalDimens.current.dimen12)
    ) {
        groupedMessages.forEach { (date, messagesByDay) ->
            stickyHeader {
                Box(
                    modifier = Modifier
                        .fillMaxWidth()
                        .background(LocalAppColor.current.bgDefault)
                        .padding(8.dp),
                    contentAlignment = Alignment.Center,
                ) {
                    Text(
                        text = date,
                        textAlign = TextAlign.Center,
                        style = LocalTypography.current.text10.light.colorTxtInactive(),
                        modifier = Modifier
                            .fillMaxWidth()
                    )
                }
            }
            items(messagesByDay) { message ->
                if (message.senderRole == SenderRoleType.USER) {
                    Column(modifier = Modifier.fillMaxSize(1f)) {
                        ChatUserMessageItem(
                            message = message,
                            userInfo = userInfo,
                            showAvatar = shouldShowAvatar(messagesByDay, message),
                            onAttachmentClicked = {
                                onTriggerAction(
                                    ChatAction.OnPreviewAttachment(
                                        it
                                    )
                                )
                            }
                        )
                    }
                } else {
                    Column(modifier = Modifier.fillMaxSize(1f)) {
                        ChatOtherMessageItem(
                            message = message,
                            userInfo = userInfo,
                            showAvatar = shouldShowAvatar(messagesByDay, message),
                            onAttachmentClicked = {
                                onTriggerAction(
                                    ChatAction.OnPreviewAttachment(
                                        it
                                    )
                                )
                            }
                        )
                    }
                }
            }
        }
    }
}


fun shouldShowAvatar(
    messages: List<ChatMessageDisplayModel>,
    currentMessage: ChatMessageDisplayModel,
): Boolean {
    val currentIndex = messages.indexOf(currentMessage)
    if (currentIndex == 0) return true // Show avatar for the first message of the day

    val previousMessage = messages[currentIndex - 1]
//    return previousMessage.senderId != currentMessage.senderId
    return previousMessage.senderRole != currentMessage.senderRole
}

data class ChatMessageDisplayModel(
    val text: String = "",
    val senderRole: SenderRoleType = SenderRoleType.USER,
    val messageType: MessageType = MessageType.TEXT,
    val imageUrl: String = "",
    val createdAt: Long = 0L,
    val senderId: String = "",
    val docKey: String? = null,
    val file: File? = null,
    val fileSize: String? = null,
    val senderName: String = "",
    val fileMMEIType: String = "",
    val attachments: List<AttachmentDisplayModel> = emptyList(),
)

data class AttachmentDisplayModel(
    val type: String = "",
    val url: String = "",
    val name: String = "",
    val fileSize: String = "",
    val originalFileSize: Long = 0L,
)


enum class MessageType {
    TEXT,
    IMAGE,
    VIDEO,
    AUDIO
}

enum class SenderRoleType(val value: String) {
    SUPPORT("MERIT"),
    USER("CUSTOMER");


    companion object {
        fun fromParams(value: String): SenderRoleType {
            return when (value) {
                SUPPORT.value -> SUPPORT
                USER.value -> USER
                else -> USER
            }
        }
    }
}

