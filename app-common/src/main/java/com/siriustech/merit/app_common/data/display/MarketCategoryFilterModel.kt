package com.siriustech.merit.app_common.data.display

import com.siriustech.merit.app_common.component.modalbts.ModalListDataContent
import com.siriustech.merit.app_common.typeenum.MarketCategoryType
import com.siriustech.merit.app_common.typeenum.RefineAssetSortingType
import com.siriustech.merit.app_common.typeenum.RefineFilterProductType
import kotlinx.serialization.Serializable

/**
 * Created by <PERSON><PERSON>
 */
@Serializable
data class MarketCategoryFilterModel(
    val sortingType: RefineAssetSortingType? = null,
    val productTypes: List<RefineFilterProductType> = emptyList(),
    val productCategoryTypes: List<ModalListDataContent> = emptyList(),
    val marketCategoryType: String = MarketCategoryType.CASH_EQUIVALENT.value,
    val selectedAllocationClass : AssetClassModel = AssetClassModel()
) : java.io.Serializable