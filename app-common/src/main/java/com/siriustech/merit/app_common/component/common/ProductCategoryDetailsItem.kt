package com.siriustech.merit.app_common.component.common

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.tooling.preview.Preview
import coil3.compose.AsyncImage
import coil3.request.ImageRequest
import coil3.request.crossfade
import com.siriustech.merit.app_common.R
import com.siriustech.merit.app_common.component.modalbts.ProductCategoryDetailsModalDisplayData
import com.siriustech.merit.app_common.component.text.BadgeText
import com.siriustech.merit.app_common.ext.colorTxtInactive
import com.siriustech.merit.app_common.ext.colorTxtParagraph
import com.siriustech.merit.app_common.ext.colorTxtTitle
import com.siriustech.merit.app_common.theme.LocalAppColor
import com.siriustech.merit.app_common.theme.LocalDimens
import com.siriustech.merit.app_common.theme.LocalTypography
import com.siriustech.merit.app_common.typeenum.RiskLevel
import timber.log.Timber

/**
 * Created by Hein Htet
 */

@Composable
fun ProductCategoryDetailsItem(
    modifier: Modifier = Modifier,
    data: ProductCategoryDetailsModalDisplayData = ProductCategoryDetailsModalDisplayData(),
) {
    Column(
        modifier = Modifier
            .padding(vertical = LocalDimens.current.dimen12)
            .background(LocalAppColor.current.bgDefault)
            .then(modifier)
    ) {
        Row(
            modifier = Modifier
                .fillMaxWidth(),
            verticalAlignment = Alignment.CenterVertically
        ) {
            Row(modifier = Modifier.weight(1f),
                verticalAlignment = Alignment.CenterVertically) {
                AsyncImage(
                    modifier = Modifier.size(LocalDimens.current.dimen32)
                        .clip(RoundedCornerShape(50)),
                    model = ImageRequest.Builder(LocalContext.current)
                        .data(data.logo)
                        .crossfade(true)
                        .build(),
                    placeholder = painterResource(R.drawable.ic_product_category_placeholder),
                    error = painterResource(R.drawable.ic_product_category_placeholder),
                    contentDescription = "Product Category Placeholder",
                    contentScale = ContentScale.Crop,
                    onError = {
                        Timber.d("ON_ERROR_IMAGE_LOADING ${it.result.throwable.message} URL ${data.logo}")
                    }
                )
                Spacer(modifier = Modifier.width(LocalDimens.current.dimen8))
                Column {
                    Row(
                        modifier = Modifier
                    ) {
                        Text(
                            modifier = Modifier.weight(1f, fill = false),
                            text = data.name,
                            overflow = TextOverflow.Ellipsis,
                            style = LocalTypography.current.text14.semiBold.colorTxtTitle()
                        )
//                        BadgeText(
//                            label = data.exchange,
//                            bgColor = LocalAppColor.current.bgAccent,
//                            textColor = LocalAppColor.current.txtLabel
//                        )
                    }
                    Row {
                        Text(
                            text = data.symbol.uppercase(),
                            maxLines = 2,
                            overflow = TextOverflow.Ellipsis,
                            style = LocalTypography.current.text12.medium.colorTxtParagraph(),
                            modifier = Modifier.weight(1f, fill = false)
                        )
                        Spacer(modifier = Modifier.width(LocalDimens.current.dimen4))
                        if (data.riskLevel != null) {
                            BadgeText(
                                label = data.riskLevel.value,
                                bgColor = data.riskLevel.getAssetRiskBackgroundColor(),
                                textColor = data.riskLevel.getAssetRiskLevelTextColor()
                            )
                        }
                    }

                }
            }
            Column(
                modifier = Modifier
                    .weight(0.5f),
                verticalArrangement = Arrangement.Top,
                horizontalAlignment = Alignment.End

            ) {
                MarketPriceChange(
                    data = MarketPriceChangeDisplayData(
                        marketValue = data.marketValue,
                        currency = data.currency,
                        unrealizedGL = data.unrealizedGl,
                        unrealizedGLRate = data.unrealizedGlRate
                    )
                )
            }
        }
        if (data.asOfDate.isNotEmpty()) {
            Row(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(top = LocalDimens.current.dimen4),
                horizontalArrangement = Arrangement.SpaceBetween
            ) {
                Row(modifier = Modifier, verticalAlignment = Alignment.CenterVertically) {
//                    Image(
//                        painter = painterResource(id = R.drawable.ic_flag_us),
//                        contentDescription = "US Flag Image Resource"
//                    )
                    Text(
                        modifier = Modifier.padding(start = LocalDimens.current.dimen2),
                        text = data.country,
                        style = LocalTypography.current.text12.medium.colorTxtParagraph()
                    )
                }
                Text(
                    text = data.asOfDate,
                    style = LocalTypography.current.text12.light.colorTxtInactive()
                )
            }
        }
    }
}

@Preview
@Composable
fun PreviewProductCategoryDetailsItem() {
    ProductCategoryDetailsItem(
        data = ProductCategoryDetailsModalDisplayData(
            symbol = "Asset",
            name = "TEXT 01",
            riskLevel = RiskLevel.HIGH,
            exchange = "HKEX",
            marketPrice = "377.60",
            currency = "HKD",
            marketValue = "29",
            unrealizedGl = "10.99",
            logo = "https://api.twelvedata.com/logo/apple.com",
            unrealizedGlRate = "120.0",
            country = "US",
            asOfDate = "as of2024-12-23"
        ),
        modifier = Modifier
            .background(LocalAppColor.current.bgDefault)
            .padding(
                horizontal = LocalDimens.current.dimen12,
                vertical = LocalDimens.current.dimen8
            )
    )
}