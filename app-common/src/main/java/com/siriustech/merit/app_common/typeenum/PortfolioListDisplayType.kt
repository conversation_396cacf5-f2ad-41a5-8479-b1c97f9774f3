package com.siriustech.merit.app_common.typeenum

import androidx.compose.runtime.Composable
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.AnnotatedString
import com.siriustech.merit.app_common.ext.AttributeStringData
import com.siriustech.merit.app_common.ext.buildAttrString
import com.siriustech.merit.app_common.ext.colorTxtParagraph
import com.siriustech.merit.app_common.theme.LocalTypography
import com.siriustech.merit.app_common.R as AppCommonR

/**
 * Created by Hein Htet
 */
enum class PortfolioListDisplayType(val type: String) {
    ALL("ALL"),
    ALLOCATION_CLASS("ALLOCATION_CLASS"),
    RISK_RATING("RISK_RATING"),
    RISK_LEVEL("RISK_LEVEL"),
    ASSET("ASSET"),
    PERFORMANCE("PERFORMANCE"),
    GAIN_LOSS_ANALYSIS("GAIN_LOSS_ANALYSIS");

    companion object {
        fun fromParams(value: String): PortfolioListDisplayType {
            return when (value) {
                ALLOCATION_CLASS.type -> ALLOCATION_CLASS
                RISK_RATING.type -> RISK_RATING
                RISK_LEVEL.type -> RISK_LEVEL
                ASSET.type -> ASSET
                PERFORMANCE.type -> PERFORMANCE
                GAIN_LOSS_ANALYSIS.type -> GAIN_LOSS_ANALYSIS
                else -> ALLOCATION_CLASS
            }
        }
    }

    @Composable
    fun getDisplayName(): AnnotatedString {
        return buildAttrString(
            listOf(
//                AttributeStringData(
//                    text = stringResource(id = AppCommonR.string.key0412).plus(" "),
//                    textStyle = LocalTypography.current.text14.medium.colorTxtTitle(),
//                ),
                AttributeStringData(
                    text = stringResource(id = getStringResId()),
                    textStyle = LocalTypography.current.text14.regular.colorTxtParagraph(),
                )
            )
        )
    }

    private fun getStringResId(): Int {
        return when (this) {
            ALLOCATION_CLASS -> AppCommonR.string.key0413
            RISK_RATING -> AppCommonR.string.key0414
            RISK_LEVEL -> AppCommonR.string.key0415
            ASSET -> AppCommonR.string.key0416
            PERFORMANCE -> AppCommonR.string.key0379
            GAIN_LOSS_ANALYSIS -> AppCommonR.string.key0380
            else -> AppCommonR.string.key0450
        }
    }
}