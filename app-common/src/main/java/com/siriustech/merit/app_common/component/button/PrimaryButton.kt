package com.siriustech.merit.app_common.component.button

import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.Button
import androidx.compose.material3.ButtonDefaults
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import com.siriustech.merit.app_common.theme.LocalAppColor
import com.siriustech.merit.app_common.theme.LocalDimens
import com.siriustech.merit.app_common.theme.LocalTypography
import com.siriustech.merit.app_common.typeenum.UIConfig

/**
 * Created by <PERSON><PERSON>te<PERSON>
 */
@Composable
fun PrimaryButton(
    modifier: Modifier = Modifier,
    properties: ButtonProperties = ButtonProperties(),
    onClicked: () -> Unit = {},
) {
    Button(
        enabled = properties.enabled,
        onClick = { onClicked() }, modifier = Modifier
            .height(LocalDimens.current.dimen44)
            .fillMaxWidth()
            .then(modifier),
        shape = RoundedCornerShape(UIConfig.BUTTON_CONNER_RADIUS.dp),
        colors = ButtonDefaults.buttonColors(
            containerColor = LocalAppColor.current.btnPrimary,
            disabledContainerColor = LocalAppColor.current.btnDisabled,
            contentColor = LocalAppColor.current.txtInverted,
            disabledContentColor = LocalAppColor.current.txtDisabled
        )
    ) {
        Text(text = properties.text, style = LocalTypography.current.text14.medium,
            maxLines = 1,
            overflow = TextOverflow.Ellipsis)
    }
}

@Preview(showBackground = true, showSystemUi = true)
@Composable
fun PreviewPrimaryButton() {
    PrimaryButton(properties = ButtonProperties(text = "Primary Button"))
}