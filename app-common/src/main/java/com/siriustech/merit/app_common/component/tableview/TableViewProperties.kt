package com.siriustech.merit.app_common.component.tableview

import androidx.compose.ui.graphics.Color
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.unit.sp

/**
 * Created by <PERSON><PERSON><PERSON><PERSON>
 */

data class TableViewProperties(
    val headerCellTextStyle: TextStyle = TextStyle(fontSize = 10.sp, color = Color.Gray),
    val rowCellTextStyle : TextStyle = TextStyle(fontSize = 10.sp, color = Color.Black),
    val headerCellBackgroundColor : Color = Color.White,
)