package com.siriustech.merit.app_common.component.modalbts

import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.IntrinsicSize
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxHeight
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.verticalScroll
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.ModalBottomSheet
import androidx.compose.material3.SheetState
import androidx.compose.material3.Text
import androidx.compose.material3.rememberModalBottomSheetState
import androidx.compose.runtime.Composable
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.tooling.preview.Devices
import androidx.compose.ui.tooling.preview.Preview
import com.siriustech.merit.app_common.R
import com.siriustech.merit.app_common.component.container.PaddingTop
import com.siriustech.merit.app_common.ext.colorTxtParagraph
import com.siriustech.merit.app_common.ext.colorTxtTitle
import com.siriustech.merit.app_common.theme.LocalAppColor
import com.siriustech.merit.app_common.theme.LocalDimens
import com.siriustech.merit.app_common.theme.LocalTypography
import com.siriustech.merit.app_common.typeenum.PortfolioAssetARRType
import kotlinx.coroutines.launch

/**
 * Created by Hein Htet
 */

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun RiskRatingInfoModalBts(
    sheetState: SheetState = rememberModalBottomSheetState(skipPartiallyExpanded = true),
    onDismissed: () -> Unit = {},
) {
    val scope = rememberCoroutineScope()

    fun onDismiss() {
        scope.launch {
            sheetState.hide()
            onDismissed()
        }
    }
    ModalBottomSheet(
        dragHandle = {},
        shape = RoundedCornerShape(LocalDimens.current.dimen4),
        containerColor = LocalAppColor.current.bgDefault,
        onDismissRequest = onDismissed, sheetState = sheetState,
        modifier = Modifier
    ) {
        RiskRatingInfoModalContent(
            onDismissed = {
                onDismiss()
            },
        )
    }
}

@Composable
fun RiskRatingInfoModalContent(
    onDismissed: () -> Unit = {},
) {
    Column(
        modifier = Modifier
            .fillMaxHeight(0.9f)
            .fillMaxWidth()
            .verticalScroll(rememberScrollState())
            .padding(
                horizontal = LocalDimens.current.dimen14,
                vertical = LocalDimens.current.dimen16
            )
    ) {
        ListTitle(onDismissed = onDismissed)
        Spacer(modifier = Modifier.height(LocalDimens.current.dimen16))
        Image(
            modifier = Modifier
                .align(Alignment.CenterHorizontally),
            painter = painterResource(id = R.drawable.ic_risk_rating_graph),
            contentDescription = "Risk Rating Image Resource"
        )
        Spacer(modifier = Modifier.height(LocalDimens.current.dimen16))


        val list = listOf(
            RiskRatingModalDisplayData(
                color = PortfolioAssetARRType.ARR1.getFillColor(),
                title = PortfolioAssetARRType.ARR1.aarName,
                subTitle = stringResource(id = R.string.key0433),
                PortfolioAssetARRType.ARR1.description()
            ),
            RiskRatingModalDisplayData(
                color = PortfolioAssetARRType.ARR2.getFillColor(),
                title = PortfolioAssetARRType.ARR2.aarName,
                subTitle = stringResource(id = R.string.key0434),
                PortfolioAssetARRType.ARR2.description()
            ),
            RiskRatingModalDisplayData(
                color = PortfolioAssetARRType.ARR3.getFillColor(),
                title = PortfolioAssetARRType.ARR3.aarName,
                subTitle = stringResource(id = R.string.key0434),
                PortfolioAssetARRType.ARR3.description()
            ),
            RiskRatingModalDisplayData(
                color = PortfolioAssetARRType.ARR4.getFillColor(),
                title = PortfolioAssetARRType.ARR4.aarName,
                subTitle = stringResource(id = R.string.key0434),
                PortfolioAssetARRType.ARR4.description()
            ),
            RiskRatingModalDisplayData(
                color = PortfolioAssetARRType.ARR5.getFillColor(),
                title = PortfolioAssetARRType.ARR5.aarName,
                subTitle = stringResource(id = R.string.key0434),
                PortfolioAssetARRType.ARR5.description()
            ),
            RiskRatingModalDisplayData(
                color = PortfolioAssetARRType.ARR6.getFillColor(),
                title = PortfolioAssetARRType.ARR6.aarName,
                subTitle = stringResource(id = R.string.key1062),
                PortfolioAssetARRType.ARR6.description()
            ),
            RiskRatingModalDisplayData(
                color = PortfolioAssetARRType.ARR7.getFillColor(),
                title = PortfolioAssetARRType.ARR7.aarName,
                subTitle = stringResource(id = R.string.key1061),
                PortfolioAssetARRType.ARR7.description()
            ),
            RiskRatingModalDisplayData(
                color = PortfolioAssetARRType.ARR8.getFillColor(),
                title = PortfolioAssetARRType.ARR8.aarName,
                subTitle = stringResource(id = R.string.key0437),
                PortfolioAssetARRType.ARR8.description()
            ),
            RiskRatingModalDisplayData(
                color = PortfolioAssetARRType.ARR9.getFillColor(),
                title = PortfolioAssetARRType.ARR9.aarName,
                subTitle = stringResource(id = R.string.key0439),
                PortfolioAssetARRType.ARR9.description()
            ),
            RiskRatingModalDisplayData(
                color = PortfolioAssetARRType.ARR10.getFillColor(),
                title = PortfolioAssetARRType.ARR10.aarName,
                subTitle = stringResource(id = R.string.key0440),
                PortfolioAssetARRType.ARR10.description()
            ),
        )
        list.forEach {
            RiskRatingInfo(
                color = it.color,
                title = it.title,
                subTitle = it.subTitle,
                description = it.description
            )
            PaddingTop(value = LocalDimens.current.dimen8)
        }
    }
}

data class RiskRatingModalDisplayData(
    val color: Color,
    val title: String,
    val subTitle: String,
    val description: String,
)

@Composable
fun RiskRatingInfo(
    color: Color,
    title: String,
    subTitle: String,
    description: String,
) {
    Row(
        modifier = Modifier
            .height(IntrinsicSize.Min)
            .background(LocalAppColor.current.bgPale)
            .clip(RoundedCornerShape(LocalDimens.current.dimen2))
    ) {
        Box(
            modifier = Modifier
                .width(LocalDimens.current.dimen6)
                .fillMaxHeight()
                .background(color)
        )
        Column(
            modifier = Modifier
                .weight(1f)
                .padding(
                    vertical = LocalDimens.current.dimen8,
                    horizontal = LocalDimens.current.dimen16
                )
        ) {
            Text(text = title, style = LocalTypography.current.text12.bold.copy(color = color))
            Text(text = subTitle, style = LocalTypography.current.text10.medium.colorTxtTitle())
            Text(
                text = description,
                style = LocalTypography.current.text10.medium.colorTxtParagraph()
            )
        }
    }
}


@Composable
fun ListTitle(onDismissed: () -> Unit = {}) {
    Row(
        modifier = Modifier.fillMaxWidth(),
        verticalAlignment = Alignment.CenterVertically
    ) {
        Spacer(modifier = Modifier.width(LocalDimens.current.dimen4))
        Image(
            painter = painterResource(id = R.drawable.ic_info_big),
            contentDescription = "Prefix Title Icon"
        )
        Spacer(modifier = Modifier.width(LocalDimens.current.dimen4))

        Text(
            text = stringResource(id = R.string.key0418),
            style = LocalTypography.current.text14.semiBold.colorTxtTitle()
        )
        Spacer(modifier = Modifier.weight(1f))
        Image(
            painter = painterResource(id = R.drawable.ic_action_close),
            contentDescription = "Close Image Resource",
            modifier = Modifier.clickable { onDismissed() }
        )
    }
}


@Preview(showBackground = true, showSystemUi = true, device = Devices.PIXEL_XL)
@Composable
fun PreviewRiskRatingInfoModalContent() {
    RiskRatingInfoModalContent()
}