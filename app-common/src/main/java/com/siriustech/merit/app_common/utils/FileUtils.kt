package com.siriustech.merit.app_common.utils

import android.annotation.SuppressLint
import android.content.Context
import android.net.Uri
import android.provider.OpenableColumns
import android.util.Log
import androidx.core.content.FileProvider
import com.siriustech.merit.app_common.R
import java.io.File
import java.io.FileOutputStream
import java.io.InputStream
import okhttp3.MediaType
import okhttp3.RequestBody

/**
 * Created by <PERSON><PERSON>t
 */
object FileUtils {

    fun Context.getTempUri(): Uri? {
        val authority = getString(R.string.fileprovider)
        File("${cacheDir}/images").let {
            it.mkdirs()
            val file = File.createTempFile(
                "image_" + System.currentTimeMillis().toString(),
                ".jpg",
                it
            )
            return FileProvider.getUriForFile(
                this,
                authority,
                file
            )
        }
    }

    fun Context.createTempVideoFile(onFileCreated : (filePath: String,uri : Uri) -> Unit){
        val authority = getString(R.string.fileprovider)
        File("${cacheDir}/videos").let {
            it.mkdirs()
            val file = File.createTempFile(
                "video_" + System.currentTimeMillis().toString(),
                ".mp4",
                it
            )
            val uri =  FileProvider.getUriForFile(
                this,
                authority,
                file
            )
            onFileCreated(file.absolutePath,uri)
        }
    }

    @SuppressLint("Recycle")
    fun Context.getRealPathFromURI(uri: Uri): String? {
        val returnCursor = contentResolver.query(uri, null, null, null, null)
        val nameIndex = returnCursor!!.getColumnIndex(OpenableColumns.DISPLAY_NAME)
        val sizeIndex = returnCursor.getColumnIndex(OpenableColumns.SIZE)
        returnCursor.moveToFirst()
        val name = returnCursor.getString(nameIndex)
        val size = returnCursor.getLong(sizeIndex).toString()
        val file = File(filesDir, name)
        try {
            val inputStream: InputStream? = contentResolver.openInputStream(uri)
            val outputStream = FileOutputStream(file)
            var read = 0
            val maxBufferSize = 1 * 1024 * 1024
            val bytesAvailable: Int = inputStream?.available() ?: 0
            //int bufferSize = 1024;
            val bufferSize = Math.min(bytesAvailable, maxBufferSize)
            val buffers = ByteArray(bufferSize)
            while (inputStream?.read(buffers).also {
                    if (it != null) {
                        read = it
                    }
                } != -1) {
                outputStream.write(buffers, 0, read)
            }
            Log.e("File Size", "Size " + file.length())
            inputStream?.close()
            outputStream.close()
            Log.e("File Path", "Path " + file.path)

        } catch (e: java.lang.Exception) {
            Log.e("Exception", e.message!!)
        }
        return file.path
    }

    fun fileToRequestBody(file: File, contentType: MediaType? = null): RequestBody {
        return RequestBody.create(contentType, file = file)
    }


    private const val FILE_TIMESTAMP_FORMAT = "yyyy-MM-dd-HH-mm-ss-SSS"
}

fun Uri.getFileSize(context: Context): Long? {
    val uri = this
    val cursor = context.contentResolver.query(uri, arrayOf(OpenableColumns.SIZE), null, null, null)
    return cursor?.use {
        if (it.moveToFirst()) {
            val sizeIndex = it.getColumnIndex(OpenableColumns.SIZE)
            if (sizeIndex != -1) it.getLong(sizeIndex) else null
        } else null
    }
}
fun formatFileSize(sizeInBytes: Long): String {
    val units = arrayOf("B", "KB", "MB", "GB", "TB")
    var size = sizeInBytes.toDouble()
    var unitIndex = 0

    while (size >= 1024 && unitIndex < units.size - 1) {
        size /= 1024
        unitIndex++
    }

    return String.format("%.2f %s", size, units[unitIndex])
}