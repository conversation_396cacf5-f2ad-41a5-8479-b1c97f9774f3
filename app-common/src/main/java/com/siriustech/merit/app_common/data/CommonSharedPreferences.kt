package com.siriustech.merit.app_common.data

import android.content.Context
import android.content.SharedPreferences
import com.core.localstorage.pref.SecureSharedPreferences


class CommonSharedPreferences(
    context: Context,
    sharedPreferences: SharedPreferences,
) : SecureSharedPreferences(context, sharedPreferences) {

    private val publicPref = context.getSharedPreferences(PUBLIC_PREF_NAME, Context.MODE_PRIVATE)

    var mockSessionTimeOut = false

    fun setRememberMe(value: Boolean) {
        putBoolean(KEY_REMEMBER_ME, value)
    }

    val isRememberMe: Boolean
        get() = getBoolean(KEY_REMEMBER_ME, true)

    val sessionId: String
        get() = getString(KEY_SESSION_ID, "")

    val loginPinCode: String
        get() = getString(KEY_USER_LOGIN_PIN, "")

    val userEmail: String
        get() = getString(KEY_USER_EMAIL, "")

    val userLoginPassword: String
        get() = getString(KEY_USER_LOGIN_PASSWORD, "")

    val alreadyPinSetup: Boolean
        get() = getBoolean(KEY_HAS_USER_PIN_SETUP, false)
    val alreadyBiometricSetup: Boolean
        get() = getBoolean(KEY_HAS_USER_BIOMETRIC_SETUP, false)
    val appLocale: String
        get() = publicPref.getString(KEY_LANGUAGE, "en").orEmpty()

    val appVersion: String
        get() = publicPref.getString(KEY_APP_VERSION, "1.0.0").orEmpty()


    fun setSessionID(id: String) {
        putString(KEY_SESSION_ID, id)
    }

    fun setUserEmail(email: String) {
        putString(KEY_USER_EMAIL, email)
    }

    fun setUserLoginPin(pin: String) {
        putString(KEY_USER_LOGIN_PIN, pin)
    }

    fun setPinSetUp(setup: Boolean) {
        putBoolean(KEY_HAS_USER_PIN_SETUP, setup)
    }

    fun setBiometricSetUp(setup: Boolean) {
        putBoolean(KEY_HAS_USER_BIOMETRIC_SETUP, setup)
    }

    fun setUserLoginPassword(pin: String) {
        putString(KEY_USER_LOGIN_PASSWORD, pin)
    }

    fun setAppLocale(code: String) {
        putString(KEY_LANGUAGE, code)
        publicPref.edit().putString(KEY_LANGUAGE, code).apply()
    }

    fun setAppVersion(version: String) {
        putString(KEY_APP_VERSION, version)
        publicPref.edit().putString(KEY_APP_VERSION, version).apply()
    }

    fun clearData() {
        val isRememberMe = isRememberMe
        val email = userEmail
        setSessionID("")
        setPinSetUp(false)
        setUserLoginPin("")
        setBiometricSetUp(false)
        setUserLoginPassword("")

        setRememberMe(isRememberMe)
        setUserEmail(email)
    }

    companion object {
        private const val RSA_PUB_KEY = "RSA_PUB_KEY"
        private const val LOCALE = "LOCALE"

        private const val KEY_REMEMBER_ME = "KEY_REMEMBER_ME"
        private const val KEY_SESSION_ID = "KEY_SESSION_ID"
        private const val KEY_USER_LOGIN_PIN = "KEY_USER_LOGIN_PIN"
        private const val KEY_USER_EMAIL = "KEY_USER_EMAIL"
        private const val KEY_USER_LOGIN_PASSWORD = "KEY_USER_LOGIN_PASSWORD"
        private const val KEY_HAS_USER_PIN_SETUP = "KEY_HAS_USER_PIN_SETUP"
        private const val KEY_HAS_USER_BIOMETRIC_SETUP = "KEY_HAS_USER_BIOMETRIC_SETUP"
        private const val KEY_APP_VERSION = "KEY_APP_VERSION"
        const val KEY_LANGUAGE = "KEY_LANGUAGE"

        const val PUBLIC_PREF_NAME = "public_merit_pref"

    }
}
