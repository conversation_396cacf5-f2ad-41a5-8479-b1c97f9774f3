package com.siriustech.merit.app_common.component.chart.custom

import android.annotation.SuppressLint
import android.content.Context
import android.graphics.Canvas
import android.graphics.Color
import android.graphics.Paint
import android.graphics.RectF
import android.util.AttributeSet
import androidx.compose.ui.graphics.toArgb
import com.github.mikephil.charting.charts.CandleStickChart
import timber.log.Timber

/**
 * Created by He<PERSON> Htet
 */
class CustomCandleChart(context: Context) : CandleStickChart(context, ) {


    private val backgroundPaint = Paint().apply {
        color = androidx.compose.ui.graphics.Color.LightGray.copy(alpha = 0.5f).toArgb()
//        color = 0xFFDDDDDD.toInt()
        style = Paint.Style.FILL
    }


    private val limitLinePaint = Paint().apply {
        strokeWidth = 1f
        style = Paint.Style.STROKE
        textSize = 12f
        isAntiAlias = true
    }


    @SuppressLint("DrawAllocation")
    override fun onDraw(canvas: Canvas) {
        super.onDraw(canvas)

        val yAxis = axisRight
        val limitLines = yAxis.limitLines
        if (limitLines.isNotEmpty()) {
            for (limitLine in limitLines) {
                val position = floatArrayOf(0f, limitLine.limit)
                getTransformer(yAxis.axisDependency).pointValuesToPixel(position)
                val maxY = yAxis.axisMaximum
                val minY = yAxis.axisMinimum
                var adjustedPosition = position[1]
                if (adjustedPosition > maxY) {
                    adjustedPosition = viewPortHandler.contentTop()
                } else if (adjustedPosition < minY) {
                    adjustedPosition = viewPortHandler.contentBottom()
                }

                if (position[1] >= viewPortHandler.contentTop() && position[1] <= viewPortHandler.contentBottom()) {
                    val priceText = limitLine.label
                    val textWidth = limitLinePaint.measureText(priceText)
                    val textHeight = limitLinePaint.textSize
                    val rect = RectF(
                        viewPortHandler.contentRight() - textWidth - 85,
                        position[1] - textHeight / 2 - 42,
                        viewPortHandler.contentRight() - 8f,
                        position[1] + textHeight / 2
                    )
                    rect.top = rect.top.coerceAtLeast(viewPortHandler.contentTop())
                    rect.bottom = rect.bottom.coerceAtMost(viewPortHandler.contentBottom())
                    canvas.drawRect(rect, backgroundPaint)
                }
            }
        }
    }
}
