package com.siriustech.merit.app_common.component.marquee

import androidx.compose.animation.AnimatedVisibility
import androidx.compose.animation.slideInVertically
import androidx.compose.animation.slideOutVertically
import androidx.compose.foundation.Image
import androidx.compose.foundation.MarqueeAnimationMode
import androidx.compose.foundation.background
import androidx.compose.foundation.basicMarquee
import androidx.compose.foundation.clickable
import androidx.compose.foundation.focusable
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.width
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.mutableStateListOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.snapshots.SnapshotStateList
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.focus.FocusRequester
import androidx.compose.ui.focus.focusRequester
import androidx.compose.ui.graphics.painter.Painter
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import com.siriustech.merit.app_common.R
import com.siriustech.merit.app_common.component.common.onGetPriceChangeResource
import com.siriustech.merit.app_common.ext.colorTxtNegative
import com.siriustech.merit.app_common.ext.colorTxtParagraph
import com.siriustech.merit.app_common.ext.colorTxtTitle
import com.siriustech.merit.app_common.ext.isPriceDown
import com.siriustech.merit.app_common.ext.isPriceUp
import com.siriustech.merit.app_common.ext.isPriceZero
import com.siriustech.merit.app_common.ext.noRippleClickable
import com.siriustech.merit.app_common.theme.LocalAppColor
import com.siriustech.merit.app_common.theme.LocalDimens
import com.siriustech.merit.app_common.theme.LocalTypography

@Composable
fun StockExchangeMarquee(
    modifier: Modifier = Modifier,
    items: SnapshotStateList<StockExchangeMarqueeData>,
    onItemClick: (StockExchangeMarqueeData) -> Unit = {},
    onClicked: () -> Unit = {},
) {
    AnimatedVisibility(
        enter = slideInVertically(),
        exit = slideOutVertically(),
        visible = items.isNotEmpty()
    ) {
        Box(
            contentAlignment = Alignment.Center
        ) {
            val focusRequester = remember { FocusRequester() }
            LaunchedEffect(Unit) {
                focusRequester.requestFocus()
            }
            Row(
                modifier = Modifier
                    .basicMarquee(
                        animationMode = MarqueeAnimationMode.WhileFocused,
                        iterations = Int.MAX_VALUE,
                        velocity = 50.dp,
                    )
                    .focusRequester(focusRequester)
                    .focusable()
                    .then(modifier)
            ) {
                repeat(items.count()) { index ->
                    StockExchangeItem(
                        modifier = Modifier
                            .noRippleClickable { onItemClick(items[index]) },
                        items[index],
                    )
                    Spacer(modifier = Modifier.width(8.dp))
                }
            }
        }
    }
}

@Composable
fun StockExchangeItem(modifier: Modifier = Modifier, data: StockExchangeMarqueeData) {

    val uiModel = onGetPriceChangeResource(priceChange = data.price)

    Box(
        modifier = Modifier
            .background(LocalAppColor.current.bgInfo)
            .then(modifier)

    ) {
        Row(
            modifier = Modifier
                .padding(horizontal = LocalDimens.current.dimen8),
            verticalAlignment = Alignment.CenterVertically,
        ) {
            Text(
                text = data.exchange,
                style = LocalTypography.current.text12.medium.colorTxtTitle()
            )
            Text(
                text = data.price,
                modifier = Modifier.padding(horizontal = LocalDimens.current.dimen4),
                style = LocalTypography.current.text12.medium.colorTxtParagraph()
            )
//            Image(
//                painter = uiModel.iconPainter,
//                contentDescription = "Exchange Price Info Image Resource"
//            )
        }
    }
}

@Preview
@Composable
fun PreviewStockExchangeMarquee() {
    val icon = painterResource(id = R.drawable.ic_exchange_price_up)
    val items = remember {
        mutableStateListOf(
            StockExchangeMarqueeData(
                price = "0.87",
                exchange = "USD/EUR",
            )
        )
    }
    StockExchangeMarquee(items = items)
}

data class StockExchangeMarqueeData(
    val exchange: String = "",
    val price: String = "",
)