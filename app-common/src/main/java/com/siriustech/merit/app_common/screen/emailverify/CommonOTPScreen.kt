package com.siriustech.merit.app_common.screen.emailverify

import androidx.compose.runtime.Composable
import androidx.compose.runtime.collectAsState
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import com.siriustech.core_ui_compose.base.SingleEventEffect
import com.siriustech.merit.app_common.R
import com.siriustech.merit.app_common.component.alert.BannerAlertProperties
import com.siriustech.merit.app_common.component.otp.OTPVerification
import com.siriustech.merit.app_common.component.otp.OTPVerificationProperties
import com.siriustech.merit.app_common.component.otp.VerificationEvent
import com.siriustech.merit.app_common.ext.AttributeStringData
import com.siriustech.merit.app_common.ext.buildAttrString
import com.siriustech.merit.app_common.ext.colorTxtLabel
import com.siriustech.merit.app_common.ext.colorTxtParagraph
import com.siriustech.merit.app_common.ext.displayEmailAddress
import com.siriustech.merit.app_common.theme.LocalTypography
import com.siriustech.merit.app_common.typeenum.Auth2FAType

/**
 * Created by Hein Htet
 */
class CommonOTPScreen {
}


@Composable
fun CommonOTPScreen(
    viewModel: CommonOTPViewModel,
    onSubmitVerificationCode: (code: String) -> Unit = {},
) {

    val context = LocalContext.current
    val authType = viewModel.authType.collectAsState()
    val email = viewModel.email.collectAsState()
    val phone = viewModel.phone.collectAsState()
    val otpCode = viewModel.otpCode.collectAsState()
    val otpResendCount = viewModel.otpResendCount.collectAsState()
    val refNumber = viewModel.refNumber.collectAsState()
    val otpExpireTime = viewModel.otpExpiredTime.collectAsState()


    fun showOtpSentAlert() {
        viewModel.emitBannerAlert(
            BannerAlertProperties(
                title = context.getString(R.string.key0111),
                description = context.getString(R.string.key0814),
            ),
        )
    }

    SingleEventEffect(viewModel.verificationEvent) { sideEffect ->
        when (sideEffect) {
            is VerificationEvent.OnOTPSubmit -> {
                onSubmitVerificationCode(sideEffect.code)
            }

            is VerificationEvent.OnResendOTPCode -> {
                viewModel.onResendOTPCode()
            }

            is VerificationEvent.OnOTPCodeChanged -> {
                viewModel.updateOtpCode(sideEffect.code)
            }

            is VerificationEvent.OnOTPSent -> {
                showOtpSentAlert()
            }

            is VerificationEvent.OnConfigurePin -> {
            }

            is VerificationEvent.VerificationSuccess -> {
            }

            else -> {}
        }
    }

    OTPVerification(
        otpResendCount = otpResendCount.value,
        otpExpireDuration = otpExpireTime.value,
        defaultOtpCode = otpCode,
        referenceNumber = refNumber.value,
        onEventChanged = { event -> viewModel.emitOtpEvent(event) },
        properties = OTPVerificationProperties(
            title = stringResource(id = if (authType.value == Auth2FAType.PHONE) R.string.key0198 else R.string.key0816),
            description = buildAttrString(
                arrayListOf(
                    AttributeStringData(
                        text = context.getString(if (authType.value == Auth2FAType.PHONE) R.string.key0032 else R.string.key0033),
                        textStyle = LocalTypography.current.text14.light.colorTxtParagraph()
                    ),
                    AttributeStringData(
                        text = if (authType.value == Auth2FAType.EMAIL) email.value.displayEmailAddress() else phone.value.displayEmailAddress(),
                        textStyle = LocalTypography.current.text14.medium.colorTxtLabel()
                    ),
                ),
            ),
            iconPainter = painterResource(id = if (authType.value == Auth2FAType.PHONE) R.drawable.ic_phone_otp_verification else R.drawable.ic_email_otp_verification),
            leftButtonText = stringResource(id = R.string.key0046),
            rightButtonText = stringResource(id = R.string.key0045)
        )
    )
}
