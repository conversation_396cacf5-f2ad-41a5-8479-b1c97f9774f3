package com.siriustech.merit.app_common.screen.emailverify

import android.app.Activity
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.collectAsState
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.fragment.app.FragmentActivity
import androidx.hilt.navigation.compose.hiltViewModel
import com.siriustech.core_ui_compose.base.SingleEventEffect
import com.siriustech.merit.app_common.R
import com.siriustech.merit.app_common.component.alert.BannerAlertProperties
import com.siriustech.merit.app_common.component.otp.OTPVerification
import com.siriustech.merit.app_common.component.otp.OTPVerificationProperties
import com.siriustech.merit.app_common.component.otp.VerificationEvent
import com.siriustech.merit.app_common.ext.AttributeStringData
import com.siriustech.merit.app_common.ext.buildAttrString
import com.siriustech.merit.app_common.ext.colorTxtLabel
import com.siriustech.merit.app_common.ext.colorTxtParagraph
import com.siriustech.merit.app_common.ext.displayEmailAddress
import com.siriustech.merit.app_common.theme.LocalTypography
import com.siriustech.merit.app_common.typeenum.Auth2FAType
import timber.log.Timber

/**
 * Created by Hein Htet
 */

@Composable
fun CommonOTPVerificationScreen(
    arguments: CommonOTPVerificationArguments,
    viewModel: CommonOTPVerificationViewModel = hiltViewModel(),
    onHandleAction: (VerificationEvent) -> Unit = {},
    event: VerificationEvent? = null,
    onOtpSubmit : (code : String) -> Unit = {}
) {
    Timber.d("COMMON_ARGUMENT $arguments")
    val context = LocalContext.current
    val authType = viewModel.authType.collectAsState()
    val email = viewModel.email.collectAsState()
    val phone = viewModel.phone.collectAsState()
    val otpCode = viewModel.otpCode.collectAsState()
    val otpResendCount = viewModel.otpResendCount.collectAsState()
    val refNumber = viewModel.refNumber.collectAsState()
    val otpExpireTime = viewModel.otpExpiredTime.collectAsState()

    LaunchedEffect(Unit) {
        viewModel.inputs.onUpdateArgument(arguments)
        if (!arguments.isManualHandleOTPRequest) {
            viewModel.onRequestOTP()
        }
    }

    LaunchedEffect(key1 = event) {
        if (event != null) {
            viewModel.inputs.onOTPVerificationEventChanged(event)
        }
    }

    fun showOtpSentAlert() {
        viewModel.emitBannerAlert(
            BannerAlertProperties(
                title = context.getString(R.string.key0111),
                description = if (authType.value == Auth2FAType.EMAIL) context.getString(R.string.key0814) else context.getString(
                    R.string.key0112
                ),
            ),
        )
    }

    SingleEventEffect(viewModel.verificationEvent) { sideEffect ->
        when (sideEffect) {
            is VerificationEvent.OnOTPSubmit -> {
                onHandleAction(
                    VerificationEvent.OnOTPSubmit(code = sideEffect.code)
                )
                onOtpSubmit(sideEffect.code)
            }

            is VerificationEvent.OnResendOTPCode -> {
                viewModel.onResendOTPCode()
            }

            is VerificationEvent.OnOTPCodeChanged -> {
                viewModel.updateOtpCode(sideEffect.code)
            }

            is VerificationEvent.OnOTPSent -> {
                showOtpSentAlert()
            }

            is VerificationEvent.OnConfigurePin -> {
            }

            is VerificationEvent.VerificationSuccess -> {
            }

            else -> {}
        }
    }

    SingleEventEffect(sideEffectFlow = viewModel.appAction) {
        when (it) {
            CommonOTPVerificationAction.DepositSuccess -> {
                val activity = context as FragmentActivity
                activity.setResult(Activity.RESULT_OK)
                activity.finish()
            }

            CommonOTPVerificationAction.WithdrawalSuccess -> {
                val activity = context as FragmentActivity
                activity.setResult(Activity.RESULT_OK)
                activity.finish()
            }

            CommonOTPVerificationAction.UserProfileUpdatedSuccess -> {
                val activity = context as FragmentActivity
                activity.setResult(Activity.RESULT_OK)
                activity.finish()
            }
        }
    }

    OTPVerification(
        otpResendCount = otpResendCount.value,
        otpExpireDuration = otpExpireTime.value,
        defaultOtpCode = otpCode,
        referenceNumber = refNumber.value,
        onEventChanged = { event -> viewModel.inputs.onOTPVerificationEventChanged(event) },
        properties = OTPVerificationProperties(
            title = stringResource(id = if (authType.value == Auth2FAType.PHONE) R.string.key0198 else R.string.key0816),
            description = buildAttrString(
                arrayListOf(
                    AttributeStringData(
                        text = context.getString(if (authType.value == Auth2FAType.PHONE) R.string.key0032 else R.string.key0033),
                        textStyle = LocalTypography.current.text14.light.colorTxtParagraph()
                    ),
                    AttributeStringData(
                        text = if (authType.value == Auth2FAType.EMAIL) (email.value
                            ?: "").displayEmailAddress() else (phone.value ?: ""),
                        textStyle = LocalTypography.current.text14.medium.colorTxtLabel()
                    ),
                ),
            ),
            iconPainter = painterResource(id = if (authType.value == Auth2FAType.PHONE) R.drawable.ic_phone_otp_verification else R.drawable.ic_email_otp_verification),
            leftButtonText = stringResource(id = R.string.key0046),
            rightButtonText = stringResource(id = R.string.key0045)
        )
    )
}