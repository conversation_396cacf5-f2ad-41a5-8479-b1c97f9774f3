package com.siriustech.merit.app_common.screen.createpin

import androidx.compose.foundation.Image
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxHeight
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.width
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.remember
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.tooling.preview.Preview
import androidx.fragment.app.FragmentActivity
import androidx.navigation.NavController
import androidx.navigation.compose.rememberNavController
import com.siriustech.merit.app_common.Constants
import com.siriustech.merit.app_common.component.button.ButtonProperties
import com.siriustech.merit.app_common.component.button.SecondaryBorderButton
import com.siriustech.merit.app_common.component.button.SecondaryButton
import com.siriustech.merit.app_common.ext.colorTxtParagraph
import com.siriustech.merit.app_common.ext.colorTxtTitle
import com.siriustech.merit.app_common.ext.navigateReplaceAll
import com.siriustech.merit.app_common.ext.popBackWithResult
import com.siriustech.merit.app_common.navigation.CreateNewPinSuccess
import com.siriustech.merit.app_common.navigation.Dashboard
import com.siriustech.merit.app_common.theme.LocalDimens
import com.siriustech.merit.app_common.theme.LocalTypography
import com.siriustech.merit.app_common.utils.BiometricHelper
import com.siriustech.merit.app_common.utils.BiometricHelper.promptInfo
import com.siriustech.merit.app_common.R as AppCommonR

/**
 * Created by Hein Htet
 */

@Composable
fun EnableBiometricScreen(
    navController: NavController,
    needCallback: Boolean = false,
    onBiometricSetupSuccess: () -> Unit = {},
) {
    val fragmentActivity = LocalContext.current as FragmentActivity

    val isBiometricAvailable = remember {
        BiometricHelper.isBiometricAvailable(fragmentActivity)
    }

    Column(
        modifier = Modifier
            .fillMaxWidth()
            .fillMaxHeight()
            .padding(LocalDimens.current.dimen12),
    ) {
        Spacer(modifier = Modifier.fillMaxHeight(0.15f))
        Image(
            painter = painterResource(id = AppCommonR.drawable.ic_enable_biometric),
            contentDescription = "Setup 2FA Resource",
            modifier = Modifier.align(Alignment.CenterHorizontally)
        )
        Spacer(modifier = Modifier.height(LocalDimens.current.dimen34))
        Text(
            modifier = Modifier.fillMaxWidth(),
            text = stringResource(id = AppCommonR.string.key0222),
            style = LocalTypography.current.text18.semiBold.colorTxtTitle(),
            textAlign = TextAlign.Center
        )
        Spacer(modifier = Modifier.height(LocalDimens.current.dimen16))
        Text(
            modifier = Modifier.fillMaxWidth(),
            text = stringResource(id = AppCommonR.string.key0223),
            style = LocalTypography.current.text14.light.colorTxtParagraph(),
            textAlign = TextAlign.Start
        )
        Spacer(modifier = Modifier.weight(1f))
        Row {
            SecondaryBorderButton(
                modifier = Modifier.weight(1f),
                properties = ButtonProperties(text = stringResource(id = AppCommonR.string.key0084)),
                onClicked = {
                    if (needCallback) {
                        navController.popBackStack()
                    } else {
                        navController.navigateReplaceAll(Dashboard)
                    }
                })
            if (isBiometricAvailable) {
                Spacer(modifier = Modifier.width(LocalDimens.current.dimen16))
                SecondaryButton(
                    modifier = Modifier.weight(1f),
                    properties = ButtonProperties(text = stringResource(id = AppCommonR.string.key0699)),
                    onClicked = {
                        BiometricHelper.getBiometricPrompt(fragmentActivity, onAuthSucceed = {
                            onBiometricSetupSuccess()
                            if (needCallback) {
                                navController.popBackWithResult(
                                    Constants.NAV_RETURN_SUCCESS,
                                    Constants.STATUS_BIOMETRIC_SUCCESS
                                )
                            } else {
                                navController.navigate(CreateNewPinSuccess)
                            }
                        }).authenticate(promptInfo)
                    })
            }
        }
    }
}

@Preview(showBackground = true, showSystemUi = true)
@Composable
fun PreviewEnableBiometricScreen() {
    EnableBiometricScreen(rememberNavController())
}