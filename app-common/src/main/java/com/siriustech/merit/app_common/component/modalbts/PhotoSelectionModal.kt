package com.siriustech.merit.app_common.component.modalbts

import androidx.compose.foundation.layout.fillMaxHeight
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Modifier
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.stringResource
import com.siriustech.merit.app_common.R
import com.siriustech.merit.app_common.typeenum.LanguageType
import com.siriustech.merit.app_common.typeenum.PortfolioListDisplayType

/**
 * Created by He<PERSON> Htet
 */

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun PhotoSelectionModal(
    showModal: Boolean = false,
    onDismissed: () -> Unit = {},
    onSelect: (PhotoSelectionEnum) -> Unit = {},
) {
    if (showModal) {
        val context = LocalContext.current
        ModelListBottomSheet(
            onDismissed = {
                onDismissed()
            },
            modifier = Modifier
                .fillMaxHeight(0.3f),
            onItemClicked = {
                onSelect(PhotoSelectionEnum.fromParams(it.id))
            },
            properties = ModelListBottomSheetProperties(
                backgroundType = BackgroundType.SECONDARY,
                searchEnable = false,
                prefixTitle = context.getString(R.string.key0410),
                title = stringResource(id = R.string.key0760),
                items = listOf(
                    ModalListDataContent(
                        id = PhotoSelectionEnum.OPEN_CAMERA.value,
                        title = context.getString(R.string.key0761),
                        isSelected = false,
                        iconResId = R.drawable.ic_camera
                    ),
                    ModalListDataContent(
                        id = PhotoSelectionEnum.OPEN_GALLERY.value,
                        title = context.getString(R.string.key0762),
                        isSelected = false,
                        iconResId = R.drawable.ic_gallery_frame
                    ),
                    ModalListDataContent(
                        id = PhotoSelectionEnum.DELETE_PHOTO.value,
                        title = context.getString(R.string.key0763),
                        isSelected = false,
                        iconResId = R.drawable.ic_trash_bin_black
                    ),
                )
            )
        )
    }
}

enum class PhotoSelectionEnum(val value: String) {
    OPEN_CAMERA("OPEN_CAMERA"),
    OPEN_GALLERY("OPEN_GALLERY"),
    DELETE_PHOTO("DELETE_PHOTO");

    companion object {
        fun fromParams(value: String): PhotoSelectionEnum {
            return when (value) {
                OPEN_CAMERA.value -> OPEN_CAMERA
                OPEN_GALLERY.value -> OPEN_GALLERY
                DELETE_PHOTO.value -> DELETE_PHOTO
                else -> OPEN_CAMERA
            }
        }
    }
}