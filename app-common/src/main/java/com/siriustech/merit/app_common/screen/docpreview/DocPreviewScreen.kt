package com.siriustech.merit.app_common.screen.docpreview

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.fillMaxHeight
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.platform.LocalConfiguration
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.tooling.preview.Preview
import androidx.fragment.app.FragmentActivity
import androidx.hilt.navigation.compose.hiltViewModel
import coil3.compose.AsyncImage
import coil3.network.NetworkHeaders
import coil3.network.httpHeaders
import coil3.request.CachePolicy
import coil3.request.ImageRequest
import coil3.request.crossfade
import com.siriustech.core_ui_compose.component.CommonToolbar
import com.siriustech.core_ui_compose.component.ToolbarProperties
import com.siriustech.merit.app_common.Constants
import com.siriustech.merit.app_common.R
import com.siriustech.merit.app_common.component.alert.BannerAlertProperties
import com.siriustech.merit.app_common.ext.colorTxtTitle
import com.siriustech.merit.app_common.screen.pdfviewer.PDFViewerArguments
import com.siriustech.merit.app_common.screen.pdfviewer.PDFViewerScreen
import com.siriustech.merit.app_common.theme.AppScreen
import com.siriustech.merit.app_common.theme.AppViewModel
import com.siriustech.merit.app_common.theme.LocalTypography
import com.siriustech.merit.app_common.utils.DownloadHelper
import timber.log.Timber
import com.siriustech.merit.app_common.R as AppCommonR

/**
 * Created by Hein Htet
 */

@Composable
fun DocPreviewScreen(viewModel: DocPreviewViewModel = hiltViewModel()) {
    val context = LocalContext.current
    var argument by remember {
        mutableStateOf(DocPreviewArgument())
    }
    val config = LocalConfiguration.current
    if (context is FragmentActivity) {
        val data =
            context.intent.getSerializableExtra(DocPreviewActivity.EXTRA_DOC_PREVIEW) as DocPreviewArgument
        argument = data
    }
    AppScreen(vm = viewModel, ignorePaddingValue = true) {
        Column {
            CommonToolbar(
                onLeftActionClicked = {
                    (context as FragmentActivity).finish()
                },
                onRightActionClicked = {
                    DownloadHelper(context) {
                        viewModel.emitBannerAlert(
                            BannerAlertProperties(
                                title = context.getString(AppCommonR.string.key0960),
                                description = context.getString(AppCommonR.string.key0961)
                            )
                        )
                    }
                        .downloadFile(argument.url, argument.name, viewModel.sessionId)
                },
                properties = ToolbarProperties(
                    leftActionResId = AppCommonR.drawable.ic_action_close,
                    rightActionResId = AppCommonR.drawable.ic_download,
                    title = argument.name,
                    titleTextStyle = LocalTypography.current.text14.medium.colorTxtTitle()
                )
            )
            if (argument.type == Constants.DOC_TYPE_PDF) {
                PDFViewerScreen(
                    arguments = PDFViewerArguments(
                        url = argument.url, showToolbar = false
                    )
                )
            } else {
                Box(
                    modifier = Modifier
                        .fillMaxSize()
                        .background(Color.Black), contentAlignment = Alignment.Center
                ) {
                    AsyncImage(
                        modifier = Modifier
                            .fillMaxWidth(0.9f)
                            .fillMaxHeight(0.5f),
                        model = ImageRequest.Builder(LocalContext.current)
                            .data(argument.url)
                            .crossfade(true)
                            .httpHeaders(
                                headers = NetworkHeaders.Builder()
                                    .set("sid", viewModel.sessionId)
                                    .build()
                            )
                            .diskCachePolicy(CachePolicy.ENABLED)
                            .build(),
                        placeholder = painterResource(R.drawable.ic_product_category_placeholder),
                        error = painterResource(R.drawable.ic_product_category_placeholder),
                        contentDescription = "Product Category Placeholder",
                        contentScale = ContentScale.Crop,
                        onError = {
                            Timber.d("Image Loading error ${it.result}")
                        }
                    )
                }
            }
        }
    }
}

class PreviewViewModel : AppViewModel() {}

@Preview(showBackground = true, showSystemUi = true)
@Composable
fun PreviewDocPreviewScreen() {
    DocPreviewScreen(viewModel = hiltViewModel())
}