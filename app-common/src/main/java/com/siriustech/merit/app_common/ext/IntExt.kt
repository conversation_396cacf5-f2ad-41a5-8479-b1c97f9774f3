package com.siriustech.merit.app_common.ext

import android.content.Context
import com.siriustech.merit.app_common.R

/**
 * Created by <PERSON><PERSON> <PERSON><PERSON>t
 */



fun Context.getStepDisplay(current : Int,totalStep : Int) : String{
    val displayStep: String
    val currentStepNoDisplay = getStepNumber(current)
    val totalStepNoDisplay = getStepNumber(totalStep)
    displayStep = "$currentStepNoDisplay/$totalStepNoDisplay"
    return displayStep
}

fun Context.getStepNumber(number : Int) : String{
    return when(number) {
        1 -> getString(R.string._key0120)
        2 -> getString(R.string._key0121)
        3 -> getString(R.string._key0122)
        4 -> getString(R.string._key0123)
        5 -> getString(R.string._key0124)
        6 -> getString(R.string._key0125)
        else -> number.toString()
    }
}