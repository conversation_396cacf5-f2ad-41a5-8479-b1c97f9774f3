package com.siriustech.merit.app_common.typeenum

import androidx.compose.runtime.Composable
import androidx.compose.runtime.ReadOnlyComposable
import androidx.compose.ui.graphics.Color
import com.siriustech.merit.app_common.component.text.BadgeText
import com.siriustech.merit.app_common.ext.capitalizeWords
import com.siriustech.merit.app_common.theme.LocalAppColor

/**
 * Created by Hein Htet
 */
enum class RiskLevel(val value: String) {
    LOW("LOW"),
    MEDIUM("MEDIUM"),
    HIGH("HIGH"),
    PRO("PRO");


    companion object {
        fun fromParam(name: String): RiskLevel? {
            return when (name) {
                LOW.value -> LOW
                MEDIUM.value -> MEDIUM
                HIGH.value -> HIGH
                PRO.value -> PRO
                else -> null
            }
        }

        fun getLevelNumber(type: RiskLevel?): Int {
            return when (type) {
                LOW -> 1
                MEDIUM -> 2
                HIGH -> 3
                PRO -> 4
                else -> -1
            }
        }
    }

    @Composable
    @ReadOnlyComposable
    fun getDisplayName(): String {
        return when (this) {
            LOW -> {
                "Low Risk"
            }

            MEDIUM -> {
                "Medium Risk"
            }

            HIGH -> {
                "High Risk"
            }

            PRO -> {
                "Pro Risk"
            }
        }
    }

    @Composable
    fun getDisplayVolatilityName(): String {
        return when (this) {
            LOW -> {
                "Low Volatility"
            }

            MEDIUM -> {
                "Medium Volatility"
            }

            HIGH -> {
                "High Volatility"
            }

            PRO -> "Pro Volatility"
        }
    }

    @Composable
    @ReadOnlyComposable
    fun getRiskLevelFillColor(): Color {
        return when (this) {
            LOW -> LocalAppColor.current.riskLevelTextLow
            MEDIUM -> LocalAppColor.current.riskLevelTextMedium
            else -> LocalAppColor.current.riskLevelTextHigh
        }
    }


    @Composable
    @ReadOnlyComposable
    fun getRiskLevelColor(): Color {
        val bgColor: Color = when (this) {
            LOW -> {
                LocalAppColor.current.btnInfo
            }

            MEDIUM -> {
                LocalAppColor.current.btnCaution
            }

            HIGH -> {
                LocalAppColor.current.btnNegative
            }

            PRO -> {
                LocalAppColor.current.btnNegative
            }
        }
        return bgColor
    }

    @Composable
    fun RiskLevelBadge() {
        val badgeText = this.value
        val bgColor: Color = getAssetRiskBackgroundColor()
        val textColor: Color = getAssetRiskLevelTextColor()
//        when (this) {
//            LOW -> {
//                bgColor = LocalAppColor.current.bgInfo
//                textColor = LocalAppColor.current.txtInfo
//            }
//
//            MEDIUM -> {
//                bgColor = LocalAppColor.current.bgCaution
//                textColor = LocalAppColor.current.txtCaution
//            }
//
//            HIGH -> {
//                bgColor = LocalAppColor.current.bgNegative
//                textColor = LocalAppColor.current.txtNegative
//            }
//            PRO -> {
//                bgColor = LocalAppColor.current.bgNegative
//                textColor = LocalAppColor.current.txtNegative
//            }
//        }
        BadgeText(label = badgeText, bgColor = bgColor, textColor = textColor)
    }

    @Composable
    @ReadOnlyComposable
    fun getAssetRiskLevelTextColor(): Color {
        return when (this) {
            LOW -> LocalAppColor.current.riskLevelTextLow
            MEDIUM -> LocalAppColor.current.riskLevelTextMedium
            else -> LocalAppColor.current.riskLevelTextHigh
        }
    }

    @Composable
    @ReadOnlyComposable
    fun getAssetRiskBackgroundColor(): Color {
        return when (this) {
            LOW -> LocalAppColor.current.riskLevelBgLow
            MEDIUM -> LocalAppColor.current.riskLevelBgMedium
            else -> LocalAppColor.current.riskLevelBgHigh
        }
    }
}