package com.siriustech.merit.app_common.screen.emailverify

import com.siriustech.merit.apilayer.service.user.updateuserinfo.UpdateUserInfoRequest
import com.siriustech.merit.apilayer.service.wallet.deposit.DepositRequest
import com.siriustech.merit.apilayer.service.wallet.withdraw.WithdrawRequest
import com.siriustech.merit.app_common.typeenum.Auth2FAType
import kotlinx.serialization.Serializable

@Serializable
data class CommonOTPVerificationArguments(
    val email: String = "",
    val phone : String? = null,
    val bizTypeStr: String,
    val authType : Auth2FAType = Auth2FAType.EMAIL,
    val depositRequest: DepositRequest? = null,
    val withdrawRequest: WithdrawRequest? = null,
    val commonOTPVerificationType: CommonOTPVerificationType? = null,
    val isManualHandleOTPRequest :Boolean = false,
    val updateProfileRequest: UpdateUserInfoRequest? = null,
    val mobileRegion : String? = null
) : java.io.Serializable


@Serializable
data class CommonOTPVerificationResultArguments(
    val otpCode: String,
    val refCode: String,
) : java.io.Serializable