package com.siriustech.merit.app_common.screen.pdfviewer

import android.os.Build
import android.os.Bundle
import androidx.activity.compose.setContent
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.toArgb
import androidx.fragment.app.FragmentActivity
import com.siriustech.core_ui_compose.ext.ChangeSystemBarsTheme
import com.siriustech.merit.app_common.R
import com.siriustech.merit.app_common.theme.AppScreen
import com.siriustech.merit.app_common.theme.AppViewModel
import dagger.hilt.android.AndroidEntryPoint

/**
 * Created by <PERSON><PERSON><PERSON><PERSON>
 */
@AndroidEntryPoint
class PDFViewerActivity : FragmentActivity() {


    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        val arguments = intent.getSerializableExtra(EXTRA_PDF_VIEWER_ARGUMENT) as? PDFViewerArguments?
        arguments?.let {  }
        setContent {
            ChangeSystemBarsTheme(lightTheme = true, Color.White.toArgb())
            AppScreen(vm = AppViewModel()) {
                PDFViewerScreen(arguments)
            }
        }
    }
    override fun finish() {
        super.finish()
        onFinishWithAnimate()
    }

    private fun onFinishWithAnimate() {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.UPSIDE_DOWN_CAKE) {
            overrideActivityTransition(
                OVERRIDE_TRANSITION_CLOSE,
                R.anim.slide_in_left,
                R.anim.slide_out_left
            )
        } else {
            overridePendingTransition(
                R.anim.slide_in_left,
                R.anim.slide_out_left
            )
        }
    }

    companion object {
        const val EXTRA_PDF_VIEWER_ARGUMENT = "EXTRA_PDF_VIEWER_ARGUMENT"
    }
}