package com.siriustech.merit.app_common.component.image

import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.painterResource
import androidx.hilt.navigation.compose.hiltViewModel
import androidx.lifecycle.Lifecycle
import androidx.lifecycle.compose.LifecycleEventEffect
import coil3.compose.AsyncImage
import coil3.network.NetworkHeaders
import coil3.network.httpHeaders
import coil3.request.CachePolicy
import coil3.request.ImageRequest
import coil3.request.crossfade
import com.siriustech.merit.app_common.Constants
import com.siriustech.merit.app_common.R
import com.siriustech.merit.app_common.data.AppCache
import com.siriustech.merit.app_common.data.CommonSharedPreferences
import com.siriustech.merit.app_common.theme.AppViewModel
import dagger.hilt.android.lifecycle.HiltViewModel
import javax.inject.Inject
import javax.inject.Named
import timber.log.Timber

/**
 * Created by Hein Htet
 */

@Composable
fun ProfileImage(
    modifier: Modifier = Modifier,
    viewModel: ProfileImageViewModel = hiltViewModel(),
) {

    var imageUrl by remember {
        mutableStateOf(viewModel.profileUrl)
    }

    val sid = viewModel.sessionId

    LifecycleEventEffect(Lifecycle.Event.ON_RESUME) {
        Timber.d("ProfileImage: ON_RESUME ${viewModel.profileUrl}")
        imageUrl = viewModel.profileUrl
    }

    AsyncImage(
        modifier =
        modifier.clip(RoundedCornerShape(50)),
        model = ImageRequest.Builder(LocalContext.current)
            .data(imageUrl)
            .crossfade(true)
            .httpHeaders(
                headers = NetworkHeaders.Builder()
                    .set("sid", sid)
                    .build()
            )
            .diskCachePolicy(CachePolicy.ENABLED)
            .build(),
        placeholder = painterResource(R.drawable.user_default_profile),
        error = painterResource(R.drawable.user_default_profile),
        contentDescription = "Product Category Placeholder",
        contentScale = ContentScale.Crop,
        onError = {
            Timber.d("Image Loading error ${it.result.throwable.message}")
        }
    )
}


@HiltViewModel
class ProfileImageViewModel @Inject constructor(
    private val appCache: AppCache,
    private val commonSharedPreferences: CommonSharedPreferences,
    @Named("BaseUrl") private val baseUrl: String,
) : AppViewModel() {

    val profileUrl: String
        get() {
            val image =
                "$baseUrl${Constants.COMMON_FILE_BASE_URL}${appCache.userBasicInfoDisplay?.profileImage.orEmpty()}"
            return image
        }

    val sessionId: String
        get() = commonSharedPreferences.sessionId

}