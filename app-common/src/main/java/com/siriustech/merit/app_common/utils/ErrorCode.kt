package com.siriustech.merit.app_common.utils

import com.core.network.base.getError
import com.core.network.model.Error
import com.siriustech.core_ui_compose.model.ErrorDisplay

/**
 * Created by <PERSON><PERSON><PERSON>
 */
object ErrorCode {





    // extension
    fun Error.mapToErrorDisplay() = ErrorDisplay(this.getError().errorResponse.status, message = this.getError().errorResponse.message)

}