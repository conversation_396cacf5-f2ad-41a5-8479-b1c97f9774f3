package com.siriustech.merit.app_common.screen.setup2FA

import androidx.compose.foundation.Image
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxHeight
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.tooling.preview.Preview
import androidx.navigation.NavController
import androidx.navigation.compose.rememberNavController
import com.siriustech.merit.app_common.component.button.ButtonProperties
import com.siriustech.merit.app_common.component.button.SecondaryButton
import com.siriustech.merit.app_common.component.header.CommonToolbarWithBackMenu
import com.siriustech.merit.app_common.navigation.Verification2FA
import com.siriustech.merit.app_common.theme.AppScreen
import com.siriustech.merit.app_common.theme.LocalDimens
import com.siriustech.merit.app_common.theme.LocalTypography
import com.siriustech.merit.app_common.ext.colorTxtTitle
import com.siriustech.merit.app_common.navigation.argument.authentication.Verification2FAArguments
import com.siriustech.merit.app_common.theme.AppViewModel
import com.siriustech.merit.app_common.typeenum.Auth2FAType
import com.siriustech.merit.app_common.R as AppCommonR

/**
 * Created by Hein Htet
 */

@Composable
fun Setup2FALandingScreen(navController: NavController,
                          arguments: Verification2FAArguments
) {
    AppScreen(vm = AppViewModel(), toolbar = { CommonToolbarWithBackMenu(onBackPressed = { navController.popBackStack()})}) {
            Column(
                modifier = Modifier
                    .fillMaxSize()
                    .padding(LocalDimens.current.dimen12)
            ) {
                Spacer(modifier = Modifier.fillMaxHeight(0.15f))
                Column(
                    modifier = Modifier
                        .weight(1f)
                        .fillMaxWidth()
                        .fillMaxSize(),
                ) {
                    Image(
                        painter = painterResource(id = AppCommonR.drawable.ic_2fa_landing),
                        contentDescription = "Setup 2FA Resource",
                        modifier = Modifier.align(Alignment.CenterHorizontally)
                    )
                    Spacer(modifier = Modifier.height(LocalDimens.current.dimen32))
                    Text(
                        text = stringResource(id = AppCommonR.string.key0191),
                        textAlign = TextAlign.Center,
                        modifier = Modifier.fillMaxWidth(),
                        style = LocalTypography.current.text18.semiBold.colorTxtTitle()
                    )
                    Spacer(modifier = Modifier.height(LocalDimens.current.dimen16))
                    Text(
                        text = stringResource(id = AppCommonR.string.key0193),
                        textAlign = TextAlign.Start,
                        modifier = Modifier.fillMaxWidth(),
                        style = LocalTypography.current.text14.light.colorTxtTitle()
                    )
                    Text(
                        text = stringResource(id = AppCommonR.string.key0194),
                        textAlign = TextAlign.Start,
                        modifier = Modifier
                            .fillMaxWidth()
                            .padding(top = LocalDimens.current.dimen8),
                        style = LocalTypography.current.text14.light.colorTxtTitle()
                    )
                    Spacer(modifier = Modifier.height(LocalDimens.current.dimen32))
//                    PrimaryButton(
//                        properties = ButtonProperties(
//                            text = stringResource(id = AppCommonR.string.),
//                        ), onClicked = {
//                            navController.navigate(Verification2FA(args = arguments.copy(authType = Auth2FAType.PHONE)))
//                        })
//                    Spacer(modifier = Modifier.height(LocalDimens.current.dimen12))
                    SecondaryButton(
                        properties = ButtonProperties(
                            text = stringResource(id = AppCommonR.string.key0196),
                        ), onClicked = {
                            navController.navigate(Verification2FA(args = arguments.copy(authType = Auth2FAType.EMAIL)))
                        })
                }
        }
    }
}

@Preview(showBackground = true, showSystemUi = true)
@Composable
fun PreviewSetup2FALandingScreen() {
    Setup2FALandingScreen(rememberNavController(), Verification2FAArguments())
}