package com.siriustech.merit.app_common.component.common

import androidx.compose.foundation.Image
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.padding
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import com.core.util.toAmount
import com.siriustech.merit.app_common.data.display.PriceChangeUIModel
import com.siriustech.merit.app_common.theme.LocalDimens
import com.siriustech.merit.app_common.theme.LocalTypography

/**
 * Created by <PERSON><PERSON><PERSON>
 */


@Composable
fun MarketPrice(modifier: Modifier = Modifier,
                priceChangeUIModel: PriceChangeUIModel, value: String, currency: String) {
    Row(
        modifier = modifier,
        verticalAlignment = Alignment.CenterVertically,
        horizontalArrangement = Arrangement.End
    ) {
        Text(
            text = value.toAmount(4).orEmpty(),
            style = LocalTypography.current.text14.semiBold.copy(color = priceChangeUIModel.textColor),
        )
        Text(
            text = currency,
            modifier = Modifier.padding(start = LocalDimens.current.dimen1),
            style = LocalTypography.current.text12.semiBold.copy(color = priceChangeUIModel.textColor),
        )
        Image(
            modifier = Modifier.padding(start = LocalDimens.current.dimen1),
            painter = priceChangeUIModel.iconPainter,
            contentDescription = "Price Change Image Resource"
        )
    }
}