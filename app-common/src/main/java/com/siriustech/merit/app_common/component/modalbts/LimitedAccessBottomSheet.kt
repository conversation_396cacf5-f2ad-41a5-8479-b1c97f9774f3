package com.siriustech.merit.app_common.component.modalbts

import androidx.compose.foundation.Image
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.ModalBottomSheet
import androidx.compose.material3.Text
import androidx.compose.material3.rememberModalBottomSheetState
import androidx.compose.runtime.Composable
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.ui.Modifier
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.tooling.preview.Preview
import com.siriustech.merit.app_common.component.button.ButtonProperties
import com.siriustech.merit.app_common.component.button.SecondaryButton
import com.siriustech.merit.app_common.component.container.PaddingTop
import com.siriustech.merit.app_common.ext.colorTxtParagraph
import com.siriustech.merit.app_common.ext.colorTxtTitle
import com.siriustech.merit.app_common.ext.noRippleClickable
import com.siriustech.merit.app_common.theme.LocalAppColor
import com.siriustech.merit.app_common.theme.LocalDimens
import com.siriustech.merit.app_common.theme.LocalTypography
import com.siriustech.merit.app_common.typeenum.UserStatus
import com.siriustech.merit.app_common.typeenum.UserStatus.Companion.isAccountIsPendingStatus
import kotlinx.coroutines.launch
import com.siriustech.merit.app_common.R as AppCommonR


/**
 * Created by Hein Htet
 */
@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun LimitedAccessBottomSheet(
    userStatus: UserStatus,
    onDismissed: () -> Unit = {},
    onResumeOnboardingStep: () -> Unit = {},
) {
    val sheetState = rememberModalBottomSheetState(skipPartiallyExpanded = true)
    val scope = rememberCoroutineScope()

    fun onDismiss() {
        scope.launch {
            sheetState.hide()
            onDismissed()
        }
    }

    ModalBottomSheet(
        dragHandle = {},
        shape = RoundedCornerShape(LocalDimens.current.dimen4),
        containerColor = LocalAppColor.current.bgDefault,
        onDismissRequest = {
            onDismissed()
        }, sheetState = sheetState,
        modifier = Modifier
    ) {
        LimitedAccessBottomSheetContent(
            userStatus = userStatus,
            onDismissed = {
                onDismiss()
            }, onResumeOnboardingStep = {
                onDismiss()
                if (userStatus == UserStatus.ONBOARDING) {
                    onResumeOnboardingStep()
                }
            })
    }
}

@Composable
fun LimitedAccessBottomSheetContent(
    userStatus: UserStatus,
    onDismissed: () -> Unit = {},
    onResumeOnboardingStep: () -> Unit = {},
) {
    val isPendingAccount = userStatus.isAccountIsPendingStatus()

    Column(
        modifier = Modifier
            .padding(LocalDimens.current.dimen12)
    ) {
        Row {
            Text(
                text = stringResource(id = AppCommonR.string.key1023),
                modifier = Modifier.weight(1f),
                style = LocalTypography.current.text14.semiBold.colorTxtTitle()
            )
            Image(
                modifier = Modifier.noRippleClickable {
                    onDismissed()
                },
                painter = painterResource(id = AppCommonR.drawable.ic_action_close),
                contentDescription = "Close Image Resource"
            )
        }
        PaddingTop(value = LocalDimens.current.dimen8)
        Text(
            text = stringResource(id = if (isPendingAccount) AppCommonR.string.key1027 else AppCommonR.string.key1024),
            style = LocalTypography.current.text12.light.colorTxtParagraph()
        )
        PaddingTop(value = LocalDimens.current.dimen16)
        SecondaryButton(
            onClicked = { onResumeOnboardingStep() },
            properties = ButtonProperties(
                text = stringResource(id = if (isPendingAccount) AppCommonR.string.key1026 else AppCommonR.string.key1025),
            )
        )
        PaddingTop(value = LocalDimens.current.dimen16)
    }
}

@Preview(showBackground = true, showSystemUi = true)
@Composable
fun PreviewLimitedAccessBottomSheet() {
    LimitedAccessBottomSheetContent(userStatus = UserStatus.NORMAL)
}