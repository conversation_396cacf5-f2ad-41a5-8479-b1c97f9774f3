package com.siriustech.merit.app_common.component.indicator

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.layout.wrapContentHeight
import androidx.compose.foundation.pager.PagerState
import androidx.compose.foundation.pager.rememberPagerState
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import com.siriustech.merit.app_common.theme.LocalDimens

/**
 * Created by <PERSON><PERSON>
 */

@Composable
fun HorizontalPagerIndicator(
    modifier: Modifier = Modifier,
    pagerState: PagerState,
    selectedCurrentIndex: Boolean = false,
) {
    Row(
        Modifier
            .wrapContentHeight()
            .fillMaxWidth()
            .padding(bottom = 8.dp)
            .then(modifier),
        horizontalArrangement = Arrangement.Center,
    ) {
        repeat(pagerState.pageCount) { iteration ->
            var color = if (pagerState.currentPage >= iteration) Color.DarkGray else Color.LightGray
            if (selectedCurrentIndex) {
                color = if (pagerState.currentPage == iteration) Color.DarkGray else Color.LightGray
            }
            Box(
                modifier = Modifier
                    .padding(2.dp)
                    .clip(RoundedCornerShape(LocalDimens.current.dimen4))
                    .background(color)
                    .width(LocalDimens.current.dimen30)
                    .height(LocalDimens.current.dimen2)
            )
        }
    }
}

@Preview(showBackground = true, showSystemUi = true)
@Composable
fun PreviewHorizontalPagerIndicator() {
    val pagerState = rememberPagerState(pageCount = {
        4
    })
    HorizontalPagerIndicator(modifier = Modifier.padding(top = 24.dp), pagerState = pagerState)
}

