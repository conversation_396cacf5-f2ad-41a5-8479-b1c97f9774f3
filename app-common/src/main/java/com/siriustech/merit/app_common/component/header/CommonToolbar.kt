package com.siriustech.merit.app_common.component.header

import androidx.compose.runtime.Composable
import androidx.compose.ui.text.AnnotatedString
import com.siriustech.core_ui_compose.component.CommonToolbar
import com.siriustech.core_ui_compose.component.ToolbarProperties
import com.siriustech.merit.app_common.R
import com.siriustech.merit.app_common.ext.colorTxtTitle
import com.siriustech.merit.app_common.theme.LocalAppColor
import com.siriustech.merit.app_common.theme.LocalDimens
import com.siriustech.merit.app_common.theme.LocalTypography

/**
 * Created by Hein Htet
 */


@Composable
fun CommonToolbarWithBackMenu(
    title: String = "",
    annotatedTitleString: AnnotatedString? = null,
    onBackPressed: () -> Unit = {},
) {
    CommonToolbar(
        properties = ToolbarProperties(
            title = title,
            annotatedTitleString = annotatedTitleString,
            titleTextStyle = LocalTypography.current.text14.semiBold.colorTxtTitle(),
            backgroundColor = LocalAppColor.current.bgDefault,
            height = LocalDimens.current.dimen44,
            leftActionResId = R.drawable.ic_back_arrow
        ),
        onLeftActionClicked = { onBackPressed() },
    )
}

@Composable
fun CommonToolbarWithBackAndResetMenu(
    title: String = "",
    annotatedTitleString: AnnotatedString? = null,
    onBackClicked: () -> Unit = {},
    onResetButtonClicked: () -> Unit = {},
) {
    CommonToolbar(
        properties = ToolbarProperties(
            title = title,
            annotatedTitleString = annotatedTitleString,
            rightActionResId = R.drawable.ic_action_refersh,
            leftActionResId = R.drawable.ic_back_arrow,
            backgroundColor = LocalAppColor.current.bgDefault,
            height = LocalDimens.current.dimen44,
            titleTextStyle = LocalTypography.current.text14.semiBold.colorTxtTitle(),
        ),
        onRightActionClicked = { onResetButtonClicked() },
        onLeftActionClicked = { onBackClicked() }
    )
}

@Composable
fun defaultToolbarProperties() = ToolbarProperties(
    titleTextStyle = LocalTypography.current.text14.semiBold.colorTxtTitle(),
    backgroundColor = LocalAppColor.current.bgDefault,
    height = LocalDimens.current.dimen44,
)