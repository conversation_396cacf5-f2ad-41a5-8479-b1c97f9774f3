package com.siriustech.merit.app_common.component.text

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.tooling.preview.Preview
import com.siriustech.merit.app_common.Constants
import com.siriustech.merit.app_common.theme.LocalDimens
import com.siriustech.merit.app_common.theme.LocalTypography
import com.siriustech.merit.app_common.typeenum.RiskLevel

/**
 * Created by Hein Htet
 */

@Composable
fun BadgeText(modifier: Modifier = Modifier, label: String, bgColor: Color, textColor: Color) {
    if (label != Constants.NO_EXCHANGE) {
        Box(
            modifier = Modifier
                .clip(RoundedCornerShape(LocalDimens.current.dimen2))
                .background(bgColor)
                .then(modifier)
        ) {
            Text(
                text = label,
                style = LocalTypography.current.text12.medium.copy(color = textColor),
                maxLines = 1,
                overflow = TextOverflow.Ellipsis,
                modifier = Modifier.padding(
                    horizontal = LocalDimens.current.dimen6,
                )
            )
        }
    }
}

@Preview(showBackground = true, showSystemUi = true)
@Composable
fun PreviewBadgeText() {
    BadgeText(
        label = "MEDIUM",
        bgColor = RiskLevel.MEDIUM.getAssetRiskBackgroundColor(),
        textColor = RiskLevel.MEDIUM.getAssetRiskLevelTextColor()
    )
}