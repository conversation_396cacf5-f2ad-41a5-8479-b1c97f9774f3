package com.siriustech.merit.app_common.component.common

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.tooling.preview.Preview
import com.siriustech.merit.app_common.R
import com.siriustech.merit.app_common.component.container.PaddingStart
import com.siriustech.merit.app_common.component.text.BadgeText
import com.siriustech.merit.app_common.component.toggle.ToggleRadioButton
import com.siriustech.merit.app_common.ext.colorTxtParagraph
import com.siriustech.merit.app_common.ext.colorTxtTitle
import com.siriustech.merit.app_common.ext.noRippleClickable
import com.siriustech.merit.app_common.theme.LocalAppColor
import com.siriustech.merit.app_common.theme.LocalDimens
import com.siriustech.merit.app_common.theme.LocalTypography
import com.siriustech.merit.app_common.typeenum.BankAccountStatus
import timber.log.Timber

/**
 * Created by Hein Htet
 */
@Composable
fun BankAccountItem(
    properties: BankAccountItemProperties = BankAccountItemProperties(),
    onBankAccountSelected: (BankAccountItemProperties) -> Unit = {},
) {
    Row(
        modifier = Modifier
            .fillMaxWidth()
            .clip(RoundedCornerShape(LocalDimens.current.dimen4))
            .background(LocalAppColor.current.bgTone)
            .padding(horizontal = LocalDimens.current.dimen12)
            .noRippleClickable {
                if (properties.status == BankAccountStatus.APPROVED) {
                    onBankAccountSelected(properties)
                }
            },
        verticalAlignment = Alignment.CenterVertically
    ) {
        Timber.d("BankAccountItem ${properties}")
        ToggleRadioButton(defaultValue = properties.isSelected)
        Column(modifier = Modifier.padding(LocalDimens.current.dimen8)) {
            Row {
                Text(
                    modifier = Modifier.weight(1f, false),
                    text = properties.name,
                    maxLines = 1,
                    overflow = TextOverflow.Ellipsis,
                    style = LocalTypography.current.text14.medium.colorTxtTitle(),
                )
                PaddingStart(value = LocalDimens.current.dimen8)
                if (properties.isPrimary) {
                    BadgeText(
                        label = stringResource(id = R.string.key0383),
                        bgColor = LocalAppColor.current.bgDefault,
                        textColor = LocalAppColor.current.txtLabel
                    )
                }
                if (properties.status != BankAccountStatus.APPROVED) {
                    properties.status.DisplayStatusBadge()
                }
            }
            Text(
                text = properties.accountNo,
                style = LocalTypography.current.text14.light.colorTxtParagraph(),
            )
        }
    }
}

data class BankAccountItemProperties(
    val id: String = "",
    val name: String = "",
    val accountNo: String = "",
    val isPrimary: Boolean = false,
    var isSelected: Boolean = false,
    val status: BankAccountStatus = BankAccountStatus.PENDING,
)

@Preview(showBackground = true)
@Composable
fun PreviewBankAccount() {
    BankAccountItem(
        properties = BankAccountItemProperties(
            name = "Bank Account",
            accountNo = "*********",
            isPrimary = true,
            isSelected = true,
            status = BankAccountStatus.PENDING
        )
    )
}
