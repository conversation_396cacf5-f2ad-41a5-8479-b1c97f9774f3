package com.siriustech.merit.app_common.component.common

import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.tooling.preview.Preview
import com.siriustech.merit.app_common.R
import com.siriustech.merit.app_common.component.container.PaddingTop
import com.siriustech.merit.app_common.component.textfield.InputBox
import com.siriustech.merit.app_common.component.textfield.InputProperties
import com.siriustech.merit.app_common.ext.colorTxtTitle
import com.siriustech.merit.app_common.ext.noRippleClickable
import com.siriustech.merit.app_common.theme.LocalAppColor
import com.siriustech.merit.app_common.theme.LocalDimens
import com.siriustech.merit.app_common.theme.LocalTypography

/**
 * Created by Hein Htet
 */

@Composable
fun DateRangeInputPicker(
    modifier: Modifier = Modifier,
    properties: DateRangeInputPickerProperties = DateRangeInputPickerProperties(),
    onPickStatDate: () -> Unit = {},
    onPickEndDate: () -> Unit = {},
    onClearStartDate: () -> Unit = {},
    onClearEndDate: () -> Unit = {},
) {
    Column(
        modifier = Modifier
            .fillMaxWidth()
            .then(modifier)
    ) {
        Text(
            text = properties.title, style = LocalTypography.current.text14.medium.colorTxtTitle()
                .merge(properties.titleTextStyle)
        )
        PaddingTop(value = LocalDimens.current.dimen8)
        Row(modifier = Modifier, verticalAlignment = Alignment.CenterVertically) {
            InputBox(
                onTextChange = {
                    if (it.isEmpty()) {
                        onClearStartDate()
                    }
                },
                inputBoxModifier = Modifier.noRippleClickable {
                    onPickStatDate()
                },
                value = properties.startDate,
                modifier = Modifier.weight(1f),
                properties = InputProperties(
                    editable = false,
                    showClearButton = true,
                    pickerTypeIcon = painterResource(id = R.drawable.ic_calendar_black),
                    placeholder = "DD-MM-YY"
                )
            )
            Box(
                modifier = Modifier
                    .padding(horizontal = LocalDimens.current.dimen8)
                    .size(LocalDimens.current.dimen32)
                    .background(LocalAppColor.current.btn3rd)
                    .clip(RoundedCornerShape(LocalDimens.current.dimen2)),
                contentAlignment = Alignment.Center
            ) {
                Image(
                    painter = painterResource(id = R.drawable.ic_range_arrow),
                    contentDescription = "Arrow Image Resource"
                )
            }
            InputBox(
                onTextChange = {
                    if (it.isEmpty()) {
                        onClearEndDate()
                    }
                },
                inputBoxModifier = Modifier.noRippleClickable {
                    onPickEndDate()
                },
                value = properties.endDate,
                modifier = Modifier.weight(1f),
                properties = InputProperties(
                    editable = false,
                    showClearButton = true,
                    pickerTypeIcon = painterResource(id = R.drawable.ic_calendar_black),
                    placeholder = "DD-MM-YY"
                )
            )
        }
    }
}

data class DateRangeInputPickerProperties(
    val startDate: String = "",
    val endDate: String = "",
    val title: String = "",
    val titleTextStyle: TextStyle = TextStyle(),
)

@Preview(
    showSystemUi = true, showBackground = true
)
@Composable
fun PreviewDateRangeInputPicker() {
    DateRangeInputPicker(
        modifier = Modifier
            .padding(
                horizontal = LocalDimens.current.dimen12,
                vertical = LocalDimens.current.dimen12
            )
    )
}