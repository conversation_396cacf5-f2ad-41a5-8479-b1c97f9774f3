package com.siriustech.merit.app_common.typeenum

import androidx.compose.runtime.Composable
import androidx.compose.runtime.ReadOnlyComposable
import androidx.compose.ui.res.stringResource
import com.siriustech.merit.app_common.R

/**
 * Created by Hein Htet
 */
enum class HistoryFilterType(val value: String) {
    TRADE_HISTORY("TRADE_HISTORY"),
    DEPOSIT_HISTORY("DEPOSIT"),
    WITHDRAWAL_HISTORY("WITHDRAW");


    @Composable
    @ReadOnlyComposable
    fun getDisplayName() : String {
        return when(this){
            TRADE_HISTORY -> stringResource(id = R.string.key0678)
            DEPOSIT_HISTORY -> stringResource(id = R.string.key0662)
            WITHDRAWAL_HISTORY -> stringResource(id = R.string.key0663)
        }
    }

    fun getIconResId() : Int {
        return when(this){
            TRADE_HISTORY -> R.drawable.ic_history_trade
            DEPOSIT_HISTORY -> R.drawable.ic_history_deposit
            WITHDRAWAL_HISTORY -> R.drawable.ic_history_withdrawal
        }
    }

    fun fromParam(value: String): HistoryFilterType {
        return when (value) {
            TRADE_HISTORY.value -> TRADE_HISTORY
            DEPOSIT_HISTORY.value -> DEPOSIT_HISTORY
            WITHDRAWAL_HISTORY.value -> WITHDRAWAL_HISTORY
            else -> TRADE_HISTORY
        }
    }


    companion object {

    }


}