package com.siriustech.merit.app_common.component.container

import androidx.compose.foundation.Image
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.tooling.preview.Preview
import com.siriustech.merit.app_common.R
import com.siriustech.merit.app_common.ext.colorTxtParagraph
import com.siriustech.merit.app_common.theme.LocalDimens
import com.siriustech.merit.app_common.theme.LocalTypography

/**
 * Created by <PERSON><PERSON>tet
 */
@Composable
fun LabelValue(
    modifier: Modifier = Modifier,
    label: String,
    value: String,
    valueTextStyle: TextStyle? = null,
    extraUi: @Composable() (() -> Unit?)? = null,
) {
    var textValue = value
    if (value.isEmpty()) {
        textValue = "-"
    }
    Row(
        modifier = modifier.fillMaxWidth(),
        horizontalArrangement = Arrangement.SpaceBetween,
    ) {
        Text(
            text = label,
            style = LocalTypography.current.text14.light.colorTxtParagraph()
        )
        Row(verticalAlignment = Alignment.CenterVertically) {
            extraUi?.let {
                it()
                PaddingEnd(value = LocalDimens.current.dimen4)
            }
            Text(
                text = textValue,
                textAlign = TextAlign.End,
                style = LocalTypography.current.text14.regular.colorTxtParagraph()
                    .merge(valueTextStyle)
            )
        }
    }
}

@Preview(showBackground = true)
@Composable
fun PreviewLabelValue() {
    LabelValue(
        label = "Structured Description",
        value = "This is a s",
    )
}