package com.siriustech.merit.app_common.typeenum

enum class CreateNewPinStep(val step: Int) {
    CREATE_PIN(0),
    CONFIRM_PIN(1),
    ENABLE_BIOMETRIC(2);

    companion object {

        const val TOTAL_STEP = 3

        fun fromParam(value: Int): CreateNewPinStep {
            return when (value) {
                0 -> CREATE_PIN
                1 -> CONFIRM_PIN
                2 -> ENABLE_BIOMETRIC
                else -> CREATE_PIN
            }
        }
    }
}
