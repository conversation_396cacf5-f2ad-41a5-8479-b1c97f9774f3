package com.siriustech.merit.app_common.component.alert;

import android.app.Dialog;
import android.content.Context;
import android.graphics.Color;
import android.graphics.drawable.ColorDrawable;
import android.os.Bundle;
import android.view.View;
import android.view.Window;
import android.widget.Button;
import android.widget.TextView;

import com.siriustech.merit.app_common.R;

import org.w3c.dom.Text;

import java.util.Objects;

public class IOSAlertDialog extends Dialog {
    private String title;
    private String message;
    private String positiveButtonText;
    private String negativeButtonText;
    private View.OnClickListener positiveButtonClickListener;
    private View.OnClickListener negativeButtonClickListener;

    private TextView titleTextView;
    private TextView messageTextView;
    private TextView positiveButton;
    private TextView negativeButton;
    private View buttonDivider;

    public IOSAlertDialog(Context context) {
        super(context);
    }

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        requestWindowFeature(Window.FEATURE_NO_TITLE);
        setContentView(R.layout.ios_alert_dialog);

        // Make dialog background transparent to show rounded corners properly
        Objects.requireNonNull(getWindow()).setBackgroundDrawable(new ColorDrawable(Color.TRANSPARENT));

        // Initialize views
        titleTextView = findViewById(R.id.alertTitle);
        messageTextView = findViewById(R.id.alertMessage);
        positiveButton = findViewById(R.id.positiveButton);
        negativeButton = findViewById(R.id.negativeButton);
        buttonDivider = findViewById(R.id.buttonDivider);

        // Set text content if available
        if (title != null) {
            titleTextView.setText(title);
        } else {
            titleTextView.setVisibility(View.GONE);
        }

        if (message != null) {
            messageTextView.setText(message);
        } else {
            messageTextView.setVisibility(View.GONE);
        }

        // Configure buttons
        if (positiveButtonText != null) {
            positiveButton.setText(positiveButtonText);
            positiveButton.setOnClickListener(view -> {
                if (positiveButtonClickListener != null) {
                    positiveButtonClickListener.onClick(view);
                }
                dismiss();
            });
        } else {
            positiveButton.setVisibility(View.GONE);
        }

        if (negativeButtonText != null) {
            negativeButton.setText(negativeButtonText);
            negativeButton.setOnClickListener(view -> {
                if (negativeButtonClickListener != null) {
                    negativeButtonClickListener.onClick(view);
                }
                dismiss();
            });
        } else {
            negativeButton.setVisibility(View.GONE);
        }

        // Show divider if both buttons are visible
        if (positiveButtonText != null && negativeButtonText != null) {
            buttonDivider.setVisibility(View.VISIBLE);
        }
    }

    // Builder pattern for easy configuration
    public static class Builder {
        private IOSAlertDialog dialog;

        public Builder(Context context) {
            dialog = new IOSAlertDialog(context);
        }

        public Builder setTitle(String title) {
            dialog.title = title;
            return this;
        }

        public Builder setMessage(String message) {
            dialog.message = message;
            return this;
        }

        public Builder setPositiveButton(String text, View.OnClickListener listener) {
            dialog.positiveButtonText = text;
            dialog.positiveButtonClickListener = listener;
            return this;
        }

        public Builder setNegativeButton(String text, View.OnClickListener listener) {
            dialog.negativeButtonText = text;
            dialog.negativeButtonClickListener = listener;
            return this;
        }

        public IOSAlertDialog create() {
            return dialog;
        }

        public IOSAlertDialog show() {
            dialog.show();
            return dialog;
        }
    }
}