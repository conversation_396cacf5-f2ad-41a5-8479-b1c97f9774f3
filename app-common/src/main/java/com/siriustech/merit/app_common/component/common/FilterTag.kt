package com.siriustech.merit.app_common.component.common

import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.ExperimentalLayoutApi
import androidx.compose.foundation.layout.FlowRow
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.tooling.preview.Preview
import com.siriustech.merit.app_common.R
import com.siriustech.merit.app_common.component.container.PaddingEnd
import com.siriustech.merit.app_common.component.container.PaddingStart
import com.siriustech.merit.app_common.ext.colorTxtLabel
import com.siriustech.merit.app_common.ext.noRippleClickable
import com.siriustech.merit.app_common.theme.LocalAppColor
import com.siriustech.merit.app_common.theme.LocalDimens
import com.siriustech.merit.app_common.theme.LocalTypography

/**
 * Created by Hein Htet
 */

@OptIn(ExperimentalLayoutApi::class)
@Composable
fun FilterTags(
    modifier: Modifier = Modifier,
    items: List<FilterTagDisplayModel> = emptyList(),
    onItemDelete: (item: FilterTagDisplayModel) -> Unit = {},
) {
    FlowRow(
        modifier = Modifier
            .padding(top = LocalDimens.current.dimen8)
            .fillMaxWidth()
            .background(LocalAppColor.current.bgDefault)
            .then(modifier),
        horizontalArrangement = Arrangement.spacedBy(LocalDimens.current.dimen4),
    ) {
        repeat(items.size) {
            FilterTagContent(items[it], onItemDelete = onItemDelete)
        }
    }
}

@Composable
fun FilterTagContent(
    item: FilterTagDisplayModel,
    onItemDelete: (item: FilterTagDisplayModel) -> Unit = {},
) {
    Row(
        verticalAlignment = Alignment.CenterVertically,
        modifier = Modifier
            .padding(vertical = LocalDimens.current.dimen2)
            .background(LocalAppColor.current.bgAccent)
            .clip(RoundedCornerShape(LocalDimens.current.dimen2))
    ) {
        PaddingStart(value = LocalDimens.current.dimen4)
        Text(
            text = item.text,
            style = LocalTypography.current.text10.medium.colorTxtLabel()
        )
        if (item.showClearButton) {
            PaddingStart(value = LocalDimens.current.dimen4)
            Image(
                painter = painterResource(id = R.drawable.ic_clear_blue),
                contentDescription = "",
                modifier = Modifier.noRippleClickable {
                    onItemDelete(item)
                }
            )
        }
        PaddingEnd(value = LocalDimens.current.dimen4)
    }
}

data class FilterTagDisplayModel(
    val id: String = "",
    val text: String = "",
    val showClearButton: Boolean = true,
)


@Preview(showBackground = true, showSystemUi = true)
@Composable
fun PreviewFilterTag() {
    val items = ArrayList<FilterTagDisplayModel>()
    repeat(20) {
        items.add(FilterTagDisplayModel(id = it.toString(), text = "Item ${it}"))
    }
    FilterTags(items = items)
}