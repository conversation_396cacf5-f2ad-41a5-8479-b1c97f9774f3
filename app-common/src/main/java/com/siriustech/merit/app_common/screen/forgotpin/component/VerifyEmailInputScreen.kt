package com.siriustech.merit.app_common.screen.forgotpin.component

import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.padding
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.State
import androidx.compose.runtime.collectAsState
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.tooling.preview.Preview
import com.siriustech.merit.app_common.component.button.ButtonProperties
import com.siriustech.merit.app_common.component.button.SecondaryButton
import com.siriustech.merit.app_common.component.common.ConfigCredentialInput
import com.siriustech.merit.app_common.component.common.ConfigCredentialInputProperties
import com.siriustech.merit.app_common.component.container.PaddingTop
import com.siriustech.merit.app_common.ext.colorTxtInactive
import com.siriustech.merit.app_common.ext.underline
import com.siriustech.merit.app_common.theme.LocalDimens
import com.siriustech.merit.app_common.theme.LocalTypography
import com.siriustech.merit.app_common.screen.forgotpin.ForgotPinAction
import com.siriustech.merit.app_common.screen.forgotpin.ForgotPinViewModel
import com.siriustech.merit.app_common.R as AppCommonR

/**
 * Created by Hein Htet
 */

@Composable
fun VerifyEmailInputScreen(viewModel: ForgotPinViewModel) {
    val isValidEmail = viewModel.outputs.isValidEmail.collectAsState()
    VerifyEmailInputContent(isValidEmail = isValidEmail, onEmailTextChange = {
        viewModel.onTriggerActions(ForgotPinAction.OnEmailTextChanged(it))
    }, onSubmit = {
        viewModel.onTriggerActions(ForgotPinAction.OnSubmittedEmail)
    })
}

@Composable
fun VerifyEmailInputContent(
    isValidEmail: State<Boolean>? = null,
    onEmailTextChange: (String) -> Unit = {},
    onSubmit: () -> Unit = {},
) {
    Column(
        horizontalAlignment = Alignment.CenterHorizontally,
        modifier = Modifier.padding(horizontal = LocalDimens.current.dimen12)
    ) {
        PaddingTop(value = LocalDimens.current.dimen12)
        ConfigCredentialInput(
            properties = ConfigCredentialInputProperties(
                title = stringResource(id = AppCommonR.string.key0242),
                description = stringResource(id = AppCommonR.string.key0243),
                inputTitle = stringResource(id = AppCommonR.string.key0244),
                inputPlaceholder = stringResource(id = AppCommonR.string.key0245),
                onInputTextChanged = onEmailTextChange,
            ),
        )
        PaddingTop(value = LocalDimens.current.dimen24)
        Text(
            text = stringResource(id = AppCommonR.string.key0246),
            style = LocalTypography.current.text14.regular.colorTxtInactive().underline()
        )
        Spacer(modifier = Modifier.weight(1f))
        SecondaryButton(
            onClicked = onSubmit,
            properties = ButtonProperties(
                enabled = isValidEmail?.value == true,
                text = stringResource(id = AppCommonR.string.key0247)
            )
        )
    }
}

@Preview(showBackground = true, showSystemUi = true)
@Composable
fun PreviewVerifyEmailInputScreen() {
    VerifyEmailInputContent()
}