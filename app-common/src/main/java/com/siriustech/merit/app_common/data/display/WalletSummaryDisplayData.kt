package com.siriustech.merit.app_common.data.display

data class WalletSummaryDisplayData(
    val totalBalance: String = "",
    val currency: String = "",
    val priceChange: String = "",
    val priceChangeRate: String = "",
    val cashBalance: String? = "",
    val costValue: String? = "",
    val marketValue: String? = "",
){
    fun isValid(): Boolean {
        return totalBalance.isNotEmpty() && !cashBalance.isNullOrEmpty()
    }
}
