package com.siriustech.merit.app_common.ext

import androidx.compose.runtime.Composable
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.AnnotatedString
import androidx.compose.ui.text.SpanStyle
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.text.buildAnnotatedString
import androidx.compose.ui.text.font.FontStyle
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextDecoration
import androidx.compose.ui.text.withStyle
import com.siriustech.merit.app_common.R
import com.siriustech.merit.app_common.theme.LocalAppColor
import com.siriustech.merit.app_common.theme.LocalTypography
import java.text.AttributedString

/**
 * Created by <PERSON><PERSON>tet
 */

// text ext

@Composable
fun TextStyle.colorTxtParagraph() = this.copy(LocalAppColor.current.txtParagraph)

@Composable
fun TextStyle.colorTxtTitle() = this.copy(LocalAppColor.current.txtTitle)

@Composable
fun TextStyle.colorTxtLabel() = this.copy(LocalAppColor.current.txtLabel)

@Composable
fun TextStyle.colorTxtCaution() = this.copy(LocalAppColor.current.txtCaution)

@Composable
fun TextStyle.colorTxtPositive() = this.copy(LocalAppColor.current.txtPositive)

@Composable
fun TextStyle.colorTxtNegative() = this.copy(LocalAppColor.current.txtNegative)

@Composable
fun TextStyle.colorTxtInfo() = this.copy(LocalAppColor.current.txtInfo)

@Composable
fun TextStyle.colorTxtDisabled() = this.copy(LocalAppColor.current.txtDisabled)

@Composable
fun TextStyle.colorTxtInactive() = this.copy(LocalAppColor.current.txtInactive)


@Composable
fun TextStyle.colorTxtInverted() = this.copy(LocalAppColor.current.txtInverted)


@Composable
fun TextStyle.underline() = this.copy(textDecoration = TextDecoration.Underline)


// Helper function to convert TextStyle to SpanStyle
@Composable
fun TextStyle.toSpanStyle(): SpanStyle {
    return SpanStyle(
        color = this.color,
        fontSize = this.fontSize,
        fontWeight = this.fontWeight ?: FontWeight.Normal,
        fontStyle = this.fontStyle ?: FontStyle.Normal,
        letterSpacing = this.letterSpacing,
        background = this.background,
        textDecoration = this.textDecoration,
        shadow = this.shadow
    )
}


@Composable
fun getToolbarStepLabel(stepNo : String,stepLabel : String): AnnotatedString {
    return buildAttrString(attributedString = listOf(
        AttributeStringData(stepNo, textStyle = LocalTypography.current.text14.semiBold.colorTxtTitle(),),
        AttributeStringData(stepLabel, textStyle = LocalTypography.current.text14.regular.colorTxtParagraph(),),),)
}


data class AttributeStringData(
    val text: String,
    val textStyle: TextStyle = TextStyle(),
)

fun buildAttrString(attributedString: List<AttributeStringData>): AnnotatedString {
   return buildAnnotatedString {
        attributedString.forEach { data ->
            withStyle(
                style = data.textStyle
                    .toSpanStyle(),
            ) {
                append(data.text)
            }
        }
    }
}