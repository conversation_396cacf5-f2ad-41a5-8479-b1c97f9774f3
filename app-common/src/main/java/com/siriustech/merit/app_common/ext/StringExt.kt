package com.siriustech.merit.app_common.ext

import android.content.Context
import android.content.pm.PackageInfo
import android.content.pm.PackageManager
import android.net.Uri
import android.os.Build
import android.webkit.MimeTypeMap
import com.core.util.toAmount
import com.siriustech.merit.app_common.R

/**
 * Created by <PERSON><PERSON>t
 */


fun String.isPriceUp(): Boolean {
    return !this.contains("-") && (this.toDoubleOrNull() ?: 0.0) > 0.0
}

fun String.isPriceDown(): Boolean {
    return this.contains("-")
}

fun String.isPriceZero() : Boolean {
    return this.toDoubleOrNull() == 0.0
}

fun String.displayPriceChange(): String {
    return if (this.contains("-")) this else "+$this"
}

fun String.displayPriceChangeRate(): String {
    return if (this.contains("-")) this.plus("%") else "+$this%"
}
fun String.capitalizeWords(): String =
    this.lowercase().split(" ").joinToString(" ") { it.replaceFirstChar { char -> char.uppercase() } }

fun String.cleanDigits() : Float{
    return this.replace(",","").replace("%","").toFloatOrNull() ?: 0f
}

fun String?.formatVolume(): String {
    if(this.isNullOrEmpty()) return ""
    val volume = this.toAmount(0)?.replace(",", "")?.toFloatOrNull() ?: 0f
    return when {
        volume >= 1_000_000_000 -> String.format(
            "%.2fB", (volume) / 1_000_000_000.0
        ) // Format billions
        volume >= 1_000_000 -> String.format("%.2fM", volume / 1_000_000.0) // Format millions
        volume >= 1_000 -> String.format("%.2fK", volume / 1_000.0) // Format thousands
        else -> volume.toString() // No formatting needed if less than 1,000
    }
}
fun String.formatMarketCap(context: Context?): String {
    val value = this.toFloatOrNull() ?: 0f
    return when {
        value >= 1_000_000_000_000 -> String.format("%.1f ${context?.getString(R.string.key0986) ?: "Trillion"}", value / 1_000_000_000_000.0)
        value >= 1_000_000_000 -> String.format("%.1f ${context?.getString(R.string.key0985) ?: "Billion"}", value / 1_000_000_000.0)
        value >= 1_000_000 -> String.format("%.1f ${context?.getString(R.string.key0984) ?: "Million"}", value / 1_000_000.0)
        value >= 1_000 -> String.format("%.1f ${context?.getString(R.string.key1002) ?: "Thousand"}", value / 1_000.0)
        else -> value.toString()
    }
}

fun String.displayEmailAddress(): String {
    return try {
        val values = this.split("@")
        val part1 = values[0]
        val part2 = values[1]

        val starCount = part1.drop(2).count()
        var label = ""
        for (i in 0 until starCount) {
            label = label.plus("*")
        }
        val formatEmailAddress = part1.replaceRange(1, part1.length - 1, label)
        formatEmailAddress.plus("@$part2")
    } catch (e: Exception) {
        this
    }
}


fun Context.displayAppVersioning(): String {
    try {
        val packageInfo: PackageInfo = packageManager.getPackageInfo(packageName, 0)
        val versionName = packageInfo.versionName
        val versionCode = if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.P) {
            packageInfo.longVersionCode
        } else {
            packageInfo.versionCode
        } // Use `longVersionCode` for better compatibility
        return "V:${versionName} ($versionCode)"
    } catch (e: PackageManager.NameNotFoundException) {
        e.printStackTrace()
        return ""
    }
}

fun guessMimeTypeFromExtension(uri: Uri): String? {
    val extension = MimeTypeMap.getFileExtensionFromUrl(uri.toString())
    return MimeTypeMap.getSingleton().getMimeTypeFromExtension(extension)
}