package com.siriustech.merit.app_common.component.common

import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.RowScope
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import com.siriustech.merit.app_common.ext.colorTxtInactive
import com.siriustech.merit.app_common.ext.colorTxtTitle
import com.siriustech.merit.app_common.theme.LocalAppColor
import com.siriustech.merit.app_common.theme.LocalDimens
import com.siriustech.merit.app_common.theme.LocalTypography
import com.siriustech.merit.app_common.typeenum.ChartTimeFrame

/**
 * Created by Hein Htet
 */


@Composable
fun TimeFrameSelector(
    modifier: Modifier = Modifier,
    properties: TimeFrameSelectorProperties = TimeFrameSelectorProperties(),
    onTimeFrameSelect: (chartTimeFrame: ChartTimeFrame) -> Unit = {},
) {

    var selectedTimeFrame by remember {
        mutableStateOf(properties.selectedTimeFrame)
    }
    
    Row(modifier) {
        properties.items.forEachIndexed { index, chartTimeFrame ->
            TimeFrame(
                modifier = Modifier.clickable {
                    selectedTimeFrame = chartTimeFrame
                    onTimeFrameSelect(chartTimeFrame)
                },
                showStartLine = index != 0,
                showEndLine = index != properties.items.count() - 1,
                isSelected = chartTimeFrame == selectedTimeFrame,
                chartTimeFrame = chartTimeFrame,
            )
        }
    }
}

@Composable
fun RowScope.TimeFrame(
    modifier: Modifier = Modifier,
    isSelected: Boolean = true,
    showStartLine: Boolean = true,
    showEndLine: Boolean = true,
    chartTimeFrame: ChartTimeFrame,
) {
    Column(
        modifier = Modifier
            .weight(1f)
            .width(50.dp)
            .padding(top = LocalDimens.current.dimen4)
            .then(modifier),
        verticalArrangement = Arrangement.Center,
        horizontalAlignment = Alignment.CenterHorizontally
    ) {
        Box(
            modifier = Modifier
                .clip(RoundedCornerShape(16.dp))
                .background(if(isSelected) LocalAppColor.current.bgAccent else LocalAppColor.current.bgDefault)
        , contentAlignment = Alignment.Center) {
            Text(
                text = chartTimeFrame.getDisplayName(),
                style = if(isSelected) LocalTypography.current.text14.medium.colorTxtTitle() else LocalTypography.current.text14.light.colorTxtInactive(),
                maxLines = 1,
                modifier = Modifier.padding(horizontal = 6.dp, vertical = 1.dp)
            )
        }
        Row(
            Modifier
                .fillMaxWidth()
                .padding(vertical = LocalDimens.current.dimen4),
            verticalAlignment = Alignment.CenterVertically
        ) {
            Line(color = if (!showStartLine) LocalAppColor.current.bgDefault else LocalAppColor.current.bgAccent)
            Box(
                modifier = Modifier
                    .size(LocalDimens.current.dimen16)
                    .clip(RoundedCornerShape(50))
                    .background(LocalAppColor.current.bgAccent)
                ,
                contentAlignment = Alignment.Center
            ) {
                if (isSelected) {
                    Box(
                        modifier = Modifier
                            .fillMaxSize()
                            .padding(LocalDimens.current.dimen4)
                            .clip(RoundedCornerShape(50))
                            .background(LocalAppColor.current.txtTitle)
                    )
                }
            }
            Line(color = if (!showEndLine) LocalAppColor.current.bgDefault else LocalAppColor.current.bgAccent)
        }
    }
}

@Composable
fun RowScope.Line(
    modifier: Modifier = Modifier,
    color: androidx.compose.ui.graphics.Color = LocalAppColor.current.bgAccent,
) {
    Box(
        modifier = Modifier
            .weight(1f)
            .height(LocalDimens.current.dimen4)
            .background(color)
            .then(modifier)
    )
}

data class TimeFrameSelectorProperties(
    val items: List<ChartTimeFrame> = emptyList(),
    val selectedTimeFrame: ChartTimeFrame = ChartTimeFrame.ONE_WEEK,
)

@Preview(showBackground = true)
@Composable
fun PreviewTimeFrameSelector() = TimeFrameSelector(
    properties = TimeFrameSelectorProperties(
        items = listOf(
            ChartTimeFrame.ONE_WEEK,
            ChartTimeFrame.ONE_MONTH,
            ChartTimeFrame.ONE_YEAR,
            ChartTimeFrame.THREE_YEAR,
            ChartTimeFrame.FIVE_YEAR,
            ChartTimeFrame.ALL_TIME,
        )
    )
)