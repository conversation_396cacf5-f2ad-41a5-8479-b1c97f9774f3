package com.siriustech.merit.app_common.data

import com.siriustech.merit.apilayer.service.onboarding.config.CommonConfigResponse
import com.siriustech.merit.app_common.Constants
import com.siriustech.merit.app_common.component.modalbts.ModalListDataContent
import com.siriustech.merit.app_common.data.display.AssetClassModel
import com.siriustech.merit.app_common.data.display.HistoryFilterModel
import com.siriustech.merit.app_common.data.display.MarketCategoryFilterModel
import com.siriustech.merit.app_common.data.display.UserBasicInfoDisplay
import com.siriustech.merit.app_common.typeenum.HistoryFilterType
import com.siriustech.merit.app_common.typeenum.HomeMarketListType
import com.siriustech.merit.app_common.typeenum.HomeShortcutMenuAction
import com.siriustech.merit.app_common.typeenum.MarketCategoryType
import com.siriustech.merit.app_common.typeenum.PortfolioAssetType
import javax.inject.Inject
import javax.inject.Named

/**
 * Created by <PERSON><PERSON>
 */

class AppCache @Inject constructor(
    @Named("BaseUrl") val baseUrl: String,
) {
    private var _enumerationItems = ArrayList<ModalListDataContent>()
    private var _hasFullAccess = true
    private var _userBasicInfoDisplay: UserBasicInfoDisplay? = null
    private var _unreadNotificationCount = 0
    private var _historyFilterTypeCache = mutableMapOf<HistoryFilterType, HistoryFilterModel>(
        HistoryFilterType.TRADE_HISTORY to HistoryFilterModel(type = HistoryFilterType.TRADE_HISTORY),
        HistoryFilterType.DEPOSIT_HISTORY to HistoryFilterModel(type = HistoryFilterType.DEPOSIT_HISTORY),
        HistoryFilterType.WITHDRAWAL_HISTORY to HistoryFilterModel(type = HistoryFilterType.WITHDRAWAL_HISTORY),
    )

    private var _marketFilterTypeCache = mutableMapOf(
        MarketCategoryType.CASH_EQUIVALENT.value to MarketCategoryFilterModel()
    )


    private var _homeMarketListType = HomeMarketListType.GAINER
    private var _homePageSelectedAllocationClass = PortfolioAssetType.CASH_EQUIVALENT
    private var _marketSelectedAllocationClass: String = ""
    private var _marketSelectedAllocationClassTitle = ""
    private var _homeShortcutMenuAction: HomeShortcutMenuAction? = null
    private var _allocationClass = ArrayList<AssetClassModel>()
    private var _commonConfig = CommonConfigResponse(
        supportEmail = Constants.SUPPORT_EMAIL,
        supportMobile = Constants.SUPPORT_MOBILE,
    )

    fun updateSelectedMarketAllocationClass(value: String, title: String) {
        _marketSelectedAllocationClass = value
        _marketSelectedAllocationClassTitle = title
    }

    fun updateEnumerationItems(items: List<ModalListDataContent>) {
        _enumerationItems.clear()
        _enumerationItems.addAll(items)
    }


    fun getEnumerationItemsByCode(id: String): List<ModalListDataContent> {
        return _enumerationItems.findLast { it.id == id }?.items.orEmpty()
    }

    fun updateFullAccess(access: Boolean) {
        _hasFullAccess = access
    }

    fun updateUserBasicInfo(infoResponse: UserBasicInfoDisplay) {
        _userBasicInfoDisplay = infoResponse
    }

    fun updateUnReadNotificationCount(count: Int) {
        _unreadNotificationCount = count
    }

    fun clearNotificationCount() {
        _unreadNotificationCount = 0
    }

    fun updateHistoryFilterMap(map: MutableMap<HistoryFilterType, HistoryFilterModel>) {
        _historyFilterTypeCache = map
    }

    fun updateMarketFilterMap(map: MutableMap<String, MarketCategoryFilterModel>) {
        _marketFilterTypeCache = map
    }

    fun updateHomePageSelectedAllocationClass(type: PortfolioAssetType) {
        _homePageSelectedAllocationClass = type
    }

    fun updateHomeMarketListType(type: HomeMarketListType) {
        _homeMarketListType = type
    }

    fun updateMenuCache(menuAction: HomeShortcutMenuAction?) {
        _homeShortcutMenuAction = menuAction
    }

    fun updateAssetAllocationClass(allocationClass: List<AssetClassModel>) {
        _allocationClass.clear()
        _allocationClass.addAll(allocationClass)
    }

    fun updateCommonConfig(commonConfig: CommonConfigResponse) {
        _commonConfig = commonConfig
    }

    val enumerationItems: List<ModalListDataContent>
        get() = _enumerationItems

    val hasFullAccess: Boolean
        get() = _hasFullAccess

    val userBasicInfoDisplay: UserBasicInfoDisplay?
        get() = _userBasicInfoDisplay

    val unreadNotificationCount: Int
        get() = _unreadNotificationCount

    val historyFilterTypeCache: MutableMap<HistoryFilterType, HistoryFilterModel>
        get() = _historyFilterTypeCache

    val marketFilterTypeCache: MutableMap<String, MarketCategoryFilterModel>
        get() = _marketFilterTypeCache

    val homePageSelectedAllocationClass: PortfolioAssetType
        get() = _homePageSelectedAllocationClass

    val homeMarketListType: HomeMarketListType
        get() = _homeMarketListType

    val marketSelectedAllocationClass: String
        get() = _marketSelectedAllocationClass

    val marketSelectedAllocationClassTitle: String
        get() = _marketSelectedAllocationClassTitle

    val homeShortcutMenuAction: HomeShortcutMenuAction?
        get() = _homeShortcutMenuAction

    val allocationClass: List<AssetClassModel>
        get() = _allocationClass

    val commonConfig: CommonConfigResponse
        get() = _commonConfig

}