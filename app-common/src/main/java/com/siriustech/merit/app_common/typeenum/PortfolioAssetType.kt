package com.siriustech.merit.app_common.typeenum

import androidx.compose.runtime.Composable
import androidx.compose.runtime.ReadOnlyComposable
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.res.stringResource
import com.siriustech.merit.app_common.R
import com.siriustech.merit.app_common.component.text.BadgeText
import com.siriustech.merit.app_common.theme.LocalAppColor


/**
 * Created by <PERSON><PERSON>t
 */


enum class PortfolioAssetType(val assetName: String) {
    CASH_EQUIVALENT("CASH_EQUIVALENT"),
    FIX_INCOME("FIX_INCOME"),
    FIX_INCOME_2("FIX_INCOME_2"),
    FIX_INCOME_3("FIX_INCOME_3"),
    STRUCTURED_PRODUCTS("STRUCTURED_PRODUCTS"),
    PRIVATE_CREDIT("PRIVATE_CREDIT"),
    EQUITY("EQUITY"),
    VIRTUAL_ASSET("VIRTUAL_ASSET"),
    MULTI_ASSET("MULTI_ASSET"),
    COMMODITIES("COMMODITIES"),
    PRIVATE_CAPITAL("PRIVATE_CAPITAL"),
    PRIVATE_EQUITY("PRIVATE_EQUITY"),
    UNKNOWN_TYPE("UNKNOWN");


    val color: Color
        @Composable
        @ReadOnlyComposable
        get() = when (this) {
            CASH_EQUIVALENT -> LocalAppColor.current.chartEmeraldGreen
            FIX_INCOME, FIX_INCOME_2, FIX_INCOME_3 -> LocalAppColor.current.chartPurple
            STRUCTURED_PRODUCTS -> LocalAppColor.current.chartOrange
            PRIVATE_EQUITY -> LocalAppColor.current.chartBlue
            PRIVATE_CREDIT -> LocalAppColor.current.chartRed
            COMMODITIES -> LocalAppColor.current.chartGreen
            EQUITY -> LocalAppColor.current.chartYellow
            VIRTUAL_ASSET -> LocalAppColor.current.chartRed
            MULTI_ASSET -> LocalAppColor.current.chartPink
            PRIVATE_CAPITAL -> LocalAppColor.current.chartRed
            UNKNOWN_TYPE -> LocalAppColor.current.bgDisabled
        }

    val displayName: String
        @Composable
        @ReadOnlyComposable
        get() = when (this) {
            CASH_EQUIVALENT -> stringResource(id = R.string.key0402)
            FIX_INCOME, FIX_INCOME_2, FIX_INCOME_3 -> stringResource(id = R.string.key0403)
            STRUCTURED_PRODUCTS -> stringResource(id = R.string.key0404)
            PRIVATE_EQUITY -> stringResource(id = R.string.key0451)
            PRIVATE_CREDIT -> stringResource(id = R.string.key0422)
            VIRTUAL_ASSET -> stringResource(id = R.string.key0406)
            COMMODITIES -> stringResource(id = R.string.key0407)
            EQUITY -> stringResource(id = R.string.key0408)
            MULTI_ASSET -> stringResource(id = R.string.key0409)
            PRIVATE_CAPITAL -> stringResource(id = R.string.key0405)
            else -> this.assetName
        }

    val arrDisplayName: String
        @Composable
        @ReadOnlyComposable
        get() = when (this) {
            CASH_EQUIVALENT -> "ARR1"
            FIX_INCOME, FIX_INCOME_2, FIX_INCOME_3 -> "AAR2"
            STRUCTURED_PRODUCTS -> "ARR4"
            PRIVATE_EQUITY -> "ARR9"
            PRIVATE_CREDIT -> "ARR10"
            VIRTUAL_ASSET -> "ARR10"
            COMMODITIES -> "ARR6"
            EQUITY -> "ARR8"
            MULTI_ASSET -> "ARR5"
            PRIVATE_CAPITAL -> "ARR5"
            UNKNOWN_TYPE -> "-"
        }

    val arrType: PortfolioAssetARRType
        @Composable
        @ReadOnlyComposable
        get() = when (this) {
            CASH_EQUIVALENT -> PortfolioAssetARRType.ARR1
            FIX_INCOME, FIX_INCOME_2, FIX_INCOME_3 -> PortfolioAssetARRType.ARR2
            STRUCTURED_PRODUCTS -> PortfolioAssetARRType.ARR4
            PRIVATE_CREDIT -> PortfolioAssetARRType.ARR5
            COMMODITIES -> PortfolioAssetARRType.ARR6
            MULTI_ASSET -> PortfolioAssetARRType.ARR7
            EQUITY -> PortfolioAssetARRType.ARR8
            PRIVATE_EQUITY -> PortfolioAssetARRType.ARR9
            VIRTUAL_ASSET -> PortfolioAssetARRType.ARR10
            PRIVATE_CAPITAL -> PortfolioAssetARRType.ARR10
            UNKNOWN_TYPE -> PortfolioAssetARRType.ARR1
        }


    @Composable
    fun getBadgeLabel(): String {
        var label = ""
        when (this) {
            CASH_EQUIVALENT -> {
                label = "ARR1"
            }

            FIX_INCOME, FIX_INCOME_2, FIX_INCOME_3 -> {
                label = "ARR2"
            }

            STRUCTURED_PRODUCTS -> {
                label = "ARR4"
            }

            PRIVATE_EQUITY -> {
                label = "ARR9"
            }

            PRIVATE_CREDIT, VIRTUAL_ASSET -> {
                label = "ARR10"
            }

            COMMODITIES -> {
                label = "ARR6"
            }

            EQUITY -> {
                label = "ARR8"
            }

            MULTI_ASSET -> {
                label = "ARR5"
            }

            PRIVATE_CAPITAL -> {
                label = "ARR10"
            }

            UNKNOWN_TYPE -> {
                label = "-"
            }
        }
        return label
    }

//    @Composable
//    fun BadgeLabel() {
//        var label = ""
//        var textColor = LocalAppColor.current.bgDefault
//        var bgColor = LocalAppColor.current.bgDefault
//        when (this) {
//            CASH_EQUIVALENT -> {
//                label = "ARR1"
//                textColor = Color(51, 122, 72, 255)
//                bgColor = Color(235, 249, 231, 255)
//            }
//
//            FIX_INCOME -> {
//                label = "ARR2"
//                textColor = Color(51, 122, 72, 255)
//                bgColor = Color(235, 249, 231, 255)
//            }
//
//            STRUCTURED_PRODUCTS -> {
//                label = "ARR4"
//                textColor = Color(47, 63, 144, 255)
//                bgColor = Color(240, 242, 255, 255)
//            }
//
//            PRIVATE_EQUITY -> {
//                label = "ARR9"
//                textColor = Color(82, 34, 131, 255)
//                bgColor = Color(247, 234, 253, 255)
//            }
//
//            PRIVATE_CREDIT,VIRTUAL_ASSET -> {
//                label = "ARR10"
//                textColor = Color(82, 34, 131, 255)
//                bgColor = Color(247, 234, 253, 255)
//            }
//
//            COMMODITIES -> {
//                label = "ARR6"
//                textColor = Color(47, 63, 144, 255)
//                bgColor = Color(240, 242, 255, 255)
//            }
//
//            EQUITY -> {
//                label = "ARR8"
//                textColor = Color(82, 34, 131, 255)
//                bgColor = Color(247, 234, 253, 255)
//            }
//
//            MULTI_ASSET -> {
//                label = "ARR5"
//                textColor = LocalAppColor.current.txtNegative
//                bgColor = Color(255, 245, 245, 255)
//            }
//        }
//        BadgeText(label = label, bgColor = bgColor, textColor = textColor)
//    }

    companion object {

        fun fromParam(name: String): PortfolioAssetType? {
            return when (name) {
                CASH_EQUIVALENT.assetName -> CASH_EQUIVALENT
                FIX_INCOME.assetName -> FIX_INCOME
                STRUCTURED_PRODUCTS.assetName -> STRUCTURED_PRODUCTS
                PRIVATE_EQUITY.assetName -> PRIVATE_EQUITY
                PRIVATE_CAPITAL.assetName -> PRIVATE_CAPITAL
                VIRTUAL_ASSET.assetName -> VIRTUAL_ASSET
                COMMODITIES.assetName -> COMMODITIES
                PRIVATE_CREDIT.assetName -> PRIVATE_CREDIT
                EQUITY.assetName -> EQUITY
                MULTI_ASSET.assetName -> MULTI_ASSET
                else -> null
            }
        }
    }
}


//CASH_EQUIVALENT FIX_INCOME STRUCTURED_PRODUCTS PRIVATE_CREDIT MULTI-ASSET EQUITY PRIVATE_EQUITY

