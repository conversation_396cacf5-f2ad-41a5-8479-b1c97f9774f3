package com.siriustech.merit.app_common.component.modalbts

import androidx.compose.foundation.Image
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxHeight
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.ModalBottomSheet
import androidx.compose.material3.SheetState
import androidx.compose.material3.Text
import androidx.compose.material3.rememberModalBottomSheetState
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.painter.Painter
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.tooling.preview.Preview
import com.siriustech.merit.app_common.R
import com.siriustech.merit.app_common.component.button.ButtonProperties
import com.siriustech.merit.app_common.component.button.SecondaryBorderButton
import com.siriustech.merit.app_common.component.button.SecondaryButton
import com.siriustech.merit.app_common.component.textfield.InputBox
import com.siriustech.merit.app_common.component.textfield.InputProperties
import com.siriustech.merit.app_common.ext.colorTxtParagraph
import com.siriustech.merit.app_common.ext.colorTxtTitle
import com.siriustech.merit.app_common.ext.noRippleClickable
import com.siriustech.merit.app_common.theme.LocalAppColor
import com.siriustech.merit.app_common.theme.LocalDimens
import com.siriustech.merit.app_common.theme.LocalTypography
import kotlinx.coroutines.launch
import kotlinx.serialization.Serializable

/**
 * Created by Hein Htet
 */

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun BenchMarkListModalBts(
    selectedGainLossBenchmarkItems: List<BenchMarkDataContent> = emptyList(),
    properties: BenchMarkListModalBtsProperties,
    sheetState: SheetState = rememberModalBottomSheetState(skipPartiallyExpanded = true),
    onItemClicked: (item: BenchMarkDataContent) -> Unit = {},
    onBenchMarkApply: (item: List<BenchMarkDataContent>) -> Unit = {},
    onDismissed: () -> Unit = {},
) {
    val scope = rememberCoroutineScope()

    fun onDismiss() {
        scope.launch {
            sheetState.hide()
            onDismissed()
        }
    }
    ModalBottomSheet(
        dragHandle = {},
        shape = RoundedCornerShape(LocalDimens.current.dimen4),
        containerColor = LocalAppColor.current.bgDefault,
        onDismissRequest = onDismissed, sheetState = sheetState,
        modifier = Modifier
    ) {
        BenchMarkListModalContent(
            selectedGainLossBenchmarkItems,
            properties = properties,
            onDismissed = {
                onDismiss()
            }, onItemClicked = { item ->
                onItemClicked(item)
            }, onApplyBenchMark = {
                onBenchMarkApply(it)
                onDismiss()
            })

    }
}

@Composable
fun BenchMarkListModalContent(
    selectedGainLossBenchmarkItems: List<BenchMarkDataContent> = emptyList(),
    properties: BenchMarkListModalBtsProperties,
    onItemClicked: (item: BenchMarkDataContent) -> Unit = {},
    onApplyBenchMark: (item: List<BenchMarkDataContent>) -> Unit = {},
    onDismissed: () -> Unit = {},
) {
    var items by remember {
        properties.items.map {
            it.isSelected =
                selectedGainLossBenchmarkItems.findLast { selectedItem -> it.id == selectedItem.id }?.isSelected
                    ?: false
            it
        }
        mutableStateOf(
            properties.items
        )
    }

    val originalItems by remember {
        mutableStateOf(properties.items)
    }

    var searchKeyword by remember {
        mutableStateOf("")
    }

    LaunchedEffect(searchKeyword) {
        items = if (searchKeyword.isEmpty()) {
            originalItems
        } else {
            originalItems.filter { it.title.contains(searchKeyword, ignoreCase = true) }
        }
    }

    Column(
        modifier = Modifier
            .fillMaxHeight(0.6f)
            .fillMaxWidth()
            .padding(
                horizontal = LocalDimens.current.dimen14,
                vertical = LocalDimens.current.dimen16
            )
    ) {
        BenchMarkListTitle(properties, onDismissed = onDismissed)
        Spacer(modifier = Modifier.height(LocalDimens.current.dimen16))
        InputBox(
            onTextChange = {
                searchKeyword = it
            },
            properties =
            InputProperties(
                inputPrefixIcon = painterResource(id = R.drawable.ic_search),
                placeholder = properties.searchPlaceholder,
            ),
        )
        Spacer(modifier = Modifier.height(LocalDimens.current.dimen16))
        LazyColumn {
            items(items, key = { it.id }) {
                BenchMarkListItemContent(modifier = Modifier.animateItem(
                    fadeInSpec = null,
                    fadeOutSpec = null
                ), it, onClicked = { content ->
                    items = items.mapIndexed { i, it ->
                        if (it.id == content.id) it.copy(isSelected = !it.isSelected) else it.copy(
                            isSelected = it.isSelected
                        )
                    }
                    onItemClicked(content)
                }, onUnSelected = { item ->
                    items = items.mapIndexed { i, it ->
                        if (it.id == item.id) it.copy(isSelected = false) else it.copy(
                            isSelected = it.isSelected
                        )
                    }
                })
            }
        }
        Spacer(modifier = Modifier.weight(1f))
        Row(
            modifier = Modifier.padding(top = LocalDimens.current.dimen16)
        ) {
            SecondaryBorderButton(
                modifier = Modifier.weight(1f),
                properties = ButtonProperties(text = stringResource(id = R.string.key1011)),
                onClicked = {
                    onDismissed()
                }
            )
            Spacer(modifier = Modifier.width(LocalDimens.current.dimen8))
            SecondaryButton(
                modifier = Modifier.weight(1f),
                properties = ButtonProperties(text = stringResource(id = R.string.key0411),
                    enabled = items.any { it.isSelected } || selectedGainLossBenchmarkItems.isNotEmpty()),
                onClicked = {
                    onApplyBenchMark(items.filter { it.isSelected })
                }
            )
        }
    }
}


@Composable
fun BenchMarkListTitle(properties: BenchMarkListModalBtsProperties, onDismissed: () -> Unit = {}) {
    Row(
        modifier = Modifier.fillMaxWidth(),
        verticalAlignment = Alignment.CenterVertically
    ) {
        Spacer(modifier = Modifier.width(LocalDimens.current.dimen4))
        if (properties.prefixTitleIcon != null) {
            Image(
                painter = properties.prefixTitleIcon,
                contentDescription = "Prefix Title Icon"
            )
            Spacer(modifier = Modifier.width(LocalDimens.current.dimen4))
        }
        Text(
            text = properties.prefixTitle,
            style = LocalTypography.current.text14.semiBold.colorTxtTitle()
        )
        Text(
            text = properties.title,
            style = LocalTypography.current.text14.regular.colorTxtParagraph()
        )
        Spacer(modifier = Modifier.weight(1f))
        Image(
            painter = painterResource(id = R.drawable.ic_action_close),
            contentDescription = "Close Image Resource",
            modifier = Modifier.noRippleClickable { onDismissed() }
        )
    }
}

@Composable
fun BenchMarkListItemContent(
    modifier: Modifier = Modifier,
    item: BenchMarkDataContent,
    onClicked: (item: BenchMarkDataContent) -> Unit = {},
    onUnSelected: (item: BenchMarkDataContent) -> Unit = {},
) {

    Box(
        modifier = modifier
            .fillMaxWidth()
            .clickable { onClicked(item) }
            .padding(
                vertical = LocalDimens.current.dimen12,
                horizontal = LocalDimens.current.dimen12
            )
    ) {
        Row() {
            Text(
                text = item.title,
                style = LocalTypography.current.text14.medium.colorTxtTitle()
            )
            Spacer(modifier = Modifier.width(LocalDimens.current.dimen8))
            Text(
                modifier = Modifier.weight(1f),
                text = item.description,
                style = if (item.isSelected) LocalTypography.current.text14.medium.colorTxtTitle() else LocalTypography.current.text14.light.colorTxtParagraph()
            )
            if (item.isSelected) {
                Image(
                    modifier = Modifier.clickable { onUnSelected(item) },
                    painter = painterResource(id = R.drawable.ic_clear_circle_fill),
                    contentDescription = "Clear Benchmark Image Resource"
                )
            }
        }

    }
}

data class BenchMarkListModalBtsProperties(
    val prefixTitleIcon: Painter? = null,
    val prefixTitle: String = "",
    val title: String = "",
    val iconTitle: Painter? = null,
    val description: String = "",
    val buttonText: String = "",
    val items: List<BenchMarkDataContent> = emptyList(),
    val searchEnable: Boolean = false,
    val searchPlaceholder: String = "",
)


@Serializable
data class BenchMarkDataContent(
    val id: String = "",
    val title: String = "",
    val value: String = "",
    var isSelected: Boolean = false,
    var description: String = "",
): java.io.Serializable


@Preview(showBackground = true, showSystemUi = true)
@Composable
fun PreviewBranchMarkModelListBottomSheet() {
    BenchMarkListModalContent(
        properties = BenchMarkListModalBtsProperties(
            prefixTitleIcon = painterResource(id = R.drawable.ic_chart_time_frame),
            prefixTitle = "Action: ",
            title = "Select Benchmark",
            searchPlaceholder = "Search benchmark",
            items = listOf(
                BenchMarkDataContent(
                    id = "1",
                    title = "DJCI",
                    value = "DJCI",
                    description = "Dow Jones Commodity Index",
                    isSelected = false
                ),
                BenchMarkDataContent(
                    id = "2",
                    title = "United State",
                    value = "US"
                ),
            )
        )
    )
}
