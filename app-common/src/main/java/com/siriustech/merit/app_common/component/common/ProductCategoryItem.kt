package com.siriustech.merit.app_common.component.common

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.sp
import coil3.compose.AsyncImage
import coil3.request.ImageRequest
import coil3.request.crossfade
import com.core.util.toAmount
import com.siriustech.merit.app_common.R
import com.siriustech.merit.app_common.component.text.AutoSizeText
import com.siriustech.merit.app_common.component.text.BadgeText
import com.siriustech.merit.app_common.data.display.ProductCategoryDisplayData
import com.siriustech.merit.app_common.ext.colorTxtParagraph
import com.siriustech.merit.app_common.ext.colorTxtTitle
import com.siriustech.merit.app_common.ext.displayPriceChangeRate
import com.siriustech.merit.app_common.ext.noRippleClickable
import com.siriustech.merit.app_common.theme.LocalAppColor
import com.siriustech.merit.app_common.theme.LocalDimens
import com.siriustech.merit.app_common.theme.LocalTypography
import com.siriustech.merit.app_common.typeenum.RiskLevel
import timber.log.Timber

/**
 * Created by Hein Htet
 */

@Composable
fun ProductCategoryItem(
    modifier: Modifier = Modifier,
    data: ProductCategoryDisplayData = ProductCategoryDisplayData(),
    onItemClicked: (item: ProductCategoryDisplayData) -> Unit = {},
) {
    val priceChangeUIModel = onGetPriceChangeResource(priceChange = data.unrealizedGlRate)

    if (data.isSection) {
        Text(
            data.categoryName.orEmpty(),
            style = LocalTypography.current.text14.regular.colorTxtTitle(),
            modifier = Modifier.padding(vertical = LocalDimens.current.dimen8)
        )
    } else {
        Row(
            verticalAlignment = Alignment.CenterVertically,
            modifier = Modifier
                .fillMaxWidth()
                .padding(vertical = LocalDimens.current.dimen12)
                .noRippleClickable { onItemClicked(data) }
                .then(modifier)
        ) {
            Row(modifier = Modifier.weight(1f), verticalAlignment = Alignment.CenterVertically) {
                AsyncImage(
                    model = ImageRequest.Builder(LocalContext.current)
                        .data(data.logo)
                        .crossfade(true)
                        .build(),
                    placeholder = painterResource(R.drawable.ic_product_category_placeholder),
                    error = painterResource(R.drawable.ic_product_category_placeholder),
                    contentDescription = "Product Category Placeholder",
                    contentScale = ContentScale.Crop,
                    onError = {
                        Timber.d("Image Loading error ${it.result}")
                    },
                    modifier = Modifier.size(LocalDimens.current.dimen32)
                        .clip(RoundedCornerShape(50))
                )
                Spacer(modifier = Modifier.width(LocalDimens.current.dimen8))
                Column {
                    Text(
                        text = data.name.uppercase(),
                        maxLines = 1,
                        overflow = TextOverflow.Ellipsis,
                        style = LocalTypography.current.text14.semiBold.colorTxtTitle()
                    )
                    Row {
                        BadgeText(
                            label = data.exchange,
                            bgColor = LocalAppColor.current.bgAccent,
                            textColor = LocalAppColor.current.txtLabel
                        )
                        Spacer(modifier = Modifier.width(LocalDimens.current.dimen4))
                        data.riskLevel?.RiskLevelBadge()
                    }
                }
            }
            Column(
                modifier = Modifier
                    .weight(0.7f),
                verticalArrangement = Arrangement.Top,
                horizontalAlignment = Alignment.End

            ) {
                Row(
                    verticalAlignment = Alignment.CenterVertically,
                    horizontalArrangement = Arrangement.Start
                ) {
                    Text(
                        maxLines = 1,
                        text = data.marketValue.toAmount(4) ?: "0.0000",
                        style = LocalTypography.current.text14.semiBold.copy(color = priceChangeUIModel.textColor),
                    )
                    Text(
                        text = data.currency,
                        modifier = Modifier.padding(start = LocalDimens.current.dimen1),
                        style = LocalTypography.current.text12.semiBold.copy(color = priceChangeUIModel.textColor),
                    )
                }
                Text(
                    text = data.unit,
                    style = LocalTypography.current.text12.medium.colorTxtParagraph(),
                    textAlign = TextAlign.End
                )
            }

            Column(
                modifier = Modifier
                    .weight(0.5f),
                verticalArrangement = Arrangement.Top,
                horizontalAlignment = Alignment.End

            ) {
                Row(
                    verticalAlignment = Alignment.CenterVertically,
                    horizontalArrangement = Arrangement.Start
                ) {
                    AutoSizeText(
                        style = LocalTypography.current.text14.semiBold.copy(color = priceChangeUIModel.textColor),
                        maxFontSize = 14.sp,
                        minFontSize = 8.sp,
                        text = data.unrealizedGl
                    )
                }
                BadgeText(
                    label = data.unrealizedGlRate.displayPriceChangeRate(),
                    bgColor = priceChangeUIModel.priceChangeBgColor,
                    textColor = priceChangeUIModel.textColor,
                )
            }
        }
    }
}

@Preview(showBackground = false, showSystemUi = false)
@Composable
fun PreviewProductCategoryItem() {
    ProductCategoryItem(
        data = ProductCategoryDisplayData(
            name = "TENCENTTENCENTTENCENT 01",
            riskLevel = RiskLevel.HIGH,
            exchange = "HKEX",
            marketPrice = "377.60",
            currency = "HKD",
            unrealizedGl = "2192.99",
            unrealizedGlRate = "2192.0",
            logo = "https://api.twelvedata.com/logo/apple.com"
        ),
        modifier = Modifier
            .background(LocalAppColor.current.bgDefault)
            .padding(
                horizontal = LocalDimens.current.dimen12,
                vertical = LocalDimens.current.dimen8
            )
    )
}