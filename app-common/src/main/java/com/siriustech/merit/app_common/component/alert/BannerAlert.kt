package com.siriustech.merit.app_common.component.alert

import androidx.compose.foundation.BorderStroke
import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.defaultMinSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import com.siriustech.merit.app_common.R
import com.siriustech.merit.app_common.ext.colorTxtNegative
import com.siriustech.merit.app_common.ext.colorTxtParagraph
import com.siriustech.merit.app_common.ext.colorTxtTitle
import com.siriustech.merit.app_common.ext.noRippleClickable
import com.siriustech.merit.app_common.theme.LocalAppColor
import com.siriustech.merit.app_common.theme.LocalDimens
import com.siriustech.merit.app_common.theme.LocalTypography

/**
 * Created by Hein Htet
 */
@Composable
fun BannerAlert(properties: BannerAlertProperties = BannerAlertProperties(),onCloseButtonClicked : () -> Unit= {}) {
    Box(modifier = Modifier.padding(LocalDimens.current.dimen16)) {
        Box(
            modifier = Modifier
                .fillMaxWidth()
                .border(
                    BorderStroke(1.dp, LocalAppColor.current.bgDisabled),
                    shape = RoundedCornerShape(LocalDimens.current.dimen2)
                )
                .defaultMinSize(minHeight = LocalDimens.current.dimen60)
                .background(LocalAppColor.current.bgDefault)
                .padding(horizontal = LocalDimens.current.dimen16, vertical = LocalDimens.current.dimen8),
            contentAlignment = Alignment.Center
        ) {
            Row(modifier = Modifier.fillMaxWidth(),
                verticalAlignment = Alignment.CenterVertically) {
                Column(modifier = Modifier.weight(1f)) {
                    Text(text = properties.title, style = getTitleStyle(properties.type))
                    Text(
                        text = properties.description,
                        style = LocalTypography.current.text12.light.colorTxtParagraph()
                    )
                }
                Image(
                    painter = painterResource(id = R.drawable.ic_close),
                    contentDescription = "Close Icon",
                    modifier = Modifier.noRippleClickable { onCloseButtonClicked() }
                )
            }
        }
    }
}

@Composable
fun getTitleStyle(type: BannerAlertType): TextStyle {
    return when (type) {
        BannerAlertType.ALERT_ERROR -> LocalTypography.current.text14.medium.colorTxtNegative()
        BannerAlertType.ALERT_TOAST -> LocalTypography.current.text14.medium.colorTxtTitle()
    }
}

data class BannerAlertProperties(
    val title: String = "",
    val description: String = "",
    val type: BannerAlertType = BannerAlertType.ALERT_TOAST,
)

enum class BannerAlertType {
    ALERT_ERROR,
    ALERT_TOAST
}

@Preview(showBackground = true, showSystemUi = false)
@Composable
fun PreviewBannerAlertTypeToast() {
    BannerAlert(
        properties = BannerAlertProperties(
            title = "This is title",
            description = "This is descriptionThis is description"
        )
    )
}

@Preview(showBackground = true, showSystemUi = false)
@Composable
fun PreviewBannerAlertTypeError() {
    BannerAlert(
        properties = BannerAlertProperties(
            title = "This is title",
            description = "This is descriptionThis is description",
            type = BannerAlertType.ALERT_ERROR
        )
    )
}