package com.siriustech.merit.app_common.screen.chat.component

import androidx.compose.animation.AnimatedVisibility
import androidx.compose.animation.core.tween
import androidx.compose.animation.fadeIn
import androidx.compose.animation.fadeOut
import androidx.compose.foundation.Image
import androidx.compose.foundation.border
import androidx.compose.foundation.horizontalScroll
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.mutableStateListOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.runtime.snapshots.SnapshotStateList
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.platform.LocalConfiguration
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import com.siriustech.merit.app_common.R
import com.siriustech.merit.app_common.component.container.PaddingStart
import com.siriustech.merit.app_common.ext.colorTxtParagraph
import com.siriustech.merit.app_common.ext.colorTxtTitle
import com.siriustech.merit.app_common.ext.noRippleClickable
import com.siriustech.merit.app_common.theme.LocalDimens
import com.siriustech.merit.app_common.theme.LocalTypography
import kotlinx.coroutines.launch

/**
 * Created by Hein Htet
 */

@Composable
fun ChatAttachmentList(
    attachments: SnapshotStateList<AttachmentDisplayModel>,
    modifier: Modifier = Modifier,
    onRemove: (AttachmentDisplayModel) -> Unit = {},
) {
    val configuration = LocalConfiguration.current
    val screenWidth = configuration.screenWidthDp.dp

    val itemWidth = screenWidth * 0.8f
    val scrollState = rememberScrollState()
    val scope = rememberCoroutineScope()

    LaunchedEffect(attachments.size) {
        if (attachments.isNotEmpty()) {
            scope.launch {
                scrollState.scrollTo(attachments.size - 1)
            }
        }
    }

    AnimatedVisibility(
        visible = attachments.isNotEmpty(),
        enter = fadeIn(initialAlpha = 0.4f),
        exit = fadeOut(animationSpec = tween(durationMillis = 250))
    ) {
        Row(
            modifier = Modifier
                .horizontalScroll(scrollState)
                .then(modifier)
        ) {
            attachments.forEachIndexed { index, chatMessageDisplayModel ->
                Box(
                    modifier = Modifier
                        .width(itemWidth)
                )
                {
                    ChatAttachment(
                        attachmentDisplayModel = chatMessageDisplayModel,
                        showRemoveButton = true,
                        onRemove = onRemove,
                        modifier = Modifier
                            .padding(LocalDimens.current.dimen8)
                    )

                }
            }
        }
    }
}

@Composable
fun ChatAttachment(
    modifier: Modifier = Modifier,
    attachmentDisplayModel: AttachmentDisplayModel,
    showRemoveButton: Boolean = true,
    showBorder: Boolean = true,
    onRemove: (AttachmentDisplayModel) -> Unit = {},
) {
    Row(
        verticalAlignment = Alignment.CenterVertically,
        modifier = Modifier
            .padding(horizontal = LocalDimens.current.dimen4)
            .border(
                if (showBorder) LocalDimens.current.dimen1 else 0.dp,
                color = Color.Gray.copy(
                    alpha = if (showBorder) 0.2f else 0f
                ), RoundedCornerShape(LocalDimens.current.dimen4)
            )
            .padding(LocalDimens.current.dimen8)
            .then(modifier)
    ) {
        Box(
            modifier = Modifier
                .size(LocalDimens.current.dimen44)
                .border(
                    LocalDimens.current.dimen1,
                    Color.Gray.copy(alpha = 0.1f),
                    RoundedCornerShape(LocalDimens.current.dimen4)
                ), contentAlignment = Alignment.Center
        ) {
            val filePainter =
                if (attachmentDisplayModel.type.lowercase() == "pdf") R.drawable.ic_pdf_thumbnail else R.drawable.ic_image_thumbnail
            Image(
                painter = painterResource(id = filePainter),
                contentDescription = "File Thumbnail Image Resource"
            )
        }
        PaddingStart(value = LocalDimens.current.dimen8)
        Column(
            verticalArrangement = Arrangement.Center,
            modifier = Modifier.weight(1f)
        ) {
            Text(
                text = attachmentDisplayModel.name,
                style = LocalTypography.current.text10.medium.colorTxtTitle(),
                maxLines = 1,
                overflow = TextOverflow.Ellipsis
            )
            Text(
                text = "${attachmentDisplayModel.type.uppercase()} ${attachmentDisplayModel.fileSize}",
                style = LocalTypography.current.text12.light.colorTxtParagraph()
            )
        }
        if (showRemoveButton) {
            PaddingStart(value = LocalDimens.current.dimen8)
            Image(
                modifier = Modifier.noRippleClickable {
                    onRemove(attachmentDisplayModel)
                },
                painter = painterResource(id = R.drawable.ic_close),
                contentDescription = "Clear Image Resource"
            )
        }
    }
}


@Preview(showBackground = true)
@Composable
fun PreviewChatAttachmentList() {
    val items = remember {
        mutableStateListOf<AttachmentDisplayModel>()
    }
    items.apply {
        val add = add(
            AttachmentDisplayModel(
                name = "testing.png",
                type = "png",
                url = "",
                fileSize = "100KB"
            )
        )
        add(AttachmentDisplayModel())
        add(AttachmentDisplayModel())
        add(AttachmentDisplayModel())
    }
    ChatAttachmentList(items)
}

@Preview(showBackground = true)
@Composable
fun PreviewChatAttachment() {
    ChatAttachment(

        attachmentDisplayModel = AttachmentDisplayModel(
            name = "testing.png",
            type = "png",
            url = "",
            fileSize = "100KB"
        )
    ) {
    }
}