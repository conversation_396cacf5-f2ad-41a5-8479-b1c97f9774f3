package com.siriustech.merit.app_common.component.text

import androidx.compose.foundation.layout.Box
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Modifier
import androidx.compose.ui.layout.onGloballyPositioned
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.unit.TextUnit
import androidx.compose.ui.unit.sp

@Composable
fun AutoSizeText(
    text: String,
    modifier: Modifier = Modifier,
    maxFontSize: TextUnit = 24.sp,
    minFontSize: TextUnit = 12.sp,
    maxLines: Int = 1,
    style: TextStyle = TextStyle.Default,
    textAlign: TextAlign? = null,
) {
    var fontSize by remember { mutableStateOf(maxFontSize) }
    var isOverflow by remember { mutableStateOf(false) }

    Box(modifier = modifier) {
        Text(
            text = text,
            fontSize = fontSize,
            maxLines = maxLines,
            style = style,
            textAlign = textAlign,
            overflow = TextOverflow.Ellipsis,
            softWrap = false,
            onTextLayout = { textLayoutResult ->
                isOverflow = textLayoutResult.hasVisualOverflow
            },
            modifier = Modifier.onGloballyPositioned {
                if (isOverflow && fontSize > minFontSize) {
                    fontSize *= 0.9f
                }
            }
        )
    }
}
