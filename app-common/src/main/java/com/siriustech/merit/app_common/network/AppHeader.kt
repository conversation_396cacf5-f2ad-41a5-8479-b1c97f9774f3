package com.siriustech.merit.app_common.network

import com.core.network.base.ApiHeaderInterceptor
import com.siriustech.merit.app_common.data.CommonSharedPreferences
import com.siriustech.merit.app_common.typeenum.LanguageType

class AppHeader(
    private val ipAddress: String,
    private val deviceID: String,
    private val commonSharedPreferences: CommonSharedPreferences,
) : ApiHeaderInterceptor() {
    override fun getHeader(urlEncodedPath: String): HashMap<String, String> {
        val headerMaps = hashMapOf<String, String>()
        headerMaps[IP_ADDRESS] = ipAddress
        headerMaps[DEVICE] = "Android"
        headerMaps[DEVICE_ID] = deviceID
        headerMaps[LANGUAGE] = LanguageType.toApiValue(commonSharedPreferences.appLocale)
        headerMaps[APP_VERSION] = commonSharedPreferences.appVersion
        val sid = commonSharedPreferences.sessionId
        if (sid.isNotEmpty()) {
            headerMaps[ACCESS_TOKEN] = sid
        }
        return headerMaps
    }

    companion object {
        private const val ACCESS_TOKEN = "sid"
        private const val LANGUAGE = "Language"
        private const val LANGUAGE_TH = "th"
        private const val IP_ADDRESS = "IpAddress"
        private const val DEVICE_ID = "DeviceId"
        private const val DEVICE = "Device"
        private const val APP_VERSION = "AppVersion"
    }
}