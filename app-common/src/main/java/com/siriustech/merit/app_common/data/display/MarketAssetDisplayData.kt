package com.siriustech.merit.app_common.data.display

import com.siriustech.merit.app_common.typeenum.MarketAssetType
import com.siriustech.merit.app_common.typeenum.PortfolioAssetARRType
import com.siriustech.merit.app_common.typeenum.RiskLevel
import kotlinx.serialization.Serializable

/**
 * Created by <PERSON><PERSON>
 */

@Serializable
data class MarketAssetDisplayData(
    val id: String = "-1",
    val categoryName: String? = null,
    val isSection: Boolean = false,
    val symbol: String = "",
    val venue: String = "",
    val name: String = "",
    val logo: String = "",
    val exchange: String = "",
    val aarType: PortfolioAssetARRType = PortfolioAssetARRType.ARR1,
    val riskLevel: RiskLevel? = null,
    val currency: String = "",
    val unit: String = "",
    val marketPrice: String = "",
    val marketValue: String = "",
    val costPrice: String = "",
    val costValue: String = "",
    val unrealizedGl: String = "",
    val unrealizedGlRate: String = "",
    val percentage: String = "0.0",
    val assetAllocationClass: String = "",
    var isFavorite: Boolean = false,
    val marketAssetType: MarketAssetType = MarketAssetType.US_EQUITY,
    var region: String = "",
) : java.io.Serializable