package com.siriustech.merit.app_common.screen.docpreview

import android.os.Bundle
import androidx.activity.compose.setContent
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.toArgb
import androidx.fragment.app.FragmentActivity
import com.siriustech.core_ui_compose.ext.ChangeSystemBarsTheme
import com.siriustech.merit.app_common.theme.AppScreen
import com.siriustech.merit.app_common.theme.AppViewModel
import dagger.hilt.android.AndroidEntryPoint

/**
 * Created by <PERSON><PERSON><PERSON>
 */

@AndroidEntryPoint
class DocPreviewActivity : FragmentActivity() {

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContent {
            ChangeSystemBarsTheme(lightTheme = true, Color.White.toArgb())
            AppScreen(vm = AppViewModel()) {
                DocPreviewScreen()
            }
        }
    }


    companion object {
        const val EXTRA_DOC_PREVIEW = "EXTRA_DOC_PREVIEW"
    }
}

