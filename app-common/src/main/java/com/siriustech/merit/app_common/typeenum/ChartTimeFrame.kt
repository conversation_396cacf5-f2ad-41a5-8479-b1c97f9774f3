package com.siriustech.merit.app_common.typeenum

import androidx.compose.runtime.Composable
import androidx.compose.runtime.ReadOnlyComposable

/**
 * Created by <PERSON><PERSON> <PERSON>tet
 */
enum class ChartTimeFrame {
    ONE_DAY,
    ONE_WEEK,
    ONE_MONTH,
    ONE_YEAR,
    THREE_YEAR,
    FIVE_YEAR,
    ALL_TIME;


    fun getDisplayName(): String {
        return when (this) {
            ONE_DAY -> "Today"
            ONE_WEEK -> "1W"
            ONE_MONTH -> "1M"
            ONE_YEAR -> "1Y"
            THREE_YEAR -> "3Y"
            FIVE_YEAR -> "5Y"
            else -> "YTD"
        }
    }

    fun getApiRequest(): String {
        return when (this) {
            ONE_DAY -> "1D"
            ONE_WEEK -> "1W"
            ONE_MONTH -> "1M"
            ONE_YEAR -> "1Y"
            THREE_YEAR -> "3Y"
            FIVE_YEAR -> "5Y"
            else -> "YTD"
        }
    }
}