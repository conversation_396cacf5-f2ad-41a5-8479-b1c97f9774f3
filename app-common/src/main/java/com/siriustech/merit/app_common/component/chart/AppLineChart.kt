package com.siriustech.merit.app_common.component.chart

import android.content.Context
import android.graphics.Canvas
import android.graphics.Paint
import android.graphics.Paint.Align
import android.graphics.Rect
import android.graphics.RectF
import android.util.AttributeSet
import android.view.MotionEvent
import android.view.View
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.toArgb
import androidx.compose.ui.util.trace
import androidx.compose.ui.viewinterop.AndroidView
import androidx.core.content.res.ResourcesCompat
import com.github.mikephil.charting.charts.Chart
import com.github.mikephil.charting.charts.LineChart
import com.github.mikephil.charting.components.XAxis
import com.github.mikephil.charting.components.YAxis
import com.github.mikephil.charting.data.Entry
import com.github.mikephil.charting.data.LineData
import com.github.mikephil.charting.data.LineDataSet
import com.github.mikephil.charting.highlight.Highlight
import com.github.mikephil.charting.listener.ChartTouchListener
import com.github.mikephil.charting.listener.OnChartGestureListener
import com.github.mikephil.charting.listener.OnChartValueSelectedListener
import com.github.mikephil.charting.renderer.XAxisRenderer
import com.github.mikephil.charting.renderer.YAxisRenderer
import com.github.mikephil.charting.utils.Transformer
import com.github.mikephil.charting.utils.Utils
import com.github.mikephil.charting.utils.ViewPortHandler
import com.siriustech.merit.app_common.R
import com.siriustech.merit.app_common.theme.LocalAppColor

/**
 * Created by Hein Htet
 */

@Composable
fun AppLineChart(
    modifier: Modifier = Modifier,
    lineChart: LineChart,
    onConfigChart: (LineChart) -> Unit = {},
    lineData: LineData,
    enableHighlightGesture : Boolean = true,
) {

    LaunchedEffect(Unit) {
        lineChart.also { chart ->
            chart.setDrawBorders(false)
            chart.isHorizontalScrollBarEnabled = false
            chart.isVerticalScrollBarEnabled = false
            val typeface = ResourcesCompat.getFont(lineChart.context, R.font.noto_sans_medium)
            chart.setDrawGridBackground(false)
            chart.setDrawBorders(false)
            chart.setDrawMarkers(true)
            chart.isDoubleTapToZoomEnabled = false
            chart.setPinchZoom(false)
            chart.axisRight.setDrawLabels(true)
            chart.axisRight.setPosition(YAxis.YAxisLabelPosition.INSIDE_CHART)
            chart.axisLeft.setPosition(YAxis.YAxisLabelPosition.INSIDE_CHART)
            chart.axisRight.setLabelCount(3, true)
            chart.legend.isEnabled = false
            chart.description.isEnabled = false
            chart.description.isEnabled = false
            chart.isHighlightPerTapEnabled = false
            chart.axisLeft.setDrawLabels(false)
            chart.xAxis.isGranularityEnabled = true
            chart.xAxis.setDrawLabels(false)
            chart.xAxis.setDrawGridLines(false)
            chart.axisLeft.setDrawGridLines(false)
            chart.axisRight.setDrawGridLines(false)
            chart.xAxis.setDrawAxisLine(false)
            chart.axisLeft.setDrawAxisLine(false)
            chart.axisRight.setDrawAxisLine(false)
            chart.axisRight.textSize = 12f
            chart.axisRight.typeface = typeface
            chart.axisLeft.textSize = 12f
            chart.axisLeft.typeface = typeface
            chart.data = lineData
            chart.setViewPortOffsets(0f, 0f, 0f, 0f)
            val xAxis = chart.xAxis
            xAxis.position = XAxis.XAxisPosition.BOTTOM

            if(enableHighlightGesture){
                chart.onHighlightGesture()
            }
            onConfigChart(chart)
            chart.invalidate()
        }
    }

    AndroidView(
        modifier = Modifier
            .fillMaxSize()
            .then(modifier),
        factory = { context ->
            val typeface = ResourcesCompat.getFont(context, R.font.noto_sans_medium)
            lineChart
                .also { chart ->
                    chart.setDrawBorders(false)
                    chart.isHorizontalScrollBarEnabled = false
                    chart.isVerticalScrollBarEnabled = false
                    chart.onChartGestureListener = object : OnChartGestureListener {
                        override fun onChartGestureStart(
                            me: MotionEvent?,
                            lastPerformedGesture: ChartTouchListener.ChartGesture?,
                        ) {

                        }

                        override fun onChartGestureEnd(
                            me: MotionEvent?,
                            lastPerformedGesture: ChartTouchListener.ChartGesture?,
                        ) {
                        }

                        override fun onChartLongPressed(me: MotionEvent?) {
                        }

                        override fun onChartDoubleTapped(me: MotionEvent?) {
                        }

                        override fun onChartSingleTapped(me: MotionEvent?) {
                            me?.let {
                                val highlight = chart.getHighlightByTouchPoint(me.x, me.y)
                                if (highlight != null) {
                                    chart.highlightValue(highlight)
                                }
                            }
                        }

                        override fun onChartFling(
                            me1: MotionEvent?,
                            me2: MotionEvent?,
                            velocityX: Float,
                            velocityY: Float,
                        ) {
                        }

                        override fun onChartScale(
                            me: MotionEvent?,
                            scaleX: Float,
                            scaleY: Float,
                        ) {
                        }

                        override fun onChartTranslate(me: MotionEvent?, dX: Float, dY: Float) {
                        }
                    }
                    chart.setDrawGridBackground(false)
                    chart.setDrawBorders(false)
                    chart.setDrawMarkers(true)
                    chart.isDoubleTapToZoomEnabled = false
                    chart.setPinchZoom(false)
                    chart.axisRight.setDrawLabels(true)
                    chart.axisRight.setPosition(YAxis.YAxisLabelPosition.INSIDE_CHART)
                    chart.axisLeft.setPosition(YAxis.YAxisLabelPosition.INSIDE_CHART)
                    chart.axisRight.setLabelCount(3, true)
                    chart.legend.isEnabled = false
                    chart.description.isEnabled = false
                    chart.description.isEnabled = false
                    chart.isHighlightPerTapEnabled = true
                    chart.axisLeft.setDrawLabels(false)
                    chart.xAxis.setDrawLabels(false)
                    chart.xAxis.setDrawGridLines(false)
                    chart.axisLeft.setDrawGridLines(false)
                    chart.axisRight.setDrawGridLines(false)
                    chart.xAxis.setDrawAxisLine(false)
                    chart.axisLeft.setDrawAxisLine(false)
                    chart.axisRight.setDrawAxisLine(false)
                    chart.axisRight.textSize = 12f
                    chart.axisRight.typeface = typeface
                    chart.axisLeft.textSize = 12f
                    chart.axisLeft.typeface = typeface
                    chart.data = lineData
                    chart.setViewPortOffsets(0f, 0f, 0f, 0f)
                    val xAxis = chart.xAxis
                    xAxis.position = XAxis.XAxisPosition.BOTTOM
                    chart.moveViewToX(lineData.entryCount.toFloat())
                    onConfigChart(chart)
                    chart.invalidate()
                }
        },
        update = { chart ->
            chart.data = lineData
            chart.post {
                chart.setViewPortOffsets(0f, 0f, 0f, 0f)
                chart.moveViewToX(lineData.entryCount.toFloat())
                chart.notifyDataSetChanged()
                val labelBackgroundPaint = Paint().apply {
//                    color = 0xFFDDDDDD.toInt() // Set the background color (light gray)
                    color = Color.Transparent.toArgb()
                    style = Paint.Style.FILL // Fill the background
                }
                if (lineData.dataSetCount > 0) {
                    val customYAxisRenderer = CustomYAxisRenderer(
                        chart,
                        chart.viewPortHandler,
                        chart.axisRight,
                        chart.getTransformer(YAxis.AxisDependency.RIGHT),
                        labelBackgroundPaint,
                    )
                    chart.rendererRightYAxis = customYAxisRenderer
                }
                chart.invalidate()
            }
        }
    )
}

private fun calculateRequiredRightOffset(chart: LineChart): Float {
    val yAxis = chart.axisRight  // Get the right Y-axis (or use axisLeft if needed)
    val paint = chart.rendererRightYAxis.paintAxisLabels  // Get the paint object for Y-axis labels
    // Find the maximum Y-axis value to determine the largest label
    val maxLabelValue = yAxis.mEntries.maxOrNull() ?: 0f
    val label =
        Utils.formatNumber(maxLabelValue, 0, true)  // Format the label as it appears on the chart
    // Measure the width of the label
    val bounds = Rect()
    paint.getTextBounds(label, 0, label.length, bounds)
    // Return the width of the label plus some padding (e.g., 5dp) for safety
    return bounds.width().toFloat() + Utils.convertDpToPixel(5f)
}


class CustomYAxisRenderer(
    val chart: Chart<*>,
    viewPortHandler: ViewPortHandler,
    yAxis: YAxis,
    transformer: Transformer,
    private val labelBackgroundPaint: Paint,
) : YAxisRenderer(viewPortHandler, yAxis, transformer) {

    /**
     * draws the y-labels on the specified x-position
     *
     * @param fixedPosition
     * @param positions
     */
    override fun drawYLabels(
        c: Canvas,
        fixedPosition: Float,
        positions: FloatArray,
        offset: Float,
    ) {
        val from = if (mYAxis.isDrawBottomYLabelEntryEnabled) 0 else 1
        val to =
            if (mYAxis.isDrawTopYLabelEntryEnabled) mYAxis.mEntryCount else (mYAxis.mEntryCount - 1)

        val dashPaint = Paint().apply {
            color = Color.LightGray.toArgb()
            strokeWidth = 2f
            pathEffect =
                android.graphics.DashPathEffect(floatArrayOf(15f, 15f), 0f) // Set dash pattern
        }

        val fontMetrics: Paint.FontMetrics = mAxisLabelPaint.fontMetrics

        for (i in from until to) {
            val text = mYAxis.getFormattedLabel(i)
            val textWidth = mAxisLabelPaint.measureText(text)
            val yPos = positions[i * 2 + 1]
            val startX = mViewPortHandler.contentLeft()
            val endX = mViewPortHandler.contentRight()
            c.drawLine(startX, yPos, endX - textWidth, yPos, dashPaint)
            // Background rectangle for the label (if needed)
            val adjustY = yPos - fontMetrics.descent - 6f // 基线对齐虚线
            if (i == to - 1) {
                val backgroundRect = RectF()
                backgroundRect.set(
                    fixedPosition - textWidth - 20,
                    adjustY - mAxisLabelPaint.textSize + 5,
                    fixedPosition + textWidth,
                    adjustY + 20
                )
                c.drawRoundRect(backgroundRect, 8f, 8f, labelBackgroundPaint)
            }
            c.drawText(text, fixedPosition, adjustY + offset, mAxisLabelPaint);
        }
    }

}

fun Chart<*>.onHighlightGesture(){
    onChartGestureListener = object : OnChartGestureListener {
        override fun onChartGestureStart(
            me: MotionEvent?,
            lastPerformedGesture: ChartTouchListener.ChartGesture?,
        ) {
        }
        override fun onChartGestureEnd(
            me: MotionEvent?,
            lastPerformedGesture: ChartTouchListener.ChartGesture?,
        ) {
        }

        override fun onChartLongPressed(me: MotionEvent?) {
        }

        override fun onChartDoubleTapped(me: MotionEvent?) {
        }

        override fun onChartSingleTapped(me: MotionEvent?) {
            me?.let {
                val highlight = <EMAIL>(me.x, me.y)
                if (highlight != null) {
                    <EMAIL>(highlight)
                }
            }
        }

        override fun onChartFling(
            me1: MotionEvent?,
            me2: MotionEvent?,
            velocityX: Float,
            velocityY: Float,
        ) {
        }

        override fun onChartScale(
            me: MotionEvent?,
            scaleX: Float,
            scaleY: Float,
        ) {
        }

        override fun onChartTranslate(me: MotionEvent?, dX: Float, dY: Float) {
        }
    }
}