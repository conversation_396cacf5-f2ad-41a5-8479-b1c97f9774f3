package com.siriustech.merit.app_common.navigation.argument.notification

import androidx.lifecycle.SavedStateHandle
import androidx.navigation.toRoute
import com.siriustech.merit.app_common.component.common.NotificationItemDisplayData
import com.siriustech.merit.app_common.ext.serializableType
import com.siriustech.merit.app_common.navigation.MessageCenterDetails
import kotlin.reflect.typeOf
import kotlinx.serialization.Serializable

@Serializable
data class MessageCenterDetailsArgument(
    val notificationItemDisplayData: NotificationItemDisplayData,
) {
    companion object {
        val typeMap =
            mapOf(typeOf<MessageCenterDetailsArgument>() to serializableType<MessageCenterDetailsArgument>())

        fun from(savedStateHandle: SavedStateHandle) =
            savedStateHandle.toRoute<MessageCenterDetails>(typeMap)
    }
}