package com.siriustech.merit.app_common.component.chart

import android.graphics.Paint
import android.view.MotionEvent
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.ui.Modifier
import androidx.compose.ui.viewinterop.AndroidView
import androidx.core.content.res.ResourcesCompat
import com.github.mikephil.charting.charts.CombinedChart
import com.github.mikephil.charting.components.XAxis
import com.github.mikephil.charting.components.YAxis
import com.github.mikephil.charting.data.CombinedData
import com.github.mikephil.charting.listener.ChartTouchListener
import com.github.mikephil.charting.listener.OnChartGestureListener
import com.siriustech.merit.app_common.R
import com.github.mikephil.charting.formatter.ValueFormatter
/**
 * Created by <PERSON><PERSON>
 */

@Composable
fun AppCombineChart(
    combinedChart: CombinedChart,
    chartData: CombinedData,
    onConfigChart: (chart: CombinedChart) -> Unit = {},
) {
    LaunchedEffect(Unit) {
        combinedChart.also { chart ->
            chart.setDrawBorders(false)
            chart.isHorizontalScrollBarEnabled = false
            chart.isVerticalScrollBarEnabled = false
            chart.onChartGestureListener = object : OnChartGestureListener {
                override fun onChartGestureStart(
                    me: MotionEvent?,
                    lastPerformedGesture: ChartTouchListener.ChartGesture?,
                ) {

                }

                override fun onChartGestureEnd(
                    me: MotionEvent?,
                    lastPerformedGesture: ChartTouchListener.ChartGesture?,
                ) {
                }

                override fun onChartLongPressed(me: MotionEvent?) {
                }

                override fun onChartDoubleTapped(me: MotionEvent?) {
                }

                override fun onChartSingleTapped(me: MotionEvent?) {
                    me?.let {
                        val highlight = chart.getHighlightByTouchPoint(me.x, me.y)
                        if (highlight != null) {
                            chart.highlightValue(highlight)
                        }
                    }
                }

                override fun onChartFling(
                    me1: MotionEvent?,
                    me2: MotionEvent?,
                    velocityX: Float,
                    velocityY: Float,
                ) {
                }

                override fun onChartScale(
                    me: MotionEvent?,
                    scaleX: Float,
                    scaleY: Float,
                ) {
                }

                override fun onChartTranslate(me: MotionEvent?, dX: Float, dY: Float) {
                }
            }
            val typeface = ResourcesCompat.getFont(combinedChart.context, R.font.noto_sans_medium)
            chart.setDrawGridBackground(false)
            chart.setDrawBorders(false)
            chart.setDrawMarkers(true)
            chart.isDoubleTapToZoomEnabled = false
            chart.setPinchZoom(false)
            chart.axisRight.setDrawLabels(true)
            chart.axisRight.setPosition(YAxis.YAxisLabelPosition.INSIDE_CHART)
            chart.axisLeft.setPosition(YAxis.YAxisLabelPosition.INSIDE_CHART)
            chart.axisRight.setLabelCount(3, true)
            chart.legend.isEnabled = false
            chart.description.isEnabled = false
            chart.description.isEnabled = false
            chart.isHighlightPerTapEnabled = false
            chart.axisLeft.setDrawLabels(false)
            chart.xAxis.setDrawLabels(false)
            chart.xAxis.setDrawGridLines(false)
            chart.axisLeft.setDrawGridLines(false)
            chart.axisRight.setDrawGridLines(false)
            chart.xAxis.setDrawAxisLine(false)
            chart.axisLeft.setDrawAxisLine(false)
            chart.axisRight.setDrawAxisLine(false)
            chart.axisRight.textSize = 12f
            chart.axisRight.typeface = typeface
            chart.axisLeft.textSize = 12f
            chart.axisLeft.typeface = typeface
            chart.data = chartData
            chart.setViewPortOffsets(0f, 0f, 0f, 0f)
            val xAxis = chart.xAxis
            xAxis.position = XAxis.XAxisPosition.BOTTOM
            onConfigChart(chart)
            chart.invalidate()
        }
    }
    AndroidView(
        modifier = Modifier.fillMaxSize(),
        factory = { context ->
            val typeface = ResourcesCompat.getFont(context, R.font.noto_sans_medium)
            combinedChart
                .also { chart ->
                    chart.setDrawBorders(false)
                    chart.isHorizontalScrollBarEnabled = false
                    chart.isVerticalScrollBarEnabled = false
                    chart.onChartGestureListener = object : OnChartGestureListener {
                        override fun onChartGestureStart(
                            me: MotionEvent?,
                            lastPerformedGesture: ChartTouchListener.ChartGesture?,
                        ) {

                        }

                        override fun onChartGestureEnd(
                            me: MotionEvent?,
                            lastPerformedGesture: ChartTouchListener.ChartGesture?,
                        ) {
                        }

                        override fun onChartLongPressed(me: MotionEvent?) {
                        }

                        override fun onChartDoubleTapped(me: MotionEvent?) {
                        }

                        override fun onChartSingleTapped(me: MotionEvent?) {
                            me?.let {
                                val highlight = chart.getHighlightByTouchPoint(me.x, me.y)
                                if (highlight != null) {
                                    chart.highlightValue(highlight)
                                }
                            }
                        }

                        override fun onChartFling(
                            me1: MotionEvent?,
                            me2: MotionEvent?,
                            velocityX: Float,
                            velocityY: Float,
                        ) {
                        }

                        override fun onChartScale(
                            me: MotionEvent?,
                            scaleX: Float,
                            scaleY: Float,
                        ) {
                        }

                        override fun onChartTranslate(me: MotionEvent?, dX: Float, dY: Float) {
                        }
                    }
                    chart.setDrawGridBackground(false)
                    chart.setDrawBorders(false)
                    chart.setDrawMarkers(true)
                    chart.isDoubleTapToZoomEnabled = false
                    chart.setPinchZoom(false)
                    chart.axisRight.setDrawLabels(true)
                    chart.axisRight.setPosition(YAxis.YAxisLabelPosition.INSIDE_CHART)
                    chart.axisLeft.setPosition(YAxis.YAxisLabelPosition.INSIDE_CHART)
                    chart.axisRight.setLabelCount(3, true)
                    chart.legend.isEnabled = false
                    chart.description.isEnabled = false
                    chart.description.isEnabled = false
                    chart.isHighlightPerTapEnabled = false
                    chart.axisLeft.setDrawLabels(false)
                    chart.xAxis.setDrawLabels(false)
                    chart.xAxis.setDrawGridLines(false)
                    chart.axisLeft.setDrawGridLines(false)
                    chart.axisRight.setDrawGridLines(false)
                    chart.xAxis.setDrawAxisLine(false)
                    chart.axisLeft.setDrawAxisLine(false)
                    chart.axisRight.setDrawAxisLine(false)
                    chart.axisRight.textSize = 12f
                    chart.axisRight.typeface = typeface
                    chart.axisLeft.textSize = 12f
                    chart.axisLeft.typeface = typeface
                    chart.data = chartData
                    chart.setViewPortOffsets(0f, 0f, 0f, 0f)
                    val xAxis = chart.xAxis
                    xAxis.position = XAxis.XAxisPosition.BOTTOM
                    onConfigChart(chart)
                    chart.invalidate()
                }
        },
        update = { chart ->
            chart.data = chartData
            val labelBackgroundPaint = Paint().apply {
                color = 0xFFDDDDDD.toInt() // Set the background color (light gray)
                style = Paint.Style.FILL // Fill the background
            }
            if (combinedChart.data.dataSetCount > 0) {
                val customYAxisRenderer = CustomYAxisRenderer(
                    chart,
                    chart.viewPortHandler,
                    chart.axisRight,
                    chart.getTransformer(YAxis.AxisDependency.RIGHT),
                    labelBackgroundPaint,
                )
                chart.rendererRightYAxis = customYAxisRenderer
            }
            chart.post {
                chart.setViewPortOffsets(0f, 0f, 0f, 0f)
                chart.notifyDataSetChanged()
                chart.invalidate()
            }
        }
    )
}


@Composable
fun AppCombineChartV2(
    chartData: CombinedData,
    onConfigChart: (chart: CombinedChart) -> Unit = {},
) {
    var mChart : CombinedChart?= null
    AndroidView(
        modifier = Modifier.fillMaxSize(),
        factory = { context ->
            val typeface = ResourcesCompat.getFont(context, R.font.noto_sans_medium)
            mChart =   CombinedChart(context)
            mChart?.also { chart ->
                chart.setDrawBorders(false)
                chart.isHorizontalScrollBarEnabled = false
                chart.isVerticalScrollBarEnabled = false
                chart.onChartGestureListener = object : OnChartGestureListener {
                    override fun onChartGestureStart(
                        me: MotionEvent?,
                        lastPerformedGesture: ChartTouchListener.ChartGesture?,
                    ) {

                    }

                    override fun onChartGestureEnd(
                        me: MotionEvent?,
                        lastPerformedGesture: ChartTouchListener.ChartGesture?,
                    ) {
                    }

                    override fun onChartLongPressed(me: MotionEvent?) {
                    }

                    override fun onChartDoubleTapped(me: MotionEvent?) {
                    }

                    override fun onChartSingleTapped(me: MotionEvent?) {
                        me?.let {
                            val highlight = chart.getHighlightByTouchPoint(me.x, me.y)
                            if (highlight != null) {
                                chart.highlightValue(highlight)
                            }
                        }
                    }

                    override fun onChartFling(
                        me1: MotionEvent?,
                        me2: MotionEvent?,
                        velocityX: Float,
                        velocityY: Float,
                    ) {
                    }

                    override fun onChartScale(
                        me: MotionEvent?,
                        scaleX: Float,
                        scaleY: Float,
                    ) {
                    }

                    override fun onChartTranslate(me: MotionEvent?, dX: Float, dY: Float) {
                    }
                }
                chart.setDrawGridBackground(false)
                chart.setDrawBorders(false)
                chart.setDrawMarkers(true)
                chart.isDoubleTapToZoomEnabled = false
                chart.setPinchZoom(false)
                chart.axisRight.setDrawLabels(true)
                chart.axisRight.setPosition(YAxis.YAxisLabelPosition.INSIDE_CHART)
                chart.axisLeft.setPosition(YAxis.YAxisLabelPosition.INSIDE_CHART)
                chart.axisRight.setLabelCount(3, true)
                chart.legend.isEnabled = false
                chart.description.isEnabled = false
                chart.description.isEnabled = false
                chart.isHighlightPerTapEnabled = false
                chart.axisLeft.setDrawLabels(false)
                chart.xAxis.setDrawLabels(false)
                chart.xAxis.setDrawGridLines(false)
                chart.axisLeft.setDrawGridLines(false)
                chart.axisRight.setDrawGridLines(false)
                chart.xAxis.setDrawAxisLine(false)
                chart.axisLeft.setDrawAxisLine(false)
                chart.axisRight.setDrawAxisLine(false)
                chart.axisRight.textSize = 12f
                chart.axisRight.typeface = typeface
                chart.axisLeft.textSize = 12f
                chart.axisLeft.typeface = typeface
                chart.data = chartData
                chart.setViewPortOffsets(0f, 0f, 0f, 0f)
                val xAxis = chart.xAxis
                xAxis.position = XAxis.XAxisPosition.BOTTOM
                onConfigChart(chart)
                chart.invalidate()
            }
            onConfigChart(mChart!!)
            mChart!!
        },
        update = { chart ->
            chart.data = chartData
            val labelBackgroundPaint = Paint().apply {
                color = 0xFFDDDDDD.toInt() // Set the background color (light gray)
                style = Paint.Style.FILL // Fill the background
            }
            if (chart.data.dataSetCount > 0) {
                val customYAxisRenderer = CustomYAxisRenderer(
                    chart,
                    chart.viewPortHandler,
                    chart.axisRight,
                    chart.getTransformer(YAxis.AxisDependency.RIGHT),
                    labelBackgroundPaint,
                )
                chart.rendererRightYAxis = customYAxisRenderer
                chart.axisRight.valueFormatter = YAxisValueFormatter(true)
            }
            chart.post {
                chart.setViewPortOffsets(0f, 0f, 0f, 0f)
                chart.notifyDataSetChanged()
                chart.invalidate()
            }
        }
    )
}


class YAxisValueFormatter(private val showPercentage: Boolean) : ValueFormatter() {
    override fun getFormattedValue(value: Float): String {
        return if (showPercentage) {
            if (value >= 0) "+%.2f%%".format(value) else "%.2f%%".format(value)
        } else {
            value.toString()
        }
    }
}