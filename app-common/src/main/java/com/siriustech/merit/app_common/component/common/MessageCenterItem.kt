package com.siriustech.merit.app_common.component.common

import androidx.compose.foundation.Image
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.tooling.preview.Preview
import com.siriustech.merit.app_common.R
import com.siriustech.merit.app_common.component.container.PaddingStart
import com.siriustech.merit.app_common.component.container.PaddingTop
import com.siriustech.merit.app_common.ext.colorTxtInactive
import com.siriustech.merit.app_common.ext.colorTxtParagraph
import com.siriustech.merit.app_common.ext.noRippleClickable
import com.siriustech.merit.app_common.theme.LocalDimens
import com.siriustech.merit.app_common.theme.LocalTypography

/**
 * Created by <PERSON><PERSON>
 */

@Composable
fun MessageCenterItem(
    modifier: Modifier = Modifier,
    data: NotificationItemDisplayData = NotificationItemDisplayData(),
    onClicked: () -> Unit = {},
) {
    Row(
        modifier = Modifier
            .then(modifier)
            .padding(LocalDimens.current.dimen8)
            .noRippleClickable {
                onClicked()
            }, verticalAlignment = Alignment.CenterVertically
    ) {
        Column(modifier = Modifier.weight(1f), verticalArrangement = Arrangement.Top) {
            Text(
                text = data.title,
                style = LocalTypography.current.text14.medium.colorTxtParagraph()
            )
            Text(
                text = data.description,
                style = LocalTypography.current.text12.regular.colorTxtInactive()
            )
        }
        Box(
            modifier = Modifier
                .size(LocalDimens.current.dimen44)
                .noRippleClickable { },
            contentAlignment = Alignment.Center
        ) {
            Image(
                painter = painterResource(id = R.drawable.ic_right_arrow),
                contentDescription = "More Info Image Resource"
            )
        }
    }
}

@Preview(showBackground = true, showSystemUi = true)
@Composable
fun PreviewMessageCenterItem() {
    MessageCenterItem(
        data = NotificationItemDisplayData(
            title = "Order Approved",
            description = "Order Approved"
        )
    )
}