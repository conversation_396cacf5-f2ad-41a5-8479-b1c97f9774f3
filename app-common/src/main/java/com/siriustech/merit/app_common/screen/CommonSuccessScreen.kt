package com.siriustech.merit.app_common.screen

import androidx.activity.compose.BackHandler
import androidx.compose.foundation.Image
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxHeight
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.painter.Painter
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.tooling.preview.Preview
import com.siriustech.merit.app_common.R
import com.siriustech.merit.app_common.component.button.AccentButton
import com.siriustech.merit.app_common.component.button.ButtonProperties
import com.siriustech.merit.app_common.component.button.PrimaryButton
import com.siriustech.merit.app_common.component.button.SecondaryBorderButton
import com.siriustech.merit.app_common.component.button.SecondaryButton
import com.siriustech.merit.app_common.ext.colorTxtParagraph
import com.siriustech.merit.app_common.ext.colorTxtTitle
import com.siriustech.merit.app_common.theme.AppScreen
import com.siriustech.merit.app_common.theme.AppViewModel
import com.siriustech.merit.app_common.theme.LocalDimens
import com.siriustech.merit.app_common.theme.LocalTypography

/**
 * Created by Hein Htet
 */

@Composable
fun CommonSuccessScreen(
    properties: CommonSuccessScreenProperties = CommonSuccessScreenProperties(),
    onPrimaryButtonClick: () -> Unit = {},
    onSecondaryButtonClick: () -> Unit = {},
    onSecondaryBorderButtonClick: () -> Unit = {},
    onAccentButtonClick: () -> Unit = {},
    contents: @Composable () -> Unit,
) {

    if (!properties.disableBackPressed) {
        BackHandler {}
    }


    AppScreen(vm = AppViewModel(), ignorePaddingValue = properties.ignorePadding) {
        Column(modifier = Modifier.fillMaxWidth()) {
            Spacer(modifier = Modifier.fillMaxHeight(0.15f))
            Column(
                modifier = Modifier
                    .weight(1f)
                    .fillMaxWidth()
                    .fillMaxHeight()
                    .padding(LocalDimens.current.dimen12),
                horizontalAlignment = Alignment.Start
            ) {
                if (properties.iconPainter != null) {
                    Image(
                        painter = properties.iconPainter,
                        contentDescription = "Success Image Resource",
                        modifier = Modifier.align(Alignment.CenterHorizontally)
                    )
                }
                Spacer(modifier = Modifier.height(LocalDimens.current.dimen32))
                Text(
                    text = properties.title,
                    modifier = Modifier.align(Alignment.CenterHorizontally),
                    style = LocalTypography.current.text18.semiBold.colorTxtTitle()
                )
                Spacer(modifier = Modifier.height(LocalDimens.current.dimen16))
                contents()
                Spacer(modifier = Modifier.weight(properties.spacerBetweenAction))
                if (properties.primaryButtonText != null) {
                    PrimaryButton(
                        properties = ButtonProperties(text = properties.primaryButtonText),
                        onClicked = onPrimaryButtonClick
                    )
                    Spacer(modifier = Modifier.height(LocalDimens.current.dimen8))
                }
                if (properties.secondaryBorderButtonText != null) {
                    SecondaryBorderButton(
                        properties = ButtonProperties(text = properties.secondaryBorderButtonText),
                        onClicked = onSecondaryBorderButtonClick
                    )
                    Spacer(modifier = Modifier.height(LocalDimens.current.dimen8))
                }
                if (properties.secondaryButtonText.isNotEmpty()) {
                    SecondaryButton(
                        properties = ButtonProperties(text = properties.secondaryButtonText),
                        onClicked = onSecondaryButtonClick
                    )
                }
                if (properties.accentButtonText != null) {
                    Spacer(modifier = Modifier.height(LocalDimens.current.dimen8))
                    AccentButton(
                        properties = ButtonProperties(text = properties.accentButtonText),
                        onClicked = onAccentButtonClick
                    )
                }
            }
        }
    }
}

data class CommonSuccessScreenProperties(
    val title: String = "",
    val iconPainter: Painter? = null,
    val secondaryButtonText: String = "",
    val primaryButtonText: String? = null,
    val secondaryBorderButtonText: String? = null,
    val accentButtonText: String? = null,
    val spacerBetweenAction: Float = 1f,
    val disableBackPressed: Boolean = false,
    val ignorePadding : Boolean = false
)

@Preview(showBackground = true, showSystemUi = true)
@Composable
fun PreviewCommonSuccessScreen() {
    CommonSuccessScreen(
        properties = CommonSuccessScreenProperties(
            title = stringResource(id = R.string.key0209),
            iconPainter = painterResource(
                id = R.drawable.ic_2fa_verify_success,
            ),
            secondaryButtonText = stringResource(id = R.string.key0211),
            secondaryBorderButtonText = "Border Button",
            primaryButtonText = "Border Button",
            accentButtonText = "Accent Button"
        )
    ) {
        Column {
            Text(
                text = stringResource(id = R.string.key0210),
                style = LocalTypography.current.text14.light.colorTxtParagraph()
            )
//            Spacer(modifier = Modifier.height(LocalDimens.current.dimen8))
//            Text(
//                text = stringResource(id = R.string._key0026),
//                style = LocalTypography.current.text14.light.colorTxtParagraph()
//            )
        }
    }
}

@Preview(showBackground = true, showSystemUi = true)
@Composable
fun PreviewCommonSuccessScreen2() {
    CommonSuccessScreen(properties = CommonSuccessScreenProperties(
        title = stringResource(id = R.string.key0224), iconPainter = painterResource(
            id = R.drawable.ic_green_success,
        ),
        secondaryButtonText = stringResource(id = R.string.key0211)
    ), onSecondaryButtonClick = {
    }) {
        Column(horizontalAlignment = Alignment.Start) {
            Text(
                text = stringResource(id = R.string.key0225),
                style = LocalTypography.current.text14.light.colorTxtParagraph()
            )
        }
    }
}

