package com.siriustech.merit.app_common.component.header

import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.collectAsState
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.hilt.navigation.compose.hiltViewModel
import com.siriustech.core_ui_compose.ext.bottomStroke
import com.siriustech.merit.apilayer.service.home.notification.NotificationUseCase
import com.siriustech.merit.app_common.R
import com.siriustech.merit.app_common.component.image.ProfileImage
import com.siriustech.merit.app_common.component.textfield.InputBox
import com.siriustech.merit.app_common.component.textfield.InputBoxType
import com.siriustech.merit.app_common.component.textfield.InputProperties
import com.siriustech.merit.app_common.data.AppCache
import com.siriustech.merit.app_common.data.CommonSharedPreferences
import com.siriustech.merit.app_common.ext.noRippleClickable
import com.siriustech.merit.app_common.theme.AppViewModel
import com.siriustech.merit.app_common.theme.LocalAppColor
import com.siriustech.merit.app_common.theme.LocalDimens
import dagger.hilt.android.lifecycle.HiltViewModel
import javax.inject.Inject
import kotlinx.coroutines.delay
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.launch
import timber.log.Timber

/**
 * Created by Hein Htet
 */

@Composable
fun DashboardToolbar(
    viewModel: DashboardToolbarViewModel = hiltViewModel(),
    properties: DashboardToolbarProperties = DashboardToolbarProperties(),
    onBackPress: () -> Unit = {},
    onSearchInstrument: () -> Unit = {},
    onNotificationClicked: () -> Unit = {},
    onSettingsClicked: () -> Unit = {},
    onChatIconClicked: () -> Unit = {},
) {

    val unReadNotificationCount = viewModel.unReadNotificationCount.collectAsState()
    LaunchedEffect(unReadNotificationCount.value) {
        Timber.d("NOTIFICATION_CHANGED ${unReadNotificationCount.value}")
    }


    Column(
        modifier = Modifier
            .fillMaxWidth()
            .height(LocalDimens.current.dimen56)
    ) {
        Row(
            modifier = Modifier
                .background(LocalAppColor.current.bgDefault)
                .fillMaxSize()
                .padding(horizontal = LocalDimens.current.dimen12),
            verticalAlignment = Alignment.CenterVertically
        ) {
            if (properties.showBackArrow) {
                Image(
                    modifier = Modifier
                        .noRippleClickable {
                            onBackPress()
                        },
                    painter = painterResource(id = R.drawable.ic_back_arrow),
                    contentDescription = "Back Image Resource"
                )
            }
            InputBox(
                modifier = Modifier
                    .weight(1f)
                    .padding(horizontal = LocalDimens.current.dimen12)
                    .noRippleClickable { onSearchInstrument() },
                inputBoxModifier = Modifier
                    .clip(RoundedCornerShape(LocalDimens.current.dimen8)),
                properties = InputProperties(
                    placeholder = stringResource(id = R.string.key0400),
                    inputPrefixIcon = painterResource(
                        id = R.drawable.ic_search
                    ),
                    inputBoxType = InputBoxType.PICKER,
                    editable = false,
                    showRightPickerIcon = false
                )
            )
            if (properties.showChatButton) {
                Image(
                    modifier = Modifier
                        .noRippleClickable { onChatIconClicked() },
                    painter = painterResource(id = R.drawable.ic_chat_message),
                    contentDescription = "Back Image Resource"
                )
            }
            Image(
                modifier = Modifier
                    .noRippleClickable { onNotificationClicked() },
                painter = painterResource(
                    id = if (unReadNotificationCount.value > 0)
                        R.drawable.ic_new_notification_bell else R.drawable.ic_notification_bell
                ),
                contentDescription = "Back Image Resource"
            )
            Spacer(modifier = Modifier.width(LocalDimens.current.dimen8))
            ProfileImage(
                modifier = Modifier
                    .size(18.dp)
                    .noRippleClickable {
                        onSettingsClicked()
                    }
            )
        }
    }
    Spacer(
        modifier = Modifier
            .fillMaxWidth()
            .padding(top = LocalDimens.current.dimen4)
            .bottomStroke(LocalAppColor.current.bgAccent, LocalDimens.current.dimen1)
    )
}

data class DashboardToolbarProperties(
    val showBackArrow: Boolean = true,
    val showChatButton : Boolean = false
)

@Preview(showBackground = true, showSystemUi = true)
@Composable
fun DashboardToolbarPreview() {
    DashboardToolbar()
}


@HiltViewModel
class DashboardToolbarViewModel @Inject constructor(
    private val appCache: AppCache,
    private val commonSharedPreferences: CommonSharedPreferences,
    private val getNotificationListUseCase: NotificationUseCase,
) : AppViewModel() {

    private val _unreadNotificationCount = MutableStateFlow(0)

    val profileUrl: String
        get() = appCache.userBasicInfoDisplay?.profileImage.orEmpty()

    val sessionId: String
        get() = commonSharedPreferences.sessionId

    val unReadNotificationCount: StateFlow<Int>
        get() = _unreadNotificationCount


    init {
        _unreadNotificationCount.value = appCache.unreadNotificationCount
        listenNotification()
    }


    private fun listenNotification() {
        scope.launch {
            while (true) {
                _unreadNotificationCount.value = appCache.unreadNotificationCount
                delay(10_000L)
            }
        }
    }
}