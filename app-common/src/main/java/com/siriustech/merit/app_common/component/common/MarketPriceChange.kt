package com.siriustech.merit.app_common.component.common

import androidx.compose.foundation.Image
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.padding
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.tooling.preview.Preview
import com.core.util.toAmount
import com.siriustech.merit.app_common.theme.LocalDimens
import com.siriustech.merit.app_common.theme.LocalTypography

/**
 * Created by <PERSON><PERSON><PERSON>
 */

@Composable
fun MarketPriceChange(
    modifier: Modifier = Modifier,
    data: MarketPriceChangeDisplayData = MarketPriceChangeDisplayData(),
) {
    val priceChangeUIModel = onGetPriceChangeResource(priceChange = data.unrealizedGLRate)

    Column(modifier = Modifier.then(modifier), horizontalAlignment = Alignment.End) {
        Row(
            verticalAlignment = Alignment.CenterVertically,
            horizontalArrangement = Arrangement.Start
        ) {
            Text(
                text = data.marketValue.toAmount(4) ?: "0.0000",
                style = LocalTypography.current.text14.semiBold.copy(color = priceChangeUIModel.textColor),
            )
            Text(
                text = data.currency,
                modifier = Modifier.padding(start = LocalDimens.current.dimen1),
                style = LocalTypography.current.text12.semiBold.copy(color = priceChangeUIModel.textColor),
            )
            Image(
                modifier = Modifier.padding(start = LocalDimens.current.dimen1),
                painter = priceChangeUIModel.iconPainter,
                contentDescription = "Price Change Image Resource"
            )
        }
        PriceChangeRate(
            modifier = Modifier,
            priceChange = data.unrealizedGL,
            priceChangeRate = data.unrealizedGLRate
        )
    }
}

@Preview(showBackground = true, showSystemUi = true)
@Composable
fun PreviewMarketPriceChange() {
    MarketPriceChange(
        data = MarketPriceChangeDisplayData(
            marketValue = "50",
            currency = "USD",
            unrealizedGL = "60",
            unrealizedGLRate = "20"
        )
    )
}


data class MarketPriceChangeDisplayData(
    val marketValue: String = "",
    val currency: String = "",
    val unrealizedGL: String = "",
    val unrealizedGLRate: String = "",
)

