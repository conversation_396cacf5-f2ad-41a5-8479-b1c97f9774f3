package com.siriustech.merit.app_common.component.toggle

import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import com.siriustech.merit.app_common.R
import com.siriustech.merit.app_common.theme.LocalDimens
import com.siriustech.merit.app_common.theme.LocalTypography
import com.siriustech.merit.app_common.ext.colorTxtTitle

/**
 * Created by Hein Htet
 */
@Composable
fun ToggleRadioButton(
    defaultValue: Boolean,
    onToggleChanged: (isOn: Boolean) -> Unit = {},
) {
    val normalImage = painterResource(id = R.drawable.ic_rv_control_default)
    val selectedImage = painterResource(id = R.drawable.ic_rv_control_selected)
    Box(
        modifier = Modifier
            .size(LocalDimens.current.dimen24)
            .clickable {
                onToggleChanged(defaultValue)
            },
    ) {
        Image(
            modifier = Modifier,
            painter = if (defaultValue) selectedImage else normalImage, // Show image based on the toggle state
            contentDescription = if (defaultValue) "Toggle On" else "Toggle Off",
        )
    }
}

@Preview(showBackground = false)
@Composable
fun PreviewToggleRadioButton() {
    ToggleRadioButton(defaultValue = false)
}