package com.siriustech.merit.app_common.component.tableview


/**
 * Created by <PERSON><PERSON>
 */
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.horizontalScroll
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.verticalScroll
import androidx.compose.foundation.background
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.remember
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.platform.LocalConfiguration
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.Dp
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import coil3.compose.AsyncImage
import coil3.request.ImageRequest
import coil3.request.crossfade
import com.siriustech.merit.app_common.R
import com.siriustech.merit.app_common.component.container.PaddingBottom
import com.siriustech.merit.app_common.component.container.PaddingEnd
import com.siriustech.merit.app_common.component.tableview.TableViewHelper.calculateMaxColumnWidths
import com.siriustech.merit.app_common.ext.colorTxtInactive
import com.siriustech.merit.app_common.ext.colorTxtLabel
import com.siriustech.merit.app_common.ext.colorTxtParagraph
import com.siriustech.merit.app_common.theme.LocalAppColor
import com.siriustech.merit.app_common.theme.LocalDimens
import com.siriustech.merit.app_common.theme.LocalTypography
import timber.log.Timber

@Composable
fun ScrollableTableView(
    sections: List<TableSection>,
    properties: TableViewProperties = TableViewProperties(),
) {
    val horizontalScrollState = rememberScrollState()
    val configuration = LocalConfiguration.current
    val screenWidth = configuration.screenWidthDp.dp

    val columnWidths = remember(sections) {
        calculateMaxColumnWidths(sections, screenWidth)
    }

    Column(modifier = Modifier.fillMaxSize()) {

        // Headers
        Row(
            modifier = Modifier
                .background(properties.headerCellBackgroundColor)
                .horizontalScroll(horizontalScrollState)
        ) {
            sections.firstOrNull()?.columnHeaders?.forEachIndexed { index, header ->
                TableHeaderCell(header, columnWidths[index], properties)
            }
        }

        Spacer(modifier = Modifier.height(8.dp))

        Column(
            modifier = Modifier
                .fillMaxSize()
                .verticalScroll(rememberScrollState())
        ) {

            // Sections and Rows
            sections.forEachIndexed { index, section ->

                val color =
                    if (index % 2 == 0) LocalAppColor.current.bgPale else LocalAppColor.current.bgDefault
                // Section Title
                Column(
                    modifier = Modifier
                        .fillMaxWidth()
                        .heightIn(LocalDimens.current.dimen20)
                        .background(color),
                    verticalArrangement = Arrangement.Center,
                ) {
                    Text(
                        text = section.title,
                        style = LocalTypography.current.text10.regular.colorTxtLabel(),
                        modifier = Modifier.fillMaxSize(),
                    )
                }
                PaddingBottom(value = LocalDimens.current.dimen4)

                // Rows
                section.rows.forEach { row ->
                    Row(
                        modifier = Modifier
                            .horizontalScroll(horizontalScrollState)
                            .background(color)
                            .padding(top = LocalDimens.current.dimen2)
                    ) {
                        row.forEachIndexed { index, cell ->
                            if (cell is TableCellContent) {
                                TableCell(cell, columnWidths[index])
                            } else if (cell is TableAssetContent) {
                                TableAssetCell(cell, columnWidths[index])
                            }
                        }
                    }
                }
            }
        }
    }
}


@Composable
fun TableAssetCell(content: TableAssetContent, width: Dp) {
    Row(
        modifier = Modifier
            .width(width)
            .heightIn(34.dp)
            .padding(4.dp),
        verticalAlignment = Alignment.CenterVertically
    ) {
        AsyncImage(
            modifier = Modifier.size(LocalDimens.current.dimen14),
            model = ImageRequest.Builder(LocalContext.current)
                .data(content.logo)
                .crossfade(true)
                .build(),
            placeholder = painterResource(R.drawable.ic_product_category_placeholder),
            error = painterResource(R.drawable.ic_product_category_placeholder),
            contentDescription = "Product Category Placeholder",
            contentScale = ContentScale.Crop,
            onError = {
                Timber.d("Image Loading error ${it.result}")
            }
        )
        PaddingEnd(value = LocalDimens.current.dimen4)
        Text(
            text = content.content,
            style = LocalTypography.current.text10.light.colorTxtParagraph(),
            textAlign = TextAlign.Center
        )
    }
}

@Composable
fun TableCell(content: TableCellContent, width: Dp) {
    Box(
        modifier = Modifier
            .width(width)
            .heightIn(34.dp)
            .padding(4.dp),
        contentAlignment = Alignment.Center
    ) {
        Text(
            text = content.content,
            style = LocalTypography.current.text10.regular.merge(content.contentTextStyle),
            textAlign = TextAlign.Center
        )
    }
}

@Composable
fun TableHeaderCell(content: String, width: Dp, properties: TableViewProperties) {
    Box(
        modifier = Modifier
            .width(width)
            .heightIn(34.dp)
            .padding(4.dp),
        contentAlignment = Alignment.Center
    ) {
        Text(
            text = content,
            style = properties.headerCellTextStyle,
            textAlign = TextAlign.Center
        )
    }
}


@Preview(
    showSystemUi = true, showBackground = true,
    device = "spec:width=411dp,height=891dp,dpi=420,isRound=false,chinSize=0dp,orientation=landscape"
)
@Composable
fun PreviewTableView() {
    val sections = listOf(
        TableSection(
            title = "Section 1",
            columnHeaders = listOf(
                "Column 1 Column 1 ",
                "Column 2",
                "Column 3",
                "Column 4",
                "Column 5",
                "Column 6",
                "Column 7",
                "Column 8",
                "Column 9",
                "Column 10",
                "Column 11",
                "Column 12"
            ),
            rows = listOf(
                listOf(
                    TableAssetContent(content = "Row 1, Col 1 Row 1, Col 1"),
                    TableCellContent(content = "Asset01Asset01Asset01Asset01Asset01"),
                ),
                listOf(
                    TableAssetContent(content = "Row 1, Col 1 Row 1, Col 1"),
                    TableCellContent(content = "Asset01"),
                ),
                listOf(
                    TableAssetContent(content = "Row 1, Col 1 Row 1, Col 1"),
                    TableCellContent(content = "Asset01"),
                ),
            )
        ),
    )

    ScrollableTableView(
        sections, properties = TableViewProperties(
            headerCellTextStyle = LocalTypography.current.text10.light.colorTxtInactive(),
        )
    )
}
