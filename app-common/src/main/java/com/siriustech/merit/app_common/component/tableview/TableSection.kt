package com.siriustech.merit.app_common.component.tableview

import androidx.compose.ui.text.TextStyle

data class TableSection(
    val title: String,
    val columnHeaders: List<String> = emptyList(),
    val rows: List<List<TableCell>>,
)

open class TableCell {
    open val content :String = ""
    open var  contentTextStyle: TextStyle = TextStyle()

}

data class TableAssetContent(
    val logo: String? = null,
    override val content: String,
    override var contentTextStyle: TextStyle = TextStyle()

) : TableCell()

data class TableCellContent(
    override val content: String = "",
    override var contentTextStyle: TextStyle = TextStyle()
) : TableCell()