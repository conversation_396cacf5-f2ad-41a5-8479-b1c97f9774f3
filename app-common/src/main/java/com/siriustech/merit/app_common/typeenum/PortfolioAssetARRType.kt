package com.siriustech.merit.app_common.typeenum

import androidx.compose.runtime.Composable
import androidx.compose.runtime.ReadOnlyComposable
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.res.stringResource
import com.siriustech.merit.app_common.R
import com.siriustech.merit.app_common.theme.LocalAppColor

/**
 * Created by Hein Htet
 */

enum class PortfolioAssetARRType(val aarName: String) {
    ARR1("ARR1"),
    ARR2("ARR2"),
    ARR3("ARR3"),
    ARR4("ARR4"),
    ARR5("ARR5"),
    ARR6("ARR6"),
    ARR7("ARR7"),
    ARR8("ARR8"),
    ARR9("ARR9"),
    ARR10("ARR10");


    @Composable
    @ReadOnlyComposable
    fun getFillColor(): Color {
        return when (this) {
            ARR1 -> LocalAppColor.current.riskLevelFillLow1
            ARR2 -> LocalAppColor.current.riskLevelFillLow2
            ARR3 -> LocalAppColor.current.riskLevelFillLow3
            ARR4 -> LocalAppColor.current.riskLevelFillMedium4
            ARR5 -> LocalAppColor.current.riskLevelFillMedium5
            ARR6 -> LocalAppColor.current.riskLevelFillMedium6
            ARR7 -> LocalAppColor.current.riskLevelFillHigh7
            ARR8 -> LocalAppColor.current.riskLevelFillHigh8
            ARR9 -> LocalAppColor.current.riskLevelFillHigh9
            ARR10 -> LocalAppColor.current.riskLevelFillHigh10
        }
    }


    @Composable
    @ReadOnlyComposable
    fun getBackgroundColor(): Color {
        return when (this) {
            ARR1, ARR2, ARR3 -> LocalAppColor.current.riskLevelBgLow
            ARR4, ARR5, ARR6 -> LocalAppColor.current.riskLevelBgMedium
            else -> LocalAppColor.current.riskLevelBgHigh
        }
    }

    @Composable
    @ReadOnlyComposable
    fun getRiskLevel(): RiskLevel {
        return when (this) {
            ARR1, ARR2, ARR3 -> RiskLevel.LOW
            ARR4, ARR5, ARR6 ->  RiskLevel. MEDIUM
            ARR7, ARR8, ARR9,ARR10 ->  RiskLevel. HIGH
            else -> RiskLevel.PRO
        }
    }

    @Composable
    @ReadOnlyComposable
    fun getTextColor(): Color {
        return when (this) {
            ARR1, ARR2, ARR3 -> LocalAppColor.current.riskLevelTextLow
            ARR4, ARR5, ARR6 -> LocalAppColor.current.riskLevelTextMedium
            else -> LocalAppColor.current.riskLevelTextHigh
        }
    }

    @Composable
    @ReadOnlyComposable
    fun description(): String {
        return when (this) {
            ARR1 -> stringResource(id = R.string.key0423)
            ARR2 -> stringResource(id = R.string.key0424)
            ARR3 -> stringResource(id = R.string.key0425)
            ARR4 -> stringResource(id = R.string.key0426)
            ARR5 -> stringResource(id = R.string.key1060)
            ARR6 -> stringResource(id = R.string.key1063)
//            ARR7 -> stringResource(id = R.string.key0429)
//            ARR8 -> stringResource(id = R.string.key0430)
            ARR7 -> ""
            ARR8 -> ""
            ARR9 -> stringResource(id = R.string.key0431)
            ARR10 -> stringResource(id = R.string.key0432)
        }
    }

    companion object {
        fun fromParam(value: String): PortfolioAssetARRType? {
            return when (value) {
                ARR1.aarName -> ARR1
                ARR2.aarName -> ARR2
                ARR3.aarName -> ARR3
                ARR4.aarName -> ARR4
                ARR5.aarName -> ARR5
                ARR6.aarName -> ARR6
                ARR7.aarName -> ARR7
                ARR8.aarName -> ARR8
                ARR9.aarName -> ARR9
                ARR10.aarName -> ARR10
                else -> null
            }
        }
    }
}