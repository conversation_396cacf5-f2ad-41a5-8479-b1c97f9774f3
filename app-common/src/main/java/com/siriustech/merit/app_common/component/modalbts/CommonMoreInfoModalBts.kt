package com.siriustech.merit.app_common.component.modalbts

import androidx.compose.foundation.Image
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.ModalBottomSheet
import androidx.compose.material3.Text
import androidx.compose.material3.rememberModalBottomSheetState
import androidx.compose.runtime.Composable
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.tooling.preview.Preview
import com.siriustech.merit.app_common.R
import com.siriustech.merit.app_common.component.container.PaddingStart
import com.siriustech.merit.app_common.component.container.PaddingTop
import com.siriustech.merit.app_common.ext.colorTxtTitle
import com.siriustech.merit.app_common.ext.noRippleClickable
import com.siriustech.merit.app_common.theme.LocalAppColor
import com.siriustech.merit.app_common.theme.LocalDimens
import com.siriustech.merit.app_common.theme.LocalTypography
import kotlinx.coroutines.launch

/**
 * Created by Hein Htet
 */

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun CommonMoreInfoModalBts(
    modifier: Modifier = Modifier,
    menus: List<CommonMoreInfoModalData> = emptyList(),
    onDismissed: () -> Unit = {},
) {
    val sheetState = rememberModalBottomSheetState(skipPartiallyExpanded = true)
    val scope = rememberCoroutineScope()

    fun onDismiss() {
        scope.launch {
            sheetState.hide()
            onDismissed()
        }
    }

    ModalBottomSheet(
        modifier = modifier,
        dragHandle = {},
        shape = RoundedCornerShape(LocalDimens.current.dimen4),
        containerColor = LocalAppColor.current.bgDefault,
        onDismissRequest = {
            onDismissed()
        },
        sheetState = sheetState,
    ) {
        CommonMoreInfoContent(menus = menus,
            onDismissed = {
                onDismiss()
            })
    }
}

@Composable
fun CommonMoreInfoContent(
    menus: List<CommonMoreInfoModalData> = emptyList(),
    onDismissed: () -> Unit = {},
) {
    Column(
        modifier = Modifier
            .fillMaxWidth()
            .padding(LocalDimens.current.dimen12)
    ) {
        Row {
            Text(
                text = stringResource(id = R.string.key0410),
                style = LocalTypography.current.text14.semiBold.colorTxtTitle()
            )
            Spacer(modifier = Modifier.weight(1f))
            Image(
                painter = painterResource(id = R.drawable.ic_action_close),
                contentDescription = "Close Action",
                modifier = Modifier
                    .noRippleClickable { onDismissed() }
            )
        }
        PaddingTop(value = LocalDimens.current.dimen12)
        menus.forEachIndexed { index, item ->
            Row(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(LocalDimens.current.dimen8)
                    .noRippleClickable {
                        item.onClick()
                        onDismissed()
                    },
                verticalAlignment = Alignment.CenterVertically
            ) {
                if (item.iconResId != null) {
                    Image(
                        painter = painterResource(id = item.iconResId),
                        contentDescription = "Menu Image"
                    )
                    PaddingStart(value = LocalDimens.current.dimen8)
                    Text(
                        text = item.title,
                        style = LocalTypography.current.text14.regular.colorTxtTitle()
                    )
                }
            }
        }
    }
}


@Preview(showBackground = true, showSystemUi = true)
@Composable
fun PreviewCommonMoreInfoModalBts() {
    CommonMoreInfoContent(
        menus = listOf(
            CommonMoreInfoModalData(
                title = "Copy",
                iconResId = R.drawable.ic_action_refersh
            ),
            CommonMoreInfoModalData(
                title = "Refresh",
                iconResId = R.drawable.ic_action_refersh
            )
        )
    )
}

data class CommonMoreInfoModalData(
    val title: String,
    val iconResId: Int? = null,
    val onClick: () -> Unit = {},
)