package com.siriustech.merit.app_common.component.common

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.padding
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.painter.Painter
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.tooling.preview.Preview
import com.core.util.toAmount
import com.siriustech.merit.app_common.R
import com.siriustech.merit.app_common.data.display.PriceChangeUIModel
import com.siriustech.merit.app_common.ext.colorTxtCaution
import com.siriustech.merit.app_common.ext.colorTxtInactive
import com.siriustech.merit.app_common.ext.colorTxtParagraph
import com.siriustech.merit.app_common.ext.displayPriceChange
import com.siriustech.merit.app_common.ext.displayPriceChangeRate
import com.siriustech.merit.app_common.ext.isPriceDown
import com.siriustech.merit.app_common.ext.isPriceUp
import com.siriustech.merit.app_common.ext.isPriceZero
import com.siriustech.merit.app_common.theme.LocalAppColor
import com.siriustech.merit.app_common.theme.LocalDimens
import com.siriustech.merit.app_common.theme.LocalTypography

@Composable
fun CashBalance(
    modifier: Modifier = Modifier,
    cashBalance: String? = ""
) {
    if (cashBalance.isNullOrEmpty()) {
        return
    }
    Row(
        modifier = Modifier.then(modifier),
        horizontalArrangement = Arrangement.Start,
        verticalAlignment = Alignment.Bottom
    ) {
        Box(
            modifier = Modifier.background(LocalAppColor.current.bgCaution),
        ) {
            Text(
                modifier = Modifier
                    .padding(
                        horizontal = LocalDimens.current.dimen6,
                        vertical = LocalDimens.current.dimen2
                    ),
                maxLines = 1,
                overflow = TextOverflow.Ellipsis,
                text = "Cash:" ?: "0.0%",
                style = LocalTypography.current.text10.regular.colorTxtCaution()
                //style = LocalTypography.current.text12.medium.copy(color = priceChangeUIModel.textColor)
            )
        }
        Text(
            modifier = Modifier
                .padding(
                    end = LocalDimens.current.dimen2
                ),
            text = cashBalance ?: "0.00",
            style = LocalTypography.current.text14.medium.colorTxtParagraph()
        )
        Text(
            modifier = Modifier
                .padding(
                    top = LocalDimens.current.dimen2,
                    end = LocalDimens.current.dimen6
                ),
            text = "USD" ?: "0.00",
            style = LocalTypography.current.text12.regular.colorTxtInactive()
        )
    }
}


@Preview
@Composable
fun PreviewCashBalance() {
    CashBalance(
        modifier = Modifier.background(LocalAppColor.current.bgDefault),
        cashBalance = "55"
    )
}


