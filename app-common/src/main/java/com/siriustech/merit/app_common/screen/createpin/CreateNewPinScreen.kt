package com.siriustech.merit.app_common.screen.createpin

import androidx.activity.compose.BackHandler
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.pager.HorizontalPager
import androidx.compose.foundation.pager.rememberPagerState
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.runtime.snapshotFlow
import androidx.compose.ui.Modifier
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.AnnotatedString
import androidx.compose.ui.tooling.preview.Preview
import androidx.lifecycle.viewmodel.compose.viewModel
import androidx.navigation.NavController
import androidx.navigation.compose.rememberNavController
import com.siriustech.core_ui_compose.base.SingleEventEffect
import com.siriustech.core_ui_compose.component.CommonToolbar
import com.siriustech.merit.app_common.Constants
import com.siriustech.merit.app_common.Constants.DEFAULT_OTP_COUNT
import com.siriustech.merit.app_common.component.alert.BannerAlertProperties
import com.siriustech.merit.app_common.component.alert.BannerAlertType
import com.siriustech.merit.app_common.component.header.defaultToolbarProperties
import com.siriustech.merit.app_common.component.indicator.HorizontalPagerIndicator
import com.siriustech.merit.app_common.component.pin.PinView
import com.siriustech.merit.app_common.component.pin.PinViewProperties
import com.siriustech.merit.app_common.ext.getToolbarStepLabel
import com.siriustech.merit.app_common.ext.navigateReplaceAll
import com.siriustech.merit.app_common.ext.popBackWithResult
import com.siriustech.merit.app_common.navigation.Dashboard
import com.siriustech.merit.app_common.theme.AppScreen
import com.siriustech.merit.app_common.theme.LocalDimens
import com.siriustech.merit.app_common.typeenum.CreateNewPinStep
import kotlinx.coroutines.launch
import com.siriustech.merit.app_common.R as AppCommonR

/**
 * Created by Hein Htet
 */

@Composable
fun CreateNewPinScreen(
    viewModel: CreateNewPinViewModel = viewModel(),
    navController: NavController,
    includeBiometricSetup: Boolean = true,
    needCallback: Boolean = false,
) {
    val pagerState =
        rememberPagerState(pageCount = { if (includeBiometricSetup) CreateNewPinStep.TOTAL_STEP else 2 })
    val coroutineScope = rememberCoroutineScope()


    fun onNavigateNextPage() {
        coroutineScope.launch {
            pagerState.animateScrollToPage(pagerState.currentPage + 1)
        }
    }

    fun onNavigateToPage(page: Int) {
        coroutineScope.launch {
            pagerState.animateScrollToPage(page)
        }
    }

    fun onHandleBack() {
        when {
            // it step is already configured
            pagerState.currentPage == 2 -> {
                navController.navigateReplaceAll(Dashboard)
            }

            pagerState.currentPage != 0 -> {
                coroutineScope.launch { pagerState.animateScrollToPage(pagerState.currentPage - 1) }
            }

            else -> {
                navController.popBackStack()
            }
        }
    }


    BackHandler(enabled = true, onBack = {
        onHandleBack()
    })

    val currentStep = viewModel.outputs.currentStep.collectAsState()
    val createNewPinCode = viewModel.outputs.createPin.collectAsState()
    val confirmPinCode = viewModel.outputs.confirmPin.collectAsState()

    val activity = LocalContext.current

    fun onHandleEvent(it: CreatePinCodeEvent) {
        when (it) {
            CreatePinCodeEvent.OnPinCodeMatched -> {
                viewModel.inputs.onConfigurePin()
            }

            CreatePinCodeEvent.OnConfiguredPinCode -> {
                if (needCallback && !includeBiometricSetup) {
                    navController.popBackWithResult(
                        Constants.NAV_RETURN_SUCCESS,
                        Constants.STATUS_PASSED
                    )
                } else {
                    onNavigateNextPage()
                }
            }


            is CreatePinCodeEvent.PinUnMatchExceed -> {
                viewModel.emitBannerAlert(
                    BannerAlertProperties(
                        title = activity.getString(AppCommonR.string.key0937),
                        description = activity.getString(AppCommonR.string.key0938),
                    )
                )
                onNavigateToPage(CreateNewPinStep.CREATE_PIN.step)
            }

            is CreatePinCodeEvent.PinUnMatched -> {
                viewModel.emitBannerAlert(
                    BannerAlertProperties(
                        title = activity.getString(AppCommonR.string.key0942),
                        description = activity.getString(
                            AppCommonR.string.key0943,
                            it.count.toString()
                        ),
                        type = BannerAlertType.ALERT_ERROR
                    )
                )
            }
        }
    }

    SingleEventEffect(sideEffectFlow = viewModel.outputs.createPinCodeEvent) {
        it?.let { onHandleEvent(it) }
    }


    LaunchedEffect(pagerState) {
        snapshotFlow { pagerState.currentPage }.collect { page ->
            viewModel.inputs.updateCurrentStep(page)
        }
    }

    AppScreen(vm = viewModel) {
        Column(modifier = Modifier.fillMaxSize()) {
            CreateNewPinToolbar(currentStep = currentStep.value, onResetPin = {
                viewModel.inputs.onResetPin()
            }, onBackPressed = {
                onHandleBack()
            })
            HorizontalPagerIndicator(
                pagerState = pagerState,
                modifier = Modifier.padding(top = LocalDimens.current.dimen32)
            )
            HorizontalPager(
                userScrollEnabled = false,
                state = pagerState,
                modifier = Modifier.fillMaxSize()
            ) { page ->
                val step = CreateNewPinStep.fromParam(page)
                when (step) {
                    CreateNewPinStep.CREATE_PIN -> CreatePinView(createNewPinCode.value,
                        PinViewProperties(
                            title = stringResource(id = AppCommonR.string.key0217),
                            subTitle = stringResource(id = AppCommonR.string.key0218)
                        ),
                        onPinCodeChanged = {
                            viewModel.inputs.onCreateNewPinChange(it)
                        },
                        onPinCodeCreated = { code ->
                            onNavigateNextPage()
                        })

                    CreateNewPinStep.CONFIRM_PIN -> CreatePinView(confirmPinCode.value,
                        PinViewProperties(
                            title = stringResource(id = AppCommonR.string.key0219),
                            subTitle = stringResource(id = AppCommonR.string.key0220)
                        ),
                        onPinCodeChanged = {
                            viewModel.inputs.onConfirmPinChange(it)
                        },
                        onPinCodeCreated = { code ->
                            viewModel.inputs.onComparePinCode()
                        })

                    CreateNewPinStep.ENABLE_BIOMETRIC -> EnableBiometricScreen(
                        navController,
                        needCallback = needCallback,
                        onBiometricSetupSuccess = {
                            viewModel.inputs.onBiometricSetupSuccess()
                        })
                }
            }
        }
    }
}


@Composable
fun CreatePinView(
    pinCodeDefaultValue: String,
    properties: PinViewProperties,
    onPinCodeChanged: (code: String) -> Unit = {},
    onPinCodeCreated: (code: String) -> Unit = {},
) {

    PinView(
        pinCodeDefaultValue = pinCodeDefaultValue,
        modifier = Modifier.fillMaxWidth(),
        properties = properties,
        onPinCodeChanged = {
            onPinCodeChanged(it)
            if (it.length == DEFAULT_OTP_COUNT) {
                onPinCodeCreated(it)
            }
        })
}

@Composable
fun CreateNewPinToolbar(
    currentStep: Int,
    onBackPressed: () -> Unit = {},
    onResetPin: () -> Unit = {},
) {
    CommonToolbar(
        onLeftActionClicked = onBackPressed,
        onRightActionClicked = onResetPin,
        properties = defaultToolbarProperties().copy(
            annotatedTitleString = getCurrentStepToolbarTitle(currentStep),
            rightActionResId = AppCommonR.drawable.ic_action_refersh,
            leftActionResId = AppCommonR.drawable.ic_back_arrow
        )
    )
}

@Composable
fun getCurrentStepToolbarTitle(page: Int): AnnotatedString {
    return when (CreateNewPinStep.fromParam(page)) {
        CreateNewPinStep.CREATE_PIN -> getToolbarStepLabel(
            stepNo = stringResource(id = AppCommonR.string.key0003),
            stepLabel = stringResource(id = AppCommonR.string.key0217)
        )

        CreateNewPinStep.CONFIRM_PIN -> getToolbarStepLabel(
            stepNo = stringResource(id = AppCommonR.string.key0004),
            stepLabel = stringResource(id = AppCommonR.string.key0219)
        )

        CreateNewPinStep.ENABLE_BIOMETRIC -> getToolbarStepLabel(
            stepNo = stringResource(id = AppCommonR.string.key0005),
            stepLabel = stringResource(id = AppCommonR.string.key0221)
        )
    }
}

@Preview(showBackground = true, showSystemUi = true)
@Composable
fun PreviewCreateNewPinScreenPreview() {
    CreateNewPinScreen(navController = rememberNavController())
}