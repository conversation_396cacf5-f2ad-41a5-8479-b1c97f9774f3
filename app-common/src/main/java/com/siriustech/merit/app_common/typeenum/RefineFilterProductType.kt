package com.siriustech.merit.app_common.typeenum

import android.content.Context

/**
 * Created by <PERSON><PERSON>t
 */
enum class RefineFilterProductType(val value : String) {
    ALL("ALL"),
    US_EQUITY("US_EQUITY"),
    HK_EQUITY("HK_EQUITY"),
    MUTUAL_FUNDS("MUTUAL_FUNDS"),
    PRIVATE_EQUITY_FUNDS("PRIVATE_EQUITY_FUNDS"),
    <PERSON>ON<PERSON>("BONDS"),
    STRUCTURED_PRODUCTS("STRUCTURED_PRODUCTS");

    fun displayName(context: Context) : String {
        return when(this) {
            ALL -> context.getString(com.siriustech.merit.app_common.R.string.key0466)
            US_EQUITY -> context.getString(com.siriustech.merit.app_common.R.string.key0467)
            HK_EQUITY -> context.getString(com.siriustech.merit.app_common.R.string.key0468)
            MUTUAL_FUNDS -> context.getString(com.siriustech.merit.app_common.R.string.key0469)
            PRIVATE_EQUITY_FUNDS -> context.getString(com.siriustech.merit.app_common.R.string.key0470)
            BONDS -> context.getString(com.siriustech.merit.app_common.R.string.key0471)
            STRUCTURED_PRODUCTS -> context.getString(com.siriustech.merit.app_common.R.string.key0472)
        }
    }

    companion object {
        fun fromParam(value:String) : RefineFilterProductType? {
            return when(value) {
                ALL.value -> ALL
                US_EQUITY.value -> US_EQUITY
                HK_EQUITY.value -> HK_EQUITY
                MUTUAL_FUNDS.value -> MUTUAL_FUNDS
                PRIVATE_EQUITY_FUNDS.value -> PRIVATE_EQUITY_FUNDS
                BONDS.value -> BONDS
                STRUCTURED_PRODUCTS.value -> STRUCTURED_PRODUCTS
                else -> null
            }
        }
    }
}