package com.siriustech.merit.app_common.screen.accountclosure

import android.os.Bundle
import androidx.activity.compose.setContent
import androidx.compose.foundation.layout.Column
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.remember
import androidx.compose.ui.Alignment
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.toArgb
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.fragment.app.FragmentActivity
import androidx.hilt.navigation.compose.hiltViewModel
import com.siriustech.core_ui_compose.base.BaseComposeActivity
import com.siriustech.core_ui_compose.ext.ChangeSystemBarsTheme
import com.siriustech.merit.app_common.R
import com.siriustech.merit.app_common.component.container.PaddingTop
import com.siriustech.merit.app_common.ext.colorTxtParagraph
import com.siriustech.merit.app_common.navigation.AppCommonNavigationEntryPoint
import com.siriustech.merit.app_common.screen.CommonSuccessScreen
import com.siriustech.merit.app_common.screen.CommonSuccessScreenProperties
import com.siriustech.merit.app_common.theme.AppScreen
import com.siriustech.merit.app_common.theme.LocalDimens
import com.siriustech.merit.app_common.theme.LocalTypography
import dagger.hilt.android.AndroidEntryPoint
import dagger.hilt.android.EntryPointAccessors

/**
 * Created by Hein Htet
 */
@AndroidEntryPoint
class AccountClosureActivity : BaseComposeActivity() {
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContent {
            ChangeSystemBarsTheme(lightTheme = true, Color.White.toArgb())
            AccountClosure()
        }
    }
}

@Composable
fun AccountClosure(viewModel: AccountClosureViewModel = hiltViewModel()) {
    val activity = LocalContext.current as FragmentActivity
    val navigation = remember {
        EntryPointAccessors.fromApplication(
            activity,
            AppCommonNavigationEntryPoint::class.java
        ).appCommonNavigation()
    }
    AppScreen(vm = viewModel, ignorePaddingValue = true) {
        CommonSuccessScreen(properties = CommonSuccessScreenProperties(
            title = stringResource(id = R.string.key0804),
            iconPainter = painterResource(
                id = R.drawable.ic_hand_circle,
            ),
            accentButtonText = stringResource(id = R.string.key0808),
//            secondaryButtonText = stringResource(id = R.string.key0807)

        ), onAccentButtonClick = {
            viewModel.clearData()
            navigation.onNavigateToLoginActivity(activity)
        }) {
            Column(horizontalAlignment = Alignment.Start) {
                Text(
                    text = stringResource(id = R.string.key0805),
                    style = LocalTypography.current.text14.light.colorTxtParagraph()
                )
                PaddingTop(value = LocalDimens.current.dimen8)
                Text(
                    text = stringResource(id = R.string.key0806),
                    style = LocalTypography.current.text14.light.colorTxtParagraph()
                )
            }
        }
    }
}