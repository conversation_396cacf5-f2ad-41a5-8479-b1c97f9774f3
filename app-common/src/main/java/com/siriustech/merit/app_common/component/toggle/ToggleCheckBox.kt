package com.siriustech.merit.app_common.component.toggle

import androidx.compose.foundation.Image
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.size
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Modifier
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.tooling.preview.Preview
import com.siriustech.merit.app_common.R
import com.siriustech.merit.app_common.theme.LocalDimens

/**
 * Created by <PERSON><PERSON>tet
 */
@Composable
fun ToggleCheckBox(
    defaultValue: Boolean,
    onToggleChanged: (isOn: Boolean) -> Unit = {},
) {
    var isChecked by remember { mutableStateOf(defaultValue) }
    val normalImage = painterResource(id = R.drawable.ic_checkbox_normal)
    val selectedImage = painterResource(id = R.drawable.ic_checkbox_selected)


    LaunchedEffect(defaultValue) {
        isChecked = defaultValue
    }

    Box(
        modifier = Modifier
            .size(LocalDimens.current.dimen24)
            .clickable {
                isChecked = !isChecked
                onToggleChanged(defaultValue)
            },
    ) {
        Image(
            modifier = Modifier,
            painter = if (isChecked) selectedImage else normalImage, // Show image based on the toggle state
            contentDescription = if (isChecked) "Toggle On" else "Toggle Off",
        )
    }
}

@Preview(showBackground = false)
@Composable
fun PreviewToggleCheckBox() {
    ToggleCheckBox(defaultValue = false)
}