package com.siriustech.merit.app_common.component.button

import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.ColorFilter
import androidx.compose.ui.graphics.painter.Painter
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import com.siriustech.merit.app_common.R
import com.siriustech.merit.app_common.component.container.PaddingEnd
import com.siriustech.merit.app_common.ext.colorTxtInactive
import com.siriustech.merit.app_common.ext.colorTxtTitle
import com.siriustech.merit.app_common.ext.noRippleClickable
import com.siriustech.merit.app_common.theme.LocalAppColor
import com.siriustech.merit.app_common.theme.LocalDimens
import com.siriustech.merit.app_common.theme.LocalTypography
import com.siriustech.merit.app_common.typeenum.UIConfig.TOGGLE_BUTTON_CORNER_RADIUS

/**
 * Created by Hein Htet
 */


@Composable
fun ToggleThirdButton(
    modifier: Modifier = Modifier,
    isSelectedValue: Boolean = false,
    properties: ToggleThirdButtonProperties = ToggleThirdButtonProperties(),
    onSelectedChanged: (value: Boolean) -> Unit = {},
) {

    var isSelected by remember {
        mutableStateOf(isSelectedValue)
    }

    LaunchedEffect(isSelectedValue) {
        isSelected = isSelectedValue
    }

    Box(
        modifier = modifier
            .height(LocalDimens.current.dimen44)
            .clip(RoundedCornerShape(TOGGLE_BUTTON_CORNER_RADIUS.dp))
            .background(if (isSelected) LocalAppColor.current.btn3rd else LocalAppColor.current.bgDefault)
            .noRippleClickable {
                isSelected = !isSelected
                onSelectedChanged(isSelected)
            },
        contentAlignment = Alignment.Center
    ) {
        Row(verticalAlignment = Alignment.CenterVertically) {
            if (properties.startIcon != null) {
                Image(
                    painter = properties.startIcon, contentDescription = "Start Image Resource",
                    colorFilter = if (isSelected) null else ColorFilter.tint(
                        LocalAppColor.current.txtInactive
                    )
                )
                PaddingEnd(value = LocalDimens.current.dimen4)
            }
            Text(
                maxLines = 1,
                overflow = TextOverflow.Ellipsis,
                text = properties.text,
                style = if (isSelected) LocalTypography.current.text14.medium.colorTxtTitle() else LocalTypography.current.text14.light.colorTxtInactive()
            )
        }
    }
}

data class ToggleThirdButtonProperties(
    val text: String = "",
    val startIcon: Painter? = null,
)

@Preview(showBackground = true, showSystemUi = true)
@Composable
fun PreviewToggleThirdButton() {
    Column(modifier = Modifier.padding(LocalDimens.current.dimen24)) {
        ToggleThirdButton(
            isSelectedValue = true,
            modifier = Modifier.fillMaxWidth(),
            properties = ToggleThirdButtonProperties(
                startIcon = painterResource(id = R.drawable.ic_exchange_price_up),
                text = "% Change"
            )
        )
    }
}