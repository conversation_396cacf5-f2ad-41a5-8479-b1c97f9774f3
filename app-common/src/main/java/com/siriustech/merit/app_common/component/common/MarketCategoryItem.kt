package com.siriustech.merit.app_common.component.common

import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.RowScope
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.painter.Painter
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.siriustech.merit.app_common.R
import com.siriustech.merit.app_common.component.text.AutoSizeText
import com.siriustech.merit.app_common.data.display.AssetClassModel
import com.siriustech.merit.app_common.ext.colorTxtParagraph
import com.siriustech.merit.app_common.ext.noRippleClickable
import com.siriustech.merit.app_common.theme.LocalAppColor
import com.siriustech.merit.app_common.theme.LocalDimens
import com.siriustech.merit.app_common.theme.LocalTypography
import com.siriustech.merit.app_common.typeenum.PortfolioAssetType
import com.siriustech.merit.app_common.typeenum.RiskLevel

/**
 * Created by Hein Htet
 */
@Composable
fun RowScope.MarketCategoryItem(
    allowShrinkText: Boolean = false,
    isExtraBox: Boolean = false,
    item: MarketCategoryContentData,
    isSelected: Boolean = false,
    onItemClicked: (item: MarketCategoryContentData) -> Unit = {},
) {
    val itemModifier = Modifier
        .padding(vertical = LocalDimens.current.dimen2, horizontal = LocalDimens.current.dimen1)
        .height(80.dp)
        .weight(1f)
        .clip(RoundedCornerShape(4.dp))
        .background(if (isExtraBox) Color.Transparent else if (isSelected) LocalAppColor.current.btn3rd else LocalAppColor.current.bgTone)
        .noRippleClickable { onItemClicked(item) }
    if (isExtraBox) {
        Spacer(modifier = itemModifier)
    } else {
        Column(
            modifier = itemModifier,
            horizontalAlignment = Alignment.CenterHorizontally,
            verticalArrangement = Arrangement.Center,
        ) {
            if (item.icon != null) {
                Image(painter = item.icon, contentDescription = "Item Image Resource")
            }
            if (item.riskLevel != null) {
                item.riskLevel.RiskLevelBadge()
            }
            Box(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(horizontal = LocalDimens.current.dimen14),
                contentAlignment = Alignment.Center
            ) {
                if (allowShrinkText) {
                    AutoSizeText(
                        maxFontSize = 14.sp,
                        minFontSize = 8.sp,
                        text = item.title,
                        style = LocalTypography.current.text14.medium.colorTxtParagraph(),
                        textAlign = TextAlign.Center
                    )
                } else {
                    Text(
                        text = item.title,
                        style = LocalTypography.current.text14.medium.colorTxtParagraph(),
                        textAlign = TextAlign.Center
                    )
                }
            }

        }
    }
}

data class MarketCategoryContentData(
    val riskLevel: RiskLevel? = null,
    val allocationClassType: PortfolioAssetType? = null,
    val title: String = "",
    val icon: Painter? = null,
    val other: String? = null,
    val assetClassModel: AssetClassModel? = null,
)

@Preview
@Composable
fun PreviewMarketCategoryItem() {
    Row(modifier = Modifier.fillMaxWidth()) {
        MarketCategoryItem(
            isSelected = true,
            item = MarketCategoryContentData(
                allocationClassType = PortfolioAssetType.CASH_EQUIVALENT,
                title = "Structured Products",
                icon = painterResource(id = R.drawable.ic_recommended_thumb),
            )
        )
    }
}