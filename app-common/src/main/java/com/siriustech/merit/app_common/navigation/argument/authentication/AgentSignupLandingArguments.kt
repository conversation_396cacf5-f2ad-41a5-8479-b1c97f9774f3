package com.siriustech.merit.app_common.navigation.argument.authentication

import androidx.lifecycle.SavedStateHandle
import androidx.navigation.toRoute
import com.siriustech.merit.app_common.ext.serializableType
import com.siriustech.merit.app_common.navigation.AgentSignupLanding
import com.siriustech.merit.app_common.navigation.Verification2FA
import com.siriustech.merit.app_common.typeenum.Auth2FAType
import com.siriustech.merit.app_common.typeenum.BizType
import kotlin.reflect.typeOf
import kotlinx.serialization.Serializable

/**
 * Created by <PERSON><PERSON>t
 */

@Serializable
data class AgentSignupLandingArguments(
    val email: String,
    val phone: String,
    val password : String
) {
    companion object {
        val typeMap =
            mapOf(typeOf<AgentSignupLandingArguments>() to serializableType<AgentSignupLandingArguments>())

        fun from(savedStateHandle: SavedStateHandle) =
            savedStateHandle.toRoute<AgentSignupLanding>(typeMap)
    }
}
