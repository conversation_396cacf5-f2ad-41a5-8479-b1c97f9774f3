package com.siriustech.merit.app_common.screen.setuppin

import androidx.compose.foundation.Image
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxHeight
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.tooling.preview.Preview
import androidx.navigation.NavController
import androidx.navigation.compose.rememberNavController
import com.siriustech.core_ui_compose.component.CommonToolbar
import com.siriustech.merit.app_common.component.button.ButtonProperties
import com.siriustech.merit.app_common.component.button.SecondaryBorderButton
import com.siriustech.merit.app_common.component.button.SecondaryButton
import com.siriustech.merit.app_common.component.header.defaultToolbarProperties
import com.siriustech.merit.app_common.ext.colorTxtParagraph
import com.siriustech.merit.app_common.ext.colorTxtTitle
import com.siriustech.merit.app_common.ext.navigateReplaceAll
import com.siriustech.merit.app_common.navigation.CreateNewPin
import com.siriustech.merit.app_common.navigation.Dashboard
import com.siriustech.merit.app_common.theme.AppScreen
import com.siriustech.merit.app_common.theme.AppViewModel
import com.siriustech.merit.app_common.theme.LocalDimens
import com.siriustech.merit.app_common.theme.LocalTypography
import com.siriustech.merit.app_common.R as AppCommonR

/**
 * Created by Hein Htet
 */

@Composable
fun SetupPinLandingScreen(navController: NavController) {
    AppScreen(vm = AppViewModel()) {
        Column(
            modifier = Modifier
                .fillMaxSize()
        ) {
            CommonToolbar(
                onLeftActionClicked = {
                    navController.popBackStack()
                },
                properties = defaultToolbarProperties().copy(title = stringResource(id = AppCommonR.string.key0216))
            )
            Column(
                modifier = Modifier
                    .padding(LocalDimens.current.dimen12)
            ) {
                Spacer(modifier = Modifier.fillMaxHeight(0.15f))
                Column(
                    modifier = Modifier
                        .weight(1f)
                        .fillMaxWidth()
                        .fillMaxSize(),
                ) {
                    Image(
                        painter = painterResource(id = AppCommonR.drawable.ic_setup_pin_landing),
                        contentDescription = "Setup 2FA Resource",
                        modifier = Modifier.align(Alignment.CenterHorizontally)
                    )
                    Spacer(modifier = Modifier.height(LocalDimens.current.dimen32))
                    Text(
                        text = stringResource(id = AppCommonR.string.key0212),
                        textAlign = TextAlign.Center,
                        modifier = Modifier.fillMaxWidth(),
                        style = LocalTypography.current.text18.semiBold.colorTxtTitle()
                    )
                    Spacer(modifier = Modifier.height(LocalDimens.current.dimen16))

                    Text(
                        text = stringResource(id = AppCommonR.string.key0213)
//                        text = buildAttrString(
//                            attributedString = arrayListOf(
//                                AttributeStringData(
//                                    text = stringResource(id = AppCommonR.string._key0031),
//                                    textStyle = LocalTypography.current.text14.light.colorTxtParagraph()
//                                ),
//                                AttributeStringData(
//                                    text = stringResource(id = AppCommonR.string._key0032),
//                                    textStyle = LocalTypography.current.text14.regular.colorTxtTitle()
//                                ),
//                                AttributeStringData(
//                                    text = stringResource(id = AppCommonR.string._key0033),
//                                    textStyle = LocalTypography.current.text14.light.colorTxtParagraph()
//                                ),
//                            )
//                        ),
                                ,
                        style = LocalTypography.current.text14.light.colorTxtParagraph(),
                        modifier = Modifier.fillMaxWidth()
                    )
                    Spacer(modifier = Modifier.height(LocalDimens.current.dimen32))
                    SecondaryButton(
                        properties = ButtonProperties(
                            text = stringResource(id = AppCommonR.string.key0215),
                        ), onClicked = {
                            navController.navigate(CreateNewPin)
                        })
                    Spacer(modifier = Modifier.height(LocalDimens.current.dimen12))
                    SecondaryBorderButton(
                        properties = ButtonProperties(
                            text = stringResource(id = AppCommonR.string.key0197),
                        ), onClicked = {
                            navController.navigateReplaceAll(Dashboard)
                        })
                }

            }
        }
    }
}

@Preview(showBackground = true, showSystemUi = true)
@Composable
fun PreviewSetupPinLandingScreen() {
    SetupPinLandingScreen(rememberNavController())
}