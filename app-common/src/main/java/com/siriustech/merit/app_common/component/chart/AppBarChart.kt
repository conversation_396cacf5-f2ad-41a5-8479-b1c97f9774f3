package com.siriustech.merit.app_common.component.chart

import android.graphics.Color
import android.view.MotionEvent
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.viewinterop.AndroidView
import androidx.core.content.res.ResourcesCompat
import com.github.mikephil.charting.charts.BarChart
import com.github.mikephil.charting.components.YAxis
import com.github.mikephil.charting.data.BarData
import com.github.mikephil.charting.listener.ChartTouchListener
import com.github.mikephil.charting.listener.OnChartGestureListener
import com.siriustech.merit.app_common.R
import com.siriustech.merit.app_common.component.chart.renderer.RoundedBarChartRenderer

/**
 * Created by Hein Htet
 */

@Composable
fun AppBarChart(
    modifier: Modifier = Modifier,
    barChart: BarChart,
    barData: BarData,
    onConfigChart: (chart: BarChart) -> Unit = {},
    onUpdatedChart : (chart :BarChart) ->Unit = {}
) {
    AndroidView(
        modifier = modifier,
        factory = { context ->
            val typeface = ResourcesCompat.getFont(context, R.font.noto_sans_medium)
            barChart.also { chart ->
                    chart.onChartGestureListener = object : OnChartGestureListener {
                        override fun onChartGestureStart(
                            me: MotionEvent?,
                            lastPerformedGesture: ChartTouchListener.ChartGesture?,
                        ) {

                        }

                        override fun onChartGestureEnd(
                            me: MotionEvent?,
                            lastPerformedGesture: ChartTouchListener.ChartGesture?,
                        ) {
                        }

                        override fun onChartLongPressed(me: MotionEvent?) {
                        }

                        override fun onChartDoubleTapped(me: MotionEvent?) {
                        }

                        override fun onChartSingleTapped(me: MotionEvent?) {
                            me?.let {
                                val highlight = chart.getHighlightByTouchPoint(me.x, me.y)
                                if (highlight != null) {
                                    chart.highlightValue(highlight)
                                }
                            }
                        }

                        override fun onChartFling(
                            me1: MotionEvent?,
                            me2: MotionEvent?,
                            velocityX: Float,
                            velocityY: Float,
                        ) {
                        }

                        override fun onChartScale(
                            me: MotionEvent?,
                            scaleX: Float,
                            scaleY: Float,
                        ) {
                        }

                        override fun onChartTranslate(me: MotionEvent?, dX: Float, dY: Float) {
                        }

                    }
                    chart.setDrawGridBackground(false)
                    chart.setDrawBorders(false)
                    chart.setDrawMarkers(true)
                    chart.isDoubleTapToZoomEnabled = false
                    chart.setPinchZoom(false)
                    chart.axisRight.setDrawLabels(true)
                    chart.axisRight.setPosition(YAxis.YAxisLabelPosition.INSIDE_CHART)
                    chart.axisLeft.setPosition(YAxis.YAxisLabelPosition.INSIDE_CHART)
                    chart.axisRight.setLabelCount(2, true)
                    chart.legend.isEnabled = false
                    chart.description.isEnabled = false
                    chart.isHighlightPerTapEnabled = false
                    chart.axisLeft.setDrawLabels(false)
                    chart.xAxis.setDrawLabels(false)
                    chart.xAxis.setDrawGridLines(false)
                    chart.axisLeft.setDrawGridLines(false)
                    chart.axisRight.setDrawGridLines(false)
                    chart.axisRight.textColor = Color.parseColor("#585858")
                    chart.xAxis.setDrawAxisLine(false)
                    chart.axisLeft.setDrawAxisLine(false)
                    chart.axisRight.setDrawAxisLine(false)
                    chart.axisRight.textSize = 12f
                    chart.axisRight.typeface = typeface
                    chart.axisLeft.textSize = 12f
                    chart.axisLeft.typeface = typeface
                    onConfigChart(chart)
                    chart.data = barData
                    chart.xAxis.isGranularityEnabled = true
                    chart.isAutoScaleMinMaxEnabled = true
                    chart.setVisibleXRangeMaximum(10f)
                    chart.setVisibleXRangeMinimum(40f)
                    chart.moveViewToX(barData.entryCount.toFloat())
                    chart.renderer = RoundedBarChartRenderer(
                        chart,
                        chart.animator,
                        chart.viewPortHandler,
                    ).apply {
                        setRadius(4)
                    }
                    chart.invalidate()
                }
        },
        update = { chart ->
            chart.data = barData
            chart.post {
                chart.setViewPortOffsets(0f, 0f, 0f, 0f)
                chart.xAxis.isGranularityEnabled = true
                chart.isAutoScaleMinMaxEnabled = true
                chart.setVisibleXRangeMaximum(10f)
                chart.setVisibleXRangeMinimum(40f)
                chart.moveViewToX(barData.entryCount.toFloat())
                chart.invalidate()
            }
            onUpdatedChart(chart)
        }
    )
}

