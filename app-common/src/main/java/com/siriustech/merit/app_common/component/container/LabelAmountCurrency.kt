package com.siriustech.merit.app_common.component.container

import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.text.TextStyle
import com.siriustech.merit.app_common.component.common.onGetPriceChangeResource
import com.siriustech.merit.app_common.ext.colorTxtParagraph
import com.siriustech.merit.app_common.theme.LocalAppColor
import com.siriustech.merit.app_common.theme.LocalDimens
import com.siriustech.merit.app_common.theme.LocalTypography

/**
 * Created by He<PERSON>tet
 */
@Composable
fun LabelAmountCurrency(
    modifier: Modifier = Modifier,
    label: String,
    amount: String? = "",
    currency: String,
    enableTextColoring: Boolean = false,
    amountTextStyle: TextStyle? = null,
) {
    var textValue = amount
    if (amount.isNullOrEmpty()) {
        textValue = "-"
    }
    var color = LocalAppColor.current.txtParagraph


    if (amountTextStyle != null) {
        color = amountTextStyle.color
    }

    if (enableTextColoring) {
        val resource = onGetPriceChangeResource(priceChange = textValue.orEmpty())
        color = resource.textColor
    }

    Row(modifier = modifier.fillMaxWidth(), horizontalArrangement = Arrangement.SpaceBetween) {
        Text(
            text = label,
            style = LocalTypography.current.text14.light.colorTxtParagraph()
        )
        Row(verticalAlignment = Alignment.Bottom) {
            Text(
                text = textValue.orEmpty(),
                style = LocalTypography.current.text14.regular.copy(color = color)
                    .merge(amountTextStyle)
            )
            Text(
                modifier = Modifier.padding(start = LocalDimens.current.dimen2),
                text = currency,
                style = LocalTypography.current.text12.light.copy(color = color)
            )
        }
    }
}