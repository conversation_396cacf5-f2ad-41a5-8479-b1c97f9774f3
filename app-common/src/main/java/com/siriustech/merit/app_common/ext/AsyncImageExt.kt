package com.siriustech.merit.app_common.ext

import androidx.compose.foundation.layout.size
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.unit.dp
import coil3.compose.AsyncImage
import coil3.network.NetworkHeaders
import coil3.network.httpHeaders
import coil3.request.CachePolicy
import coil3.request.ImageRequest
import coil3.request.crossfade
import com.siriustech.merit.app_common.R
import timber.log.Timber

/**
 * Created by <PERSON><PERSON>tet
 */


@Composable
fun ToolbarProfileImage(
    modifier: Modifier = Modifier,
    imageUrl: String? = null,
    sid: String,
) {
    Timber.d("LOAD_ToolbarProfileImage ${imageUrl}")
    AsyncImage(
        modifier = Modifier
            .size(18.dp)
            .clip(RoundedCornerShape(50))
            .then(modifier),
        model = ImageRequest.Builder(LocalContext.current)
            .data(imageUrl)
            .crossfade(true)
            .httpHeaders(
                headers = NetworkHeaders.Builder()
                    .set("sid", sid)
                    .build()
            )
            .diskCachePolicy(CachePolicy.ENABLED)
            .build(),
        placeholder = painterResource(R.drawable.user_default_profile),
        error = painterResource(R.drawable.user_default_profile),
        contentDescription = "Product Category Placeholder",
        contentScale = ContentScale.Crop,
        onError = {
            Timber.d("Image Loading error ${it.result.throwable.message}")
        }
    )
}

@Composable
fun ProfileImage(
    modifier: Modifier = Modifier,
    imageUrl: String? = null,
    sid: String,
) {
    AsyncImage(
        modifier = Modifier
            .size(120.dp)
            .clip(RoundedCornerShape(50))
            .then(modifier),
        model = ImageRequest.Builder(LocalContext.current)
            .data(imageUrl)
            .crossfade(true)
            .httpHeaders(
                headers = NetworkHeaders.Builder()
                    .set("sid", sid)
                    .build()
            )
            .diskCachePolicy(CachePolicy.ENABLED)
            .build(),
        placeholder = painterResource(R.drawable.user_default_profile),
        error = painterResource(R.drawable.user_default_profile),
        contentDescription = "Product Category Placeholder",
        contentScale = ContentScale.Crop,
        onError = {
            Timber.d("Image Loading error ${it.result}")
        }
    )
}
