package com.siriustech.merit.app_common.screen.chat

import android.content.Intent
import androidx.activity.compose.rememberLauncherForActivityResult
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.padding
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.collectAsState
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.tooling.preview.Preview
import androidx.hilt.navigation.compose.hiltViewModel
import com.siriustech.core_ui_compose.base.SingleEventEffect
import com.siriustech.core_ui_compose.component.CommonToolbar
import com.siriustech.core_ui_compose.component.ToolbarProperties
import com.siriustech.merit.app_common.R
import com.siriustech.merit.app_common.component.loading.HorizontalProgressBar
import com.siriustech.merit.app_common.ext.colorTxtTitle
import com.siriustech.merit.app_common.screen.chat.component.ChatAttachmentList
import com.siriustech.merit.app_common.screen.chat.component.ChatInputAction
import com.siriustech.merit.app_common.screen.chat.component.ChatList
import com.siriustech.merit.app_common.screen.docpreview.DocPreviewActivity
import com.siriustech.merit.app_common.screen.docpreview.DocPreviewArgument
import com.siriustech.merit.app_common.theme.AppScreen
import com.siriustech.merit.app_common.theme.LocalDimens
import com.siriustech.merit.app_common.theme.LocalTypography
import com.siriustech.merit.app_common.utils.FileUtils
import com.siriustech.merit.app_common.utils.FileUtils.getRealPathFromURI
import com.siriustech.merit.app_common.utils.GetCustomContents
import java.io.File
import okhttp3.MediaType.Companion.toMediaTypeOrNull
import timber.log.Timber

/**
 * Created by Hein Htet
 */
@Composable
fun ChatScreen(
    onBackPressed: () -> Unit = {},
    viewModel: ChatViewModel = hiltViewModel(),
) {
    val context = LocalContext.current
    val userInfo = viewModel.outputs.userInfo.collectAsState()
    val messages = viewModel.outputs.messages
    val message = viewModel.outputs.message.collectAsState()
    val loadingMore = viewModel.outputs.loadingMore.collectAsState()
    val uploadedFile = viewModel.outputs.uploadedFiles


    LaunchedEffect(Unit) {
        viewModel.inputs.onGetUserInfo()
    }


    val filePickerResult = rememberLauncherForActivityResult(
        contract = GetCustomContents(isMultiple = false),
        onResult = { uris ->
            if (uris.isNotEmpty()) {
                val urisJoined = uris.joinToString(", ")
                Timber.d("FILE_PICKED $urisJoined")
                val fileUri = uris.first()
                val file = context.getRealPathFromURI(fileUri)
                val requestBody = FileUtils.fileToRequestBody(
                    File(file.orEmpty()),
                    "application/octet-stream".toMediaTypeOrNull()
                )
                viewModel.onTriggerActions(
                    ChatAction.OnUploadFile(
                        context,
                        File(file.orEmpty()),
                        fileUri,
                        requestBody
                    )
                )
            }
        })

    SingleEventEffect(sideEffectFlow = viewModel.appAction) {
        when (it) {
            is ChatAction.OnPreviewAttachment -> {
                Timber.d("PREVIEW_ATTACHMENT_${it.item}")
                context.startActivity(
                    Intent(context, DocPreviewActivity::class.java).apply {
                        putExtra(
                            DocPreviewActivity.EXTRA_DOC_PREVIEW, DocPreviewArgument(
                                url = it.item.url,
                                type = it.item.type,
                                fileSize = it.item.fileSize,
                                name = it.item.name
                            )
                        )
                    }
                )
            }
        }
    }

    AppScreen(vm = viewModel, ignorePaddingValue = true) {
        Column(
            modifier = Modifier
                .fillMaxSize()
        ) {
            Box(modifier = Modifier) {
                CommonToolbar(
                    onLeftActionClicked = onBackPressed,
                    properties = ToolbarProperties(
                        leftActionResId = R.drawable.ic_back_arrow,
                        title = "Support",
                        titleTextStyle = LocalTypography.current.text14.medium.colorTxtTitle()
                    )
                )
                Box(modifier = Modifier.align(Alignment.BottomCenter)) {
                    HorizontalProgressBar(visible = loadingMore.value)
                }
            }
            ChatList(messages, userInfo.value, onTriggerAction = {
                viewModel.onTriggerActions(it)
            })
            ChatAttachmentList(
                attachments = uploadedFile,
                modifier = Modifier.padding(
                    horizontal = LocalDimens.current.dimen12,
                    vertical = LocalDimens.current.dimen8
                ),
                onRemove = {
                    viewModel.onTriggerActions(ChatAction.OnRemoveUploadedFile(it))
                },
            )
            ChatInputAction(
                sendEnabled = message.value.isNotEmpty() || uploadedFile.isNotEmpty(),
                onTextChanged = {
                    viewModel.onTriggerActions(ChatAction.ChatInputTextChanged(it))
                },
                defaultText = message.value,
                onPickFile = {
                    filePickerResult.launch("*/*")
                },
                onSendMessage = { viewModel.onTriggerActions(ChatAction.OnSendMessage) }
            )
        }
    }
}

@Preview(showBackground = true, showSystemUi = true)
@Composable
fun PreviewChatScreen() {
    ChatScreen()
}

