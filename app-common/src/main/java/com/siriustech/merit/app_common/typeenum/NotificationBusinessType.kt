package com.siriustech.merit.app_common.typeenum

/**
 * Created by <PERSON><PERSON>
 */
enum class NotificationBusinessType(val value: String) {
    ORDER("ORDER"),
    WITHDRAW("WITHDRAW"),
    DEPOSIT("DEPOSIT");

    companion object {
        fun fromParam(value: String): NotificationBusinessType? {
            return when (value) {
                ORDER.value -> ORDER
                WITHDRAW.value -> WITHDRAW
                DEPOSIT.value -> DEPOSIT
                else -> null
            }
        }
    }
}

enum class NotificationTagType(val value: String) {
    APPROVED("APPROVED"),
    REJECTED("REJECTION");

    companion object {
        fun fromParam(value: String): NotificationTagType? {
            return when (value) {
                APPROVED.value -> APPROVED
                REJECTED.value -> REJECTED
                else -> null
            }
        }
    }
}
