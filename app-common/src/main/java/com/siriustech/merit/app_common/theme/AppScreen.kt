package com.siriustech.merit.app_common.theme

import android.app.Activity
import androidx.compose.animation.slideInVertically
import androidx.compose.animation.slideOutVertically
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.padding
import androidx.compose.material3.Scaffold
import androidx.compose.runtime.Composable
import androidx.compose.runtime.CompositionLocalProvider
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.compositionLocalOf
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.runtime.setValue
import androidx.compose.ui.Modifier
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.hilt.navigation.compose.hiltViewModel
import androidx.navigation.NavController
import com.core.localstorage.pref.getSecureSharedPref
import com.siriustech.core_ui_compose.base.BaseScreen
import com.siriustech.core_ui_compose.base.SingleEventEffect
import com.siriustech.core_ui_compose.component.CommonToolbar
import com.siriustech.core_ui_compose.ext.CommonDialog
import com.siriustech.core_ui_compose.model.ErrorDisplay
import com.siriustech.merit.app_common.R
import com.siriustech.merit.app_common.component.alert.BannerAlert
import com.siriustech.merit.app_common.component.alert.BannerAlertProperties
import com.siriustech.merit.app_common.component.alert.IOSAlertDialog
import com.siriustech.merit.app_common.component.alert.getCommonDialogProperties
import com.siriustech.merit.app_common.component.loading.LoadingView
import com.siriustech.merit.app_common.data.CommonSharedPreferences
import com.siriustech.merit.app_common.ext.changeLocale
import com.siriustech.merit.app_common.typeenum.LanguageType
import java.util.Locale
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import timber.log.Timber

/**
 * Created by Hein Htet
 */

sealed interface AppScreenEvent {
    data class ChangeLanguage(val languageType: LanguageType) : AppScreenEvent
}

@Composable
fun AppScreen(
    vm: AppViewModel,
    navController: NavController? = null,
    toolbar: @Composable() (() -> Unit)? = null,
    ignorePaddingValue: Boolean = false,
    content: @Composable (onTriggerEvent: (AppScreenEvent) -> Unit) -> Unit,
) {
    var showBannerAlert by remember { mutableStateOf(false) }
    val coroutineScope = rememberCoroutineScope()

    var bannerAlertProperties by remember {
        mutableStateOf<BannerAlertProperties?>(null)
    }

    var showErrorPopup by remember { mutableStateOf(false) }

    var errorDisplay by remember {
        mutableStateOf<ErrorDisplay?>(null)
    }

    // Update the app's configuration when locale changes
    val context = LocalContext.current
    val commonSharedPreferences = CommonSharedPreferences(context, getSecureSharedPref(context))
    var currentLocale by remember { mutableStateOf(Locale(commonSharedPreferences.appLocale)) }

    SingleEventEffect(sideEffectFlow = vm.appEvent) {
        Timber.d("AppScreen event: $it")
        when (it) {
            is AppEvent.BannerAlertEvent -> {
                bannerAlertProperties = it.properties
                showBannerAlert = true
                coroutineScope.launch {
                    delay(2000)
                    showBannerAlert = false
                }
            }

            is AppEvent.ChangeLanguageEvent -> {
                currentLocale = Locale(it.languageType.value)
                commonSharedPreferences.setAppLocale(it.languageType.value)
            }

            is AppEvent.ErrorEvent -> {
                errorDisplay = it.errorDisplay
//                showErrorPopup = true
                val title =
                    if (errorDisplay?.code == "1") context.getString(R.string.key1016) else errorDisplay?.title
                        ?: context.getString((R.string.key1016))
                val message =
                    if (errorDisplay?.code == "1") context.getString(R.string.key1017) else errorDisplay?.message
                        ?: context.getString(R.string.key1017)
                IOSAlertDialog.Builder(context)
                    .setTitle(title)
                    .setMessage(message)
                    .setPositiveButton(context.getString(R.string.key0832)) {}
                    .show()
            }
            else -> {}
        }
    }

    // State to manage the current locale
    LaunchedEffect(currentLocale) {
        val resources = context.resources
        val configuration = resources.configuration
        configuration.setLocale(currentLocale)
        context.createConfigurationContext(configuration)
        context.changeLocale(currentLocale)
    }
    CompositionLocalProvider(LocalAppLocale provides currentLocale) {
        Scaffold(modifier = Modifier.background(LocalAppColor.current.bgDefault)) { paddingValues ->
            BaseScreen(
                vm = vm,
                modifier = Modifier
                    .padding(if (ignorePaddingValue) PaddingValues(0.dp) else paddingValues)
                    .background(LocalAppColor.current.bgDefault),
                toolbar = toolbar,
                loadingView = {
                    LoadingView()
                },
            ) {
                Box(
                    modifier = Modifier
                        .fillMaxSize()
                        .background(LocalAppColor.current.bgDefault)
                ) {
                    content() { eventType ->
                        when (eventType) {
                            is AppScreenEvent.ChangeLanguage -> {
                                currentLocale = Locale(eventType.languageType.value)
                                commonSharedPreferences.setAppLocale(eventType.languageType.value)
                                (context as Activity).recreate()
                            }
                        }
                    }
                    androidx.compose.animation.AnimatedVisibility(
                        modifier = Modifier.padding(top = 34.dp),
                        enter = slideInVertically(),
                        exit = slideOutVertically(),
                        visible = showBannerAlert
                    ) {
                        if (bannerAlertProperties != null) {
                            BannerAlert(bannerAlertProperties!!, onCloseButtonClicked = {
                                showBannerAlert = false
                            })
                        }
                    }
                }
            }
        }
    }
    if (showErrorPopup) {
        CommonDialog(commonPopupDisplayData = getCommonDialogProperties().copy(
            title = if (errorDisplay?.code == "1") stringResource(id = R.string.key1016) else errorDisplay?.title
                ?: stringResource(id = R.string.key1016),
            message = if (errorDisplay?.code == "1") stringResource(id = R.string.key1017) else errorDisplay?.message
                ?: stringResource(id = R.string.key1017),
            rightButtonPressed = {
                showErrorPopup = false
            },
            leftButtonPressed = {
                showErrorPopup = false
            }
        )) {
            showErrorPopup = false
        }
    }
}

val LocalAppLocale = compositionLocalOf { Locale.getDefault() }


@Preview(showBackground = true, showSystemUi = true)
@Composable
fun PreviewAppScreen() {
    AppScreen(vm = hiltViewModel(), toolbar = { CommonToolbar() }) {}
}