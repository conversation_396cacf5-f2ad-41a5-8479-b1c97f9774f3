package com.siriustech.merit.app_common.component.container

import androidx.compose.foundation.Image
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.painter.Painter
import androidx.compose.ui.text.TextStyle
import com.siriustech.merit.app_common.ext.colorTxtParagraph
import com.siriustech.merit.app_common.theme.LocalDimens
import com.siriustech.merit.app_common.theme.LocalTypography

/**
 * Created by <PERSON><PERSON>
 */
@Composable
fun LabelFlagValue(
    modifier: Modifier = Modifier,
    label: String,
    value: String,
    flagPainter: Painter,
    valueTextStyle: TextStyle? = null,
) {
    var textValue = value
    if (value.isEmpty()) {
        textValue = "-"
    }
    Row(modifier = modifier.fillMaxWidth(), horizontalArrangement = Arrangement.SpaceBetween) {
        Text(
            text = label,
            style = LocalTypography.current.text14.light.colorTxtParagraph()
        )

        Row(verticalAlignment = Alignment.CenterVertically) {
            Image(painter = flagPainter, contentDescription = "Flag Painter")
            Text(
                text = textValue,
                style = LocalTypography.current.text14.regular.colorTxtParagraph()
                    .merge(valueTextStyle)
            )
        }
    }
}