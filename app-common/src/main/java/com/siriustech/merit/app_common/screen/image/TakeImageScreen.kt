package com.siriustech.merit.app_common.screen.image

import android.net.Uri
import androidx.activity.compose.ManagedActivityResultLauncher
import androidx.activity.compose.rememberLauncherForActivityResult
import androidx.activity.result.contract.ActivityResultContracts
import androidx.compose.runtime.Composable
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.ui.Modifier
import androidx.compose.ui.platform.LocalContext
import com.siriustech.merit.app_common.utils.FileUtils.getRealPathFromURI
import com.siriustech.merit.app_common.utils.FileUtils.getTempUri

/**
 * Created by He<PERSON>tet
 */


@Composable
fun TakeImageScreen(
    modifier: Modifier = Modifier,
    onTakeImageResult: (uri: Uri, filePath: String) -> Unit,
    content: @Composable (cameraPermissionLauncher : ManagedActivityResultLauncher<String,Boolean>) -> Unit,
) {
    val context = LocalContext.current
    val tempUri = remember { mutableStateOf<Uri?>(null) }
    val takePhotoLauncher = rememberLauncherForActivityResult(
        contract = ActivityResultContracts.TakePicture(),
        onResult = { isSaved ->
            if(isSaved){
                tempUri.value?.let {
                    println("IMAGE_URL $it")
                    println("IMAGE_URL_PATH ${context.getRealPathFromURI(it)}")
                    onTakeImageResult(it, context.getRealPathFromURI(it).orEmpty())
                }
            }
        }
    )

    val cameraPermissionLauncher : ManagedActivityResultLauncher<String,Boolean> = rememberLauncherForActivityResult(
        contract = ActivityResultContracts.RequestPermission()
    ) { isGranted: Boolean ->
        if (isGranted) {
            val tmpUri = context.getTempUri()
            tempUri.value = tmpUri
            tempUri.value?.let {
                takePhotoLauncher.launch(it)
            }
        }
    }
    content(cameraPermissionLauncher)
}