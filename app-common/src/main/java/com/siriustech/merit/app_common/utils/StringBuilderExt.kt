package com.siriustech.merit.app_common.utils

import android.content.Context
import android.graphics.Typeface
import android.graphics.drawable.Drawable
import android.text.SpannableStringBuilder
import android.text.Spanned
import android.text.TextPaint
import android.text.style.ForegroundColorSpan
import android.text.style.ImageSpan
import android.text.style.MetricAffectingSpan
import android.text.style.TextAppearanceSpan
import androidx.core.content.res.ResourcesCompat
import androidx.core.text.color
import androidx.core.text.inSpans
import com.core.util.removeCommas
import com.siriustech.merit.app_common.R

class CustomTypefaceSpan(private val typeface: Typeface?) : MetricAffectingSpan() {
    override fun updateDrawState(paint: TextPaint) {
        paint.typeface = typeface
    }

    override fun updateMeasureState(paint: TextPaint) {
        paint.typeface = typeface
    }
}

fun getTypefaceSpan(context: Context) =
    CustomTypefaceSpan(ResourcesCompat.getFont(context, R.font.noto_sans_regular) ?: Typeface.DEFAULT)

inline fun SpannableStringBuilder.textIndicator(
    context: Context,
    color: Int,
    crossinline builderAction1: SpannableStringBuilder.() -> Unit,
    crossinline builderAction2: SpannableStringBuilder.() -> Unit,
) {
    inSpans(
        getTypefaceSpan(context),
        color(color) { builderAction1() },
        builderAction = builderAction2
    )
}



inline fun SpannableStringBuilder.text(
    context: Context,
    styleResId: Int,
    crossinline builderAction: SpannableStringBuilder.() -> Unit,
) {
    inSpans(
        getTypefaceSpan(context),
        TextAppearanceSpan(context, styleResId),
        builderAction = builderAction
    )
}

fun SpannableStringBuilder.setCustomSpannable(
    context: Context,
    textAppearanceResId: Int,
    value: String,
) {
    inSpans(
        getTypefaceSpan(context),
        TextAppearanceSpan(context, textAppearanceResId),
        builderAction = {
            append(value)
        }
    )
}

fun SpannableStringBuilder.setCustomSpannableWithHighlight(
    context: Context,
    textAppearanceResId: Int,
    value: String,
    color: Int,
) {
    inSpans(
        getTypefaceSpan(context),
        TextAppearanceSpan(context, textAppearanceResId),
        ForegroundColorSpan(color),
        builderAction = {
            append(value)
        }
    )
}

fun SpannableStringBuilder.image(context: Context, iconRes: Int) {
    append(
        "Image",
        ImageSpan(context, iconRes),
        Spanned.SPAN_INCLUSIVE_EXCLUSIVE
    )
}



fun SpannableStringBuilder.image(drawable: Drawable) {
    append(
        "Image",
        ImageSpan(drawable),
        Spanned.SPAN_INCLUSIVE_EXCLUSIVE
    )
}

