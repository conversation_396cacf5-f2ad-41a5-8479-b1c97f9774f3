package com.siriustech.merit.app_common.screen.pinlogin

import android.os.Bundle
import androidx.activity.compose.setContent
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.toArgb
import androidx.fragment.app.FragmentActivity
import androidx.navigation.compose.rememberNavController
import com.siriustech.core_ui_compose.ext.ChangeSystemBarsTheme

/**
 * Created by He<PERSON> Htet
 */
class CommonPinLoginActivity : FragmentActivity() {

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContent {
            ChangeSystemBarsTheme(lightTheme = true, Color.White.toArgb())
            PinLoginScreen(
                navController = rememberNavController(),
                properties = (intent.getSerializableExtra(EXTRA_PIN_LOGIN) as? PinLoginProperties?)
                    ?: PinLoginProperties(),
            )
        }
    }

    companion object {
        const val EXTRA_PIN_LOGIN = "EXTRA_PIN_LOGIN"
    }
}

object CommonPinLoginRoute {

}