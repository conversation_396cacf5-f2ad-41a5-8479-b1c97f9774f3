package com.siriustech.merit.app_common.typeenum

import android.content.Context
import androidx.compose.foundation.border
import androidx.compose.runtime.Composable
import androidx.compose.runtime.ReadOnlyComposable
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.platform.LocalContext
import com.siriustech.merit.app_common.R
import com.siriustech.merit.app_common.component.text.BadgeText
import com.siriustech.merit.app_common.theme.LocalAppColor
import com.siriustech.merit.app_common.theme.LocalDimens

/**
 * Created by Hein Htet
 */
enum class OrderSide(val value: String) {
    ALL("ALL"),
    BUY("BUY"),
    SELL("SELL");


    companion object {
        fun fromParam(value: String): OrderSide {
            return when (value) {
                ALL.value -> ALL
                BUY.value -> BUY
                SELL.value -> SELL
                else -> ALL
            }
        }


    }

    @Composable
    @ReadOnlyComposable
    fun getDisplayName(): String {
        return when (this) {
            ALL -> "All"
            BUY -> "Buy"
            SELL -> "Sell"
        }
    }


    fun getDisplayName(context: Context?): String {
        return when (this) {
            ALL -> context?.getString(R.string.key0681) ?: "All"
            BUY -> context?.getString(R.string.key0682) ?: "Buy"
            SELL -> context?.getString(R.string.key0683) ?: "Sell"
        }
    }


    fun getApiValue(): String {
        return when (this) {
            ALL -> ""
            BUY -> "Buy"
            SELL -> "Sell"
        }
    }

    @Composable
    fun getColor() =
        if (this == BUY) LocalAppColor.current.txtPositive else if (this == SELL) LocalAppColor.current.txtNegative else LocalAppColor.current.txtParagraph

    @Composable
    fun OrderSideBox() {
        val context = LocalContext.current
        BadgeText(
            label = this.getDisplayName(context),
            bgColor = Color.Transparent,
            textColor = getColor(),
            modifier = Modifier
                .border(LocalDimens.current.dimen1, getColor())
        )
    }
}