package com.siriustech.merit.app_common.mapper

import com.core.util.toAmount
import com.siriustech.merit.apilayer.service.market.exchangecurrency.MarketCurrencyListResponse
import com.siriustech.merit.app_common.component.marquee.StockExchangeMarqueeData

/**
 * Created by He<PERSON> Htet
 */
object MarketExchangeCurrencyMapper {

    fun MarketCurrencyListResponse.mapToStockExchangeDisplayData(): List<StockExchangeMarqueeData> {
        return this.list.orEmpty().map {
            StockExchangeMarqueeData(
                price = it.exchangeRate?.toAmount().orEmpty(),
                exchange = it.exchange.orEmpty(),
            )
        }
    }
}