package com.siriustech.merit.app_common.typeenum

import android.content.Context
import com.siriustech.merit.app_common.R

/**
 * Created by <PERSON><PERSON> Htet
 */
enum class HomeShortcutMenuAction {
    DEPOSIT,
    WITHDRAW,
    STATEMENT,
    FAVORITES;

    fun onDisplayTitle(context : Context): String {
        val s = when (this) {
            DEPOSIT -> context.getString(R.string.key0350)
            WITHDRAW -> context.getString(R.string.key0349)
            STATEMENT -> context.getString(R.string.key1042)
            FAVORITES -> context.getString(R.string.key0443)
        }
        return s
    }

    fun onDisplayIconResId() : Int {
        val resId = when (this) {
            DEPOSIT -> R.drawable.ic_deposit_shortcut
            WITHDRAW -> R.drawable.ic_withdraw_shortcut
            STATEMENT -> R.drawable.ic_report_shortcut
            FAVORITES -> R.drawable.ic_watch_list_shortcut
        }
        return resId
    }
}