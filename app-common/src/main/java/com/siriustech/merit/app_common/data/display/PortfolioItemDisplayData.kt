package com.siriustech.merit.app_common.data.display

import com.siriustech.merit.app_common.typeenum.PortfolioAssetARRType
import com.siriustech.merit.app_common.typeenum.PortfolioAssetType
import com.siriustech.merit.app_common.typeenum.RiskLevel
import kotlinx.serialization.Serializable

@Serializable
data class PortfolioItemDisplayData(
    val type: PortfolioAssetType = PortfolioAssetType.UNKNOWN_TYPE,
    val rawSummaryName : String? =null,
    val aarType: PortfolioAssetARRType? = null,
    val percentage: String = "",
    val currency: String = "",
    val unrealizedGL: String = "",
    val unrealizedGLRate: String = "",
    val costValue: String = "",
    val marketValue: String = "",
    val riskLevel: RiskLevel? = null,
    val riskTag: String = "",
    val categoryAssetList: ArrayList<ProductCategoryDisplayData>? = null,
    val displaySummaryName : String = ""
)

@Serializable
data class ProductCategoryDisplayData(
    val id : String = "",
    val categoryName: String? = null,
    val isSection: Boolean = false,
    val symbol: String = "",
    val venue: String = "",
    val name: String = "",
    val logo: String = "",
    val exchange: String = "",
    val aarType: PortfolioAssetARRType = PortfolioAssetARRType.ARR1,
    val riskLevel: RiskLevel? = null,
    val currency: String = "",
    val unit: String = "",
    val marketPrice: String = "",
    val marketValue: String = "",
    val costPrice: String = "",
    val costValue: String = "",
    val unrealizedGl: String = "",
    val unrealizedGlRate: String = "",
    val percentage: String = "0.0",
    val assetAllocationClass: String = "",
)