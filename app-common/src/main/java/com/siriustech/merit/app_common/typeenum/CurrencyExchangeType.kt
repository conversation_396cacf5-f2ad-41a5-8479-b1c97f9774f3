package com.siriustech.merit.app_common.typeenum

/**
 * Created by <PERSON><PERSON>
 */
enum class CurrencyExchangeType(currencyPair: String) {
    USD_HKD("USD/HKD"),
    USD_EUR("USD/EUR"),
    USD_CNY("USD/CNY"),
    USD_JPY("USD/JPY");

    fun description(): String {
       return when (this) {
            USD_HKD -> "US dollar / Hong Kong dollar"
            USD_EUR -> "US dollar / Euro"
            USD_CNY -> "US dollar / Chinese Yuan"
            USD_JPY -> "US dollar / Japanese Yen"
            else -> ""
        }
    }

    companion object {
        fun fromParam(pair: String): CurrencyExchangeType {
            return when (pair) {
                "USD/HKD" -> USD_HKD
                "USD/EUR" -> USD_EUR
                "USD/CNY" -> USD_CNY
                "USD/JPY" -> USD_JPY
                else -> USD_HKD
            }
        }
    }

}
