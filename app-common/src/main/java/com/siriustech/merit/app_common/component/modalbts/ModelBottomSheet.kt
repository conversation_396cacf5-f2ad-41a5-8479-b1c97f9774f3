package com.siriustech.merit.app_common.component.modalbts

import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.ModalBottomSheet
import androidx.compose.material3.Surface
import androidx.compose.material3.Text
import androidx.compose.material3.rememberModalBottomSheetState
import androidx.compose.runtime.Composable
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.painter.Painter
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.tooling.preview.Preview
import com.siriustech.merit.app_common.R
import com.siriustech.merit.app_common.component.button.ButtonProperties
import com.siriustech.merit.app_common.component.button.SecondaryBorderButton
import com.siriustech.merit.app_common.component.button.SecondaryButton
import com.siriustech.merit.app_common.ext.colorTxtCaution
import com.siriustech.merit.app_common.ext.colorTxtParagraph
import com.siriustech.merit.app_common.ext.colorTxtTitle
import com.siriustech.merit.app_common.theme.LocalAppColor
import com.siriustech.merit.app_common.theme.LocalDimens
import com.siriustech.merit.app_common.theme.LocalTypography
import kotlinx.coroutines.launch

/**
 * Created by Hein Htet
 */

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun CommonModelBottomSheet(
    properties: CommonModelBottomSheetProperties = CommonModelBottomSheetProperties(),
    onButtonClicked: () -> Unit = {},
    onCancelButtonClicked: () -> Unit = {},
    onDismissed: () -> Unit = {},
    customContent: @Composable() (() -> Unit?)? = null,
) {

    val sheetState = rememberModalBottomSheetState(skipPartiallyExpanded = true)
    val scope = rememberCoroutineScope()

    fun onDismiss() {
        scope.launch {
            sheetState.hide()
            onDismissed()
        }
    }

    ModalBottomSheet(
        dragHandle = {},
        shape = RoundedCornerShape(LocalDimens.current.dimen4),
        containerColor = LocalAppColor.current.bgDefault,
        onDismissRequest = {
            onDismissed()
        }, sheetState = sheetState,
        modifier = Modifier
    ) {
        CommonModalBottomSheetContent(
            properties,
            onButtonClicked = {
                onButtonClicked()
                onDismiss()
            },
            onDismissed = {
                onDismiss()
            },
            onCancelButtonClicked = {
                onCancelButtonClicked()
                onDismiss()
            },
            customContent = customContent,
        )
    }
}

@Composable
fun ModalTitle(properties: CommonModelBottomSheetProperties, onDismissed: () -> Unit) {
    Row(
        modifier = Modifier.fillMaxWidth(),
        verticalAlignment = Alignment.CenterVertically
    ) {
        Image(
            painter = properties.iconTitle ?: painterResource(id = R.drawable.ic_reset),
            contentDescription = "Reset Image Resource"
        )
        Spacer(modifier = Modifier.width(LocalDimens.current.dimen4))
        Text(
            text = properties.prefixTitle,
            style = LocalTypography.current.text14.semiBold.colorTxtTitle()
        )
        Text(
            text = properties.title,
            modifier = Modifier.weight(1f),
            style = LocalTypography.current.text14.regular.colorTxtParagraph()
        )
        if (properties.showCloseButton) {
            Image(
                modifier = Modifier.clickable {
                    onDismissed()
                },
                painter = painterResource(id = R.drawable.ic_action_close),
                contentDescription = "CLOSE ACTION"
            )
        }
    }
}

@Composable
fun InfoContent(properties: CommonModelBottomSheetInfoProperties) {
    Row(modifier = Modifier.then(properties.modifier)) {
        if (properties.icon != null) {
            Image(
                painter = properties.icon,
                contentDescription = "Info Image Resource"
            )
        }
        Spacer(modifier = Modifier.width(LocalDimens.current.dimen4))
        Text(
            text = properties.content,
            style = properties.contentTextStyle
        )
    }
}

@Composable
fun CommonModalBottomSheetContent(
    properties: CommonModelBottomSheetProperties,
    onButtonClicked: () -> Unit = {},
    onCancelButtonClicked: () -> Unit = {},
    onDismissed: () -> Unit = {},
    customContent: @Composable() (() -> Unit?)? = null,
) {
    Column(
        modifier = Modifier
            .fillMaxWidth()
            .padding(
                horizontal = LocalDimens.current.dimen14,
                vertical = LocalDimens.current.dimen16
            )
    ) {
        ModalTitle(properties, onDismissed = onDismissed)
        Spacer(modifier = Modifier.height(LocalDimens.current.dimen16))
        Text(
            text = properties.description,
            style = LocalTypography.current.text14.light.colorTxtParagraph()
        )
        if (customContent != null) {
            Spacer(modifier = Modifier.height(LocalDimens.current.dimen8))
            customContent()
        }
        Spacer(modifier = Modifier.height(LocalDimens.current.dimen12))
        InfoContent(properties.contentInfo)
        Spacer(modifier = Modifier.height(LocalDimens.current.dimen16))
        Row {
            if (properties.cancelButtonText != null) {
                SecondaryBorderButton(
                    modifier = Modifier.weight(1f),
                    properties = ButtonProperties(text = properties.cancelButtonText),
                    onClicked = {
                        onCancelButtonClicked()
                    }
                )
                Spacer(modifier = Modifier.width(LocalDimens.current.dimen8))
            }
            SecondaryButton(
                modifier = Modifier.weight(1f),
                properties = ButtonProperties(text = properties.buttonText),
                onClicked = onButtonClicked
            )
        }
    }
}

data class CommonModelBottomSheetProperties(
    val prefixTitleIcon: Painter? = null,
    val prefixTitle: String = "",
    val title: String = "",
    val iconTitle: Painter? = null,
    val description: String = "",
    val buttonText: String = "",
    val cancelButtonText: String? = null,
    val showCloseButton: Boolean = false,
    val contentInfo: CommonModelBottomSheetInfoProperties = CommonModelBottomSheetInfoProperties(),
)

data class CommonModelBottomSheetInfoProperties(
    val icon: Painter? = null,
    val content: String = "",
    val contentTextStyle: TextStyle = TextStyle(),
    val modifier: Modifier = Modifier,
)

@Preview(showBackground = true, showSystemUi = true)
@Composable
fun PreviewCommonModelBottomSheet() {
    Surface {
        CommonModalBottomSheetContent(
            properties = CommonModelBottomSheetProperties(
                prefixTitle = "Action: ",
                title = "Clear inputs in this page?",
                description = "Would you like to start inputting from a scratch?",
                buttonText = "Confirm",
                showCloseButton = true,
                cancelButtonText = "Cancel",
                iconTitle = painterResource(id = R.drawable.ic_settings_logout),
                contentInfo = CommonModelBottomSheetInfoProperties(
                    icon = painterResource(id = R.drawable.ic_caution),
                    content = "This is Info",
                    modifier = Modifier
                        .fillMaxWidth()
                        .clip(RoundedCornerShape(LocalDimens.current.dimen4))
                        .background(LocalAppColor.current.bgCaution)
                        .padding(
                            horizontal = LocalDimens.current.dimen8,
                            vertical = LocalDimens.current.dimen1
                        ),
                    contentTextStyle = LocalTypography.current.text14.light.colorTxtCaution()
                )
            )
        )
    }
}