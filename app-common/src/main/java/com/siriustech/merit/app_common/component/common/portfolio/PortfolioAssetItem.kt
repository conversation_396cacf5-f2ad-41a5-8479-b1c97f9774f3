package com.siriustech.merit.app_common.component.common.portfolio

import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.tooling.preview.Preview
import coil3.compose.AsyncImage
import coil3.request.ImageRequest
import coil3.request.crossfade
import com.siriustech.merit.app_common.R
import com.siriustech.merit.app_common.component.common.MarketPrice
import com.siriustech.merit.app_common.component.common.PriceChangeRate
import com.siriustech.merit.app_common.component.common.onGetPriceChangeResource
import com.siriustech.merit.app_common.component.text.BadgeText
import com.siriustech.merit.app_common.data.display.ProductCategoryDisplayData
import com.siriustech.merit.app_common.ext.colorTxtParagraph
import com.siriustech.merit.app_common.ext.colorTxtTitle
import com.siriustech.merit.app_common.ext.noRippleClickable
import com.siriustech.merit.app_common.theme.LocalDimens
import com.siriustech.merit.app_common.theme.LocalTypography
import com.siriustech.merit.app_common.typeenum.RiskLevel
import timber.log.Timber

/**
 * Created by Hein Htet
 */
@Composable
fun PortfolioAssetItem(
    modifier: Modifier = Modifier,
    showTitle : Boolean = false,
    onItemClick: (data: ProductCategoryDisplayData) -> Unit = {},
    data: ProductCategoryDisplayData = ProductCategoryDisplayData(),
) {
    val priceChangeUIModel = onGetPriceChangeResource(priceChange = data.unrealizedGlRate)
    Row(
        modifier = modifier
            .fillMaxWidth()
            .padding(vertical = LocalDimens.current.dimen12)
            .noRippleClickable { onItemClick(data) },
        horizontalArrangement = Arrangement.SpaceBetween
    ) {
        Row(
            verticalAlignment = Alignment.CenterVertically,
            modifier = Modifier.weight(1f, false)
        ) {
            AsyncImage(
                model = ImageRequest.Builder(LocalContext.current)
                    .data(data.logo)
                    .crossfade(true)
                    .build(),
                placeholder = painterResource(R.drawable.ic_product_category_placeholder),
                error = painterResource(R.drawable.ic_product_category_placeholder),
                contentDescription = "Product Category Placeholder",
                contentScale = ContentScale.Crop,
                onError = {
                    Timber.d("Image Loading error ${it.result}")
                },
                modifier = Modifier.size(LocalDimens.current.dimen32)
                    .clip(RoundedCornerShape(50))
            )
            Column(
                modifier = Modifier
                    .padding(start = LocalDimens.current.dimen8)
                    .weight(1f, fill = false)
            ) {
                Row(
                    modifier = Modifier
                        .fillMaxWidth(),
                    horizontalArrangement = Arrangement.SpaceBetween
                ) {
                    Row(modifier = Modifier) {
                        Text(
                            text = data.name,
                            modifier = Modifier.weight(1f, false),
                            maxLines = 1,
                            overflow = TextOverflow.Ellipsis,
                            style = LocalTypography.current.text14.medium.colorTxtTitle()
                        )
                        Spacer(modifier = Modifier.width(LocalDimens.current.dimen8))
                        Box(
                            modifier = Modifier.weight(1f, false)
                        ) {
                            data.riskLevel?.let {
                                BadgeText(
                                    label = it.name.uppercase(),
                                    bgColor = it.getAssetRiskBackgroundColor(),
                                    textColor = it.getAssetRiskLevelTextColor()
                                )
                            }
                        }
                    }
                    MarketPrice(
                        priceChangeUIModel = priceChangeUIModel,
                        value = data.marketValue,
                        currency = data.currency
                    )
                }
                Row {
                    Text(
                        data.percentage.plus("%"),
                        modifier = Modifier.weight(1f),
                        style = LocalTypography.current.text12.medium.colorTxtParagraph()
                    )
                    PriceChangeRate(
                        modifier = Modifier,
                        priceChange = data.unrealizedGl,
                        priceChangeRate = data.unrealizedGlRate
                    )
                }
            }
        }
    }
}

@Preview(showBackground = true, showSystemUi = true)
@Composable
fun PreviewPortfolioAssetItem() {
    PortfolioAssetItem(
        data = ProductCategoryDisplayData(
            name = "TENCENTTENCENTTENCENTTENCENTTENCENTTENCENTTENCENTTENCENTTENCENTTENCENTTENCENTTENCENTTENCENTTENCENTTENCENT 01",
            riskLevel = RiskLevel.HIGH,
            exchange = "HKEX",
            marketPrice = "377.60",
            currency = "HKD",
            marketValue = "24392",
            unrealizedGl = "2192.99",
            unrealizedGlRate = "2192.0",
            logo = "https://api.twelvedata.com/logo/apple.com"
        )
    )
}