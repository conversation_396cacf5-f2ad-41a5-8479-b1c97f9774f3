package com.siriustech.merit.app_common.component.container

import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.padding
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.unit.Dp
import com.siriustech.merit.app_common.theme.LocalDimens

/**
 * Created by <PERSON><PERSON>
 */

@Composable
fun PaddingTop(value : Dp) {
    Spacer(modifier = Modifier.padding(top = value))
}

@Composable
fun PaddingStart(value : Dp) {
    Spacer(modifier = Modifier.padding(start = value))
}

@Composable
fun PaddingBottom(value : Dp) {
    Spacer(modifier = Modifier.padding(bottom = value))
}
@Composable
fun PaddingEnd(value : Dp) {
    Spacer(modifier = Modifier.padding(end = value))
}