package com.siriustech.merit.app_common.navigation.argument.market

import androidx.lifecycle.SavedStateHandle
import androidx.navigation.toRoute
import com.siriustech.merit.app_common.data.display.MarketAssetDisplayData
import com.siriustech.merit.app_common.ext.serializableType
import com.siriustech.merit.app_common.navigation.MarketProfile
import kotlin.reflect.typeOf
import kotlinx.serialization.Serializable

/**
 * Created by <PERSON><PERSON>
 */

@Serializable
data class MarketProfileArgument(
   val data : MarketAssetDisplayData
) {
    companion object {
        val typeMap =
            mapOf(typeOf<MarketProfileArgument>() to serializableType<MarketProfileArgument>())

        fun from(savedStateHandle: SavedStateHandle) =
            savedStateHandle.toRoute<MarketProfile>(typeMap)
    }
}
