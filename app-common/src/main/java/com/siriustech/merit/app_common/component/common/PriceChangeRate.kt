package com.siriustech.merit.app_common.component.common

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.padding
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.painter.Painter
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.tooling.preview.Preview
import com.core.util.toAmount
import com.siriustech.merit.app_common.R
import com.siriustech.merit.app_common.data.display.PriceChangeUIModel
import com.siriustech.merit.app_common.ext.displayPriceChange
import com.siriustech.merit.app_common.ext.displayPriceChangeRate
import com.siriustech.merit.app_common.ext.isPriceDown
import com.siriustech.merit.app_common.ext.isPriceUp
import com.siriustech.merit.app_common.ext.isPriceZero
import com.siriustech.merit.app_common.theme.LocalAppColor
import com.siriustech.merit.app_common.theme.LocalDimens
import com.siriustech.merit.app_common.theme.LocalTypography

@Composable
fun PriceChangeRate(
    modifier: Modifier = Modifier,
    priceChange: String = "",
    priceChangeRate: String = "",
) {

    val priceChangeUIModel = onGetPriceChangeResource(priceChange = priceChangeRate)

    Row(
        modifier = Modifier.then(modifier),
        horizontalArrangement = Arrangement.Start
    ) {
        Text(
            modifier = Modifier
                .padding(
                    top = LocalDimens.current.dimen2,
                    bottom = LocalDimens.current.dimen2,
                    end = LocalDimens.current.dimen6
                ),
            text = priceChange.displayPriceChange().toAmount(4) ?: "0.00",
            style = LocalTypography.current.text12.medium.copy(color = priceChangeUIModel.textColor)
        )
        Box(
            modifier = Modifier
                .background(priceChangeUIModel.priceChangeBgColor),
        ) {
            Text(
                modifier = Modifier
                    .padding(
                        horizontal = LocalDimens.current.dimen6,
                        vertical = LocalDimens.current.dimen2
                    ),
                maxLines = 1,
                overflow = TextOverflow.Ellipsis,
                text = priceChangeRate.displayPriceChangeRate() ?: "0.0%",
                style = LocalTypography.current.text12.medium.copy(color = priceChangeUIModel.textColor)
            )
        }
    }
}

@Preview
@Composable
fun PreviewPriceChangeRatePositive() {
    PriceChangeRate(
        modifier = Modifier.background(LocalAppColor.current.bgDefault),
        priceChange = "55",
        priceChangeRate = "50"
    )
}


@Preview
@Composable
fun PreviewPriceChangeRateNegative() {
    PriceChangeRate(
        modifier = Modifier.background(LocalAppColor.current.bgDefault),
        priceChange = "-55",
        priceChangeRate = "-50"
    )
}

@Preview
@Composable
fun PreviewPriceChangeRateDefault() {
    PriceChangeRate(
        modifier = Modifier.background(LocalAppColor.current.bgDefault),
        priceChange = "0.0",
        priceChangeRate = "0.0"
    )
}


@Composable
fun onGetPriceChangeResource(priceChange: String): PriceChangeUIModel {
    val textColor: Color
    val priceChangeBgColor: Color
    val iconPainter: Painter
    when {
        priceChange.isPriceUp() -> {
            textColor = LocalAppColor.current.txtPositive
            priceChangeBgColor = LocalAppColor.current.bgPositive
            iconPainter = painterResource(id = R.drawable.ic_exchange_price_up)
        }

        priceChange.isPriceDown() -> {
            textColor = LocalAppColor.current.txtNegative
            priceChangeBgColor = LocalAppColor.current.bgNegative
            iconPainter = painterResource(id = R.drawable.ic_exchange_price_down)
        }

        priceChange.isPriceZero() -> {
            textColor = LocalAppColor.current.txtParagraph
            priceChangeBgColor = LocalAppColor.current.bgAccent
            iconPainter = painterResource(id = R.drawable.ic_exchange_price_normal)
        }

        else -> {
            textColor = LocalAppColor.current.txtParagraph
            priceChangeBgColor = LocalAppColor.current.bgAccent
            iconPainter = painterResource(id = R.drawable.ic_exchange_price_normal)
        }
    }
    return PriceChangeUIModel(
        textColor = textColor,
        priceChangeBgColor = priceChangeBgColor,
        iconPainter = iconPainter
    )
}