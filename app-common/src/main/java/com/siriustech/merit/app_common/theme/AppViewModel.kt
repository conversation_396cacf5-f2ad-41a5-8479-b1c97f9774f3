package com.siriustech.merit.app_common.theme

import com.siriustech.core_ui_compose.BaseViewModel
import com.siriustech.core_ui_compose.model.ErrorDisplay
import com.siriustech.merit.app_common.component.alert.BannerAlertProperties
import com.siriustech.merit.app_common.typeenum.LanguageType
import kotlinx.coroutines.channels.Channel
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.receiveAsFlow
import kotlinx.coroutines.launch

/**
 * Created by Hein Htet
 */
open class AppViewModel : BaseViewModel() {
    private val _appEvent = Channel<AppEvent>(capacity = Channel.BUFFERED)
    private val _appAction = Channel<AppAction>(capacity = Channel.BUFFERED)
    private val _showHorizontalLoading = MutableStateFlow(false)

    val appEvent: Flow<AppEvent>
        get() = _appEvent.receiveAsFlow()
    val appAction: Flow<AppAction>
        get() = _appAction.receiveAsFlow()

    val showHorizontalLoading: StateFlow<Boolean>
        get() = _showHorizontalLoading

    fun emitBannerAlert(bannerAlertProperties: BannerAlertProperties) {
        scope.launch {
            _appEvent.send(AppEvent.BannerAlertEvent(bannerAlertProperties))
        }
    }

    fun emitHorizontalLoading(value:Boolean) {
        _showHorizontalLoading.value = value
    }

    fun emitError(errorDisplay: ErrorDisplay) {
        scope.launch {
            _appEvent.send(AppEvent.ErrorEvent(errorDisplay))
        }
    }

    fun emitLanguageChanged(languageType: LanguageType) {
        scope.launch {
            _appEvent.send(AppEvent.ChangeLanguageEvent(languageType))
        }
    }


    open fun onTriggerActions(action: AppAction) {
        scope.launch {
            _appAction.send(action)
        }
    }

    open val TAG = "AppViewModel"
}

interface AppAction {}