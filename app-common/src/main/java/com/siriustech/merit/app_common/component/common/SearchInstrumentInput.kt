package com.siriustech.merit.app_common.component.common

import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.tooling.preview.Preview
import com.siriustech.merit.app_common.R
import com.siriustech.merit.app_common.component.textfield.InputBox
import com.siriustech.merit.app_common.component.textfield.InputBoxType
import com.siriustech.merit.app_common.component.textfield.InputProperties
import com.siriustech.merit.app_common.ext.noRippleClickable
import com.siriustech.merit.app_common.theme.LocalAppColor
import com.siriustech.merit.app_common.theme.LocalDimens

@Composable
fun SearchInstrumentInput(
    onBackPress: () -> Unit = {},
    onTextChanged: (value: String) -> Unit = {},
) {
    Column(
        modifier = Modifier
            .fillMaxWidth()
            .height(LocalDimens.current.dimen56)
    ) {
        Row(
            modifier = Modifier
                .background(LocalAppColor.current.bgDefault)
                .fillMaxSize()
                .padding(horizontal = LocalDimens.current.dimen12),
            verticalAlignment = Alignment.CenterVertically
        ) {
            Image(
                modifier = Modifier
                    .noRippleClickable {
                        onBackPress()
                    },
                painter = painterResource(id = R.drawable.ic_back_arrow),
                contentDescription = "Back Image Resource"
            )
            InputBox(
                onTextChange = onTextChanged,
                modifier = Modifier
                    .weight(1f)
                    .padding(start = LocalDimens.current.dimen12),
                properties = InputProperties(
                    placeholder = stringResource(id = R.string.key0400),
                    inputPrefixIcon = painterResource(
                        id = R.drawable.ic_search
                    ),
                    inputBoxType = InputBoxType.PICKER,
                    editable = true,
                    showRightPickerIcon = false
                )
            )
        }
    }
}

@Preview
@Composable
fun PreviewSearchInstrumentInput() {
    SearchInstrumentInput(onBackPress = {},)
}