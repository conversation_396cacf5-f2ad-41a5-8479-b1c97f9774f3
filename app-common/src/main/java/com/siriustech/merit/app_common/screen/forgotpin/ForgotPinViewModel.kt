package com.siriustech.merit.app_common.screen.forgotpin

import androidx.lifecycle.viewModelScope
import com.core.network.base.getError
import com.core.network.base.getErrorMessage
import com.siriustech.merit.apilayer.service.authentication.authresetverify.AuthResetVerifyRequest
import com.siriustech.merit.apilayer.service.authentication.authresetverify.AuthResetVerifyUseCase
import com.siriustech.merit.apilayer.service.authentication.common.GetOTPRequest
import com.siriustech.merit.apilayer.service.authentication.common.GetOTPUseCase
import com.siriustech.merit.apilayer.service.authentication.resetauth.AuthResetRequest
import com.siriustech.merit.apilayer.service.authentication.resetauth.AuthResetUseCase
import com.siriustech.merit.app_common.component.alert.BannerAlertProperties
import com.siriustech.merit.app_common.component.alert.BannerAlertType
import com.siriustech.merit.app_common.component.otp.VerificationEvent
import com.siriustech.merit.app_common.data.CommonSharedPreferences
import com.siriustech.merit.app_common.theme.AppAction
import com.siriustech.merit.app_common.theme.AppViewModel
import com.siriustech.merit.app_common.typeenum.Auth2FAType
import com.siriustech.merit.app_common.typeenum.AuthResetType
import com.siriustech.merit.app_common.typeenum.BizType
import com.siriustech.merit.app_common.typeenum.CreateNewPinStep
import com.siriustech.merit.app_common.typeenum.OtpType
import com.siriustech.merit.app_common.utils.ErrorCode.mapToErrorDisplay
import com.siriustech.merit.app_common.utils.Validation.isValidEmail
import dagger.hilt.android.lifecycle.HiltViewModel
import java.util.concurrent.TimeUnit
import javax.inject.Inject
import javax.inject.Named
import kotlinx.coroutines.channels.Channel
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.SharingStarted
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.catch
import kotlinx.coroutines.flow.collectLatest
import kotlinx.coroutines.flow.map
import kotlinx.coroutines.flow.onCompletion
import kotlinx.coroutines.flow.onStart
import kotlinx.coroutines.flow.receiveAsFlow
import kotlinx.coroutines.flow.stateIn
import kotlinx.coroutines.launch

/**
 * Created by Hein Htet
 */
@HiltViewModel
class ForgotPinViewModel @Inject constructor(
    private val commonSharedPreferences: CommonSharedPreferences,
    private val authResetVerifyUseCase: AuthResetVerifyUseCase,
    private val getOTPUseCase: GetOTPUseCase,
    private val authResetUseCase: AuthResetUseCase,
    @Named("DeviceID") private val deviceID: String,
) : AppViewModel() {
    private val _confirmPin = MutableStateFlow("")
    private val _createPin = MutableStateFlow("")
    private val _currentStep = MutableStateFlow(0)
    private val _email = MutableStateFlow("")
    private val _otpCode = MutableStateFlow("")
    private val _authType = MutableStateFlow(Auth2FAType.EMAIL)
    private val _otpExpiredTime = MutableStateFlow(0L)
    private val _otpResendCount = MutableStateFlow(0)
    private val _refNumber = MutableStateFlow("-")
    private val _token = MutableStateFlow("")

    private val _pinUnMatchCount = MutableStateFlow(0)

    private val _verificationEvent = Channel<VerificationEvent>(capacity = Channel.BUFFERED)


    inner class CreateNewPinInputs : BaseInputs() {
        fun onConfirmPinChange(value: String) {
            _confirmPin.value = value
        }

        fun onCreateNewPinChange(value: String) {
            _createPin.value = value
        }

        private fun onResetCreateNewPin() {
            _createPin.value = ""
        }

        private fun onResetConfirmPin() {
            _confirmPin.value = ""
        }

        fun updateCurrentStep(step: Int) {
            _currentStep.value = step
        }

        fun onResetPin() {
            if (_currentStep.value == CreateNewPinStep.CREATE_PIN.step) {
                onResetCreateNewPin()
            } else {
                onResetConfirmPin()
            }
        }

        fun onComparePinCode() {
            if (_createPin.value != _confirmPin.value) {
                if (_pinUnMatchCount.value == 4) {
                    _pinUnMatchCount.value = 0
                    _createPin.value = ""
                    onTriggerActions(ForgotPinAction.PinUnMatchExceed)
                } else {
                    _pinUnMatchCount.value += 1
                }
                emitBannerAlert(
                    BannerAlertProperties(
                        "Error",
                        description = "Pin code does not match",
                        type = BannerAlertType.ALERT_ERROR
                    )
                )
                _confirmPin.value = ""
            } else {
                updatePinCode()
            }
        }

        fun updateOtpCode(code: String) {
            _otpCode.value = code
        }

        fun onResendOTPCode() = onRequestOTP()

        fun onSubmitVerificationCode(code: String) {
            authResetVerify()
        }

        fun onRequestOTP() = onRequestOTPApiCall()

        fun onOTPVerificationEventChanged(event: VerificationEvent) {
            viewModelScope.launch {
                _verificationEvent.send(event)
            }
        }


    }

    inner class CreateNewPinOutputs : BaseOutputs() {

        val createPin: StateFlow<String>
            get() = _createPin

        val confirmPin: StateFlow<String>
            get() = _confirmPin

        val currentStep: StateFlow<Int>
            get() = _currentStep

        val otpCode: StateFlow<String>
            get() = _otpCode

        val authType: StateFlow<Auth2FAType>
            get() = _authType

        val email: StateFlow<String>
            get() = _email


        val otpExpiredTime: StateFlow<Long>
            get() = _otpExpiredTime

        val refNumber: StateFlow<String>
            get() = _refNumber

        val otpResendCount: StateFlow<Int>
            get() = _otpResendCount


        val isValidEmail: StateFlow<Boolean> = _email.map {
            it.isValidEmail()
        }
            .stateIn(
                viewModelScope,
                initialValue = false,
                started = SharingStarted.WhileSubscribed(5000)
            )

        val verificationEvent: Flow<VerificationEvent>
            get() = _verificationEvent.receiveAsFlow()

        val pinUnMatchCount: StateFlow<Int>
            get() = _pinUnMatchCount
    }


    override val inputs = CreateNewPinInputs()
    override val outputs = CreateNewPinOutputs()

    override fun onTriggerActions(action: AppAction) {
        when (action) {
            is ForgotPinAction.OnEmailTextChanged -> {
                _email.value = action.value
            }

            is ForgotPinAction.OnComparePinCode -> {
                inputs.onComparePinCode()
            }

            else -> super.onTriggerActions(action)
        }
    }

    private fun updatePinCode() {
        scope.launch {
            authResetUseCase(
                param = AuthResetRequest(
                    pin = _createPin.value,
                    deviceId = deviceID,
                    token = _token.value
                )
            )
                .catch {
                    emitBannerAlert(
                        BannerAlertProperties(
                            description = it.getErrorMessage(),
                            type = BannerAlertType.ALERT_ERROR
                        )
                    )
                }
                .onStart { inputs.emitLoading(true) }
                .onCompletion { inputs.emitLoading(false) }
                .collectLatest {
                    onTriggerActions(ForgotPinAction.OnPinCodeUpdated)
                }
        }
    }

    private fun onRequestOTPApiCall() {
        scope.launch {
            getOTPUseCase(
                param = GetOTPRequest(
                    bizType = BizType.FORGET_PASSWORD_PIN.type,
                    otpAddress = _email.value,
                    otpType = OtpType.EMAIL.value
                )
            )
                .onStart { inputs.emitLoading(true) }
                .catch { emitError(it.getError().mapToErrorDisplay()) }
                .onCompletion { inputs.emitLoading(false) }
                .collectLatest {
                    _otpResendCount.value += 1
                    _refNumber.value = it.refCode.orEmpty()
                    _otpExpiredTime.value = TimeUnit.SECONDS.toMillis(it.expireSeconds ?: 0)
                    _verificationEvent.send(VerificationEvent.OnOTPSent)
                }
        }
    }

    private fun authResetVerify() {
        val request = AuthResetVerifyRequest(
            username = _email.value,
            otpType = OtpType.EMAIL.value,
            otp = _otpCode.value,
            refCode = _refNumber.value,
            type = AuthResetType.FORGOT_PIN.type
        )
        scope.launch {
            authResetVerifyUseCase(param = request)
                .onStart { inputs.emitLoading(true) }
                .catch { emitError(it.getError().mapToErrorDisplay()) }
                .onCompletion { inputs.emitLoading(false) }
                .collectLatest {
                    _token.value = it.token
                    onTriggerActions(ForgotPinAction.OnOTPVerified)
                }
        }
    }
}