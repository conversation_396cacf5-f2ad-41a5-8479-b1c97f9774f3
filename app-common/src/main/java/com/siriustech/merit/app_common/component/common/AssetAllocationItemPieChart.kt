package com.siriustech.merit.app_common.component.common

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxHeight
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.remember
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.toArgb
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.tooling.preview.Preview
import com.github.mikephil.charting.charts.PieChart
import com.github.mikephil.charting.data.PieData
import com.github.mikephil.charting.data.PieDataSet
import com.github.mikephil.charting.data.PieEntry
import com.siriustech.merit.app_common.Constants.getCommonPieChartColors
import com.siriustech.merit.app_common.R
import com.siriustech.merit.app_common.component.chart.AppPieChart
import com.siriustech.merit.app_common.data.display.PortfolioItemDisplayData
import com.siriustech.merit.app_common.ext.colorTxtInactive
import com.siriustech.merit.app_common.ext.colorTxtParagraph
import com.siriustech.merit.app_common.theme.LocalDimens
import com.siriustech.merit.app_common.theme.LocalTypography

/**
 * Created by Hein Htet
 */

@Composable
fun AssetAllocationItemPieChart(
    modifier: Modifier = Modifier,
    items: List<PortfolioItemDisplayData>,
    onInfoClicked: () -> Unit = {},
) {

    val context = LocalContext.current
    val pieChart = remember {
        PieChart(context)
    }
    val colorList = getCommonPieChartColors()
    val maxItems = if (items.size > 10) {
        ArrayList(items.take(10))
    } else {
        items
    }

    val itemsWithColors = maxItems.mapIndexed { index, item ->
        item to colorList[index % colorList.size]
    }

    val middle = itemsWithColors.size / 2
    val firstHalf = itemsWithColors.subList(0, middle)
    val secondHalf = itemsWithColors.subList(middle, itemsWithColors.size)

    Column(
        modifier = Modifier
            .fillMaxSize()
            .then(modifier)
    ) {
//        Row(verticalAlignment = Alignment.CenterVertically) {
//            Text(
//                text = stringResource(id = R.string.key0401),
//                style = LocalTypography.current.text14.medium.colorTxtTitle(),
//                modifier = Modifier
//                    .padding(
//                        top = LocalDimens.current.dimen12,
//                        start = LocalDimens.current.dimen12,
//                        end = LocalDimens.current.dimen12,
//                        bottom = LocalDimens.current.dimen12
//                    )
//            )
//            Image(
//                modifier = Modifier.clickable { onInfoClicked() },
//                painter = painterResource(id = R.drawable.ic_info_big),
//                contentDescription = "Info Image Resource"
//            )
//        }
        val entries = itemsWithColors.map { PieEntry(it.first.percentage.toFloatOrNull() ?: 0f) }
        val dataSet = PieDataSet(entries, "AssetAllocation Pie Chart")
            .apply {
                colors = itemsWithColors.map { it.second.toArgb() }
                this.setAutomaticallyDisableSliceSpacing(false)
                this.setDrawValues(false)
            }
        val pieData = PieData(dataSet)
        Box(modifier = Modifier.fillMaxSize(), contentAlignment = Alignment.Center) {
            Row(modifier = Modifier.fillMaxSize()) {
                Column(
                    modifier = Modifier
                        .weight(0.6f)
                        .fillMaxHeight(),
                    verticalArrangement = Arrangement.Center
                ) {
                    secondHalf.forEachIndexed { i, item ->
                        DisplayAllocationItemLabel(isLeft = true, item.first, color = item.second)
                    }
                }
                AppPieChart(
                    pieChart,
                    pieData = pieData, modifier = Modifier
                        .weight(0.8f)
                        .fillMaxHeight()
                )
                Column(
                    modifier = Modifier
                        .weight(0.6f)
                        .fillMaxHeight(),
                    verticalArrangement = Arrangement.Center
                ) {
                    firstHalf.forEachIndexed { i, item ->
                        DisplayAllocationItemLabel(
                            isLeft = false,
                            item.first,
                            i == 4,
                            color = item.second
                        )
                    }
                }
            }
            if (entries.isEmpty()) {
                Text(
                    text = stringResource(id = R.string.key0572),
                    textAlign = TextAlign.Center,
                    style = LocalTypography.current.text12.regular.colorTxtInactive()
                )
            }
        }
    }
}

@Composable
fun DisplayAllocationItemLabel(
    isLeft: Boolean,
    item: PortfolioItemDisplayData,
    isOther: Boolean = false,
    color: Color,
) {
    Row(
        verticalAlignment = Alignment.CenterVertically,
        horizontalArrangement = if (isLeft) Arrangement.End else Arrangement.Start,
        modifier = Modifier
            .fillMaxWidth()
            .padding(
                start = LocalDimens.current.dimen8,
                end = LocalDimens.current.dimen8,
                top = LocalDimens.current.dimen4,
                bottom = LocalDimens.current.dimen4,
            )
    ) {
        if (!isLeft) {
            Box(
                modifier = Modifier
                    .size(LocalDimens.current.dimen6)
                    .clip(RoundedCornerShape(50))
                    .background(color)
            )
            Spacer(modifier = Modifier.width(LocalDimens.current.dimen6))
        }
        Text(
            text = if (isOther) "Other" else item.categoryAssetList?.firstOrNull()?.symbol.orEmpty(),
            style = LocalTypography.current.text14.regular.colorTxtParagraph()
        )
        if (isLeft && !isOther) {
            Spacer(modifier = Modifier.width(LocalDimens.current.dimen6))
            Box(
                modifier = Modifier
                    .size(LocalDimens.current.dimen6)
                    .clip(RoundedCornerShape(50))
                    .background(color)
            )
        }
    }
}

@Preview(showBackground = true)
@Composable
fun PreviewAssetAllocationItemPieChart() {
    AssetAllocationPieChart(items = listOf())
}