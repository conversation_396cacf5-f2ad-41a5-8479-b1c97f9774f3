package com.siriustech.merit.app_common.ext

import android.net.Uri
import android.os.Build
import android.os.Bundle
import android.os.Parcelable
import android.util.Log
import androidx.compose.animation.AnimatedContentTransitionScope
import androidx.compose.animation.core.tween
import androidx.compose.animation.fadeOut
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.collectAsState
import androidx.navigation.NavBackStackEntry
import androidx.navigation.NavController
import androidx.navigation.NavGraphBuilder
import androidx.navigation.NavHostController
import androidx.navigation.NavType
import androidx.navigation.compose.composable
import androidx.navigation.toRoute
import com.siriustech.merit.app_common.Constants
import kotlin.reflect.KType
import kotlinx.serialization.encodeToString
import kotlinx.serialization.json.Json

/**
 * Created by <PERSON><PERSON>tet
 */

inline fun <reified T : Any> NavGraphBuilder.composableWithTransaction(
    typeMap: Map<KType, @JvmSuppressWildcards NavType<*>> = emptyMap(),
    crossinline content: @Composable (navBackStackEntry : NavBackStackEntry) -> Unit,
) {
    composable<T>(
        typeMap = typeMap,
        enterTransition = {
            return@composable slideIntoContainer(
                AnimatedContentTransitionScope.SlideDirection.Start, tween(400)
            )
        },
        exitTransition = {
            return@composable fadeOut(tween(400))
        },
        popEnterTransition = {
            return@composable slideIntoContainer(
                AnimatedContentTransitionScope.SlideDirection.End, tween(400)
            )
        },
        popExitTransition = {
            return@composable slideOutOfContainer(
                AnimatedContentTransitionScope.SlideDirection.End, tween(400)
            )
        },
    ) {navBackStackEntry ->
        content(navBackStackEntry)
    }
}

//
inline fun <reified T : Any> serializableType(
    isNullableAllowed: Boolean = false,
    json: Json = Json,
) = object : NavType<T>(isNullableAllowed = isNullableAllowed) {

    override fun get(bundle: Bundle, key: String): T? {
        return bundle.getString(key)?.let { value ->
            if (T::class.java.isEnum) {
                java.lang.Enum.valueOf(T::class.java as Class<out Enum<*>>, value) as T
            } else {
                json.decodeFromString(value)
            }
        }
    }

    override fun parseValue(value: String): T {
        return if (T::class.java.isEnum) {
            java.lang.Enum.valueOf(T::class.java as Class<out Enum<*>>, value) as T
        } else {
            json.decodeFromString(value)
        }
    }

    override fun serializeAsValue(value: T): String {
        return if (value::class.java.isEnum) {
            (value as Enum<*>).name
        } else {
            val jsonV = json.encodeToString(value)
            Uri.encode(jsonV)
        }
    }

    override fun put(bundle: Bundle, key: String, value: T) {
        if (value::class.java.isEnum) {
            bundle.putString(key, (value as Enum<*>).name)
        } else {
            bundle.putString(key, json.encodeToString(value))
        }
    }
}

// replace all route
fun NavController.navigateReplaceAll( routeName : Any) {
    this.navigate(routeName) {
        popUpTo(<EMAIL>) {
            inclusive = true
        }
    }
}

// pop back with result data
fun NavController.popBackWithResult(key:String, value : String){
    this.popBackStack()
    this.currentBackStackEntry?.savedStateHandle?.set(
        key,
        value
    )
}

// pop back with result data
fun NavController.popBackWithResult(key:String, value : Map<String,String>){
    this.popBackStack()
    this.currentBackStackEntry?.savedStateHandle?.set(
        key,
        value
    )
}

// example usage to retrieve the value
//if (backStackEntry != null) {
//    // retrieve from previous screen
//    val popReturnResult by backStackEntry.savedStateHandle.getStateFlow(
//        Constants.NAV_RETURN_RESULT_KEY,
//        ""
//    ).collectAsState()
//
//    // listen result
//    LaunchedEffect(popReturnResult) {
//        Log.d("LoginScreen", "popReturnResult: $popReturnResult")
//        if (popReturnResult == Constants.NAV_RETURN_RESULT_PIN_LOGOUT) {
//            viewModel.inputs.clearPin()
//        }
//    }
//}
