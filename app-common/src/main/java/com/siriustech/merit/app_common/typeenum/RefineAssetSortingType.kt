package com.siriustech.merit.app_common.typeenum

import android.content.Context

/**
 * Created by <PERSON><PERSON><PERSON><PERSON>
 */
enum class RefineAssetSortingType(val value: String) {
    PERCENT_CHANGE_UP("PERCENT_CHANGE_UP"),
    PERCENT_CHANGE_DOWN("PERCENT_CHANGE_DOWN"),
    RISK_LOW_TO_HIGH("LOW_TO_HIGH"),
    RISK_HIGH_TO_LOW("HIGH_TO_LOW"),
    A_TO_Z("A_TO_Z"),
    Z_TO_A("Z_TO_A");

    fun displayName(context : Context): String {
        return when (this) {
            PERCENT_CHANGE_UP -> context.getString(com.siriustech.merit.app_common.R.string.key0478)
            PERCENT_CHANGE_DOWN ->context.getString(com.siriustech.merit.app_common.R.string.key0479)
            RISK_LOW_TO_HIGH -> context.getString(com.siriustech.merit.app_common.R.string.key0462)
            RISK_HIGH_TO_LOW -> context.getString(com.siriustech.merit.app_common.R.string.key0463)
            A_TO_Z -> context.getString(com.siriustech.merit.app_common.R.string.key0464)
            Z_TO_A -> context.getString(com.siriustech.merit.app_common.R.string.key0465)
            else -> ""
        }
    }

    companion object {
        fun fromParam(value: String): RefineAssetSortingType? {
            return when (value) {
                PERCENT_CHANGE_UP.value -> PERCENT_CHANGE_UP
                PERCENT_CHANGE_DOWN.value -> PERCENT_CHANGE_DOWN
                RISK_LOW_TO_HIGH.value -> RISK_LOW_TO_HIGH
                RISK_HIGH_TO_LOW.value -> RISK_HIGH_TO_LOW
                A_TO_Z.value -> A_TO_Z
                Z_TO_A.value -> Z_TO_A
                else -> null
            }
        }
    }
}