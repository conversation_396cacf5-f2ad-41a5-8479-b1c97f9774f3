package com.siriustech.merit.app_common.component.text

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.tooling.preview.Preview
import com.siriustech.merit.app_common.ext.colorTxtInverted
import com.siriustech.merit.app_common.ext.colorTxtParagraph
import com.siriustech.merit.app_common.theme.LocalAppColor
import com.siriustech.merit.app_common.theme.LocalDimens
import com.siriustech.merit.app_common.theme.LocalTypography

/**
 * Created by <PERSON><PERSON>
 */

@Composable
fun NumberBoxLabel(modifier: Modifier = Modifier, number: String, description: String = "") {
    Box(
        modifier = modifier,
        contentAlignment = Alignment.Center
    ) {
        Row {
            Box(
                modifier = Modifier
                    .clip(RoundedCornerShape(LocalDimens.current.dimen4))
                    .background(LocalAppColor.current.txtParagraph),
            ) {
                Text(
                    text = number,
                    modifier = Modifier.padding(
                        horizontal = LocalDimens.current.dimen6,
                        vertical = LocalDimens.current.dimen1
                    ),
                    style = LocalTypography.current.text12.semiBold.colorTxtInverted()
                )
            }
            if (description.isNotEmpty()) {
                Spacer(modifier = Modifier.width(LocalDimens.current.dimen8))
                Text(description, style = LocalTypography.current.text12.light.colorTxtParagraph())
            }
        }
    }
}

@Preview(showBackground = true, showSystemUi = true)
@Composable
fun NumberBoxLabelPreview() {
    NumberBoxLabel(number = "1", description = "Turn your head to left")
}