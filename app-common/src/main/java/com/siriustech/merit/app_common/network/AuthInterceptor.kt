package com.siriustech.merit.app_common.network

import android.content.Context
import android.content.Intent
import com.siriustech.merit.app_common.Constants
import com.siriustech.merit.app_common.data.CommonSharedPreferences
import okhttp3.Interceptor
import okhttp3.Response
import timber.log.Timber

class AuthInterceptor(
    private val context: Context,
    private val commonSharedPreferences: CommonSharedPreferences,
) : Interceptor {
    companion object {
        // 上次发送广播的时间戳
        @Volatile
        private var last403BroadcastTime = 0L
        // 两次广播最小间隔，单位毫秒
        private const val MIN_INTERVAL_MS = 2500
    }

    override fun intercept(chain: Interceptor.Chain): Response {
        val request = chain.request()
        val response = chain.proceed(request)

        if (response.code == 403 && shouldSendBroadcast()) {
            commonSharedPreferences.setSessionID("")
            Timber.d("STATUS_CODE ${response.code}")
            context.sendBroadcast(Intent(Constants.AUTH_403_ACTION))
        }
        return response
    }

    private fun shouldSendBroadcast(): Boolean {
        val now = System.currentTimeMillis()
        return if (now - last403BroadcastTime > MIN_INTERVAL_MS) {
            last403BroadcastTime = now
            true
        } else {
            false
        }
    }
}