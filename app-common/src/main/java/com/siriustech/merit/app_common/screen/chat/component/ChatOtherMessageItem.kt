package com.siriustech.merit.app_common.screen.chat.component

import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.ColumnScope
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.sp
import com.siriustech.merit.app_common.R
import com.siriustech.merit.app_common.data.display.UserBasicInfoDisplay
import com.siriustech.merit.app_common.ext.colorTxtParagraph
import com.siriustech.merit.app_common.ext.colorTxtTitle
import com.siriustech.merit.app_common.ext.noRippleClickable
import com.siriustech.merit.app_common.theme.LocalAppColor
import com.siriustech.merit.app_common.theme.LocalDimens
import com.siriustech.merit.app_common.theme.LocalTypography

/**
 * Created by Hein Htet
 */

@Composable
fun ColumnScope.ChatOtherMessageItem(
    message: ChatMessageDisplayModel = ChatMessageDisplayModel(),
    userInfo: UserBasicInfoDisplay = UserBasicInfoDisplay(),
    showAvatar: Boolean = false,
    onAttachmentClicked: (AttachmentDisplayModel) -> Unit = {},
) {
    Row(
        modifier = Modifier
            .fillMaxWidth(0.95f)
            .align(Alignment.Start),
    ) {
        if (showAvatar) {
            Box(
                modifier = Modifier
                    .size(LocalDimens.current.dimen44)
                    .border(
                        LocalDimens.current.dimen1,
                        androidx.compose.ui.graphics.Color.Gray.copy(alpha = 0.2f),
                        CircleShape
                    )
                    .padding(LocalDimens.current.dimen8),
                contentAlignment = Alignment.Center,
            ) {
                Image(
                    painter = painterResource(id = R.drawable.ic_merit_chat_logo),
                    modifier = Modifier
                        .size(LocalDimens.current.dimen32)
                        .clip(CircleShape),
                    contentDescription = ""
                )
            }
        } else {
            Box(modifier = Modifier.size(LocalDimens.current.dimen40))
        }
        Column(
            modifier = Modifier.weight(0.8f),
            horizontalAlignment = Alignment.Start
        ) {
            if (showAvatar) {
                Text(
                    text = message.senderName,
                    style = LocalTypography.current.text12.light.colorTxtParagraph()
                        .copy(fontSize = 8.sp)
                )
            }
            if (message.text.isNotEmpty()) {
                OtherChatBubble(showShape = showAvatar) {
                    Text(
                        text = message.text,
                        style = LocalTypography.current.text12.light.colorTxtTitle(),
                        modifier = Modifier.padding(LocalDimens.current.dimen8)
                    )
                }
            }
            ChatOtherAttachment(showAvatar, message.attachments, onAttachmentClicked)
        }

    }
}


@Composable
fun OtherChatBubble(showShape: Boolean = false, content: @Composable () -> Unit = {}) {
    Row(
        modifier = Modifier
            .padding(vertical = LocalDimens.current.dimen4)
    ) {
        if (showShape) {
            Image(
                modifier = Modifier,
                painter = painterResource(id = R.drawable.ic_other_chat_bubble),
                contentDescription = "Shape Image Resource",
            )
        }
        Box(
            modifier = Modifier
                .weight(1f, fill = false)
                .background(LocalAppColor.current.bgTone)
                .clip(RoundedCornerShape(LocalDimens.current.dimen2))
        ) {
            content()
        }
    }
}

@Composable
fun ChatOtherAttachment(
    showAvatar: Boolean, attachments: List<AttachmentDisplayModel>,
    onAttachmentClicked: (AttachmentDisplayModel) -> Unit = {},
) {
    if (attachments.isNotEmpty()) {
        Column(modifier = Modifier) {
            attachments.forEachIndexed { index, attachmentDisplayModel ->
                OtherChatBubble(showShape = showAvatar && index == 0) {
                    ChatAttachment(
                        attachmentDisplayModel = attachmentDisplayModel,
                        showBorder = false,
                        showRemoveButton = false,
                        modifier = Modifier
                            .clip(RoundedCornerShape(LocalDimens.current.dimen2))
                            .background(LocalAppColor.current.bgDefault)
                            .padding(LocalDimens.current.dimen8)
                            .noRippleClickable {
                                onAttachmentClicked(attachmentDisplayModel)
                            }
                    )
                }
            }
        }
    }
}

@Preview(showBackground = true, showSystemUi = true)
@Composable
fun ChatOtherMessageItemPreview() {
    Column(
        modifier = Modifier
            .fillMaxSize()
    ) {
        ChatOtherMessageItem(
            showAvatar = true,
            message = ChatMessageDisplayModel(
                senderName = "Admin",
                attachments = listOf(
                    AttachmentDisplayModel(
                        type = "PDF",
                        name = "Attachment01.pdf"
                    ),
                    AttachmentDisplayModel(
                        type = "PDF",
                        name = "Attachment02.pdf"
                    )
                ),
                text = "Lorem Ipsum is simply dummy t ambled it to make a type specimen book. It has survived not only five centuries, but also the leap into electronic typesetting, remaining essentially unchanged. It was popularised in the 1960s with the release of Letraset sheets containing Lorem Ipsum passages, and more recently with desktop publishing software like Aldus PageMaker including versions of Lorem Ipsum"
            )
        )
        ChatOtherMessageItem(
            message = ChatMessageDisplayModel(
                text = "Hello"
            )
        )
        ChatOtherMessageItem(
            message = ChatMessageDisplayModel(
                attachments = listOf(
                    AttachmentDisplayModel(
                        type = "PDF",
                        name = "Attachment01.pdf"
                    ),
                    AttachmentDisplayModel(
                        type = "PDF",
                        name = "Attachment02.pdf"
                    )
                ),
                text = "Lorem Ipsum is simply dummy t ambled it to make a type specimen book. It has survived not only five centuries, but also the leap into electronic typesetting, remaining essentially unchanged. It was popularised in the 1960s with the release of Letraset sheets containing Lorem Ipsum passages, and more recently with desktop publishing software like Aldus PageMaker including versions of Lorem Ipsum"
            )
        )
    }
}