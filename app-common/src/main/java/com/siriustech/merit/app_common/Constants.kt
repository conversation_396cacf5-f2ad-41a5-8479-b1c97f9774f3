package com.siriustech.merit.app_common

import androidx.compose.runtime.Composable
import androidx.compose.ui.graphics.Color
import com.siriustech.merit.app_common.theme.LocalAppColor

/**
 * Created by <PERSON><PERSON> <PERSON><PERSON><PERSON>
 */
object Constants {
    const val DEFAULT_OTP_COUNT = 6

    const val ACTION_ENABLE = "ENABLE"
    const val ACTION_DISABLE = "DISABLE"


    // onboarding step
    const val IDENTIFICATION_STEP = "IDENTIFICATION"
    const val PERSONAL_INFO_STEP = "PERSONAL_INFO"
    const val RISK_ASSESSMENT_STEP = "RISK_ASSESSMENT"
    const val DECLARATION_STEP = "DECLARATION"
    const val DOCUMENT_UPLOAD_STEP = "DOCUMENT_UPLOAD"
    const val CONFIRM_STEP = "CONFIRMATION"
    const val CONFIRMATION_STEP = "CONFIRMATION"
    const val STATUS_DONE = "DONE"


    // NAVIGATION
    const val NAV_RETURN_RESULT_KEY = "NAV_RETURN_RESULT_KEY"
    const val NAV_RETURN_RESULT_PIN_LOGOUT = "PIN_LOGOUT"
    const val NAV_RETURN_RESULT_LIVENESS_CHECK_PASSED = "NAV_RETURN_RESULT_LIVENESS_CHECK_PASSED"
    const val NAV_RETURN_RESULT_EMAIL_OTP_VERIFY_RESULT =
        "NAV_RETURN_RESULT_EMAIL_OTP_VERIFY_RESULT"

    const val NAV_RETURN_SUCCESS = "NAV_RETURN_SUCCESS"

    // INTENT_EXTRA_KEY
    const val KEY_INTENT_EXTRA_COMMON_EMAIL_OTP_ARGUMENT =
        "KEY_INTENT_EXTRA_COMMON_EMAIL_OTP_ARGUMENT"
    const val KEY_RETURN_INTENT_EXTRA_COMMON_EMAIL_OTP_ARGUMENT =
        "KEY_RETURN_INTENT_EXTRA_COMMON_EMAIL_OTP_ARGUMENT"
    const val RESULT_EXTRA_ASSET_DATA = "RESULT_EXTRA_ASSET_DATA"

    // PASSWORD RULE
    const val AT_LEAST_8_CHARACTERS = "AT_LEAST_8_CHARACTERS"
    const val AT_LEAST_1_UPPERCASE = "AT_LEAST_1_UPPERCASE"
    const val AT_LEAST_1_LOWERCASE = "AT_LEAST_1_LOWERCASE"
    const val AT_LEAST_1_NUMBER = "AT_LEAST_1_NUMBER"
    const val AT_LEAST_1_SPECIAL_CHAR = "AT_LEAST_1_SPECIAL_CHAR"


    // region
    const val REGION_HK = "HK"
    const val REGION_CHINA = "CN"


    // currency
    const val CURRENCY_USD = "USD"

    //file
    const val MAX_FILE_SIZE = 10485760L // 10MB

    const val STATUS_PASSED = "STATUS_PASSED"
    const val STATUS_FAILED = "STATUS_FAILED"
    const val STATUS_CHANGE_PASSWORD_SUCCESS = "STATUS_CHANGE_PASSWORD_SUCCESS"
    const val STATUS_CHANGE_PIN_SUCCESS = "STATUS_CHANGE_PIN_SUCCESS"
    const val STATUS_BIOMETRIC_SUCCESS = "STATUS_BIOMETRIC_SUCCESS"


    const val AUTH_403_ACTION = "AUTH_403_ACTION"
    const val CHANGE_LANGUAGE = "CHANGE_LANGUAGE"
    const val KEY_BENCHMARK = "BENCHMARK"

    // image base url

    const val COMMON_FILE_BASE_URL = "common/file?fileKey="

    // action
    const val ACTION_ADD = "ADD"
    const val ACTION_EDIT = "EDIT"
    const val ACTION_DUPLICATE = "DUPLICATE"
    const val ACTION_RE_CALCULATE = "RECALCULATE"
    const val ACTION_DELETE = "DELETE"
    const val ACCOUNT_PENDING_CLOSURE = "PENDING_CLOSURE"
    const val CHAT_STATUS_BEFORE = "BEFORE"

    // error code

    const val DUPLICATE_USER_INFO = "TX00000017"


    // INTERVAL API CALL
    const val INTERVAL_API_CALL_IN_SEC = 5L

    // doc type
    const val DOC_TYPE_PDF = "pdf"

    //status
    const val PENDING_ACCOUNT_STATUS = "PENDING_REVIEW"


    // risk assessment
    const val EXTRA_RISK_ASSESSMENT_TYPE = "EXTRA_RISK_ASSESSMENT_TYPE"
    const val GENERAL_RISK_ASSESSMENT_INDEX = 0
    const val INVEST_RISK_EXPERIENCES_INDEX = 1
    const val DERIVATIVE_RISK_EXPERIENCES_INDEX = 2
    const val FINANCIAL_RISK_ASSESSMENT_INDEX = 3

    val numberList = (1..100).map { it.toString() }

    val romanNumber = listOf("i", "ii", "iii", "iv", "v", "vi", "vii", "viii", "ix", "x")


    @Composable
    fun getCommonPieChartColors(): List<Color> {
        val commonColorList = listOf(
            LocalAppColor.current.chartEmeraldGreen,
            LocalAppColor.current.chartPurple,
            LocalAppColor.current.chartOrange,
            LocalAppColor.current.chartBlue,
            LocalAppColor.current.chartRed,
            LocalAppColor.current.chartLightRed,
            LocalAppColor.current.chartLightBlue,
            LocalAppColor.current.chartPink,
            LocalAppColor.current.chartYellow,
            LocalAppColor.current.chartGreen
        )
        return commonColorList
    }

    const val LOCAL_VALID_OTP_CODE = "123456"
    const val LOCAL_OTP_CODE_FOR_CREDENTIAL_UPDATE = "123456"

    //
    const val COUNTRY_CHINA = "China"
    const val COUNTRY_HONG_KONG = "Hong Kong"

    // identity information
    const val CHINA_CITIZEN_CARD = "CN_CITIZEN_CARD"
    const val CHINESE_IDENTITY_CARD = "CHINESE_ID_CARD"
    const val HK_ID_CARD = "HK_ID_CARD"
    const val HONG_KONG_CITIZEN_CARD = "HK_CITIZEN_CARD"
    const val HK_ENTRY_PERMIT_CARD = "HK_ENTRY_PERMIT"
    const val PASSPORT = "PASSPORT"
    const val OTHERS = "OTHERS"
    const val isHongKong = "HK"
    const val isChina = "CN"
    const val EXTRA_PHONE_PHONE_NUMBER = "EXTRA_PHONE_PHONE_NUMBER"


    const val RISK_LEVEL_HIGH = "HIGH"
    const val RISK_LEVEL_MEDIUM = "MEDIUM"
    const val RISK_LEVEL_LOW = "LOW"

    // status
    const val STATUS_APPROVED = "approved"
    const val STATUS_REJECTED = "rejected"

    const val ADDRESS_PROOF = "ADDRESS_PROOF"
    const val ASSET_PROOF = "ASSET_PROOF"

    // order status
    const val DEPOSIT = "Deposit"
    const val WITHDRAW = "Withdraw"


    //menu

    const val BROADCAST_ACTION_MENU_INDEX = "BROADCAST_ACTION_MENU_INDEX"
    const val EXTRA_MENU_INDEX = "EXTRA_MENU_INDEX"


    // identity card type
//    CHINESE_ID_CARD, PASSPORT, HK_ID_CARD, HK_ENTRY_PERMIT



    const val NO_EXCHANGE = "NO_EXCHANGE"

    const val ERROR_CODE_OCR_FAILED = "TXAD000001"
    const val ERROR_CODE_LIVENESS_CHECK_FAILED = "TXCM100501"

    const val FLAVOR_UAT = "UAT"
    const val FLAVOR_PROD = "PROD"


    // config
    const val SUPPORT_EMAIL  ="<EMAIL>"
    const val SUPPORT_MOBILE  ="+852 26277691"


}