package com.siriustech.merit.app_common.component.common

import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.RowScope
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.offset
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import com.siriustech.merit.app_common.R
import com.siriustech.merit.app_common.ext.colorTxtInactive
import com.siriustech.merit.app_common.ext.colorTxtTitle
import com.siriustech.merit.app_common.ext.noRippleClickable
import com.siriustech.merit.app_common.theme.LocalAppColor
import com.siriustech.merit.app_common.theme.LocalDimens
import com.siriustech.merit.app_common.theme.LocalTypography
import com.siriustech.merit.app_common.typeenum.ChartTimeFrame

/**
 * Created by Hein Htet
 */

@Composable
fun ChartTimeFrameSelector(
    currentTimeFrame: ChartTimeFrame,
    onTimeFrameSelected: (timeFrame: ChartTimeFrame) -> Unit = {},
    onMenuButtonClicked: () -> Unit = {},
) {

    var currentSelectedTypeFrame by remember {
        mutableStateOf(ChartTimeFrame.ONE_WEEK)
    }


    LaunchedEffect(currentTimeFrame) {
        currentSelectedTypeFrame = currentTimeFrame
    }

    Row(
        modifier = Modifier
            .fillMaxWidth()
    ) {
        Row(
            modifier = Modifier.weight(1f),
            verticalAlignment = Alignment.CenterVertically
        ) {
            Column(
                modifier = Modifier
                    .fillMaxWidth(),
            ) {
                Row(
                    horizontalArrangement = Arrangement.Start,
                    modifier = Modifier
                        .offset(y = LocalDimens.current.dimen6),
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    LabelBox(
                        label = ChartTimeFrame.ONE_WEEK.getDisplayName(),
                        isSelected = currentSelectedTypeFrame == ChartTimeFrame.ONE_WEEK,
                        offset = -2
                    )
                    LabelBox(
                        label = ChartTimeFrame.ONE_MONTH.getDisplayName(),
                        isSelected = currentSelectedTypeFrame == ChartTimeFrame.ONE_MONTH,
                        offset = 1
                    )
                    LabelBox(
                        label = ChartTimeFrame.ONE_YEAR.getDisplayName(),
                        isSelected = currentSelectedTypeFrame == ChartTimeFrame.ONE_YEAR
                    )
                    LabelBox(
                        label = ChartTimeFrame.THREE_YEAR.getDisplayName(),
                        isSelected = currentSelectedTypeFrame == ChartTimeFrame.THREE_YEAR,
                        offset = 16
                    )
                    LabelBox(
                        label = ChartTimeFrame.FIVE_YEAR.getDisplayName(),
                        isSelected = currentSelectedTypeFrame == ChartTimeFrame.FIVE_YEAR,
                        offset = 18
                    )
                    LabelBox(
                        label = ChartTimeFrame.ALL_TIME.getDisplayName(),
                        offset = 26,
                        isSelected = currentSelectedTypeFrame == ChartTimeFrame.ALL_TIME
                    )
                    LabelBox(label = "", lastItem = true, offset = 20)
                }
                Row(
                    modifier = Modifier
                        .padding(
                            start = LocalDimens.current.dimen16,
                            top = LocalDimens.current.dimen12,
                            bottom = LocalDimens.current.dimen8
                        ),
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    TimeFrameItem(
                        isSelected = currentSelectedTypeFrame == ChartTimeFrame.ONE_WEEK,
                        onClicked = {
                            currentSelectedTypeFrame = ChartTimeFrame.ONE_WEEK
                            onTimeFrameSelected(currentSelectedTypeFrame)
                        })
                    TimeFrameItem(
                        isSelected = currentSelectedTypeFrame == ChartTimeFrame.ONE_MONTH,
                        onClicked = {
                            currentSelectedTypeFrame = ChartTimeFrame.ONE_MONTH
                            onTimeFrameSelected(currentSelectedTypeFrame)
                        })
                    TimeFrameItem(
                        isSelected = currentSelectedTypeFrame == ChartTimeFrame.ONE_YEAR,
                        onClicked = {
                            currentSelectedTypeFrame = ChartTimeFrame.ONE_YEAR
                            onTimeFrameSelected(currentSelectedTypeFrame)
                        })
                    TimeFrameItem(
                        isSelected = currentSelectedTypeFrame == ChartTimeFrame.THREE_YEAR,
                        onClicked = {
                            currentSelectedTypeFrame = ChartTimeFrame.THREE_YEAR
                            onTimeFrameSelected(currentSelectedTypeFrame)
                        })
                    TimeFrameItem(
                        isSelected = currentSelectedTypeFrame == ChartTimeFrame.FIVE_YEAR,
                        onClicked = {
                            currentSelectedTypeFrame = ChartTimeFrame.FIVE_YEAR
                            onTimeFrameSelected(currentSelectedTypeFrame)
                        })
                    TimeFrameItem(
                        isSelected = currentSelectedTypeFrame == ChartTimeFrame.ALL_TIME,
                        lastItem = true,
                        onClicked = {
                            currentSelectedTypeFrame = ChartTimeFrame.ALL_TIME
                            onTimeFrameSelected(currentSelectedTypeFrame)
                        }
                    )
                }
            }
        }
        Box(
            modifier = Modifier
                .offset(-LocalDimens.current.dimen16)
                .padding(top = LocalDimens.current.dimen8)
                .size(LocalDimens.current.dimen40)
                .background(LocalAppColor.current.btn3rd)
                .clip(RoundedCornerShape(LocalDimens.current.dimen2))
                .noRippleClickable { onMenuButtonClicked() },
            contentAlignment = Alignment.Center
        ) {
            Image(
                painter = painterResource(id = R.drawable.ic_chart_time_frame),
                contentDescription = "TimeFrame Image Resource"
            )
        }
    }
}

@Composable
fun RowScope.ProgressLine(modifier: Modifier = Modifier) {
    Box(
        modifier = Modifier
            .weight(1f)
            .height(LocalDimens.current.dimen4)
            .background(LocalAppColor.current.bgAccent)
            .then(modifier)
    )
}

@Composable
fun RowScope.LabelBox(
    label: String,
    offset: Int = 8,
    lastItem: Boolean = false,
    isSelected: Boolean = false,
) {
    Box(
        modifier = Modifier
            .weight(1f),
        contentAlignment = Alignment.Center
    ) {
        Box(
            modifier = Modifier
                .offset(x = (offset).dp)
                .background(if (isSelected) LocalAppColor.current.btn4th else Color.Transparent)
                .clip(RoundedCornerShape(LocalDimens.current.dimen2))
                .width(32.dp),
            contentAlignment = Alignment.Center
        ) {
            if (!lastItem) {
                Text(
                    text = label.uppercase(),
                    style = if (isSelected) LocalTypography.current.text14.medium.colorTxtTitle() else LocalTypography.current.text14.light.colorTxtInactive()
                )
            } else {
                Box(
                    modifier = Modifier
                        .width(LocalDimens.current.dimen44)
                        .background(LocalAppColor.current.bgInfo)
                )
            }

        }
    }

}

@Composable
fun TimeFrameBox(
    lastItem: Boolean = false,
    isSelected: Boolean = false,
) {
    Row(verticalAlignment = Alignment.CenterVertically) {
        Box(
            modifier = Modifier
                .size(LocalDimens.current.dimen16)
                .background(LocalAppColor.current.bgAccent)
                .clip(RoundedCornerShape(LocalDimens.current.dimen4)),
            contentAlignment = Alignment.Center
        ) {
            if (isSelected) {
                Box(
                    modifier = Modifier
                        .fillMaxSize()
                        .padding(LocalDimens.current.dimen4)
                        .background(LocalAppColor.current.txtTitle)
                )
            }
        }
        if (!lastItem) {
            ProgressLine()
        }
    }
}

@Composable
fun RowScope.TimeFrameItem(
    modifier: Modifier = Modifier,
    isSelected: Boolean = false,
    lastItem: Boolean = false,
    onClicked: () -> Unit = {},
) {
    Column(
        horizontalAlignment = Alignment.CenterHorizontally,
        verticalArrangement = Arrangement.Center,
        modifier = Modifier
            .offset(if (lastItem) -(24).dp else 0.dp)
            .noRippleClickable { onClicked() }
            .weight(1f)
            .then(modifier)
    ) {
        TimeFrameBox(lastItem = lastItem, isSelected = isSelected)
    }

}


@Preview(showBackground = true)
@Composable
fun PreviewChartTimeFrameSelector() {
    ChartTimeFrameSelector(currentTimeFrame = ChartTimeFrame.ONE_WEEK)
}