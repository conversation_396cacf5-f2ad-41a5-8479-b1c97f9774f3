package com.siriustech.merit.app_common.component.wheel_picker_compose.modal

import androidx.compose.foundation.BorderStroke
import androidx.compose.foundation.Image
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.ModalBottomSheet
import androidx.compose.material3.Text
import androidx.compose.material3.rememberModalBottomSheetState
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.platform.LocalConfiguration
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.unit.DpSize
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.max
import com.siriustech.merit.app_common.R
import com.siriustech.merit.app_common.component.button.ButtonProperties
import com.siriustech.merit.app_common.component.button.SecondaryButton
import com.siriustech.merit.app_common.component.container.PaddingTop
import com.siriustech.merit.app_common.component.modalbts.ModelListContent
import com.siriustech.merit.app_common.component.wheel_picker_compose.WheelDatePicker
import com.siriustech.merit.app_common.component.wheel_picker_compose.core.SelectorProperties
import com.siriustech.merit.app_common.ext.colorTxtParagraph
import com.siriustech.merit.app_common.ext.colorTxtTitle
import com.siriustech.merit.app_common.ext.noRippleClickable
import com.siriustech.merit.app_common.theme.LocalAppColor
import com.siriustech.merit.app_common.theme.LocalDimens
import com.siriustech.merit.app_common.theme.LocalTypography
import kotlinx.coroutines.launch
import org.threeten.bp.LocalDate
import timber.log.Timber

/**
 * Created by Hein Htet
 */

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun WheelDatePickerModal(
    modifier: Modifier = Modifier,
    properties: WheelDatePickerModalProperties,
    currentLocalDate: LocalDate = LocalDate.now(),
    maxLocalDate: LocalDate = LocalDate.now(),
    onSnappedDate: (localDate: LocalDate) -> Unit = {},
    onDismissed: () -> Unit = {},
) {
    val configuration = LocalConfiguration.current
    val sheetState = rememberModalBottomSheetState()
    val coroutine = rememberCoroutineScope()
    val screenWidth = configuration.screenWidthDp.dp

    var selectedDate by remember {
        mutableStateOf(currentLocalDate)
    }

    fun onDismiss() {
        coroutine.launch {
            sheetState.hide()
            onDismissed()
        }
    }

    ModalBottomSheet(
        dragHandle = {},
        shape = RoundedCornerShape(LocalDimens.current.dimen4),
        containerColor = LocalAppColor.current.bgDefault,
        onDismissRequest = {
            onDismiss()
        }, sheetState = sheetState,
        modifier = Modifier
    ) {
        Column(
            modifier = Modifier
                .fillMaxWidth()
                .padding(
                    horizontal = LocalDimens.current.dimen14,
                    vertical = LocalDimens.current.dimen16
                )
                .then(modifier)
        ) {
            WheelDatePickerModalTitle(onDismissed = { onDismiss() }, properties)
            WheelDatePicker(
                startDate = currentLocalDate,
                maxDate = maxLocalDate,
                size = DpSize(screenWidth, 140.dp),
                onSnappedDate = {
                    Timber.d("ON_SNAPPED_DATE $it")
                    if (!properties.showConfirmButton) {
                        onSnappedDate(it)
                    } else {
                        selectedDate = it
                    }
                },
                modifier = Modifier.fillMaxWidth(),
                textStyle = LocalTypography.current.text14.medium.colorTxtTitle(),
                showDate = properties.showDate,
                selectorProperties = com.siriustech.merit.app_common.component.wheel_picker_compose.core.WheelPickerDefaults.selectorProperties(
                    border = BorderStroke(0.dp, Color.Transparent),
                    color = LocalAppColor.current.btn3rd,
                    shape = RoundedCornerShape(LocalDimens.current.dimen4)
                )
            )
            if (properties.showConfirmButton) {
                PaddingTop(value = LocalDimens.current.dimen8)
                SecondaryButton(
                    onClicked = {
                        onSnappedDate(selectedDate)
                        onDismiss()
                    }, properties = ButtonProperties(
                        text = stringResource(
                            id = R.string._key0173
                        )
                    )
                )
            }
        }
    }
}

@Composable
fun WheelDatePickerModalTitle(
    onDismissed: () -> Unit = {},
    properties: WheelDatePickerModalProperties,
) {
    Row(
        modifier = Modifier.fillMaxWidth(),
        verticalAlignment = Alignment.CenterVertically
    ) {
        Spacer(modifier = Modifier.width(LocalDimens.current.dimen4))
        Text(
            text = properties.prefixTitle,
            style = LocalTypography.current.text14.semiBold.colorTxtTitle()
        )
        Text(
            text = properties.title,
            style = LocalTypography.current.text14.regular.colorTxtParagraph()
        )
        Spacer(modifier = Modifier.weight(1f))
        Image(
            painter = painterResource(id = R.drawable.ic_action_close),
            contentDescription = "Close Image Resource",
            modifier = Modifier.noRippleClickable { onDismissed() }
        )
    }
}

data class WheelDatePickerModalProperties(
    val title: String,
    val prefixTitle: String,
    val showDate: Boolean = true,
    val showConfirmButton: Boolean = false,
)