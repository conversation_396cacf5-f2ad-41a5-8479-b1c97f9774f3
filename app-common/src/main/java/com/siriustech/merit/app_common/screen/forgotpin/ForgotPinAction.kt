package com.siriustech.merit.app_common.screen.forgotpin

import com.siriustech.merit.app_common.theme.AppAction

/**
 * Created by <PERSON><PERSON> <PERSON>tet
 */
sealed interface ForgotPinAction : AppAction {
    data class OnEmailTextChanged(val value : String) : ForgotPinAction
    data object OnSubmittedEmail : ForgotPinAction
    data object OnOTPVerified : ForgotPinAction
    data object OnComparePinCode : ForgotPinAction
    data object OnPinCodeCreated : ForgotPinAction
    data object OnPinCodeUpdated : ForgotPinAction
    data object PinUnMatchExceed : ForgotPinAction
}