package com.siriustech.merit.app_common.typeenum

import android.content.Context
import androidx.compose.runtime.Composable
import androidx.compose.ui.platform.LocalContext
import com.siriustech.merit.app_common.R
import com.siriustech.merit.app_common.theme.LocalAppColor

/**
 * Created by Hein Htet
 */
enum class OrderStatus(val value: String) {
    ALL("ALL"),
    PENDING("PENDING"),
    APPROVED("APPROVED"),
    REJECTED("REJECTED");


    companion object {
        fun fromParams(value: String): OrderStatus {
            return when (value) {
                ALL.value -> ALL
                PENDING.value -> PENDING
                APPROVED.value -> APPROVED
                REJECTED.value -> REJECTED
                else -> ALL
            }
        }
    }

    @Composable
    fun Badge() {
        androidx.compose.material3.Badge(
            containerColor = when (this) {
                PENDING -> LocalAppColor.current.btnCaution
                APPROVED -> LocalAppColor.current.btnPositive
                REJECTED -> LocalAppColor.current.btnNegative
                else -> LocalAppColor.current.bgTone
            }
        )
    }

    @Composable
    fun getDisplayName(): String {
        val context = LocalContext.current
        return getDisplayName(context)
    }

    fun getDisplayName(context: Context?): String {
        return when (this) {
            PENDING -> context?.getString(R.string.key0686) ?: "Pending"
            REJECTED ->  context?.getString(R.string.key0688) ?: "Rejected"
            APPROVED ->  context?.getString(R.string.key0687) ?: "Pending"
            else -> context?.getString(R.string.key0681) ?: "All"
        }
    }


    fun getApiValue(): String {
        return when (this) {
            PENDING -> PENDING.value
            REJECTED -> REJECTED.value
            APPROVED -> APPROVED.value
            else -> ""
        }
    }
}