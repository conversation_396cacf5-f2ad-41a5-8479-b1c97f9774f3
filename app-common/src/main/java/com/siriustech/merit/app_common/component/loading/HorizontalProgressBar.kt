package com.siriustech.merit.app_common.component.loading

import androidx.compose.animation.AnimatedVisibility
import androidx.compose.animation.core.tween
import androidx.compose.animation.fadeIn
import androidx.compose.animation.fadeOut
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.material3.LinearProgressIndicator
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.tooling.preview.Preview
import com.siriustech.merit.app_common.theme.LocalAppColor

/**
 * Created by <PERSON><PERSON>
 */

@Composable
fun HorizontalProgressBar(visible: Boolean) {
    AnimatedVisibility(
        visible = visible,
        enter = fadeIn(initialAlpha = 0.4f),
        exit = fadeOut(animationSpec = tween(durationMillis = 250)),
    ) {
        LinearProgressIndicator(
            modifier = Modifier.fillMaxWidth(),
            color = LocalAppColor.current.txtLabel
        )
    }
}

@Preview
@Composable
fun PreviewHorizontalProgressBar() {
    HorizontalProgressBar(visible = true)
}