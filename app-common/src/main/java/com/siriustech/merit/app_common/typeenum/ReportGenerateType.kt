package com.siriustech.merit.app_common.typeenum

import androidx.compose.runtime.Composable
import androidx.compose.ui.res.stringResource
import com.siriustech.merit.app_common.ext.DATE_FORMAT_18
import com.siriustech.merit.app_common.ext.DATE_FORMAT_19
import com.siriustech.merit.app_common.R as AppCommonR

/**
 * Created by Hein Htet
 */
enum class ReportGenerateType(val value: String) {
    INVESTMENT("INVESTMENT"),
    DEPOSIT_WITHDRAWAL("DEPOSIT_WITHDRAW"),
    DEPOSIT("DEPOSIT"),
    WITHDRAWAL("WITHDRAWAL");


    @Composable
    fun getDisplayName(): String {
        return when (this) {
            INVESTMENT -> stringResource(id = AppCommonR.string.key0660)
            DEPOSIT_WITHDRAWAL -> stringResource(id = AppCommonR.string.key0661)
            DEPOSIT -> stringResource(id = AppCommonR.string.key0662)
            WITHDRAWAL -> stringResource(id = AppCommonR.string.key0663)
        }
    }
}

enum class ReportGeneratePeriodType(val value: String) {
    DAILY("DAILY"),
    MONTHLY("MONTHLY");

    fun getDateRangePlaceholder() : String {
        return when (this) {
            DAILY -> "YYYY-MM-DD"
            MONTHLY -> "YYYY-MM"
        }
    }

    fun getDateFormat() : String {
        return when (this) {
            DAILY -> DATE_FORMAT_19
            MONTHLY -> DATE_FORMAT_18
        }
    }



    @Composable
    fun getDisplayName(): String {
        return when (this) {
            MONTHLY -> stringResource(id = AppCommonR.string.key0667)
            DAILY -> stringResource(id = AppCommonR.string.key0668)
        }
    }

}