package com.siriustech.merit.app_common.typeenum

/**
 * Created by <PERSON><PERSON><PERSON>
 */
enum class BizType(val type: String) {
    CUSTOMER_REGISTRATION("CUSTOMER_REGISTRATION"),
    LOGIN_2FA("2FA_LOGIN"),
    CONFIG_2FA("CONFIG_2FA"),
    FORGET_PASSWORD_PIN("FORGET_PASSWORD_PIN"),
    CASH_DEPOSIT("CASH_DEPOSIT"),
    UPDATE_USER_INFO("UPDATE_USER_INFO"),
    CASH_WITHDRAW("CASH_WITHDRAW");

    companion object {
        fun fromParam(value: String): BizType {
            return when (value) {
                CUSTOMER_REGISTRATION.type -> CUSTOMER_REGISTRATION
                LOGIN_2FA.type -> LOGIN_2FA
                CONFIG_2FA.type -> CONFIG_2FA
                FORGET_PASSWORD_PIN.type -> FORGET_PASSWORD_PIN
                CASH_DEPOSIT.type -> CASH_DEPOSIT
                CASH_WITHDRAW.type -> CASH_WITHDRAW
                UPDATE_USER_INFO.type -> UPDATE_USER_INFO
                else -> CUSTOMER_REGISTRATION
            }
        }
    }
}