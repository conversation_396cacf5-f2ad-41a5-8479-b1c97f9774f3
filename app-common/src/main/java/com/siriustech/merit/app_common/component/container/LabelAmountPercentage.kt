package com.siriustech.merit.app_common.component.container

import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.text.TextStyle
import com.siriustech.merit.app_common.ext.colorTxtParagraph
import com.siriustech.merit.app_common.theme.LocalDimens
import com.siriustech.merit.app_common.theme.LocalTypography

/**
 * Created by <PERSON><PERSON>tet
 */
@Composable
fun LabelAmountPercentage(
    modifier: Modifier = Modifier,
    label: String,
    amount: String,
    amountTextStyle : TextStyle? = null
) {
    var textValue = amount
    if(amount.isEmpty()){
        textValue = "-"
    }
    Row(modifier = modifier.fillMaxWidth(), horizontalArrangement = Arrangement.SpaceBetween) {
        Text(
            text = label,
            style = LocalTypography.current.text14.light.colorTxtParagraph()
        )
        Row(verticalAlignment = Alignment.Bottom) {
            Text(
                text = textValue,
                style = LocalTypography.current.text14.regular.colorTxtParagraph().merge(amountTextStyle)
            )
            Text(
                modifier = Modifier.padding(start = LocalDimens.current.dimen2),
                text = "%",
                style = LocalTypography.current.text12.light.colorTxtParagraph()
            )
        }
    }
}