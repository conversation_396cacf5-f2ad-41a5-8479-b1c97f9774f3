package com.siriustech.merit.app_common.component.toggle

import androidx.compose.foundation.Image
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.width
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.painter.Painter
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.tooling.preview.Preview
import com.siriustech.merit.app_common.R
import com.siriustech.merit.app_common.ext.colorTxtTitle
import com.siriustech.merit.app_common.ext.noRippleClickable
import com.siriustech.merit.app_common.theme.LocalDimens
import com.siriustech.merit.app_common.theme.LocalTypography
import timber.log.Timber

/**
 * Created by Hein Htet
 */
@Composable
fun ToggleOnOff(
    modifier: Modifier = Modifier,
    defaultValue: Boolean,
    description: String = "",
    onImage: Painter? = null,
    offImage: Painter? = null,
    isDisabled : Boolean = false,
    needToConfirm:Boolean = false,
    onToggleChanged: (isOn: Boolean) -> Unit = {},
) {
    var isToggled by remember {
        mutableStateOf(defaultValue)
    }

    LaunchedEffect(defaultValue) {
        Timber.d("ON_TOGGLE_CHANGED: $defaultValue")
        isToggled = defaultValue
    }

    val toggleOnImage = onImage ?: painterResource(id = R.drawable.ic_toggle_on_black)
    val toggleOffImage = offImage ?: painterResource(id = R.drawable.ic_toggle_off_gray)
    val disabledImage = painterResource(id = R.drawable.ic_disabled_toggle)

    Row(
        modifier = Modifier
            .then(modifier),
        verticalAlignment = Alignment.CenterVertically
    ) {
        Box(
            modifier = Modifier
                .width(LocalDimens.current.dimen38)
                .height(LocalDimens.current.dimen20)
                .noRippleClickable {
                    if(!needToConfirm){
                        isToggled = !isToggled
                    }
                    onToggleChanged(isToggled)
                },
        ) {
            Image(
                painter = if(isDisabled) disabledImage else if (isToggled) toggleOnImage else toggleOffImage, // Show image based on the toggle state
                contentDescription = if (isToggled) "Toggle On" else "Toggle Off",
                modifier = Modifier.fillMaxSize()
            )
        }
        if (description.isNotEmpty()) {
            Text(
                text = description,
                modifier = Modifier.padding(start = LocalDimens.current.dimen16),
                style = LocalTypography.current.text14.regular.colorTxtTitle()
            )
        }
    }
}

@Preview(showBackground = true)
@Composable
fun PreviewToggleOnOff() {
    ToggleOnOff(defaultValue = true)
}