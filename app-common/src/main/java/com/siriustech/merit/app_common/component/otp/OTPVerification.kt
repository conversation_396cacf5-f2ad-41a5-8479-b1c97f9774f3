package com.siriustech.merit.app_common.component.otp

import android.annotation.SuppressLint
import android.util.Log
import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxHeight
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.State
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableLongStateOf
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.painter.Painter
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.AnnotatedString
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.tooling.preview.Preview
import com.siriustech.merit.app_common.R
import com.siriustech.merit.app_common.component.button.AccentButton
import com.siriustech.merit.app_common.component.button.ButtonProperties
import com.siriustech.merit.app_common.component.button.SecondaryButton
import com.siriustech.merit.app_common.ext.AttributeStringData
import com.siriustech.merit.app_common.ext.buildAttrString
import com.siriustech.merit.app_common.ext.colorTxtLabel
import com.siriustech.merit.app_common.ext.colorTxtNegative
import com.siriustech.merit.app_common.ext.colorTxtParagraph
import com.siriustech.merit.app_common.ext.colorTxtTitle
import com.siriustech.merit.app_common.theme.LocalAppColor
import com.siriustech.merit.app_common.theme.LocalDimens
import com.siriustech.merit.app_common.theme.LocalTypography
import java.util.Locale
import kotlinx.coroutines.delay

/**
 * Created by Hein Htet
 */

@Composable
fun OTPVerification(
    modifier: Modifier = Modifier,
    defaultOtpCode: State<String> = mutableStateOf(""),
    referenceNumber: String? = null,
    otpExpireDuration: Long = 0L,
    otpResendCount: Int = 0,
    properties: OTPVerificationProperties = OTPVerificationProperties(),
    onEventChanged: (VerificationEvent) -> Unit = {},
) {
    var onResendButtonEnabled by remember {
        mutableStateOf(false)
    }

    println("OTP_RESET_COUNT ${otpResendCount}")

    LaunchedEffect(key1 = otpExpireDuration, key2 = otpResendCount) {
        onResendButtonEnabled = false
    }

    Column(
        modifier = Modifier
            .fillMaxSize()
            .fillMaxWidth()
            .fillMaxHeight()
            .padding(LocalDimens.current.dimen12)
            .then(modifier),
    ) {
        if (properties.iconPainter != null) {
            Image(
                painter = properties.iconPainter,
                contentDescription = "Setup 2FA Resource",
                modifier = Modifier.align(Alignment.CenterHorizontally)
            )
        }

        Spacer(modifier = Modifier.height(LocalDimens.current.dimen34))
        Text(
            modifier = Modifier.fillMaxWidth(),
            text = properties.title,
            style = LocalTypography.current.text18.semiBold.colorTxtTitle(),
            textAlign = TextAlign.Center
        )
        Spacer(modifier = Modifier.height(LocalDimens.current.dimen16))
        if (properties.description != null) {
            Text(
                modifier = Modifier.fillMaxWidth(),
                text = properties.description
            )
        }
        Spacer(modifier = Modifier.height(LocalDimens.current.dimen16))
        Box(
            modifier = Modifier
                .fillMaxWidth()
        ) {
            Column(
                modifier = Modifier.fillMaxWidth(),
                horizontalAlignment = Alignment.CenterHorizontally
            ) {
                AppOtpInput(
                    needFocus = properties.needFocus,
                    otpResendCount = otpResendCount,
                    defaultOtpCode = defaultOtpCode.value,
                    onOtpValueUpdated = { otp ->
                        onEventChanged.invoke(VerificationEvent.OnOTPCodeChanged(otp))
                    })

                Spacer(modifier = Modifier.height(LocalDimens.current.dimen18))
                ExpireOtpTimer(
                    otpResendCount,
                    otpExpireDuration,
                    onOTPExpired = {
                        onResendButtonEnabled = true
                    })
                Spacer(modifier = Modifier.height(LocalDimens.current.dimen16))
                ReferenceNo(referenceNumber)
            }
        }

        Spacer(modifier = Modifier.weight(1f))
        Row {
            if (properties.leftButtonText != null) {
                AccentButton(
                    modifier = Modifier.weight(1f),
                    properties = ButtonProperties(
                        text = properties.leftButtonText,
                        enabled = onResendButtonEnabled
                    ),
                    onClicked = {
                        onEventChanged(VerificationEvent.OnResendOTPCode)
                    },
                )
                Spacer(modifier = Modifier.width(LocalDimens.current.dimen8))
            }
            if (properties.rightButtonText != null) {
                SecondaryButton(
                    modifier = Modifier.weight(1f),
                    properties = ButtonProperties(
                        enabled = defaultOtpCode.value.isNotEmpty(),
                        text = stringResource(id = R.string.key0045),
                    ),
                    onClicked = {
                        onEventChanged(VerificationEvent.OnOTPSubmit(code = defaultOtpCode.value))
                    },
                )
            }
        }
    }
}

@SuppressLint("DefaultLocale")
@Composable
fun ExpireOtpTimer(
    resendOTPCount: Int = 0,
    otpExpireDuration: Long,
    onOTPExpired: () -> Unit = {},
) {
    val timeLeftMillis = remember { mutableLongStateOf(0L) }

    LaunchedEffect(key1 = resendOTPCount) {
        timeLeftMillis.longValue = otpExpireDuration
        Log.d("OTPVerification", "ExpireOtpTimer: $timeLeftMillis")
        while (timeLeftMillis.longValue > 0) {
            delay(1000L)
            (timeLeftMillis.longValue) -= 1000L
            if (timeLeftMillis.longValue == 0L) {
                onOTPExpired()
            }
        }
    }

    val minutes = (timeLeftMillis.longValue / 1000) / 60
    val seconds = (timeLeftMillis.longValue / 1000) % 60

    Row(
        modifier = Modifier
            .clip(RoundedCornerShape(LocalDimens.current.dimen2))
            .background(LocalAppColor.current.bgTone)
            .padding(
                vertical = LocalDimens.current.dimen2,
                horizontal = LocalDimens.current.dimen12
            )
    ) {
        Text(
            text = stringResource(id = R.string.key0200),
            style = LocalTypography.current.text14.regular.colorTxtTitle()
        )
        Text(
            text = String.format("%02d:%02d", minutes, seconds, Locale.ENGLISH),
            style = LocalTypography.current.text14.semiBold.colorTxtNegative()
        )
    }
}

@Composable
fun ReferenceNo(referenceNumber: String?) {
    Row(
        modifier = Modifier
    ) {
        Text(
            text = buildAttrString(
                arrayListOf(
                    AttributeStringData(
                        text = stringResource(id = R.string.key0201),
                        textStyle = LocalTypography.current.text14.regular.colorTxtParagraph()
                    ),
                    AttributeStringData(
                        text = referenceNumber.orEmpty(),
                        textStyle = LocalTypography.current.text14.regular.colorTxtLabel()
                    )
                )
            )
        )
    }
}

data class OTPVerificationProperties(
    val title: String = "",
    val iconPainter: Painter? = null,
    val description: AnnotatedString? = null,
    val leftButtonText: String? = null,
    val rightButtonText: String? = null,
    val needFocus : Boolean = true
)

sealed interface VerificationEvent {
    data object OnResendOTPCode : VerificationEvent
    data class OnOTPSubmit(val code: String) : VerificationEvent
    data class OnOTPCodeChanged(val code: String) : VerificationEvent
    data object OnOTPSent : VerificationEvent
    data object OnConfigurePin : VerificationEvent
    data object VerificationSuccess : VerificationEvent
    data class OnVerificationFailed (val value : String) : VerificationEvent
}

@Preview(showBackground = true, showSystemUi = true)
@Composable
fun PreviewOTPVerificationPreview() {
    OTPVerification(
        otpExpireDuration = 120000L,
        referenceNumber = "XXXXXXX",
        properties = OTPVerificationProperties(
            title = "OTP Verification",
            description = buildAttrString(
                listOf(
                    AttributeStringData(
                        text = "The verification code has been sent to your phone number:&#160;",
                        textStyle = LocalTypography.current.text14.light.colorTxtParagraph()
                    ),
                    AttributeStringData(
                        text = "033-****-3333",
                        textStyle = LocalTypography.current.text14.medium.colorTxtLabel()
                    ),
                )
            ),
            iconPainter = painterResource(id = R.drawable.ic_phone_otp_verification),
            leftButtonText = "Resend OTP",
            rightButtonText = "Submit"
        )
    )
}