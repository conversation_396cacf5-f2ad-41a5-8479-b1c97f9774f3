package com.siriustech.merit.app_common.component.common.bank

import androidx.compose.foundation.Image
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.ColorFilter
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.tooling.preview.Preview
import com.siriustech.merit.app_common.component.container.LabelValue
import com.siriustech.merit.app_common.component.container.PaddingStart
import com.siriustech.merit.app_common.component.toggle.ToggleOnOff
import com.siriustech.merit.app_common.ext.colorTxtDisabled
import com.siriustech.merit.app_common.ext.colorTxtLabel
import com.siriustech.merit.app_common.ext.colorTxtTitle
import com.siriustech.merit.app_common.ext.noRippleClickable
import com.siriustech.merit.app_common.ext.underline
import com.siriustech.merit.app_common.theme.LocalAppColor
import com.siriustech.merit.app_common.theme.LocalDimens
import com.siriustech.merit.app_common.theme.LocalTypography
import com.siriustech.merit.app_common.typeenum.BankAccountStatus
import kotlinx.serialization.Serializable
import com.siriustech.merit.app_common.R as AppCommonR

/**
 * Created by Hein Htet
 */

@Composable
fun BankAccountItem(
    modifier: Modifier = Modifier,
    accountNo: String = "",
    showEdit: Boolean = false,
    showDelete: Boolean = false,
    showPrimaryToggle: Boolean = false,
    bankAccountItemDisplayModel: BankAccountItemDisplayModel = BankAccountItemDisplayModel(),
    onEdit: (BankAccountItemDisplayModel) -> Unit = {},
    onDelete: (BankAccountItemDisplayModel) -> Unit = {},
    onPrimaryChanged: (BankAccountItemDisplayModel) -> Unit = {},
) {
    val isApprovedBankAccount = bankAccountItemDisplayModel.status == BankAccountStatus.APPROVED
    val isLabelDisabled =
        bankAccountItemDisplayModel.status == BankAccountStatus.PENDING || bankAccountItemDisplayModel.status == BankAccountStatus.REJECTED
    val isToggleDisabled =
        (bankAccountItemDisplayModel.status == BankAccountStatus.PENDING || bankAccountItemDisplayModel.status == BankAccountStatus.REJECTED) && !bankAccountItemDisplayModel.isPrimaryAccount

    val valueTextStyle =
        if (!isLabelDisabled || bankAccountItemDisplayModel.isPrimaryAccount) LocalTypography.current.text14.regular.colorTxtTitle() else LocalTypography.current.text14.regular.colorTxtDisabled()

    val itemModifier = Modifier.padding(top = LocalDimens.current.dimen8)
    Column(modifier = Modifier.then(modifier)) {
        Row(modifier = Modifier, verticalAlignment = Alignment.CenterVertically) {
            Text(text = accountNo, style = LocalTypography.current.text14.semiBold.colorTxtTitle())
            PaddingStart(value = LocalDimens.current.dimen4)
            bankAccountItemDisplayModel.status.DisplayStatusBadge()
            if (showPrimaryToggle) {
                Spacer(modifier = Modifier.weight(1f))
                Row(verticalAlignment = Alignment.CenterVertically) {
                    Text(
                        text = stringResource(id = AppCommonR.string.key0383),
                        style = valueTextStyle
                    )
                    PaddingStart(value = LocalDimens.current.dimen4)
                    ToggleOnOff(
                        isDisabled = isToggleDisabled,
                        needToConfirm = true,
                        defaultValue = bankAccountItemDisplayModel.isPrimaryAccount,
                        onToggleChanged = {
                            if (isApprovedBankAccount && !bankAccountItemDisplayModel.isPrimaryAccount) {
                                onPrimaryChanged(bankAccountItemDisplayModel.copy(isPrimaryAccount = it))
                            }
                        })
                }
            }
        }
        LabelValue(
            modifier = itemModifier,
            label = stringResource(id = AppCommonR.string.key0737),
            value = bankAccountItemDisplayModel.displayBankAccountType,
            valueTextStyle = valueTextStyle
        )
        LabelValue(
            modifier = itemModifier,
            label = stringResource(id = AppCommonR.string.key0738),
            value = bankAccountItemDisplayModel.bankName,
            valueTextStyle = valueTextStyle
        )
        LabelValue(
            modifier = itemModifier,
            label = stringResource(id = AppCommonR.string.key0739),
            value = bankAccountItemDisplayModel.accountName,
            valueTextStyle = valueTextStyle
        )
        LabelValue(
            modifier = itemModifier,
            label = stringResource(id = AppCommonR.string.key0740),
            value = bankAccountItemDisplayModel.accountNumber,
            valueTextStyle = valueTextStyle
        )
        LabelValue(
            modifier = itemModifier,
            label = stringResource(id = AppCommonR.string.key0741),
            value = bankAccountItemDisplayModel.swiftCode,
            valueTextStyle = valueTextStyle
        )
        LabelValue(
            modifier = itemModifier,
            label = stringResource(id = AppCommonR.string.key0513),
            value = bankAccountItemDisplayModel.currency.joinToString(","),
            valueTextStyle = valueTextStyle
        )
        var address =
            "${bankAccountItemDisplayModel.addressLine},${bankAccountItemDisplayModel.displayCountryRegion}"
        if (bankAccountItemDisplayModel.postalCode.isNotEmpty()) {
            address = address.plus(",${bankAccountItemDisplayModel.postalCode}")
        }
        LabelValue(
            modifier = itemModifier,
            label = stringResource(id = AppCommonR.string.key0742),
            value = address,
            valueTextStyle = valueTextStyle
        )
        if (showEdit) {
            Row(
                verticalAlignment = Alignment.CenterVertically,
                horizontalArrangement = Arrangement.SpaceBetween,
                modifier = Modifier
                    .padding(top = LocalDimens.current.dimen12)
                    .align(Alignment.CenterHorizontally)
            ) {
                if (showDelete) {
                    val isEnableToDelete =
                        !bankAccountItemDisplayModel.isPrimaryAccount && bankAccountItemDisplayModel.status != BankAccountStatus.PENDING
                    val color =
                        if (bankAccountItemDisplayModel.status == BankAccountStatus.PENDING && !bankAccountItemDisplayModel.isPrimaryAccount) LocalAppColor.current.txtDisabled else if (isEnableToDelete) LocalAppColor.current.txtTitle else LocalAppColor.current.txtDisabled
                    Row(
                        modifier = Modifier
                            .padding(LocalDimens.current.dimen12)
                            .weight(1f)
                            .noRippleClickable {
                                if (isEnableToDelete) {
                                    onDelete(bankAccountItemDisplayModel)
                                }
                            },
                        horizontalArrangement = Arrangement.Center,
                        verticalAlignment = Alignment.CenterVertically,

                        ) {
                        Text(
                            text = stringResource(id = AppCommonR.string.key0605),
                            style = LocalTypography.current.text12.semiBold.copy(color = color)
                                .underline()
                        )
                        PaddingStart(value = LocalDimens.current.dimen4)
                        Image(
                            painter = painterResource(id = AppCommonR.drawable.ic_trash_bin_black),
                            contentDescription = "Edit Image Resource",
                            modifier = Modifier.size(LocalDimens.current.dimen12),
                            colorFilter = ColorFilter.tint(color)
                        )
                    }
                }
                val editColor =
                    if (bankAccountItemDisplayModel.status == BankAccountStatus.PENDING && !bankAccountItemDisplayModel.isPrimaryAccount) LocalAppColor.current.txtDisabled else LocalAppColor.current.txtLabel

                Row(
                    modifier = Modifier
                        .padding(LocalDimens.current.dimen12)
                        .weight(1f)
                        .noRippleClickable {
                            if (bankAccountItemDisplayModel.status != BankAccountStatus.PENDING || bankAccountItemDisplayModel.isPrimaryAccount) {
                                onEdit(bankAccountItemDisplayModel)
                            }
                        },
                    horizontalArrangement = Arrangement.Center,
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    Text(
                        text = stringResource(id = AppCommonR.string.key0155),
                        style = LocalTypography.current.text12.semiBold.colorTxtLabel().underline()
                            .copy(
                                color = editColor
                            )
                    )
                    PaddingStart(value = LocalDimens.current.dimen4)
                    Image(
                        modifier = Modifier.size(LocalDimens.current.dimen12),
                        painter = painterResource(id = AppCommonR.drawable.ic_edit_pencil),
                        contentDescription = "Edit Image Resource",
                        colorFilter = ColorFilter.tint(editColor)
                    )
                }

            }
        }
    }
}

@Serializable
data class BankAccountItemDisplayModel(
    val id: String? = null,
    val accountType: String = "",
    val accountName: String = "",
    val bankName: String = "",
    val accountNumber: String = "",
    val swiftCode: String = "",
    val currency: List<String> = emptyList(),
    val address: String = "",
    var isPrimaryAccount: Boolean = false,
    var addressLine: String = "",
    var postalCode: String = "",
    val displayBankAccountType: String = "",
    val displayCountryRegion: String = "",
    val status: BankAccountStatus = BankAccountStatus.PENDING,
)

@Preview(showBackground = true, showSystemUi = true)
@Composable
fun BankAccountItemPreview() {
    BankAccountItem(
        showEdit = true,
        modifier = Modifier.padding(LocalDimens.current.dimen12),
        accountNo = "Bank 01",
        showDelete = true,
        showPrimaryToggle = true,
        bankAccountItemDisplayModel = BankAccountItemDisplayModel(
            address = "333 Sample Road Sample City\n" +
                    "Sample Province 123-456",
            currency = listOf("USD"),
            swiftCode = "123456",
            bankName = "SCB",
            accountNumber = "5555555",
            accountName = "Bank Account No1",
            accountType = "Hong Kong",
            isPrimaryAccount = true,
            displayBankAccountType = "Hong Kong",
            status = BankAccountStatus.PENDING
        )
    )
}