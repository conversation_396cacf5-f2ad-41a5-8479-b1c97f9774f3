package com.siriustech.merit.app_common.component.chart

import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.viewinterop.AndroidView
import com.github.mikephil.charting.charts.PieChart
import com.github.mikephil.charting.data.PieData

/**
 * Created by <PERSON><PERSON>
 */

@Composable
fun AppPieChart(
    pieChart: PieChart,
    modifier: Modifier = Modifier, pieData: PieData,
) {
    AndroidView(
        modifier = modifier,
        factory = { context ->
            pieChart
                .also { pieChart ->
                    pieChart.legend.isEnabled = false
                    pieChart.setDrawEntryLabels(false)
                    pieChart.description.isEnabled = false
                    pieChart.isDrawHoleEnabled = true
                    pieChart.setDrawCenterText(false)
                    pieChart.setUsePercentValues(true)
                    pieChart.description.isEnabled = false
                    pieChart.isHighlightPerTapEnabled = false
                    pieChart.holeRadius = 75f
                    pieChart.invalidate()
                }
        },
        update = { view ->
            try {
                view.data = pieData
            } catch (e: Exception) {
                e.printStackTrace()
            }
            view.invalidate()
        }
    )
}



