package com.siriustech.merit.app_common.utils

import org.intellij.lang.annotations.RegExp

/**
 * Created by <PERSON><PERSON><PERSON>
 */

object Validation {
    fun String.hasAtLeastOneUppercase(): Boolean {
        return Regex("[A-Z]").containsMatchIn(this)
    }
    fun String.hasAtLeastOneLowercase(): Boolean {
        return Regex("[a-z]").containsMatchIn(this)
    }
    fun String.hasAtLeastOneNumber(): Boolean {
        return Regex("[0-9]").containsMatchIn(this)
    }
    fun String.hasAtLeastOneSpecialChar(): Boolean {
        return Regex("[^a-zA-Z0-9]").containsMatchIn(this)
    }
    fun String.isValidEmail(): Boolean {
        return Regex("^[A-Za-z0-9+_.-]+@[A-Za-z0-9.-]+\\.[A-Za-z]{2,6}\$").matches(this)
    }
}