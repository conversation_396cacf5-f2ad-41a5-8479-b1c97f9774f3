package com.siriustech.merit.app_common.screen.accountclosure

import com.siriustech.merit.app_common.data.CommonSharedPreferences
import com.siriustech.merit.app_common.theme.AppViewModel
import dagger.hilt.android.lifecycle.HiltViewModel
import javax.inject.Inject

/**
 * Created by <PERSON><PERSON>
 */
@HiltViewModel
class AccountClosureViewModel @Inject constructor(
    private val commonSharedPreferences: CommonSharedPreferences,
) : AppViewModel() {
    fun clearData() {
        commonSharedPreferences.clearData()
    }
}