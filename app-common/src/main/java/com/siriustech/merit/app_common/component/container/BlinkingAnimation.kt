package com.siriustech.merit.app_common.component.container

import androidx.compose.animation.core.*
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.material3.Surface
import androidx.compose.material3.Text
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.graphicsLayer
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp

@Composable
fun BlinkingAnimation(duration: Int = 500, content: @Composable () -> Unit) {
    val animationValue = remember { Animatable(1f) }

    LaunchedEffect(Unit) {
        animationValue.animateTo(
            targetValue = 0f,
            animationSpec = infiniteRepeatable(
                animation = tween(
                    durationMillis = duration,
                    easing = LinearEasing
                ),
                repeatMode = RepeatMode.Reverse
            )
        )
    }
    Box(
        modifier = Modifier
            .graphicsLayer(alpha = animationValue.value)
    ) {
        content()
    }
}

@Preview
@Composable
fun BlinkingAnimationPreview() {
    Surface {
        Box(modifier = Modifier.fillMaxSize(), contentAlignment = Alignment.Center) {
            BlinkingAnimation() {
                Text(text = "HELLO THIS IS ANIMATION")
            }
        }
    }
}
