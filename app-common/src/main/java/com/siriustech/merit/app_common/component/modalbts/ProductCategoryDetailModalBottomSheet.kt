package com.siriustech.merit.app_common.component.modalbts

import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.ModalBottomSheet
import androidx.compose.material3.Text
import androidx.compose.material3.rememberModalBottomSheetState
import androidx.compose.runtime.Composable
import androidx.compose.runtime.mutableStateListOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.tooling.preview.Preview
import com.siriustech.merit.app_common.R
import com.siriustech.merit.app_common.component.button.AccentButton
import com.siriustech.merit.app_common.component.button.ButtonProperties
import com.siriustech.merit.app_common.component.button.SecondaryBorderButton
import com.siriustech.merit.app_common.component.common.ProductCategoryDetailsItem
import com.siriustech.merit.app_common.component.common.onGetPriceChangeResource
import com.siriustech.merit.app_common.component.container.PaddingTop
import com.siriustech.merit.app_common.component.marquee.StockExchangeMarqueeData
import com.siriustech.merit.app_common.component.separator.SeparatorLine
import com.siriustech.merit.app_common.component.text.BadgeText
import com.siriustech.merit.app_common.ext.colorTxtParagraph
import com.siriustech.merit.app_common.ext.displayPriceChangeRate
import com.siriustech.merit.app_common.theme.LocalAppColor
import com.siriustech.merit.app_common.theme.LocalDimens
import com.siriustech.merit.app_common.theme.LocalTypography
import com.siriustech.merit.app_common.typeenum.RiskLevel
import kotlinx.coroutines.launch

/**
 * Created by Hein Htet
 */
@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun ProductCategoryDetailsModalBottomSheet(
    item: ProductCategoryDetailsModalDisplayData,
    navigateToAssetDetails: (item: ProductCategoryDetailsModalDisplayData) -> Unit,
    onDismissed: () -> Unit = {},
) {
    val scope = rememberCoroutineScope()
    val sheetState = rememberModalBottomSheetState(skipPartiallyExpanded = true)
    ModalBottomSheet(
        dragHandle = {},
        shape = RoundedCornerShape(LocalDimens.current.dimen4),
        containerColor = LocalAppColor.current.bgDefault,
        onDismissRequest = {
            onDismissed()
        }, sheetState = sheetState,
        modifier = Modifier
    ) {
        ProductCategoryDetailsModalContent(item, onDismissed = {
            scope.launch {
                sheetState.hide()
                onDismissed()
            }
        }, navigateToAssetDetails = {
            navigateToAssetDetails(it)
            scope.launch {
                sheetState.hide()
                onDismissed()
            }
        })
    }
}

@Composable
fun ProductCategoryDetailsModalContent(
    item: ProductCategoryDetailsModalDisplayData,
    navigateToAssetDetails: (ProductCategoryDetailsModalDisplayData) -> Unit = {},
    onDismissed: () -> Unit = {},
) {
    Column(
        modifier = Modifier
            .fillMaxWidth()
            .padding(
                top = LocalDimens.current.dimen16,
                start = LocalDimens.current.dimen16,
                end = LocalDimens.current.dimen12,
            )
    ) {
        ProductCategoryDetailsItem(modifier = Modifier, item)
        SeparatorLine(modifier = Modifier.padding(vertical = LocalDimens.current.dimen16))

        Row(modifier = Modifier.fillMaxWidth(), horizontalArrangement = Arrangement.SpaceBetween) {
            Text(
                text = stringResource(id = R.string.key0352),
                style = LocalTypography.current.text14.light.colorTxtParagraph()
            )
            Row(verticalAlignment = Alignment.Bottom) {
                Text(
                    text = item.marketValue,
                    style = LocalTypography.current.text14.regular.colorTxtParagraph()
                )
                Text(
                    modifier = Modifier.padding(start = LocalDimens.current.dimen2),
                    text = item.currency,
                    style = LocalTypography.current.text12.light.colorTxtParagraph()
                )
            }
        }
        Spacer(modifier = Modifier.padding(top = LocalDimens.current.dimen12))
        val glResource = onGetPriceChangeResource(item.unrealizedGlRate)
        Row(modifier = Modifier.fillMaxWidth(), horizontalArrangement = Arrangement.SpaceBetween) {
            Text(
                text = "G/L",
                style = LocalTypography.current.text14.light.colorTxtParagraph()
            )
            Row(verticalAlignment = Alignment.Bottom) {
                Text(
                    text = item.unrealizedGl,
                    style = LocalTypography.current.text14.regular.colorTxtParagraph(),
                    modifier = Modifier.padding(end = LocalDimens.current.dimen4)
                )
                BadgeText(
                    label = item.unrealizedGlRate.displayPriceChangeRate(),
                    bgColor = glResource.priceChangeBgColor,
                    textColor = glResource.textColor
                )
            }
        }
        Spacer(modifier = Modifier.padding(top = LocalDimens.current.dimen12))
        Row(modifier = Modifier.fillMaxWidth(), horizontalArrangement = Arrangement.SpaceBetween) {
            Text(
                text = stringResource(id = R.string.key0354),
                style = LocalTypography.current.text14.light.colorTxtParagraph()
            )
            Row(verticalAlignment = Alignment.Bottom) {
                Text(
                    text = item.costPrice,
                    style = LocalTypography.current.text14.regular.colorTxtParagraph()
                )
                Text(
                    modifier = Modifier.padding(start = LocalDimens.current.dimen2),
                    text = item.currency,
                    style = LocalTypography.current.text12.light.colorTxtParagraph()
                )
            }
        }
        Spacer(modifier = Modifier.padding(top = LocalDimens.current.dimen12))
        Row(modifier = Modifier.fillMaxWidth(), horizontalArrangement = Arrangement.SpaceBetween) {
            Text(
                text = stringResource(id = R.string.key0355),
                style = LocalTypography.current.text14.light.colorTxtParagraph()
            )
            Text(
                text = item.unit,
                style = LocalTypography.current.text14.regular.colorTxtParagraph()
            )
        }
        Spacer(modifier = Modifier.padding(top = LocalDimens.current.dimen12))
        Row(modifier = Modifier.fillMaxWidth(), horizontalArrangement = Arrangement.SpaceBetween) {
            Text(
                text = stringResource(id = R.string.key0356),
                style = LocalTypography.current.text14.light.colorTxtParagraph()
            )
            Row(verticalAlignment = Alignment.Bottom) {
                Text(
                    text = item.costValue,
                    style = LocalTypography.current.text14.regular.colorTxtParagraph()
                )
                Text(
                    modifier = Modifier.padding(start = LocalDimens.current.dimen2),
                    text = item.currency,
                    style = LocalTypography.current.text12.light.colorTxtParagraph()
                )
            }
        }
        Spacer(modifier = Modifier.padding(top = LocalDimens.current.dimen24))
        AccentButton(properties = ButtonProperties(
            text = stringResource(id = R.string.key0715), icon = painterResource(
                id = R.drawable.ic_right_arrow,
            )
        ), onClicked = {
            navigateToAssetDetails(item)
        })
        PaddingTop(value = LocalDimens.current.dimen12)
        SecondaryBorderButton(
            properties = ButtonProperties(
                text = stringResource(id = R.string.key1015),
            ),
            onClicked = {
                onDismissed()
            }
        )
    }
}

data class ProductCategoryDetailsModalDisplayData(
    val id: String = "",
    val categoryName: String? = null,
    val isSection: Boolean = false,
    val symbol: String = "",
    val name: String = "",
    val logo: String = "",
    val exchange: String = "",
    val riskLevel: RiskLevel? = null,
    val currency: String = "",
    val unit: String = "",
    val marketPrice: String = "",
    val marketValue: String = "",
    val costPrice: String = "",
    val unrealizedGl: String = "",
    val unrealizedGlRate: String = "",
    val asOfDate: String = "",
    val country: String = "",
    val costValue: String = "",
)

@Preview(showBackground = true, showSystemUi = true)
@Composable
fun PreviewProductCategoryDetailsModalContent() {
    val items = remember {
        mutableStateListOf<StockExchangeMarqueeData>()
    }
    ProductCategoryDetailsModalContent(
        item = ProductCategoryDetailsModalDisplayData(
            name = "ASSET",
            exchange = "EXC",
            riskLevel = RiskLevel.MEDIUM,
            currency = "USD",
            unrealizedGl = "50",
            marketValue = "50",
            marketPrice = "22",
            unrealizedGlRate = "49"
        )
    )
}