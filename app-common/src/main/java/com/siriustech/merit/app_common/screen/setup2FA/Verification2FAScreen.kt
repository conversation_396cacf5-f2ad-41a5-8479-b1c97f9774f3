package com.siriustech.merit.app_common.screen.setup2FA

import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxHeight
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.collectAsState
import androidx.compose.ui.Modifier
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.tooling.preview.Preview
import androidx.lifecycle.viewmodel.compose.viewModel
import androidx.navigation.NavController
import androidx.navigation.compose.rememberNavController
import com.siriustech.core_ui_compose.base.SingleEventEffect
import com.siriustech.merit.app_common.component.alert.BannerAlertProperties
import com.siriustech.merit.app_common.component.header.CommonToolbarWithBackAndResetMenu
import com.siriustech.merit.app_common.component.otp.OTPVerification
import com.siriustech.merit.app_common.component.otp.OTPVerificationProperties
import com.siriustech.merit.app_common.component.otp.VerificationEvent
import com.siriustech.merit.app_common.ext.AttributeStringData
import com.siriustech.merit.app_common.ext.buildAttrString
import com.siriustech.merit.app_common.ext.colorTxtLabel
import com.siriustech.merit.app_common.ext.colorTxtParagraph
import com.siriustech.merit.app_common.ext.navigateReplaceAll
import com.siriustech.merit.app_common.navigation.Dashboard
import com.siriustech.merit.app_common.navigation.SetupPin
import com.siriustech.merit.app_common.theme.AppScreen
import com.siriustech.merit.app_common.theme.LocalTypography
import com.siriustech.merit.app_common.typeenum.Auth2FAType
import com.siriustech.merit.app_common.R as AppCommonR

/**
 * Created by Hein Htet
 */

@Composable
fun Verification2FAScreen(
    viewModel: Verification2FAViewModel = viewModel(),
    navController: NavController,
) {
    val context = LocalContext.current
    val authType = viewModel.outputs.authType.collectAsState()
    val userAuthValue = viewModel.outputs.userAuthValue.collectAsState()
    val otpCode = viewModel.outputs.otpCode.collectAsState()
    val otpResendCount = viewModel.outputs.otpResendCount.collectAsState()
    val refNumber = viewModel.outputs.refNumber.collectAsState()
    val otpExpireTime = viewModel.outputs.otpExpiredTime.collectAsState()

    LaunchedEffect(Unit) {
        viewModel.inputs.init()
    }

    fun showOtpSentAlert() {
        viewModel.emitBannerAlert(
            BannerAlertProperties(
                title = context.getString(AppCommonR.string.key0111),
                description = context.getString(if (authType.value == Auth2FAType.PHONE) AppCommonR.string.key0112 else AppCommonR.string.key0814),
            ),
        )
    }

    SingleEventEffect(viewModel.outputs.verificationEvent) { sideEffect ->
        when (sideEffect) {
            is VerificationEvent.OnOTPSubmit -> {
                viewModel.inputs.onSubmitVerificationCode(sideEffect.code)
            }

            is VerificationEvent.OnResendOTPCode -> {
                viewModel.inputs.onResendOTPCode()
            }

            is VerificationEvent.OnOTPCodeChanged -> {
                viewModel.inputs.updateOtpCode(sideEffect.code)
            }

            is VerificationEvent.OnOTPSent -> {
                showOtpSentAlert()
            }

            is VerificationEvent.OnConfigurePin -> {
                navController.navigate(SetupPin)
            }

            is VerificationEvent.VerificationSuccess -> {
                // navigate to home page
                navController.navigateReplaceAll(Dashboard)
            }

            is VerificationEvent.OnVerificationFailed -> {
                viewModel.emitBannerAlert(
                    BannerAlertProperties(
                        title = context.getString(AppCommonR.string.key1016),
                        description = sideEffect.value,
                    ),
                )
            }

            else -> {}
        }
    }
    AppScreen(
        vm = viewModel,
        toolbar = {
            CommonToolbarWithBackAndResetMenu(
                title = stringResource(id = AppCommonR.string.key0192),
                onBackClicked = { navController.popBackStack() },
                onResetButtonClicked = { viewModel.inputs.updateOtpCode("") })
        }) {
        Column {
            Spacer(modifier = Modifier.fillMaxHeight(0.15f))
            OTPVerification(
                otpResendCount = otpResendCount.value,
                otpExpireDuration = otpExpireTime.value,
                defaultOtpCode = otpCode,
                referenceNumber = refNumber.value,
                onEventChanged = { event ->
                    viewModel.inputs.onOTPVerificationEventChanged(event)
                },
                properties = OTPVerificationProperties(
                    title = stringResource(id = if (authType.value == Auth2FAType.PHONE) AppCommonR.string.key0198 else AppCommonR.string.key0816),
                    description = buildAttrString(
                        arrayListOf(
                            AttributeStringData(
                                text = context.getString(if (authType.value == Auth2FAType.PHONE) AppCommonR.string.key0032 else AppCommonR.string.key0033),
                                textStyle = LocalTypography.current.text14.light.colorTxtParagraph()
                            ),
                            AttributeStringData(
                                text = userAuthValue.value,
                                textStyle = LocalTypography.current.text14.medium.colorTxtLabel()
                            ),
                        ),
                    ),
                    iconPainter = painterResource(id = if (authType.value == Auth2FAType.PHONE) AppCommonR.drawable.ic_phone_otp_verification else AppCommonR.drawable.ic_email_otp_verification),
                    leftButtonText = stringResource(id = AppCommonR.string.key0046),
                    rightButtonText = stringResource(id = AppCommonR.string.key0045)
                )
            )
        }
    }

}


@Preview(showBackground = true, showSystemUi = true)
@Composable
fun PreviewVerification2FAScreen() {
    Verification2FAScreen(navController = rememberNavController())
}