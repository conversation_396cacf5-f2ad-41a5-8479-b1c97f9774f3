package com.siriustech.merit.app_common.typeenum

import android.content.Context
import com.siriustech.merit.app_common.R

/**
 * Created by <PERSON><PERSON> <PERSON>tet
 */
enum class LanguageType(val value: String) {
    ENGLISH("en"),
    CHINESE("zh");

    fun getDisplayLanguage(context: Context): String {
        return if (this@LanguageType == ENGLISH) context.getString(R.string.key0862) else context.getString(
            R.string.key0861
        )
    }

    companion object {
        fun fromParams(id: String): LanguageType {
            return when (id) {
                ENGLISH.value -> ENGLISH
                CHINESE.value -> CHINESE
                else -> ENGLISH
            }
        }

        fun toApiValue(value: String): String {
            val type = fromParams(value)
            return when (type) {
                ENGLISH -> "en_US"
                CHINESE -> "zh_CN"
            }
        }
    }
}