package com.siriustech.merit.app_common.component.modalbts

import androidx.compose.foundation.Image
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.ModalBottomSheet
import androidx.compose.material3.Text
import androidx.compose.material3.rememberModalBottomSheetState
import androidx.compose.runtime.Composable
import androidx.compose.runtime.mutableStateListOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.runtime.snapshots.SnapshotStateList
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.tooling.preview.Preview
import com.core.util.toAmount
import com.siriustech.merit.app_common.component.common.MarketPriceChange
import com.siriustech.merit.app_common.component.common.MarketPriceChangeDisplayData
import com.siriustech.merit.app_common.component.marquee.StockExchangeMarqueeData
import com.siriustech.merit.app_common.ext.colorTxtCaution
import com.siriustech.merit.app_common.ext.colorTxtInactive
import com.siriustech.merit.app_common.ext.colorTxtParagraph
import com.siriustech.merit.app_common.ext.colorTxtTitle
import com.siriustech.merit.app_common.ext.displayPriceChange
import com.siriustech.merit.app_common.ext.displayPriceChangeRate
import com.siriustech.merit.app_common.ext.noRippleClickable
import com.siriustech.merit.app_common.theme.LocalAppColor
import com.siriustech.merit.app_common.theme.LocalDimens
import com.siriustech.merit.app_common.theme.LocalTypography
import com.siriustech.merit.app_common.typeenum.CurrencyExchangeType
import kotlinx.coroutines.launch
import com.siriustech.merit.app_common.R as AppCommonR

/**
 * Created by Hein Htet
 */
@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun ExchangeCurrencyModalBottomSheet(
    items: SnapshotStateList<StockExchangeMarqueeData>,
    onDismissed: () -> Unit = {},
) {
    val sheetState = rememberModalBottomSheetState(skipPartiallyExpanded = true)
    val scope = rememberCoroutineScope()

    fun dismiss() {
        scope.launch {
            sheetState.hide()
            onDismissed()
        }
    }

    ModalBottomSheet(
        dragHandle = {},
        shape = RoundedCornerShape(LocalDimens.current.dimen4),
        containerColor = LocalAppColor.current.bgDefault,
        onDismissRequest = {
            dismiss()
        }, sheetState = sheetState,
        modifier = Modifier
    ) {
        ExchangeCurrencyModalContent(items, onDismissed = { dismiss() })
    }
}

@Composable
fun ExchangeCurrencyModalContent(
    items: SnapshotStateList<StockExchangeMarqueeData>,
    onDismissed: () -> Unit = {},
) {
    Column(
        modifier = Modifier
            .fillMaxWidth()
            .padding(
                top = LocalDimens.current.dimen16,
                start = LocalDimens.current.dimen16,
                end = LocalDimens.current.dimen12,
            )
    ) {
        Row(
            modifier = Modifier
                .fillMaxWidth(),
        ) {
            Text(
                modifier = Modifier.weight(1f),
                text = stringResource(id = AppCommonR.string.key0359),
                style = LocalTypography.current.text14.semiBold.colorTxtTitle()
            )
            Image(
                modifier = Modifier.noRippleClickable { onDismissed() },
                painter = painterResource(id = AppCommonR.drawable.ic_action_close),
                contentDescription = "Close Image Resource"
            )
        }
        Row(
            verticalAlignment = Alignment.CenterVertically,
            modifier = Modifier
                .fillMaxWidth()
                .padding(top = LocalDimens.current.dimen24)
        ) {
            Image(
                modifier = Modifier.noRippleClickable { onDismissed() },
                painter = painterResource(id = AppCommonR.drawable.ic_caution_2),
                contentDescription = "Close Image Resource"
            )
            Text(
                modifier = Modifier.padding(start = LocalDimens.current.dimen2),
                text = stringResource(id = AppCommonR.string.key0360),
                style = LocalTypography.current.text14.light.colorTxtCaution()
            )
        }

        items.forEach {
            ExchangeCurrencyItem(it)
        }
    }
}

@Composable
fun ExchangeCurrencyItem(data: StockExchangeMarqueeData) {
    Row(
        modifier = Modifier
            .fillMaxWidth()
            .padding(top = LocalDimens.current.dimen16),
        horizontalArrangement = Arrangement.SpaceBetween
    ) {
        Column {
            Text(
                text = data.exchange,
                style = LocalTypography.current.text14.semiBold.colorTxtTitle()
            )
            Text(
                text = CurrencyExchangeType.fromParam(data.exchange).description(),
                style = LocalTypography.current.text12.medium.colorTxtInactive()
            )
        }
        Text(
            text = data.price,
            modifier = Modifier.padding(horizontal = LocalDimens.current.dimen4),
            style = LocalTypography.current.text12.medium.colorTxtParagraph()
        )
//        MarketPriceChange(
//            data = MarketPriceChangeDisplayData(
//                marketValue = "5.89",
//                currency = data.exchange.substringAfterLast("/"),
//                unrealizedGL = "60".toAmount(4) ?: "0.00",
//                unrealizedGLRate = "2".toAmount(4) ?: "0.00"
//            )
//        )
    }
}

@Preview(showBackground = true, showSystemUi = true)
@Composable
fun PreviewExchangeCurrencyModalContent() {
    val items = remember {
        mutableStateListOf<StockExchangeMarqueeData>()
    }
    ExchangeCurrencyModalContent(items)
}

