package com.siriustech.merit.app_common.screen.chat

import android.content.Context
import android.net.Uri
import com.siriustech.merit.app_common.screen.chat.component.AttachmentDisplayModel
import com.siriustech.merit.app_common.theme.AppAction
import java.io.File
import okhttp3.RequestBody

/**
 * Created by <PERSON><PERSON>
 */
interface ChatAction : AppAction {
    data class ChatInputTextChanged(val text: String) : ChatAction
    data object UserInfoLoaded : ChatAction
    data class OnUploadFile(val context : Context,val file:File,val uri:Uri,val requestBody: RequestBody ) : ChatAction
    data class OnRemoveUploadedFile(val chatMessageDisplayModel: AttachmentDisplayModel) : ChatAction
    data object OnSendMessage : AppAction
    data class OnPreviewAttachment(val item:AttachmentDisplayModel) : ChatAction
    data object OnLoadPreviousChatHistory : ChatAction
}