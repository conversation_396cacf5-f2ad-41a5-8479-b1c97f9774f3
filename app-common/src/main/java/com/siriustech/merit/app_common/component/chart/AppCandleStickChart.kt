package com.siriustech.merit.app_common.component.chart

import android.graphics.Paint
import android.graphics.Typeface
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.remember
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.toArgb
import androidx.compose.ui.viewinterop.AndroidView
import androidx.core.content.ContextCompat
import androidx.core.content.res.ResourcesCompat
import com.core.util.toAmount
import com.github.mikephil.charting.charts.CandleStickChart
import com.github.mikephil.charting.components.LimitLine
import com.github.mikephil.charting.components.XAxis
import com.github.mikephil.charting.components.YAxis
import com.github.mikephil.charting.data.CandleData
import com.siriustech.merit.app_common.R
import com.siriustech.merit.app_common.component.chart.renderer.CustomLineScatterCandleRadarRenderer
import com.siriustech.merit.app_common.theme.AppColor

/**
 * Created by <PERSON><PERSON>
 */
@Composable
fun AppCandleStickChart(
    modifier: Modifier = Modifier,
    lastPrice: Float = 0f,
    candleStickChart: CandleStickChart,
    onConfigChart: (CandleStickChart) -> Unit = {},
    candleData: CandleData,
) {

    val fontTypeface = remember {
        ResourcesCompat.getFont(candleStickChart.context, R.font.noto_sans_medium)
    }

    LaunchedEffect(Unit) {
        candleStickChart.also { chart ->
            chart.setDrawBorders(false)
            chart.isHorizontalScrollBarEnabled = false
            chart.isVerticalScrollBarEnabled = false
            val typeface =
                ResourcesCompat.getFont(candleStickChart.context, R.font.noto_sans_medium)
            chart.setDrawGridBackground(false)
            chart.setDrawBorders(false)
            chart.setDrawMarkers(true)
            chart.isDoubleTapToZoomEnabled = false
            chart.setPinchZoom(false)
            chart.axisRight.setDrawLabels(true)
            chart.axisRight.setPosition(YAxis.YAxisLabelPosition.INSIDE_CHART)
            chart.axisLeft.setPosition(YAxis.YAxisLabelPosition.INSIDE_CHART)
            chart.axisRight.setLabelCount(3, true)
            chart.legend.isEnabled = false
            chart.description.isEnabled = false
            chart.description.isEnabled = false
            chart.isHighlightPerTapEnabled = false
            chart.axisLeft.setDrawLabels(false)
            chart.xAxis.isGranularityEnabled = true
            chart.xAxis.setDrawLabels(false)
            chart.xAxis.setDrawGridLines(false)
            chart.axisLeft.setDrawGridLines(false)
            chart.axisRight.setDrawGridLines(false)
            chart.xAxis.setDrawAxisLine(false)
            chart.axisLeft.setDrawAxisLine(false)
            chart.axisRight.setDrawAxisLine(false)
            chart.axisRight.textSize = 12f
            chart.axisRight.typeface = typeface
            chart.axisLeft.textSize = 12f
            chart.axisLeft.typeface = typeface
            chart.data = candleData
            chart.setViewPortOffsets(0f, 0f, 0f, 0f)
            val xAxis = chart.xAxis
            xAxis.position = XAxis.XAxisPosition.BOTTOM
            onConfigChart(chart)
            chart.invalidate()
        }
    }

    AndroidView(
        modifier = Modifier
            .fillMaxSize()
            .then(modifier),
        factory = { context ->
            candleStickChart.also { chart ->
                chart.setDrawGridBackground(false)
                chart.setDrawBorders(false)
                chart.setDrawMarkers(true)
                chart.isDoubleTapToZoomEnabled = false
                chart.setPinchZoom(false)
                chart.axisRight.setDrawLabels(true)
                chart.axisRight.setPosition(YAxis.YAxisLabelPosition.INSIDE_CHART)
                chart.axisLeft.setPosition(YAxis.YAxisLabelPosition.INSIDE_CHART)
                chart.axisRight.setLabelCount(3, true)
                chart.legend.isEnabled = false
                chart.description.isEnabled = false
                chart.description.isEnabled = false
                chart.isHighlightPerTapEnabled = true
                chart.axisLeft.setDrawLabels(false)
                chart.xAxis.setDrawLabels(false)
                chart.xAxis.setDrawGridLines(false)
                chart.axisLeft.setDrawGridLines(false)
                chart.axisRight.setDrawGridLines(false)
                chart.xAxis.setDrawAxisLine(false)
                chart.axisLeft.setDrawAxisLine(false)
                chart.axisRight.setDrawAxisLine(false)
                chart.axisRight.textSize = 12f
                chart.axisRight.typeface = fontTypeface
                chart.axisLeft.textSize = 12f
                chart.axisLeft.typeface = fontTypeface
                chart.data = candleData
                chart.setViewPortOffsets(0f, 0f, 0f, 0f)
                val xAxis = chart.xAxis
                xAxis.position = XAxis.XAxisPosition.BOTTOM
//                chart.renderer = CustomLineScatterCandleRadarRenderer(chart,  chart.animator,chart.viewPortHandler)
                chart.moveViewToX(candleData.entryCount.toFloat())
                onConfigChart(chart)
                chart.invalidate()
            }
        }, update = {
            candleStickChart.data = candleData
            candleStickChart.post {
                it.moveViewToX(candleData.entryCount.toFloat())
                candleStickChart.notifyDataSetChanged()
                val labelBackgroundPaint = Paint().apply {
//                    color = 0xFFDDDDDD.toInt()
                    color = Color.Transparent.toArgb()
                    style = Paint.Style.FILL
                }

                if (candleData.dataSetCount > 0) {
                    val customYAxisRenderer = CustomYAxisRenderer(
                        candleStickChart,
                        candleStickChart.viewPortHandler,
                        candleStickChart.axisRight,
                        candleStickChart.getTransformer(YAxis.AxisDependency.RIGHT),
                        labelBackgroundPaint,
                    )
                    candleStickChart.rendererRightYAxis = customYAxisRenderer
                }
                candleStickChart.invalidate()
            }
        })
}

