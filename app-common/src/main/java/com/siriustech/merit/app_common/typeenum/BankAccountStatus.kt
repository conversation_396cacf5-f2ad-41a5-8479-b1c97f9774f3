package com.siriustech.merit.app_common.typeenum

import androidx.compose.material3.Badge
import androidx.compose.runtime.Composable
import androidx.compose.runtime.ReadOnlyComposable
import androidx.compose.ui.Modifier
import androidx.compose.ui.platform.LocalContext
import com.siriustech.merit.app_common.R
import com.siriustech.merit.app_common.component.text.BadgeText
import com.siriustech.merit.app_common.theme.LocalAppColor

/**
 * Created by <PERSON><PERSON>tet
 */
enum class BankAccountStatus(val value: String) {
    PENDING("PENDING"),
    REJECTED("REJECTED"),
    APPROVED("APPROVED");




    @Composable
    fun DisplayStatusBadge() {
        if (this == PENDING || this == REJECTED) {
            val context = LocalContext.current
            var txtColor = LocalAppColor.current.txtCaution
            var bgColor = LocalAppColor.current.bgCaution
            var text = context.getString(R.string.key0686)
            when (this) {
                PENDING -> {
                    text = context.getString(R.string.key0686)
                    txtColor = LocalAppColor.current.txtCaution
                    bgColor = LocalAppColor.current.bgCaution
                }

                REJECTED -> {
                    text = context.getString(R.string.key0688)
                    txtColor = LocalAppColor.current.txtNegative
                    bgColor = LocalAppColor.current.bgNegative
                }
                else -> {

                }
            }
            BadgeText(modifier = Modifier, text, bgColor,txtColor, )
        }
    }

    companion object {
        fun fromParams(value: String): BankAccountStatus{
            return entries.find { it.value == value } ?: PENDING
        }
    }
}