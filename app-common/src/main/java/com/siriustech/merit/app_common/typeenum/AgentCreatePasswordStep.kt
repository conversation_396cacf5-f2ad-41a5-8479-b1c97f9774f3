package com.siriustech.merit.app_common.typeenum

/**
 * Created by <PERSON><PERSON><PERSON>
 */
enum class AgentCreatePasswordStep(val step: Int) {
    CREATE_PASSWORD(0),
    CONFIRM_DOCUMENT(1);

    companion object {
        const val TOTAL_STEP = 2
        fun fromParam(value: Int): AgentCreatePasswordStep {
            return when (value) {
                0 -> CREATE_PASSWORD
                1 -> CONFIRM_DOCUMENT
                else -> CREATE_PASSWORD
            }
        }
    }
}