package com.siriustech.merit.app_common.screen.appwebview

import android.os.Bundle
import androidx.activity.compose.setContent
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.toArgb
import com.siriustech.core_ui_compose.ext.ChangeSystemBarsTheme
import com.siriustech.merit.app_common.theme.AppComposeActivity
import com.siriustech.merit.app_common.theme.AppScreen
import com.siriustech.merit.app_common.theme.AppViewModel
import dagger.hilt.android.AndroidEntryPoint

/**
 * Created by <PERSON><PERSON>tet
 */
@AndroidEntryPoint
class AppWebViewActivity : AppComposeActivity() {

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContent {
            val argument =
                intent.getSerializableExtra(EXTRA_WEB_VIEW_ARGUMENT) as AppWebViewArgument
            ChangeSystemBarsTheme(lightTheme = true, Color.White.toArgb())
            AppScreen(vm = AppViewModel()) {
                AppWebViewScreen(
                    modifier = Modifier.fillMaxSize(1f),
                    argument
                )

            }
        }
    }

    companion object {
        const val EXTRA_WEB_VIEW_ARGUMENT = "EXTRA_WEB_VIEW_ARGUMENT"
    }
}