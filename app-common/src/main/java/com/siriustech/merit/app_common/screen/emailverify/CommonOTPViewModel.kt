package com.siriustech.merit.app_common.screen.emailverify

import androidx.lifecycle.viewModelScope
import com.core.network.base.getError
import com.siriustech.merit.apilayer.service.authentication.common.GetOTPRequest
import com.siriustech.merit.apilayer.service.authentication.common.GetOTPUseCase
import com.siriustech.merit.app_common.component.otp.VerificationEvent
import com.siriustech.merit.app_common.data.CommonSharedPreferences
import com.siriustech.merit.app_common.theme.AppViewModel
import com.siriustech.merit.app_common.typeenum.Auth2FAType
import com.siriustech.merit.app_common.typeenum.BizType
import com.siriustech.merit.app_common.typeenum.OtpType
import com.siriustech.merit.app_common.utils.ErrorCode.mapToErrorDisplay
import java.util.concurrent.TimeUnit
import kotlinx.coroutines.channels.Channel
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.catch
import kotlinx.coroutines.flow.collectLatest
import kotlinx.coroutines.flow.onCompletion
import kotlinx.coroutines.flow.onStart
import kotlinx.coroutines.flow.receiveAsFlow
import kotlinx.coroutines.launch

/**
 * Created by Hein Htet
 */
open class CommonOTPViewModel constructor(
    private val getOTPUseCase: GetOTPUseCase,
    private val commonSharedPreferences: CommonSharedPreferences,
) : AppViewModel() {


    private val _otpCode = MutableStateFlow("")
    private val _authType = MutableStateFlow(Auth2FAType.EMAIL)
    private val _verificationEvent = Channel<VerificationEvent>(capacity = Channel.BUFFERED)
    private val _otpExpiredTime = MutableStateFlow(0L)
    private val _otpResendCount = MutableStateFlow(0)
    private val _refNumber = MutableStateFlow("-")
    private var _bizType = BizType.LOGIN_2FA
    private var _phone = MutableStateFlow<String>("")
    private var _email = MutableStateFlow<String>("")
    private var _arguments = MutableStateFlow<CommonOTPVerificationArguments?>(null)
    private val _mobileRegion = MutableStateFlow<String?>(null)

    val otpCode: StateFlow<String>
        get() = _otpCode

    val authType: StateFlow<Auth2FAType>
        get() = _authType

    val verificationEvent: Flow<VerificationEvent>
        get() = _verificationEvent.receiveAsFlow()

    val otpExpiredTime: StateFlow<Long>
        get() = _otpExpiredTime

    val otpResendCount: StateFlow<Int>
        get() = _otpResendCount

    val refNumber: StateFlow<String>
        get() = _refNumber

    val email: StateFlow<String>
        get() = _email


    val phone: StateFlow<String>
        get() = _phone

    val bizType: BizType
        get() = _bizType

    val arguments: StateFlow<CommonOTPVerificationArguments?>
        get() = _arguments

    fun onUpdateAuthType(type: Auth2FAType) {
        _authType.value = type
    }

    fun onUpdateBizType(type: BizType) {
        _bizType = type
    }

    fun onUpdatePhone(phone: String) {
        _phone.value = phone
    }

    fun onUpdateEmail(email: String) {
        _email.value = email
    }

    fun updateOtpCode(code: String) {
        _otpCode.value = code
    }

    fun onResendOTPCode() {
        onRequestOTPApiCall()
    }

    fun onRequestOTP() = onRequestOTPApiCall()


    fun updateEmail(email: String) {
        _email.value = email
    }

    fun updateArgument(arguments: CommonOTPVerificationArguments) {
        _arguments.value = arguments
    }

    fun emitOtpEvent(event: VerificationEvent) {
        viewModelScope.launch {
            _verificationEvent.send(event)
        }
    }

    fun updateOtpResendCount(count: Int) {
        _otpResendCount.value = count
    }

    fun updateMobileRegion(code: String){
        _mobileRegion.value = code
    }


    private fun onRequestOTPApiCall() {
        scope.launch {
            getOTPUseCase(
                param = GetOTPRequest(
                    bizType = _bizType.type,
                    otpAddress = if (_authType.value == Auth2FAType.EMAIL) _email.value ?: commonSharedPreferences.userEmail else _phone.value,
                    otpType = if (_authType.value == Auth2FAType.EMAIL) OtpType.EMAIL.value else OtpType.PHONE.value,
                    mobileRegion = if (_authType.value == Auth2FAType.EMAIL) null else _mobileRegion.value
                )
            )
                .onStart { inputs.emitLoading(true) }
                .catch { emitError(it.getError().mapToErrorDisplay()) }
                .onCompletion { inputs.emitLoading(false) }
                .collectLatest {
                    _otpResendCount.value += 1
                    _refNumber.value = it.refCode.orEmpty()
                    _otpExpiredTime.value = TimeUnit.SECONDS.toMillis(it.expireSeconds ?: 0)
                    _verificationEvent.send(VerificationEvent.OnOTPSent)
                }
        }
    }

}