package com.siriustech.merit.app_common.component.tableview

import androidx.compose.ui.unit.Dp
import androidx.compose.ui.unit.dp
import timber.log.Timber

/**
 * Created by <PERSON><PERSON> <PERSON>tet
 */
object TableViewHelper {
    // Helper function to calculate the max width for each column
    fun calculateMaxColumnWidths(sections: List<TableSection>): List<Dp> {
        val columnCount = sections.firstOrNull()?.columnHeaders?.size ?: 0

        val maxWidths = MutableList(columnCount) { 0 } // Store max length for each column

        // Traverse all sections and rows to find the longest content
        sections.forEach { section ->
            section.columnHeaders.forEachIndexed { index, header ->
                maxWidths[index] = maxOf(maxWidths[index], header.length)
            }

            section.rows.forEach { row ->
                row.forEachIndexed { index, cell ->
                    maxWidths[index] = maxOf(maxWidths[index], cell.content.length)
                }
            }
        }
        Timber.d("MAX_WIDTH $maxWidths")
        return maxWidths.map {
            it.toDp()
        }
    }

    fun calculateMaxColumnWidths(
        sections: List<TableSection>,
        screenWidth: Dp,
        minDivisions: Int = 12
    ): List<Dp> {
        val columnCount = sections.firstOrNull()?.columnHeaders?.size ?: 0
        if (columnCount == 0) return emptyList()

        // Calculate minimum column width (screen width divided by `minDivisions`)
        val defaultColumnWidth = screenWidth / minDivisions

        // Initialize a list to store the maximum width for each column
        val maxWidths = MutableList(columnCount) { 0 }

        // Traverse all sections to find the longest content in each column
        sections.forEach { section ->
            section.columnHeaders.forEachIndexed { index, header ->
                maxWidths[index] = maxOf(maxWidths[index], header.length)
            }

            section.rows.forEach { row ->
                row.forEachIndexed { index, cell ->
                    maxWidths[index] = maxOf(maxWidths[index], cell.content.length)
                }
            }
        }
        // Convert content lengths to Dp and compare with the defaultColumnWidth
        val totalColumnWidth = screenWidth / columnCount
        return maxWidths.map { contentLength ->
            val calculatedWidth = contentLength.toDp()
            if (calculatedWidth < totalColumnWidth) {
                // Use default width if calculated width is smaller than evenly divided width
                defaultColumnWidth
            } else {
                // Use the calculated width if it's larger
                calculatedWidth
            }
        }
    }
    // Approximate character width in Dp (you can refine this based on font and size)
    fun Int.toDp(): Dp = (this * 8).dp
}