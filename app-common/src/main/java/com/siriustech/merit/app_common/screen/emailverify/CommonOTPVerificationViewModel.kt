package com.siriustech.merit.app_common.screen.emailverify

import com.core.network.base.getError
import com.siriustech.merit.apilayer.service.authentication.common.GetOTPUseCase
import com.siriustech.merit.apilayer.service.user.updateuserinfo.UpdateUserInfoUseCase
import com.siriustech.merit.apilayer.service.wallet.deposit.DepositUseCase
import com.siriustech.merit.apilayer.service.wallet.withdraw.WithdrawUseCase
import com.siriustech.merit.app_common.component.otp.VerificationEvent
import com.siriustech.merit.app_common.data.CommonSharedPreferences
import com.siriustech.merit.app_common.typeenum.BizType
import com.siriustech.merit.app_common.typeenum.OtpType
import com.siriustech.merit.app_common.utils.ErrorCode.mapToErrorDisplay
import dagger.hilt.android.lifecycle.HiltViewModel
import javax.inject.Inject
import kotlinx.coroutines.flow.catch
import kotlinx.coroutines.flow.collectLatest
import kotlinx.coroutines.flow.onCompletion
import kotlinx.coroutines.flow.onStart
import kotlinx.coroutines.launch

/**
 * Created by <PERSON><PERSON>tet
 */
@HiltViewModel
open class CommonOTPVerificationViewModel @Inject constructor(
    getOTPUseCase: GetOTPUseCase,
    commonSharedPreferences: CommonSharedPreferences,
    private val depositUseCase: DepositUseCase,
    private val withdrawUseCase: WithdrawUseCase,
    private val updateUserInfoUseCase: UpdateUserInfoUseCase,
) : CommonOTPViewModel(getOTPUseCase, commonSharedPreferences) {

    inner class CommonOTPVerificationInputs : BaseInputs() {

        fun onUpdateArgument(arguments: CommonOTPVerificationArguments) {
            updateArgument(arguments)
            updateEmail(arguments.email)
            onUpdatePhone(arguments.phone.orEmpty())
            onUpdateAuthType(arguments.authType)
            onUpdateBizType(BizType.fromParam(arguments.bizTypeStr))
            updateMobileRegion(arguments.mobileRegion.orEmpty())
        }


        fun onOTPVerificationEventChanged(event: VerificationEvent) {
            when (event) {
                is VerificationEvent.OnOTPSubmit -> {
                    when (arguments.value?.commonOTPVerificationType) {
                        CommonOTPVerificationType.DEPOSIT -> processDeposit()
                        CommonOTPVerificationType.WITHDRAW -> processWithdraw()
                        CommonOTPVerificationType.EDIT_PROFILE -> { updateProfile() }

                        else -> emitOtpEvent(event)
                    }
                }

                else -> {
                    emitOtpEvent(event)
                }
            }

        }

        fun onSubmitVerificationCode(code: String) {
            updateOtpCode(code)
            // callback to caller UI
        }

    }

    inner class CommonOTPVerificationOutputs : BaseOutputs() {


    }

    override val inputs = CommonOTPVerificationInputs()
    override val outputs = CommonOTPVerificationOutputs()


    private fun processDeposit() {
        scope.launch {
            depositUseCase(
                param = arguments.value?.depositRequest?.copy(
                    otpCode = otpCode.value,
                    refCode = refNumber.value,
                    otpType = OtpType.EMAIL.value,
                )
            )
                .onStart { inputs.emitLoading(true) }
                .onCompletion { inputs.emitLoading(false) }
                .catch { emitError(it.getError().mapToErrorDisplay()) }
                .collectLatest {
                    onTriggerActions(CommonOTPVerificationAction.DepositSuccess)
                }
        }
    }

    private fun processWithdraw() {
        scope.launch {
            withdrawUseCase(
                param = arguments.value?.withdrawRequest?.copy(
                    otpCode = otpCode.value,
                    refCode = refNumber.value,
                    otpType = OtpType.EMAIL.value,
                )
            )
                .onStart { inputs.emitLoading(true) }
                .onCompletion { inputs.emitLoading(false) }
                .catch { emitError(it.getError().mapToErrorDisplay()) }
                .collectLatest {
                    onTriggerActions(CommonOTPVerificationAction.WithdrawalSuccess)
                }
        }
    }

    private fun updateProfile() {
        scope.launch {
            updateUserInfoUseCase(param = arguments.value?.updateProfileRequest?.copy(
                otp = otpCode.value,
                refCode = refNumber.value,
            ))
                .onStart { inputs.emitLoading(true) }
                .onCompletion { inputs.emitLoading(false) }
                .catch { emitError(it.getError().mapToErrorDisplay()) }
                .collectLatest {
                    onTriggerActions(CommonOTPVerificationAction.UserProfileUpdatedSuccess)
                }
        }
    }
}
