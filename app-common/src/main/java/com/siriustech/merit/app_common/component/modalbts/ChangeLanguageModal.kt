package com.siriustech.merit.app_common.component.modalbts

import androidx.compose.foundation.layout.fillMaxHeight
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.runtime.setValue
import androidx.compose.ui.Modifier
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.stringResource
import com.siriustech.merit.app_common.R
import com.siriustech.merit.app_common.typeenum.LanguageType
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch

/**
 * Created by Hein Htet
 */

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun ChangeLanguageModal(
    currentSelectedLanguageType: LanguageType = LanguageType.ENGLISH,
    showModal: Boolean = false,
    onDismissed: () -> Unit = {},
    onSelectedLanguage: (LanguageType) -> Unit = {},
) {
    var selectedLanguage by remember {
        mutableStateOf(currentSelectedLanguageType)
    }

    val coroutineScope = rememberCoroutineScope()

    if (showModal) {
        val context = LocalContext.current
        ModelListBottomSheet(
            onDismissed = {
                onDismissed()
            },
            modifier = Modifier
                .fillMaxHeight(0.3f),
            onItemClicked = {
                selectedLanguage = LanguageType.fromParams(it.id)
                onDismissed()
                coroutineScope.launch {
                    delay(500)
                    onSelectedLanguage(selectedLanguage)
                }
            },
            properties = ModelListBottomSheetProperties(
                backgroundType = BackgroundType.SECONDARY,
                searchEnable = false,
                prefixTitle = context.getString(R.string.key0410),
                title = stringResource(id = R.string.key0731),
                items = listOf(
                    ModalListDataContent(
                        id = LanguageType.ENGLISH.value,
                        title = context.getString(R.string.key0862),
                        isSelected = selectedLanguage == LanguageType.ENGLISH
                    ),
                    ModalListDataContent(
                        id = LanguageType.CHINESE.value,
                        title = context.getString(R.string.key0861),
                        isSelected = selectedLanguage == LanguageType.CHINESE
                    ),
                )
            )
        )
    }
}