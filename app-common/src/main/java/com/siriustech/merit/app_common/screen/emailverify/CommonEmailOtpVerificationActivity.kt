package com.siriustech.merit.app_common.screen.emailverify

import android.content.Intent
import android.os.Build
import android.os.Bundle
import androidx.activity.compose.setContent
import androidx.activity.viewModels
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.toArgb
import androidx.compose.ui.res.stringResource
import androidx.fragment.app.FragmentActivity
import com.siriustech.core_ui_compose.ext.ChangeSystemBarsTheme
import com.siriustech.merit.app_common.Constants.KEY_INTENT_EXTRA_COMMON_EMAIL_OTP_ARGUMENT
import com.siriustech.merit.app_common.Constants.KEY_RETURN_INTENT_EXTRA_COMMON_EMAIL_OTP_ARGUMENT
import com.siriustech.merit.app_common.R
import com.siriustech.merit.app_common.component.container.PaddingTop
import com.siriustech.merit.app_common.component.header.CommonToolbarWithBackMenu
import com.siriustech.merit.app_common.theme.AppScreen
import com.siriustech.merit.app_common.theme.LocalDimens
import dagger.hilt.android.AndroidEntryPoint

/**
 * Created by Hein Htet
 */
@AndroidEntryPoint
class CommonEmailOtpVerificationActivity : FragmentActivity() {

    private val viewModel: CommonOTPVerificationViewModel by viewModels()

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        val arguments =
            intent.getSerializableExtra(KEY_INTENT_EXTRA_COMMON_EMAIL_OTP_ARGUMENT) as CommonOTPVerificationArguments
        setContent {
            ChangeSystemBarsTheme(lightTheme = true, Color.White.toArgb())
            AppScreen(vm = viewModel) {
                Column(modifier = Modifier.fillMaxSize()) {
                    CommonToolbarWithBackMenu(
                        onBackPressed = { <EMAIL>() },
                        title = stringResource(id = R.string.key0441)
                    )
                    PaddingTop(value = LocalDimens.current.dimen24)
                    CommonOTPVerificationScreen(
                        viewModel = viewModel,
                        arguments = arguments,
                        onOtpSubmit = {
                            setResult(RESULT_OK, Intent().apply {
                                putExtra(KEY_RETURN_INTENT_EXTRA_COMMON_EMAIL_OTP_ARGUMENT, it)
                            })
                            finish()
                        })
                }
            }

        }
    }

    override fun finish() {
        super.finish()
        onFinishWithAnimate()
    }


    private fun onFinishWithAnimate() {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.UPSIDE_DOWN_CAKE) {
            overrideActivityTransition(
                OVERRIDE_TRANSITION_CLOSE,
                R.anim.slide_in_left,
                R.anim.slide_out_left
            )
        } else {
            overridePendingTransition(
                R.anim.slide_in_left,
                R.anim.slide_out_left
            )
        }
    }
}

