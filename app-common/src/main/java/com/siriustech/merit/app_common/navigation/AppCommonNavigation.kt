package com.siriustech.merit.app_common.navigation

import androidx.fragment.app.FragmentActivity
import dagger.hilt.EntryPoint
import dagger.hilt.InstallIn
import dagger.hilt.components.SingletonComponent

/**
 * Created by <PERSON><PERSON> Htet
 */
interface AppCommonNavigation {
    fun onNavigateToLoginActivity(activity: FragmentActivity)
}
@EntryPoint
@InstallIn(SingletonComponent::class)
interface AppCommonNavigationEntryPoint {
    fun appCommonNavigation(): AppCommonNavigation
}
