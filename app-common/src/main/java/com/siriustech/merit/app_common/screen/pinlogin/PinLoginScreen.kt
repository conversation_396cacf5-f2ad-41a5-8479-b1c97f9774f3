package com.siriustech.merit.app_common.screen.pinlogin

import androidx.activity.compose.BackHandler
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.remember
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.tooling.preview.Preview
import androidx.fragment.app.FragmentActivity
import androidx.hilt.navigation.compose.hiltViewModel
import androidx.navigation.NavController
import androidx.navigation.compose.rememberNavController
import com.siriustech.core_ui_compose.base.SingleEventEffect
import com.siriustech.merit.app_common.Constants
import com.siriustech.merit.app_common.component.alert.BannerAlertProperties
import com.siriustech.merit.app_common.component.alert.BannerAlertType
import com.siriustech.merit.app_common.component.header.CommonToolbarWithBackMenu
import com.siriustech.merit.app_common.component.pin.PinView
import com.siriustech.merit.app_common.component.pin.PinViewProperties
import com.siriustech.merit.app_common.ext.colorTxtInactive
import com.siriustech.merit.app_common.ext.navigateReplaceAll
import com.siriustech.merit.app_common.ext.noRippleClickable
import com.siriustech.merit.app_common.ext.popBackWithResult
import com.siriustech.merit.app_common.ext.underline
import com.siriustech.merit.app_common.navigation.Dashboard
import com.siriustech.merit.app_common.navigation.ForgotPin
import com.siriustech.merit.app_common.navigation.Login
import com.siriustech.merit.app_common.theme.AppScreen
import com.siriustech.merit.app_common.theme.LocalDimens
import com.siriustech.merit.app_common.theme.LocalTypography
import com.siriustech.merit.app_common.utils.BiometricHelper
import kotlinx.serialization.Serializable
import com.siriustech.merit.app_common.R as AppCommonR

/**
 * Created by Hein Htet
 */
@Composable
fun PinLoginScreen(
    navController: NavController,
    properties: PinLoginProperties = PinLoginProperties(),
    viewModel: PinLoginViewModel = hiltViewModel(),
) {
    val context = LocalContext.current as FragmentActivity

    val isBiometricAvailable = remember {
        BiometricHelper.isBiometricAvailable(context) && viewModel.outputs.alreadySetupBiometric
    }

    LaunchedEffect(Unit) {
        if (viewModel.outputs.alreadySetupBiometric) {
            BiometricHelper.getBiometricPrompt(context,
                onAuthSucceed = {
                    viewModel.inputs.onBiometricLogin()
                }).authenticate(BiometricHelper.promptInfo)
        }
    }

    SingleEventEffect(sideEffectFlow = viewModel.outputs.pinLoginEvent) {
        when (it) {
            PinLoginEvent.OnPinMatched -> {
                if (properties.needCallbackResult) {
                    navController.popBackWithResult(
                        Constants.NAV_RETURN_SUCCESS,
                        Constants.STATUS_PASSED
                    )
                } else {
                    navController.navigateReplaceAll(Dashboard)
                }
            }

            is PinLoginEvent.OnPinUnMatched -> {
                viewModel.emitBannerAlert(
                    BannerAlertProperties(
                        title = context.getString(AppCommonR.string.key0239),
                        type = BannerAlertType.ALERT_ERROR,
                        description = context.getString(
                            AppCommonR.string.key0240, it.availableAttempts.toString()
                        )
                    )
                )
            }

            PinLoginEvent.ReachMaxPinLoginAttempt -> {
                navController.navigateReplaceAll(Login)
            }

            else -> {}
        }
    }

    fun onHandleBack() {
        if (!properties.needCallbackResult) { // is not using for common, only for login
//            navController.popBackWithResult(NAV_RETURN_RESULT_KEY, NAV_RETURN_RESULT_PIN_LOGOUT)
            // replaced with starter route using loginPage in MainActivity
            viewModel.inputs.clearPin()
            navController.navigateReplaceAll(Login)
        } else {
            navController.popBackStack()
        }
    }

    BackHandler {
        onHandleBack()
    }

    AppScreen(
        vm = viewModel,
        toolbar = {
            CommonToolbarWithBackMenu(
                title = properties.toolbarTitle ?: stringResource(id = AppCommonR.string.key0235),
                onBackPressed = {
                    onHandleBack()
                })
        }) {
        Column(
            modifier = Modifier.fillMaxSize(),
            horizontalAlignment = Alignment.CenterHorizontally
        ) {
            Spacer(modifier = Modifier.height(LocalDimens.current.dimen24))
            PinView(
                onBiometricAuthSuccess = {
                    viewModel.inputs.onBiometricLogin()
                },
                pinCodeResetCount = viewModel.outputs.pinResetCount.collectAsState().value,
                pinCodeDefaultValue = viewModel.outputs.pinCode.collectAsState().value,
                modifier = Modifier.fillMaxWidth(),
                properties = PinViewProperties(
                    showBiometricButton = isBiometricAvailable,
                    title = stringResource(id = AppCommonR.string.key0236),
                    subTitle = stringResource(id = AppCommonR.string.key0237)
                ),
                onPinCodeChanged = {
                    viewModel.inputs.onPinChanged(it)
                })
            Spacer(modifier = Modifier.height(LocalDimens.current.dimen24))
            Text(
                modifier = Modifier
                    .noRippleClickable {
                        navController.navigate(ForgotPin)
                    },
                textAlign = TextAlign.Center,
                text = stringResource(id = AppCommonR.string.key0238),
                style = LocalTypography.current.text12.medium.colorTxtInactive().underline()
            )
        }
    }
}

@Serializable
data class PinLoginProperties(
    val toolbarTitle: String? = null,
    val showBiometricLogin: Boolean = true,
    val needCallbackResult: Boolean = false,
) : java.io.Serializable

@Preview(showBackground = true, showSystemUi = true)
@Composable
fun PreviewPinLoginScreen() {
    PinLoginScreen(navController = rememberNavController())
}