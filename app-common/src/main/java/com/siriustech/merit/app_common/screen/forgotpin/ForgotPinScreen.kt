package com.siriustech.merit.app_common.screen.forgotpin

import androidx.activity.compose.BackHandler
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.pager.HorizontalPager
import androidx.compose.foundation.pager.rememberPagerState
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.runtime.snapshotFlow
import androidx.compose.ui.Modifier
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.tooling.preview.Preview
import androidx.lifecycle.viewmodel.compose.viewModel
import androidx.navigation.NavController
import androidx.navigation.compose.rememberNavController
import com.siriustech.core_ui_compose.base.SingleEventEffect
import com.siriustech.core_ui_compose.component.CommonToolbar
import com.siriustech.merit.app_common.Constants.DEFAULT_OTP_COUNT
import com.siriustech.merit.app_common.component.header.defaultToolbarProperties
import com.siriustech.merit.app_common.component.indicator.HorizontalPagerIndicator
import com.siriustech.merit.app_common.component.pin.PinView
import com.siriustech.merit.app_common.component.pin.PinViewProperties
import com.siriustech.merit.app_common.ext.navigateReplaceAll
import com.siriustech.merit.app_common.navigation.ForgotPinUpdatedSuccess
import com.siriustech.merit.app_common.theme.AppScreen
import com.siriustech.merit.app_common.theme.LocalDimens
import com.siriustech.merit.app_common.screen.forgotpin.component.VerifyEmailInputScreen
import com.siriustech.merit.app_common.screen.forgotpin.component.VerifyEmailOTPScreen
import kotlinx.coroutines.launch
import com.siriustech.merit.app_common.R as AppCommonR

/**
 * Created by Hein Htet
 */

@Composable
fun ForgotPinScreen(
    viewModel: ForgotPinViewModel = viewModel(),
    navController: NavController,
) {
    val pagerState = rememberPagerState(pageCount = { ForgotPinStep.TOTAL_STEP })
    val coroutineScope = rememberCoroutineScope()

    fun onNavigateToStep(step: ForgotPinStep){
        coroutineScope.launch {
            pagerState.animateScrollToPage(step.step)
        }
    }

    fun onHandleBack() {
        when {
            pagerState.currentPage != 0 -> {
                coroutineScope.launch { pagerState.animateScrollToPage(pagerState.currentPage - 1) }
            }
            else -> {
                navController.popBackStack()
            }
        }
    }


    BackHandler(enabled = true, onBack = {
        onHandleBack()
    })

    val createNewPinCode = viewModel.outputs.createPin.collectAsState()
    val confirmPinCode = viewModel.outputs.confirmPin.collectAsState()

    SingleEventEffect(sideEffectFlow = viewModel.appAction) {
        when (it) {
            is ForgotPinAction.OnSubmittedEmail -> {
                onNavigateToStep(ForgotPinStep.VERIFY_EMAIL_OTP)
            }
            is ForgotPinAction.OnOTPVerified -> {
                onNavigateToStep(ForgotPinStep.CREATE_PIN)
            }
            is ForgotPinAction.OnPinCodeCreated -> {
                onNavigateToStep(ForgotPinStep.CONFIRM_PIN)
            }
            is ForgotPinAction.OnPinCodeUpdated -> {
                navController.navigateReplaceAll(ForgotPinUpdatedSuccess)
            }
            is ForgotPinAction.PinUnMatchExceed -> {
                onNavigateToStep(ForgotPinStep.CREATE_PIN)
            }
        }
    }

    LaunchedEffect(pagerState) {
        snapshotFlow { pagerState.currentPage }.collect { page ->
            viewModel.inputs.updateCurrentStep(page)
        }
    }

    AppScreen(vm = viewModel) {
        Column(modifier = Modifier.fillMaxSize()) {
            ForgotPinToolbar(onResetPin = {
                viewModel.inputs.onResetPin()
            }, onBackPressed = {
                onHandleBack()
            })
            HorizontalPagerIndicator(
                pagerState = pagerState,
                modifier = Modifier.padding(top = LocalDimens.current.dimen32)
            )
            HorizontalPager(
                userScrollEnabled = false,
                state = pagerState,
                modifier = Modifier.fillMaxSize()
            ) { page ->
                val step = ForgotPinStep.fromParam(page)
                when (step) {
                    ForgotPinStep.INPUT_EMAIL -> VerifyEmailInputScreen(viewModel)
                    ForgotPinStep.VERIFY_EMAIL_OTP -> VerifyEmailOTPScreen(viewModel)
                    ForgotPinStep.CREATE_PIN -> CreatePinView(createNewPinCode.value,
                        PinViewProperties(
                            title = stringResource(id = AppCommonR.string.key0217),
                            subTitle = stringResource(id = AppCommonR.string.key0218)
                        ),
                        onPinCodeChanged = {
                            viewModel.inputs.onCreateNewPinChange(it)
                        },
                        onPinCodeCreated = { code ->
                            viewModel.onTriggerActions(ForgotPinAction.OnPinCodeCreated)
                        })

                    ForgotPinStep.CONFIRM_PIN -> CreatePinView(confirmPinCode.value,
                        PinViewProperties(
                            title = stringResource(id = AppCommonR.string.key0219),
                            subTitle = stringResource(id = AppCommonR.string.key0220)
                        ),
                        onPinCodeChanged = {
                            viewModel.inputs.onConfirmPinChange(it)
                        },
                        onPinCodeCreated = { code ->
                            viewModel.onTriggerActions(ForgotPinAction.OnComparePinCode)
                        })

                }
            }
        }
    }
}


@Composable
fun CreatePinView(
    pinCodeDefaultValue: String,
    properties: PinViewProperties,
    onPinCodeChanged: (code: String) -> Unit = {},
    onPinCodeCreated: (code: String) -> Unit = {},
) {

    PinView(
        pinCodeDefaultValue = pinCodeDefaultValue,
        modifier = Modifier.fillMaxWidth(),
        properties = properties,
        onPinCodeChanged = {
            onPinCodeChanged(it)
            if (it.length == DEFAULT_OTP_COUNT) {
                onPinCodeCreated(it)
            }
        })
}

@Composable
fun ForgotPinToolbar(
    onBackPressed: () -> Unit = {},
    onResetPin: () -> Unit = {},
) {
    CommonToolbar(
        onLeftActionClicked = onBackPressed,
        onRightActionClicked = onResetPin,
        properties = defaultToolbarProperties().copy(
            title = stringResource(id = AppCommonR.string.key0241),
            leftActionResId = AppCommonR.drawable.ic_back_arrow
        )
    )
}


@Preview(showBackground = true, showSystemUi = true)
@Composable
fun PreviewForgotPinScreenPreview() {
    ForgotPinScreen(navController = rememberNavController())
}


//CASH_EQUIVALENT FIX_INCOME STRUCTURED_PRODUCTS PRIVATE_CREDIT MULTI-ASSET EQUITY PRIVATE_EQUITY

