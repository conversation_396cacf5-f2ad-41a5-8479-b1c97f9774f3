package com.siriustech.merit.app_common.component.common.portfolio

import androidx.compose.animation.AnimatedVisibility
import androidx.compose.animation.core.tween
import androidx.compose.animation.fadeIn
import androidx.compose.animation.fadeOut
import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.wrapContentWidth
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.tooling.preview.Preview
import com.siriustech.merit.app_common.component.button.ButtonProperties
import com.siriustech.merit.app_common.component.button.ThirdButton
import com.siriustech.merit.app_common.data.display.PortfolioItemDisplayData
import com.siriustech.merit.app_common.data.display.ProductCategoryDisplayData
import com.siriustech.merit.app_common.ext.colorTxtInactive
import com.siriustech.merit.app_common.theme.LocalAppColor
import com.siriustech.merit.app_common.theme.LocalDimens
import com.siriustech.merit.app_common.theme.LocalTypography
import com.siriustech.merit.app_common.typeenum.PortfolioListDisplayType
import com.siriustech.merit.app_common.R as AppCommonR

/**
 * Created by Hein Htet
 */

@Composable
fun PortfolioList(
    modifier: Modifier = Modifier,
    onFilterMenuClicked: () -> Unit = {},
    selectedPortfolioListingType: PortfolioListDisplayType,
    items: List<PortfolioItemDisplayData> = emptyList(),
    onItemClick: (item: PortfolioItemDisplayData) -> Unit = {},
    onProductDetailsClick: (item: ProductCategoryDisplayData) -> Unit = {},
    onNavigateToMarket: () -> Unit = {},
) {
    Box(modifier = Modifier) {
        Column(
            modifier = Modifier
                .then(modifier)
        ) {

            if (selectedPortfolioListingType == PortfolioListDisplayType.ASSET) {
                items.flatMap { it.categoryAssetList.orEmpty() }.filter { it.name.isNotEmpty() }
                    .forEach {
                        PortfolioAssetItem(
                            data = it,
                            onItemClick = { onProductDetailsClick(it) }
                        )
                    }
            } else {
                items.forEach { item ->
                    when (selectedPortfolioListingType) {
                        PortfolioListDisplayType.ALLOCATION_CLASS -> {
                            PortfolioItem(
                                data = item,
                                modifier = Modifier.clickable { onItemClick(item) })
                        }

                        PortfolioListDisplayType.RISK_RATING -> {
                            PortfolioRiskRatingItem(
                                data = item,
                                modifier = Modifier.clickable { onItemClick(item) })
                        }

                        PortfolioListDisplayType.RISK_LEVEL -> {
                            PortfolioRiskLevelItem(
                                data = item,
                                modifier = Modifier.clickable { onItemClick(item) })
                        }

                        else -> {
                            PortfolioItem(
                                data = item,
                                modifier = Modifier.clickable { onItemClick(item) })
                        }
                    }
                }
            }
        }
        AnimatedVisibility(
            visible = items.isEmpty(),
            enter = fadeIn(initialAlpha = 0.4f),
            exit = fadeOut(animationSpec = tween(durationMillis = 250))
        ) {
            EmptyItem(onNavigateToMarket = onNavigateToMarket)
        }
    }
}

@Composable
fun EmptyItem(
    modifier: Modifier = Modifier,
    showButton: Boolean = true,
    emptyText: String? = null,
    buttonText: String? = null,
    onNavigateToMarket: () -> Unit = {},
) {
    Column(
        modifier = Modifier
            .background(LocalAppColor.current.bgDefault)
            .then(modifier),
        horizontalAlignment = Alignment.CenterHorizontally
    ) {
        Text(
            text = emptyText ?: stringResource(id = AppCommonR.string.key0376),
            style = LocalTypography.current.text12.light.colorTxtInactive()
        )
        if (showButton) {
            Spacer(modifier = Modifier.padding(vertical = LocalDimens.current.dimen16))
            Box(modifier = Modifier.padding(horizontal = LocalDimens.current.dimen12)) {
                ThirdButton(
                    modifier = Modifier.wrapContentWidth(),
                    onClicked = { onNavigateToMarket() },
                    properties = ButtonProperties(
                        text = buttonText ?: stringResource(id = AppCommonR.string.key0377),
                    )
                )
            }
        }
    }
}

@Preview(showBackground = true, showSystemUi = false)
@Composable
fun PreviewPortfolioList() {
    PortfolioList(
        selectedPortfolioListingType = PortfolioListDisplayType.ALLOCATION_CLASS,
        modifier = Modifier
            .padding(
                horizontal = LocalDimens.current.dimen12
            )
    )
}

@Preview
@Composable
fun PreviewEmptyItem() {
    EmptyItem()
}
