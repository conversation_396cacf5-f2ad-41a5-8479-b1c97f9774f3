package com.siriustech.merit.app_common.theme

import androidx.compose.ui.text.font.Font
import androidx.compose.ui.text.font.FontFamily
import androidx.compose.ui.text.font.FontWeight
import com.siriustech.core_ui_compose.theme.CoreDimens
import com.siriustech.core_ui_compose.theme.CoreTypography
import com.siriustech.core_ui_compose.theme.ThemeConfigs
import com.siriustech.core_ui_compose.theme.getProvideLocalComposition
import com.siriustech.merit.app_common.R

/**
 * Created by <PERSON><PERSON><PERSON>
 */


val natoFontFamily = FontFamily(
    Font(R.font.noto_sans_light, FontWeight.Light),
    Font(R.font.noto_sans_regular, FontWeight.Normal),
    Font(R.font.noto_sans_medium, FontWeight.Medium),
    Font(R.font.noto_sans_semi_bold, FontWeight.SemiBold),
    Font(R.font.noto_sans_bold, FontWeight.Bold),
)

val LocalAppColor = getProvideLocalComposition<AppColor> { LightColor() }
val LocalDimens = getProvideLocalComposition { CoreDimens() }
val LocalTypography = getProvideLocalComposition { CoreTypography(natoFontFamily) }

fun getAppThemeConfig(): ThemeConfigs<AppColor, CoreTypography, CoreDimens> {
    return ThemeConfigs(
        appColor = LocalAppColor,
        appTypography = LocalTypography,
        appDimen = LocalDimens
    )
}