package com.siriustech.merit.app_common.ext

import com.core.util.getThaiLocale
import java.util.Calendar
import java.util.Date
import java.util.Locale
import org.threeten.bp.Duration
import org.threeten.bp.Instant
import org.threeten.bp.LocalDate
import org.threeten.bp.LocalDateTime
import org.threeten.bp.ZoneId
import org.threeten.bp.ZoneOffset
import org.threeten.bp.ZonedDateTime
import org.threeten.bp.chrono.ThaiBuddhistDate
import org.threeten.bp.format.DateTimeFormatter
import org.threeten.bp.temporal.ChronoUnit

/**
 * Created by <PERSON><PERSON><PERSON>
 */

const val DATE_FORMAT_1 = "dd MMM"
const val DATE_FORMAT_2 = "dd MMM yyyy"
const val DATE_FORMAT_3 = "yyyy-MM-dd"
const val DATE_FORMAT_4 = "MMM dd"
const val DATE_FORMAT_5 = "dd MM yyyy"
const val DATE_FORMAT_6 = "MMMM dd"
const val DATE_FORMAT_7 = "dd-MMM-yyyy HH:mm:ss"
const val DATE_FORMAT_8 = " dd MMM HH:mm"
const val DATE_FORMAT_9 = " dd MMM YYYY HH:mm"
const val DATE_FORMAT_10 = " HH:mm"
const val DATE_FORMAT_11 = " dd MMM YYYY HH:mm:ss"
const val DATE_FORMAT_12 = " dd MMM"
const val DATE_FORMAT_13 = "yyyy/MM/dd"
const val DATE_FORMAT_14 = "MMMM yyyy"
const val DATE_FORMAT_15 = "yyyy-MM"
const val DATE_FORMAT_16 = "dd-MM-yyyy"
const val DATE_FORMAT_17 = "MM-yyyy"
const val DATE_FORMAT_18 = "yyyy-MM"
const val DATE_FORMAT_19 = "yyyy-MM-dd"
const val SERVER_DATE_PATTERN = "yyyy-MM-dd'T'HH:mm:SS'Z'"


const val TIME_FORMAT_1 = "HH:mm:ss"

fun Long.toDateTimeWithZoneId(
    format: String,
    locale: Locale = Locale.US,
    zoneId: ZoneId,
): String? {
    val formatter = DateTimeFormatter.ofPattern(format, locale)
    val localDate = Instant.ofEpochMilli(this).atZone(zoneId).toLocalDateTime()

    return if (locale == getThaiLocale()) {
        val buddhistDate = ThaiBuddhistDate.from(localDate)
        buddhistDate.format(formatter)
    } else {
        localDate.format(formatter)
    }
}

fun String.getLocalDateTimeFromUTC(format: String): String? {
    val instant = Instant.parse(this)
    val formatter = DateTimeFormatter.ofPattern(format)
    return instant.atZone(ZoneId.systemDefault()).toLocalDateTime().format(formatter)
}

fun Long.sectoDateTimeWithZoneId(
    format: String,
    locale: Locale = Locale.US,
    zoneId: ZoneId,
): String? {

    val formatter = DateTimeFormatter.ofPattern(format, locale)
    val localDate = Instant.ofEpochSecond(this).atZone(zoneId).toLocalDateTime()

    return if (locale == getThaiLocale()) {
        val buddhistDate = ThaiBuddhistDate.from(localDate)
        buddhistDate.format(formatter)
    } else {
        localDate.format(formatter)
    }
}

fun Long.isInSameYear(zoneId: ZoneId = ZoneId.systemDefault()): Boolean {
    val currentTimestamp = Instant.now().toEpochMilli()

    // Convert timestamps to LocalDateTime
    val currentDateTime = LocalDateTime.ofInstant(
        Instant.ofEpochMilli(currentTimestamp),
        zoneId
    )

    val givenDateTime = LocalDateTime.ofInstant(
        Instant.ofEpochMilli(this),
        zoneId
    )

    return currentDateTime.year == givenDateTime.year
}

fun allYearsAreSame(timestamps: List<Long>, zoneId: ZoneId): Boolean {
    val years = timestamps.map { timestamp ->
        val instant = Instant.ofEpochMilli(timestamp)
        LocalDateTime.ofInstant(instant, ZoneOffset.UTC)
    }.map { it.year }
    var allYearAreSame = true
    var year = years.firstOrNull()
    years.forEach {
        if (it != year) {
            allYearAreSame = false
        }
        year = it
    }

    return allYearAreSame
}

fun getCurrentTimestamp(): Long {
    return System.currentTimeMillis()
}

fun getLastDateRangeTimestamp(monthsToSubtract: Long): Long {
    val currentDate = LocalDate.now()
    val threeMonthsAgo = currentDate.minusMonths(monthsToSubtract)
    return threeMonthsAgo.atStartOfDay().toInstant(ZoneOffset.UTC).toEpochMilli()
}

fun Long.displayNearest15Min(zoneId: ZoneId): String {
    val dateTime = Instant.ofEpochMilli(this)
        .atZone(zoneId)
        .toLocalDateTime()

    val roundedDateTime = dateTime.truncatedTo(ChronoUnit.MINUTES)
        .withMinute((dateTime.minute / 5) * 5)

    // Format the result as HH:mm
    val formatter = DateTimeFormatter.ofPattern(DATE_FORMAT_10)
    return roundedDateTime.format(formatter)
}

fun getDateGaps(date: Date, date2: Date): Int {
    val diff: Long = date.time - date2.time
    val seconds = diff / 1000
    val minutes = seconds / 60
    val hours = minutes / 60
    val days = hours / 24
    return days.toInt()
}

fun getLastWeekTimeStamp(): Long {
    val calendar = Calendar.getInstance()
    calendar.add(Calendar.DATE, -7)
    return calendar.timeInMillis
}

fun isOneDayDifference(timestamp1: Long, timestamp2: Long): Boolean {
    val instant1 = Instant.ofEpochMilli(timestamp1)
    val instant2 = Instant.ofEpochMilli(timestamp2)
    val duration = Duration.between(instant1, instant2)
    return duration.abs() == Duration.ofDays(1)
}

fun String.toDisplayDate(
    inputFormat: String = DATE_FORMAT_3,
    outputFormat: String = DATE_FORMAT_1,
): String {
    val inputFormatter = DateTimeFormatter.ofPattern(inputFormat)
    val date = LocalDate.parse(this, inputFormatter)
    val outputFormatter = DateTimeFormatter.ofPattern(outputFormat).withLocale(Locale.ENGLISH)
    return date.format(outputFormatter).uppercase()
}

fun LocalDate.toDisplayDate(
    inputFormat: String = DATE_FORMAT_3,
    outputFormat: String = DATE_FORMAT_1,
): String {
    val inputFormatter = DateTimeFormatter.ofPattern(inputFormat)
    val date = this
    val outputFormatter = DateTimeFormatter.ofPattern(outputFormat).withLocale(Locale.ENGLISH)
    return date.format(outputFormatter).uppercase()
}

fun haveSameYear(dates: List<Date>): Boolean {
    val firstYear = Calendar.getInstance().apply { time = dates.first() }.get(Calendar.YEAR)
    return dates.all { date ->
        Calendar.getInstance().apply { time = date }.get(Calendar.YEAR) == firstYear
    }
}

fun Long.displayTimeAgo(): String {
    val timestamp = this
    val pastTime = Instant.ofEpochMilli(timestamp).atZone(ZoneId.systemDefault())
    val now = ZonedDateTime.now(ZoneId.systemDefault())
    val duration = Duration.between(pastTime, now)
    return when {
        duration.toMinutes() < 60 -> "${duration.toMinutes()} min"
        duration.toHours() < 24 -> "${duration.toHours()} h "
        duration.toDays() < 7 -> "${duration.toDays()} D "
        duration.toDays() < 30 -> "${duration.toDays() / 7} W"
        duration.toDays() < 365 -> "${duration.toDays() / 30} M"
        else -> "${duration.toDays() / 365} Y"
    }
}

fun Long.displayHourAgo(): String {
    val timestamp = this
    val pastTime = Instant.ofEpochMilli(timestamp).atZone(ZoneId.systemDefault())
    val now = ZonedDateTime.now(ZoneId.systemDefault())
    val duration = Duration.between(pastTime, now)
    return "${duration.toHours()} h"
}

fun Long?.toLocalDate(): LocalDate {
    return Instant.ofEpochMilli(this ?: Instant.now().toEpochMilli())
        .atZone(ZoneId.systemDefault())
        .toLocalDate();
}


fun LocalDate.toEpochMilli() : Long {
    return this.atStartOfDay().toInstant(ZoneOffset.UTC)
        .toEpochMilli()
}
fun LocalDateTime.toEpochMilli() : Long {
    return this.atZone(ZoneId.systemDefault())
        .toInstant()
        .toEpochMilli()
}