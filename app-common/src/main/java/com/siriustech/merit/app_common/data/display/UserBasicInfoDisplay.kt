package com.siriustech.merit.app_common.data.display

import com.siriustech.merit.app_common.typeenum.UserStatus

/**
 * Created by <PERSON><PERSON> Htet
 */
data class UserBasicInfoDisplay(
    val name: String = "",
    val mobile: String = "",
    val email: String = "",
    val profileImage: String = "",
    val language: String = "",
    val sid: String = "",
    val userStatus: UserStatus = UserStatus.NORMAL,
    val mobileRegion:String = ""
)