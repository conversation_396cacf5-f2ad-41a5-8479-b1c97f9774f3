package com.siriustech.merit.app_common.theme

import android.content.Context
import android.os.Build
import android.os.Bundle
import androidx.appcompat.app.AppCompatDelegate
import com.siriustech.core_ui_compose.base.BaseComposeActivity
import com.siriustech.merit.app_common.R
import com.siriustech.merit.app_common.data.CommonSharedPreferences
import com.siriustech.merit.app_common.data.CommonSharedPreferences.Companion.PUBLIC_PREF_NAME
import com.siriustech.merit.app_common.ext.changeLocale
import java.util.Locale
import timber.log.Timber

/**
 * Created by <PERSON><PERSON>tet
 */
open class AppComposeActivity : BaseComposeActivity() {

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        AppCompatDelegate.setDefaultNightMode(AppCompatDelegate.MODE_NIGHT_NO)
    }

    override fun attachBaseContext(newBase: Context?) {
        Timber.d("NEW_BASE_CHANGED $newBase")
        if (newBase != null) {
            val pref = newBase.getSharedPreferences(PUBLIC_PREF_NAME, Context.MODE_PRIVATE)
            val lang = pref.getString(CommonSharedPreferences.KEY_LANGUAGE, "en").orEmpty()
            val locale = Locale(lang)
            super.attachBaseContext(newBase.changeLocale(locale))
        } else {
            super.attachBaseContext(newBase)
        }
    }

    override fun finish() {
        super.finish()
        onFinishWithAnimate()
    }

    private fun onFinishWithAnimate() {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.UPSIDE_DOWN_CAKE) {
            overrideActivityTransition(
                OVERRIDE_TRANSITION_CLOSE,
                R.anim.slide_in_left,
                R.anim.slide_out_left
            )
        } else {
            overridePendingTransition(
                R.anim.slide_in_left,
                R.anim.slide_out_left
            )
        }
    }
}