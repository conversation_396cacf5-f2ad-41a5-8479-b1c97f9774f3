package com.siriustech.merit.app_common.mapper

import android.content.Context
import com.siriustech.merit.apilayer.service.user.chat.ChatItemFileResponse
import com.siriustech.merit.apilayer.service.user.chat.ChatListResponse
import com.siriustech.merit.app_common.Constants
import com.siriustech.merit.app_common.ext.DATE_FORMAT_16
import com.siriustech.merit.app_common.ext.toLocalDate
import com.siriustech.merit.app_common.screen.chat.component.AttachmentDisplayModel
import com.siriustech.merit.app_common.screen.chat.component.ChatMessageDisplayModel
import com.siriustech.merit.app_common.screen.chat.component.MessageType
import com.siriustech.merit.app_common.screen.chat.component.SenderRoleType
import com.siriustech.merit.app_common.utils.formatFileSize
import kotlin.random.Random
import org.threeten.bp.LocalDate
import org.threeten.bp.format.DateTimeFormatter
import com.siriustech.merit.app_common.R as AppCommonR

/**
 * Created by He<PERSON>te<PERSON>
 */
object ChatMapper {
    fun groupMessagesByDay(messages: List<ChatMessageDisplayModel>,context: Context): List<Pair<String, List<ChatMessageDisplayModel>>> {
        val groupedMessages = messages.groupBy { message ->
            val date = message.createdAt.toLocalDate()
            val today = LocalDate.now()
            val yesterday = today.minusDays(1)
            when {
                date.isEqual(today) -> context.getString(AppCommonR.string.key0953)
                date.isEqual(yesterday) -> context.getString(AppCommonR.string.key0962)
                else -> date.format(DateTimeFormatter.ofPattern(DATE_FORMAT_16))
            }
        }
        return groupedMessages.entries.map { it.key to it.value }
    }

    // Mock data generation function
    fun generateMockChatData(): List<ChatMessageDisplayModel> {
        val mockTexts = listOf(
            "Hello!", "How are you?", "What's the update?", "Let's meet tomorrow.",
            "Can you send the file?", "Sure, I'll do that.", "See you later!",
            "Got it.", "What time?", "Is it ready?", "Thanks a lot!", "No problem.",
            "Absolutely.", "Good morning!", "Good night!", "Let's start.",
            "I'll check.", "Yes.", "No.", "Maybe.", "Alright.", "Take care."
        )
        val userIds = listOf("user_1", "assistant_1")
        val mockData = mutableListOf<ChatMessageDisplayModel>()

        var lastSenderId = ""
        var currentTime = System.currentTimeMillis()

        repeat(50) {
            val senderId = userIds.random()
            val isNewDay =
                Random.nextBoolean() && it % 10 == 0 // Simulate new day grouping occasionally
            val createdAt = if (isNewDay) {
                currentTime -= 86_400_000 // Jump back by one day
                currentTime
            } else {
                currentTime -= Random.nextLong(
                    1_000L,
                    10_000L
                ) // Simulate interval between messages
                currentTime
            }
            val messageType = if (Random.nextBoolean()) MessageType.TEXT else MessageType.IMAGE
            val chatType = if (senderId == "user_1") SenderRoleType.USER else SenderRoleType.SUPPORT
            val imageUrl =
                if (messageType == MessageType.IMAGE) "https://example.com/image_$it.jpg" else ""

            mockData.add(
                ChatMessageDisplayModel(
                    text = mockTexts.random(),
//                    text = if (messageType == MessageType.TEXT) mockTexts.random() else "",
                    senderRole = chatType,
                    messageType = messageType,
                    imageUrl = imageUrl,
                    createdAt = createdAt,
                    senderId = senderId
                )
            )

            lastSenderId = senderId
        }
        return mockData.sortedBy { it.createdAt }
    }


    fun ChatListResponse.mapToChatMessageListDisplay(baseUrl: String): ArrayList<ChatMessageDisplayModel> {
        val items = ArrayList<ChatMessageDisplayModel>()
        this.messageList.orEmpty().sortedBy { it.sentTime }
            .forEachIndexed { _, chatItemResponse ->
                items.add(
                    ChatMessageDisplayModel(
                        text = chatItemResponse.message.orEmpty(),
                        senderId = chatItemResponse.senderId.toString(),
                        senderRole = SenderRoleType.fromParams(chatItemResponse.senderRole.orEmpty()),
                        createdAt = chatItemResponse.sentTime ?: 0L,
                        messageType = MessageType.TEXT,
                        attachments = chatItemResponse.fileList
                            .mapToChatAttachmentsDisplay(baseUrl),
                        senderName = chatItemResponse.senderName.orEmpty()
                    )
                )
            }
        return items
    }

    private fun List<ChatItemFileResponse>?.mapToChatAttachmentsDisplay(baseUrl: String): List<AttachmentDisplayModel> {
        val items = ArrayList<AttachmentDisplayModel>()
        this.orEmpty().forEach {
            val docUrl = "$baseUrl${Constants.COMMON_FILE_BASE_URL}${it.fileKey.orEmpty()}"
            items.add(
                AttachmentDisplayModel(
                    name = it.fileName.orEmpty(),
                    type = it.fileType.orEmpty(),
                    url = docUrl,
                    originalFileSize = it.fileSize ?: 0L,
                    fileSize = formatFileSize(it.fileSize ?: 0L)
                )
            )
        }
        return items
    }
}