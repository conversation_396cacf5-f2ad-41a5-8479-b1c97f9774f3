package com.siriustech.merit.app_common.screen.forgotpin

enum class ForgotPinStep(val step: Int) {
    INPUT_EMAIL(0),
    VERIFY_EMAIL_OTP(1),
    CREATE_PIN(2),
    CONFIRM_PIN(3);

    companion object {

        const val TOTAL_STEP = 4

        fun fromParam(value: Int): ForgotPinStep {
            return when (value) {
                0 -> INPUT_EMAIL
                1 -> VERIFY_EMAIL_OTP
                2 -> CREATE_PIN
                3 -> CONFIRM_PIN
                else -> INPUT_EMAIL
            }
        }
    }
}
