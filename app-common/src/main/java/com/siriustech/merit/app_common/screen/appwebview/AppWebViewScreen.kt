package com.siriustech.merit.app_common.screen.appwebview

import android.annotation.SuppressLint
import android.content.ClipData
import android.content.ClipboardManager
import android.content.Context
import android.webkit.WebView
import android.webkit.WebViewClient
import android.widget.Toast
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.key
import androidx.compose.runtime.mutableStateListOf
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clipToBounds
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.viewinterop.AndroidView
import com.siriustech.core_ui_compose.component.CommonToolbar
import com.siriustech.core_ui_compose.component.ToolbarProperties
import com.siriustech.core_ui_compose.ext.getActivity
import com.siriustech.merit.app_common.R
import com.siriustech.merit.app_common.component.modalbts.CommonMoreInfoModalBts
import com.siriustech.merit.app_common.component.modalbts.CommonMoreInfoModalData
import com.siriustech.merit.app_common.ext.openBrowser
import timber.log.Timber

/**
 * Created by Hein Htet
 */

@SuppressLint("SetJavaScriptEnabled")
@Composable
fun AppWebViewScreen(modifier: Modifier = Modifier, argument: AppWebViewArgument) {
    val activity = LocalContext.current.getActivity()
    var webViewKey by remember { mutableStateOf(0) } // Key to force recompose

    val clipboardManager = activity?.getSystemService(Context.CLIPBOARD_SERVICE) as ClipboardManager


    val menus = remember {
        mutableStateListOf(
            CommonMoreInfoModalData(
                title = activity?.getString(com.siriustech.merit.app_common.R.string.key1051)
                    .orEmpty(),
                iconResId = R.drawable.ic_language_selection,
                onClick = {
                    activity.openBrowser(argument.url.orEmpty())
                }
            ),
            CommonMoreInfoModalData(
                title = activity?.getString(com.siriustech.merit.app_common.R.string.key1050)
                    .orEmpty(),
                iconResId = R.drawable.ic_reset,
                onClick = {
                    webViewKey += 1
                }
            ),
            CommonMoreInfoModalData(
                title = activity.getString(com.siriustech.merit.app_common.R.string.key1052)
                    .orEmpty(),
                iconResId = R.drawable.ic_copy_black,
                onClick = {
                    Toast.makeText(
                        activity,
                        activity.getString(R.string.key1052),
                        Toast.LENGTH_SHORT
                    ).show()
                    clipboardManager.setPrimaryClip(
                        ClipData(
                            "Copy",
                            arrayOf("text/plain"),
                            ClipData.Item(argument.url)
                        )
                    )
                }
            )
        )
    }


    var showMoreInfoModal by remember {
        mutableStateOf(false)
    }

    Column(modifier = Modifier.fillMaxSize()) {
        CommonToolbar(
            properties = ToolbarProperties(
                leftActionResId = R.drawable.ic_action_close,
                rightActionResId = R.drawable.ic_three_dots,
                title = argument.title,
            ),
            onLeftActionClicked = {
                activity.finish()
            },
            onRightActionClicked = {
                showMoreInfoModal = true
            }
        )
        key(webViewKey) {
            AndroidView(
                modifier = Modifier
                    .clipToBounds(),
                factory = { context ->
                    WebView(context).apply {
                        webViewClient = WebViewClient()
                        settings.javaScriptEnabled = true
                        settings.domStorageEnabled = true
                        settings.loadWithOverviewMode = true
                        settings.useWideViewPort = true
                        settings.setSupportZoom(true)
                    }
                },
                update = { webView ->
                    if (!argument.url.isNullOrEmpty()) {
                        Timber.d("Load_WEB_URL ${argument.url}")
                        webView.loadUrl(argument.url)
                    } else if (!argument.htmlContent.isNullOrEmpty()) {
                        webView.loadDataWithBaseURL(
                            null,
                            argument.htmlContent,
                            "text/html",
                            "UTF-8",
                            null
                        )
                    }
                })
        }
    }
    if (showMoreInfoModal) {
        CommonMoreInfoModalBts(
            menus = menus,
            onDismissed = {
                showMoreInfoModal = false
            }
        )
    }
}