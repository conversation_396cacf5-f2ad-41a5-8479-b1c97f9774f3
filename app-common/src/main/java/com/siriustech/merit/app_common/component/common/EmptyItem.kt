package com.siriustech.merit.app_common.component.common

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.tooling.preview.Preview
import com.siriustech.merit.app_common.R
import com.siriustech.merit.app_common.component.container.PaddingTop
import com.siriustech.merit.app_common.ext.colorTxtInactive
import com.siriustech.merit.app_common.ext.colorTxtTitle
import com.siriustech.merit.app_common.ext.noRippleClickable
import com.siriustech.merit.app_common.theme.LocalAppColor
import com.siriustech.merit.app_common.theme.LocalDimens
import com.siriustech.merit.app_common.theme.LocalTypography

/**
 * Created by Hein Htet
 */
@Composable
fun EmptyItem(
    modifier: Modifier = Modifier,
    onButtonClicked : () -> Unit = {},
    properties: EmptyItemProperties = EmptyItemProperties(),
) {
    Column(
        modifier = Modifier
            .fillMaxSize()
            .then(modifier),
        verticalArrangement = Arrangement.Center,
        horizontalAlignment = Alignment.CenterHorizontally
    ) {
        Text(
            text = properties.title,
            style = LocalTypography.current.text14.light.colorTxtInactive().merge(properties.titleTextStyle)
        )
        if(properties.buttonText!=null){
            PaddingTop(value = LocalDimens.current.dimen24)
            Box(
                modifier = Modifier
                    .height(LocalDimens.current.dimen44)
                    .background(LocalAppColor.current.btn3rd)
                    .padding(horizontal = LocalDimens.current.dimen44)
                    .noRippleClickable { onButtonClicked()  },
                contentAlignment = Alignment.Center){
                Text(text = properties.buttonText, style = LocalTypography.current.text14.medium.colorTxtTitle())
            }
        }
    }
}

data class EmptyItemProperties(
    val title: String = "",
    val buttonText: String? = null,
    val titleTextStyle: TextStyle = TextStyle(),
)

@Preview(showBackground = true, showSystemUi = true)
@Composable
fun PreviewEmptyItem() {
    EmptyItem(properties = EmptyItemProperties("This is empty Title", buttonText = "This is Button Text"))
}