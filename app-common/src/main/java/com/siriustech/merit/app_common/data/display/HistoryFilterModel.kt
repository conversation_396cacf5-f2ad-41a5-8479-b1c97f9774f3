package com.siriustech.merit.app_common.data.display

import com.siriustech.merit.app_common.component.modalbts.ModalListDataContent
import com.siriustech.merit.app_common.typeenum.HistoryFilterType
import com.siriustech.merit.app_common.typeenum.OrderSide
import com.siriustech.merit.app_common.typeenum.OrderStatus
import kotlinx.serialization.Serializable

@Serializable
data class HistoryFilterModel(
    val side: OrderSide = OrderSide.ALL,
    val status: OrderStatus = OrderStatus.ALL,
    val startDate: Long? = null,
    val endDate: Long? = null,
    val productCategories: List<ModalListDataContent> = emptyList(),
    val type: HistoryFilterType = HistoryFilterType.TRADE_HISTORY,
    val currency : String = "All"
) : java.io.Serializable