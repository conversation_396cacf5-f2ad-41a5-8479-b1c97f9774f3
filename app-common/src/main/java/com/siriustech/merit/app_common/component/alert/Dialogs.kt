package com.siriustech.merit.app_common.component.alert

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.material3.ButtonDefaults
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.res.stringResource
import com.siriustech.core_ui_compose.model.CommonPopupDisplayData
import com.siriustech.merit.app_common.ext.colorTxtParagraph
import com.siriustech.merit.app_common.ext.colorTxtTitle
import com.siriustech.merit.app_common.theme.LocalAppColor
import com.siriustech.merit.app_common.theme.LocalDimens
import com.siriustech.merit.app_common.theme.LocalTypography
import com.siriustech.merit.app_common.R as AppCommonR

/**
 * Created by Hein Htet
 */

@Composable
fun getCommonDialogProperties(): CommonPopupDisplayData {
    return CommonPopupDisplayData(
        title = stringResource(id = AppCommonR.string.key1016),
        titleTextStyle = LocalTypography.current.text16.medium.colorTxtTitle(),
        messageTextStyle = LocalTypography.current.text14.regular.colorTxtParagraph(),
        leftButtonModifier = Modifier,
        backgroundColor = LocalAppColor.current.bgDefault,
        leftButtonTextStyle = LocalTypography.current.text14.medium.colorTxtTitle(),
        rightButtonTextStyle = LocalTypography.current.text14.medium.colorTxtTitle(),
        rightButtonModifier = Modifier
            .height(LocalDimens.current.dimen44)
            .fillMaxWidth(1f)
            .background(LocalAppColor.current.txtLabel),
        rightButtonColor = ButtonDefaults.buttonColors(
            containerColor = LocalAppColor.current.txtLabel,
            disabledContainerColor = LocalAppColor.current.btnDisabled,
            contentColor = LocalAppColor.current.txtInverted,
            disabledContentColor = LocalAppColor.current.txtDisabled
        ),
        rightButtonTextMessage = stringResource(id = AppCommonR.string.key0832)
    )
}