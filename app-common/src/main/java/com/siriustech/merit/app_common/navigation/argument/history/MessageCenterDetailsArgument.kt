
package com.siriustech.merit.app_common.navigation.argument.history

import androidx.lifecycle.SavedStateHandle
import androidx.navigation.toRoute
import com.siriustech.merit.app_common.data.display.HistoryFilterModel
import com.siriustech.merit.app_common.ext.serializableType
import com.siriustech.merit.app_common.navigation.OrderHistoryList
import com.siriustech.merit.app_common.typeenum.HistoryFilterType
import kotlin.reflect.typeOf
import kotlinx.serialization.Serializable

@Serializable
data class OrderHistoryListArgument(
    val historyFilterType: HistoryFilterType,
    val historyFilterModel : HistoryFilterModel
) {
    companion object {
        val typeMap =
            mapOf(typeOf<OrderHistoryListArgument>() to serializableType<OrderHistoryListArgument>())

        fun from(savedStateHandle: SavedStateHandle) =
            savedStateHandle.toRoute<OrderHistoryList>(typeMap)
    }
}