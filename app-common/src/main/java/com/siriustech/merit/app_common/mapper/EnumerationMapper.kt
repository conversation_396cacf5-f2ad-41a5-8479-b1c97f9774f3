package com.siriustech.merit.app_common.mapper

import com.siriustech.merit.apilayer.service.authentication.common.eumeration.EnumerationResponse
import com.siriustech.merit.app_common.component.modalbts.ModalListDataContent

/**
 * Created by <PERSON><PERSON>
 */
object EnumerationMapper {

    fun EnumerationResponse.toModalDisplayList(): List<ModalListDataContent> {
        return this.list.map {
            ModalListDataContent(
                id = it.code,
                items = it.list.sortedBy { it.sort }.map {
                    ModalListDataContent(
                        title = it.desc,
                        value = it.value,
                        id = it.value
                    )
                }
            )
        }
    }
}