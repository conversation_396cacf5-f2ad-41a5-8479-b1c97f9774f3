package com.siriustech.merit.app_common.ext

import android.content.ActivityNotFoundException
import android.content.Context
import android.content.Intent
import android.net.Uri
import android.widget.Toast
import androidx.core.content.FileProvider
import java.io.File
import java.util.Locale


/**
 * Created by <PERSON><PERSON>t
 */

fun Context.openPDF(pdfUri: Uri) {
    try {
        val file = File(pdfUri.path!!)
        val contentUri = FileProvider.getUriForFile(this, "${packageName}.provider", file)
        val intent = Intent(Intent.ACTION_VIEW).apply {
            setDataAndType(contentUri, "application/pdf")
            flags = Intent.FLAG_ACTIVITY_CLEAR_TOP or Intent.FLAG_GRANT_READ_URI_PERMISSION
        }
        startActivity(intent)
    } catch (e: Exception) {
        Toast.makeText(this, "No PDF viewer found", Toast.LENGTH_SHORT).show()
    }
}

fun Context.openPDFViewer(pdfUri: Uri) {
    try {
        val file = File(pdfUri.path!!)
        // Get a content URI for the file
        val contentUri = FileProvider.getUriForFile(
            this,
            "${this.packageName}.provider",
            file
        )

        // Create an Intent to view the file
        val intent = Intent(Intent.ACTION_VIEW).apply {
            setDataAndType(contentUri, "application/pdf") // Set MIME type
            flags = Intent.FLAG_ACTIVITY_CLEAR_TOP or Intent.FLAG_GRANT_READ_URI_PERMISSION
        }

        // Create a chooser to let the user pick the app
        val chooser = Intent.createChooser(intent, "Open PDF with")

        // Start the chooser
        this.startActivity(chooser)

    } catch (e: ActivityNotFoundException) {
        Toast.makeText(this, "No app found to open this file", Toast.LENGTH_SHORT).show()
    } catch (e: Exception) {
        e.printStackTrace()
        Toast.makeText(this, "Unable to open file", Toast.LENGTH_SHORT).show()
    }
}

fun Context.changeLocale(locale: Locale): Context? {
    val config = resources.configuration
    Locale.setDefault(locale)
    config.setLocale(locale)
    config.setLayoutDirection(locale)
    resources.updateConfiguration(config, resources.displayMetrics)
    return createConfigurationContext(config)
}

fun Context.openBrowser(url: String) {
    //Make sure it is a valid URL before parsing the URL.
    var url = url
    if (!url.contains("http://") && !url.contains("https://")) {
        //If it isn't, just add the HTTP protocol at the start of the URL.
        url = "http://$url"
    }
    //create the intent
    val intent = Intent(
        Intent.ACTION_VIEW,
        Uri.parse(url) /*And parse the valid URL. It doesn't need to be changed at this point, it we don't create an instance for it*/
    )
    if (intent.resolveActivity(packageManager) != null) {
        startActivity(intent)
    }
}
