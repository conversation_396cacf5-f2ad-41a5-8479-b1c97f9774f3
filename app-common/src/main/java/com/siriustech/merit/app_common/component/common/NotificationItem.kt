package com.siriustech.merit.app_common.component.common

import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.tooling.preview.Preview
import coil3.compose.AsyncImage
import coil3.request.ImageRequest
import coil3.request.crossfade
import com.siriustech.merit.app_common.R
import com.siriustech.merit.app_common.component.container.PaddingStart
import com.siriustech.merit.app_common.component.container.PaddingTop
import com.siriustech.merit.app_common.ext.colorTxtInactive
import com.siriustech.merit.app_common.ext.colorTxtParagraph
import com.siriustech.merit.app_common.ext.noRippleClickable
import com.siriustech.merit.app_common.theme.LocalAppColor
import com.siriustech.merit.app_common.theme.LocalDimens
import com.siriustech.merit.app_common.theme.LocalTypography
import com.siriustech.merit.app_common.typeenum.NotificationBusinessType
import com.siriustech.merit.app_common.typeenum.NotificationTagType
import kotlinx.serialization.Serializable
import timber.log.Timber

/**
 * Created by Hein Htet
 */


@Composable
fun NotificationItem(
    modifier: Modifier = Modifier,
    data: NotificationItemDisplayData = NotificationItemDisplayData(),
    onClicked: () -> Unit = {},
    onMoreInfoClicked: () -> Unit = {},
) {
    Row(
        modifier = Modifier
            .then(modifier)
            .padding(LocalDimens.current.dimen8)
            .noRippleClickable {
                onClicked()
            },
    ) {
        Column(horizontalAlignment = Alignment.CenterHorizontally) {
            DisplayStatusIcon(data.type, data.logo)
            PaddingTop(value = LocalDimens.current.dimen4)
            Text(
                text = data.time,
                style = LocalTypography.current.text12.regular.colorTxtInactive()
            )
        }
        PaddingStart(value = LocalDimens.current.dimen8)
        Column(modifier = Modifier.weight(1f)) {
            DisplayStateTitle(data.tag, data.title)
            Text(
                text = data.description,
                maxLines = 2,
                overflow = TextOverflow.Ellipsis,
                style = LocalTypography.current.text12.regular.colorTxtInactive()
            )
        }
        if (data.showMoreInfoIcon) {
            Box(
                modifier = Modifier
                    .size(LocalDimens.current.dimen44)
                    .noRippleClickable { onMoreInfoClicked() },
                contentAlignment = Alignment.Center
            ) {
                Image(
                    painter = painterResource(id = R.drawable.ic_three_dots),
                    contentDescription = "More Info Image Resource"
                )
            }
        }
    }
}

@Composable
fun DisplayStateTitle(tag: NotificationTagType?, title: String?) {
    val color = if (tag == NotificationTagType.APPROVED) LocalAppColor.current.txtPositive else if (tag == NotificationTagType.REJECTED) LocalAppColor.current.txtNegative else LocalAppColor.current.txtParagraph
//    color = LocalAppColor.current.txtParagraph
    Text(
        text = title.orEmpty(),
        style = LocalTypography.current.text14.medium.colorTxtParagraph().copy(color = color)
    )
}

@Composable
fun DisplayStatusIcon(notificationBusinessType: NotificationBusinessType?, logo: String? = null) {
    when (notificationBusinessType) {
        NotificationBusinessType.ORDER -> {
            AsyncImage(
                modifier = Modifier.size(LocalDimens.current.dimen24)
                    .clip(RoundedCornerShape(50)),
                model = ImageRequest.Builder(LocalContext.current)
                    .data(logo)
                    .crossfade(true)
                    .build(),
                placeholder = painterResource(R.drawable.ic_product_category_placeholder),
                error = painterResource(R.drawable.ic_product_category_placeholder),
                contentDescription = "Product Category Placeholder",
                contentScale = ContentScale.Crop,
                onError = {
                    Timber.d("Image Loading error ${it.result}")
                }
            )
        }

        NotificationBusinessType.DEPOSIT -> {
            Image(
                modifier = Modifier.size(LocalDimens.current.dimen24),
                painter = painterResource(id = R.drawable.ic_status_deposit),
                contentDescription = "Deposit Image Resource"
            )
        }

        NotificationBusinessType.WITHDRAW -> {
            Image(
                modifier = Modifier.size(LocalDimens.current.dimen24),
                painter = painterResource(id = R.drawable.ic_status_withdraw),
                contentScale = ContentScale.Crop,
                contentDescription = "Deposit Image Resource"
            )
        }

        else -> Image(
            modifier = Modifier.size(LocalDimens.current.dimen24),
            contentScale = ContentScale.Crop,
            painter = painterResource(id = R.drawable.ic_product_category_placeholder),
            contentDescription = "Deposit Image Resource"
        )
    }
}

@Serializable
data class NotificationItemDisplayData(
    val id: Int = -1,
    val title: String = "",
    val description: String = "",
    val status: String = "",
    val logo: String = "",
    var time: String = "",
    var isRead: Boolean = false,
    var showMoreInfoIcon: Boolean = true,
    val type: NotificationBusinessType? = null,
    val tag: NotificationTagType? = null,
    val displayDateTime: String = "",
    val datetime: Long = 0L,
    val headerDate: String = "",
    val isHeader : Boolean = false
) : java.io.Serializable

@Preview(showBackground = true, showSystemUi = true)
@Composable
fun PreviewNotificationItem() {
    Box(
        modifier = Modifier
            .background(LocalAppColor.current.txtPositive)
    ) {
        NotificationItem(
            modifier = Modifier
                .clip(RoundedCornerShape(LocalDimens.current.dimen2))
                .background(LocalAppColor.current.bgTone),
            data = NotificationItemDisplayData(
                title = "Your order has been approved",
                description = "Loremor Lorem ipsum dolor Lore",
                time = "6h",
                datetime = 1L
            )
        )
    }
}