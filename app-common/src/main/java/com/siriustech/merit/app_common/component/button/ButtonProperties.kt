package com.siriustech.merit.app_common.component.button

import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.painter.Painter
import androidx.compose.ui.text.TextStyle

/**
 * Created by <PERSON><PERSON>
 */
data class ButtonProperties(
    val enabled: Boolean = true,
    val text: String = "",
    val icon: <PERSON>? = null,
    val prefixIcon: Painter? = null,
    val prefixIconModifier: Modifier = Modifier,
    val textStyle: TextStyle = TextStyle(),
    val iconModifier: Modifier = Modifier,
)