package com.siriustech.merit.app_common.typeenum

/**
 * Created by <PERSON><PERSON>
 */
enum class OnboardingQuestionType(val value: String) {
    TEXT_BOX("TEXTBOX"),
    RADIO("RADIO"),
    SELECTOR_EXPAND("EXPAND");

    companion object {
        fun fromParams(id: String): OnboardingQuestionType {
            return when (id) {
                "TEXTBOX" -> TEXT_BOX
                "RADIO" -> RADIO
                "EXPAND" -> SELECTOR_EXPAND
                else -> RADIO
            }
        }
    }
}