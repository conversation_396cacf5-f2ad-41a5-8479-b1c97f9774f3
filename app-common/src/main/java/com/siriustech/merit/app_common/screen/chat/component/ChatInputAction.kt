package com.siriustech.merit.app_common.screen.chat.component

import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.ColorFilter
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.tooling.preview.Preview
import com.siriustech.merit.app_common.component.textfield.InputBox
import com.siriustech.merit.app_common.component.textfield.InputProperties
import com.siriustech.merit.app_common.ext.noRippleClickable
import com.siriustech.merit.app_common.theme.LocalAppColor
import com.siriustech.merit.app_common.theme.LocalDimens
import com.siriustech.merit.app_common.R as AppCommonR

/**
 * Created by <PERSON><PERSON><PERSON><PERSON>
 */

@Composable
fun ChatInputAction(
    onTextChanged: (text: String) -> Unit = {},
    defaultText: String = "",
    onPickFile: () -> Unit = {},
    onSendMessage: () -> Unit = {},
    sendEnabled : Boolean = false
) {


    Box(
        modifier = Modifier
            .fillMaxWidth()
            .height(LocalDimens.current.dimen1)
            .background(Color.Gray.copy(alpha = 0.1f))
    )
    Box(
        modifier = Modifier
            .fillMaxWidth()
            .padding(
                LocalDimens.current.dimen12
            )
    ) {
        Row(modifier = Modifier, verticalAlignment = Alignment.CenterVertically) {
            Box(modifier = Modifier.weight(1f)) {
                InputBox(
                    value = defaultText,
                    onTextChange = onTextChanged,
                    inputBoxModifier = Modifier,
                    properties = InputProperties(
                        onPickerIconClick = {
                            onPickFile()
                        },
                        defaultValue = defaultText,
                        placeholder = "Type message...",
                        pickerTypeIcon = painterResource(id = AppCommonR.drawable.ic_attach_file)
                    )
                )
            }
            Box(
                modifier = Modifier
                    .size(LocalDimens.current.dimen44)
                    .noRippleClickable {
                        if (sendEnabled) {
                            onSendMessage()
                        }
                    },
                contentAlignment = Alignment.Center
            ) {
                Image(
                    colorFilter = ColorFilter.tint(
                        if (sendEnabled) LocalAppColor.current.txtTitle else LocalAppColor.current.txtDisabled
                    ),
                    painter = painterResource(id = AppCommonR.drawable.ic_send_arrow),
                    contentDescription = "Send Image Resource"
                )
            }
        }
    }
}

@Preview(showBackground = true, showSystemUi = true)
@Composable
fun PreviewChatInputAction() {
    ChatInputAction()
}
