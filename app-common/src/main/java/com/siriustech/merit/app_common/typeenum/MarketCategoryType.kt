package com.siriustech.merit.app_common.typeenum

/**
 * Created by <PERSON><PERSON>
 */
enum class MarketCategoryType(val value: String) {
    CASH_EQUIVALENT("CASH_EQUIVALENT"),
    FIX_INCOME("FIX_INCOME"),
    STRUCTURED_PRODUCTS("STRUCTURED_PRODUCTS"),
    PRIVATE_CREDIT("PRIVATE_CREDIT"),
    EQUITY("EQUITY"),
    VIRTUAL_ASSET("VIRTUAL_ASSET"),
    MULTI_ASSET("MULTI_ASSET"),
    COMMODITIES("COMMODITIES"),
    PRIVATE_CAPITAL("PRIVATE_CAPITAL"),
    PRIVATE_EQUITY("PRIVATE_EQUITY"),
    LOW("LOW"),
    MEDIUM("MEDIUM"),
    HIGH("HIGH"),
    RECOMMENDED("RECOMMENDED"),
    FAVORITE("FAVORITE"),
    RECENTLY_VIEWED("RECENTLY_VIEWED");



    fun isRefineFilterEnabled() : Boolean {
        val items = listOf(RECOMMENDED,FAVORI<PERSON>,RECENTLY_VIEWED)
        return !items.contains(this)
    }

    fun isRefineRiskLevelFilterEnabled() : Boolean {
        val items = listOf(LOW,MEDIUM,HIGH)
        return !items.contains(this)
    }



    companion object {
        fun fromParam(type: String): MarketCategoryType {
            return when (type) {
                CASH_EQUIVALENT.value -> CASH_EQUIVALENT
                FIX_INCOME.value -> FIX_INCOME
                STRUCTURED_PRODUCTS.value -> STRUCTURED_PRODUCTS
                PRIVATE_CREDIT.value -> PRIVATE_CREDIT
                EQUITY.value -> EQUITY
                VIRTUAL_ASSET.value -> VIRTUAL_ASSET
                MULTI_ASSET.value -> MULTI_ASSET
                COMMODITIES.value -> COMMODITIES
                PRIVATE_CAPITAL.value -> PRIVATE_CAPITAL
                PRIVATE_EQUITY.value -> PRIVATE_EQUITY
                LOW.value -> LOW
                MEDIUM.value -> MEDIUM
                HIGH.value -> HIGH
                FAVORITE.value -> FAVORITE
                RECOMMENDED.value -> RECOMMENDED
                RECENTLY_VIEWED.value -> RECENTLY_VIEWED
                RECENTLY_VIEWED.value -> RECENTLY_VIEWED
                else -> EQUITY
            }
        }
    }

}