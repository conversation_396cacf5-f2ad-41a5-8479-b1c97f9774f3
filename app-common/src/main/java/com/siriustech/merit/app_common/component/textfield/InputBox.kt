package com.siriustech.merit.app_common.component.textfield

import android.util.Log
import androidx.compose.animation.AnimatedVisibility
import androidx.compose.animation.core.tween
import androidx.compose.animation.fadeIn
import androidx.compose.animation.fadeOut
import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxHeight
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.text.BasicTextField
import androidx.compose.foundation.text.KeyboardActions
import androidx.compose.foundation.text.KeyboardOptions
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.painter.Painter
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.text.input.PasswordVisualTransformation
import androidx.compose.ui.text.input.TextFieldValue
import androidx.compose.ui.text.input.VisualTransformation
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.tooling.preview.Preview
import coil3.compose.AsyncImage
import coil3.request.ImageRequest
import coil3.request.crossfade
import com.siriustech.merit.app_common.R
import com.siriustech.merit.app_common.component.container.PaddingStart
import com.siriustech.merit.app_common.component.container.PaddingTop
import com.siriustech.merit.app_common.ext.colorTxtInactive
import com.siriustech.merit.app_common.ext.colorTxtLabel
import com.siriustech.merit.app_common.ext.colorTxtNegative
import com.siriustech.merit.app_common.ext.colorTxtTitle
import com.siriustech.merit.app_common.ext.noRippleClickable
import com.siriustech.merit.app_common.theme.LocalAppColor
import com.siriustech.merit.app_common.theme.LocalDimens
import com.siriustech.merit.app_common.theme.LocalTypography
import kotlinx.coroutines.Job
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import timber.log.Timber

/**
 * Created by Hein Htet
 */

@Composable
fun InputBox(
    modifier: Modifier = Modifier,
    inputBoxModifier: Modifier = Modifier,
    value: String? = null,
    properties: InputProperties = InputProperties(),
    onTextChange: (String) -> Unit = {},
) {
    var text by remember { mutableStateOf(TextFieldValue(value ?: properties.defaultValue)) }
    var virtualTransformation by remember { mutableStateOf(if (!properties.isPasswordMode) VisualTransformation.None else PasswordVisualTransformation()) }

    val coroutineScope = rememberCoroutineScope()
    val debouncePeriod = properties.debouncePeriod

    // Use this Job to cancel previous debounce tasks
    var debounceJob by remember { mutableStateOf<Job?>(null) }

    LaunchedEffect(key1 = value) {
        Log.d("Input Box ", "OnValue $value")
        val data = value ?: properties.defaultValue
        text = TextFieldValue(data, selection = text.selection)
    }

    Column(modifier = Modifier.then(modifier)) {
        if (properties.title.isNotEmpty()) {
            Column {
                Row(verticalAlignment = Alignment.CenterVertically) {
                    Text(
                        text = properties.title,
                        style = if (properties.isError) LocalTypography.current.text14.medium.colorTxtNegative() else properties.titleLabelTextStyle
                            ?: LocalTypography.current.text14.medium.colorTxtLabel()
                    )
                    if (properties.showRequiredStar) {
                        Image(
                            modifier = Modifier.padding(
                                start = LocalDimens.current.dimen4,
                            ),
                            painter = painterResource(id = R.drawable.ic_required_star),
                            contentDescription = "Required Star Image Resource"
                        )
                    }
                    if (properties.infoTitleIcon != null) {
                        Image(
                            painter = properties.infoTitleIcon,
                            modifier = Modifier
                                .noRippleClickable { properties.onInfoIconClick() }
                                .padding(
                                    start = LocalDimens.current.dimen4,
                                ),
                            contentDescription = "Info Icon Resource"
                        )

                    }
                }
                if (properties.subTitle.orEmpty().isNotEmpty()) {
                    Text(text = properties.subTitle.orEmpty(), style = properties.subTitleTextStyle)
                }
            }
            Spacer(modifier = Modifier.height(LocalDimens.current.dimen8))
        }
        Box(
            modifier = inputBoxModifier
                .background(
                    if (!properties.isError) LocalAppColor.current.bgAccent else LocalAppColor.current.bgNegative,
                    shape = RoundedCornerShape(LocalDimens.current.dimen8)
                )
                .fillMaxWidth()
                .padding(horizontal = LocalDimens.current.dimen16)
                .height(LocalDimens.current.dimen44)

        ) {
            Row(verticalAlignment = Alignment.CenterVertically) {
                if (properties.inputPrefixIcon != null && properties.prefixImageLogo.isNullOrEmpty()) {
                    Image(
                        painter = properties.inputPrefixIcon,
                        contentDescription = "Image Search Resource"
                    )
                    Spacer(modifier = Modifier.width(LocalDimens.current.dimen8))
                }
                if (!properties.prefixImageLogo.isNullOrEmpty()) {
                    AsyncImage(
                        modifier = Modifier.size(LocalDimens.current.dimen16),
                        model = ImageRequest.Builder(LocalContext.current)
                            .data(properties.prefixImageLogo)
                            .crossfade(true)
                            .build(),
                        placeholder = painterResource(R.drawable.ic_product_category_placeholder),
                        error = painterResource(R.drawable.ic_product_category_placeholder),
                        contentDescription = "Product Category Placeholder",
                        contentScale = ContentScale.Crop,
                        onError = {
                            Timber.d("Image Loading error ${it.result}")
                        }
                    )
                    Spacer(modifier = Modifier.width(LocalDimens.current.dimen8))
                }
                Box(modifier = Modifier) {
                    if (text.text.isEmpty()) {
                        // Show placeholder
                        Row(
                            verticalAlignment = properties.inputTextAlignment,
                            modifier = Modifier
                                .fillMaxSize()
                                .then(properties.inputTextContainerModifier),
                            horizontalArrangement = if (properties.suffixText != null) Arrangement.End else Arrangement.Start
                        ) {
                            Text(
                                text = properties.placeholder,
                                style = LocalTypography.current.text14.light.colorTxtInactive()
                                    .merge(properties.placeholderTextStyle),
                            )
                            if (properties.suffixText != null) {
                                PaddingStart(value = LocalDimens.current.dimen4)
                                Text(
                                    text = properties.suffixText,
                                    style = TextStyle(color = Color.Transparent)
                                )
                            }
                        }
                    }
                    Row(
                        modifier = Modifier
                            .fillMaxHeight()
                            .then(properties.inputTextContainerModifier),
                        verticalAlignment = properties.inputTextAlignment
                    ) {
                        if (properties.showCheckMark && text.text.isNotEmpty()) {
                            Image(
                                painter = painterResource(id = R.drawable.ic_match),
                                contentDescription = "Match Icon "
                            )
                            Spacer(modifier = Modifier.width(LocalDimens.current.dimen8))
                        }
                        BasicTextField(
                            keyboardOptions = properties.keyboardOptions,
                            singleLine = properties.singleLine,
                            enabled = properties.editable,
                            value = text,
                            visualTransformation = virtualTransformation,
                            onValueChange = { newText ->
                                if (debouncePeriod > -1) {
                                    debounceJob?.cancel()
                                    debounceJob = coroutineScope.launch {
                                        delay(debouncePeriod)
                                        onTextChange(newText.text)
                                    }
                                } else {
                                    onTextChange(newText.text)
                                }
                                text = newText
                            },
                            modifier = Modifier
                                .weight(1f),
                            textStyle = LocalTypography.current.text14.regular.colorTxtTitle()
                                .merge(properties.inputTextStyle)
                                .merge(
                                    if (properties.isError) TextStyle(
                                        color = LocalAppColor.current.txtNegative
                                    ) else TextStyle(
                                        LocalAppColor.current.txtTitle
                                    )
                                ),
                        )

                        if (properties.isPasswordMode) {
                            PasswordToggleButton(
                                tint = if (properties.isError) LocalAppColor.current.txtNegative else null,
                                onTogglePasswordChanged = {
                                    virtualTransformation =
                                        if (it) PasswordVisualTransformation() else VisualTransformation.None
                                })
                        }

                        if (properties.inputBoxType == InputBoxType.PICKER || (properties.pickerTypeIcon != null && text.text.isEmpty())) {
                            if (properties.showRightPickerIcon) {
                                Image(
                                    painter = properties.pickerTypeIcon
                                        ?: painterResource(id = R.drawable.ic_dropdown_arrow),
                                    contentDescription = "Clear Icon Resource",
                                    modifier = Modifier.noRippleClickable {
                                        properties.onPickerIconClick()
                                    }
                                )
                            }
                        }
                        ClearTextButton(
                            visible = text.text.isNotEmpty() && properties.showClearButton && !properties.isPasswordMode,
                            tint = if (properties.isError) LocalAppColor.current.txtNegative else null,
                            onClearText = {
                                text = TextFieldValue("")
                                onTextChange("")
                            })

                        if (properties.suffixText != null) {
                            Text(
                                modifier = Modifier.padding(start = LocalDimens.current.dimen4),
                                text = properties.suffixText,
                                style = LocalTypography.current.text12.semiBold.colorTxtInactive()
                                    .copy(
                                        color = if (properties.isError) LocalAppColor.current.txtNegative else LocalAppColor.current.txtInactive
                                    )
                            )
                        }

                    }
                }

            }
        }
        if (properties.inlineError != null && properties.isError) {
            PaddingTop(value = LocalDimens.current.dimen4)
            Text(
                text = properties.inlineError,
                style = LocalTypography.current.text14.light.colorTxtNegative()
                    .merge(properties.inlineErrorTextStyle)
            )
        }
    }
}

@Composable
fun PasswordToggleButton(
    tint: Color? = null,
    onTogglePasswordChanged: (visible: Boolean) -> Unit = {},
) {
    var visible by remember { mutableStateOf(true) }
    Image(
        painter = painterResource(id = R.drawable.ic_eye_open),
        contentDescription = "Clear Icon Resource",
        colorFilter = tint?.let { androidx.compose.ui.graphics.ColorFilter.tint(it) },
        modifier = Modifier.noRippleClickable {
            visible = !visible
            onTogglePasswordChanged(visible)
        }
    )
}

@Composable
fun ClearTextButton(visible: Boolean, tint: Color? = null, onClearText: () -> Unit = {}) {
    AnimatedVisibility(
        visible = visible,
        enter = fadeIn(initialAlpha = 0.4f),
        exit = fadeOut(animationSpec = tween(durationMillis = 250)),
    ) {
        Image(
            colorFilter = tint?.let { androidx.compose.ui.graphics.ColorFilter.tint(it) },
            painter = painterResource(id = R.drawable.ic_clear_circle_fill),
            contentDescription = "Clear Icon Resource",
            modifier = Modifier.noRippleClickable { onClearText() }
        )
    }
}


enum class InputBoxType {
    PICKER,
    INPUT,
}

data class InputProperties(
    val title: String = "",
    val defaultValue: String = "",
    val placeholder: String = "",
    val placeholderTextStyle: TextStyle? = null,
    val onValueChange: (String) -> Unit = {},
    val editable: Boolean = true,
    val inputBoxType: InputBoxType = InputBoxType.INPUT,
    val inputModifier: Modifier = Modifier,
    val keyboardOptions: KeyboardOptions = KeyboardOptions.Default,
    val singleLine: Boolean = true,
    val titleLabelTextStyle: TextStyle? = null,
    val keyboardActions: KeyboardActions = KeyboardActions.Default,
    val isPasswordMode: Boolean = false,
    val showClearButton: Boolean = true,
    val isError: Boolean = false,
    val showRequiredStar: Boolean = false,
    val infoTitleIcon: Painter? = null,
    val onInfoIconClick: () -> Unit = {},
    val inputPrefixIcon: Painter? = null,
    val pickerTypeIcon: Painter? = null,
    val showCheckMark: Boolean = false,
    val onPickerIconClick: () -> Unit = {},
    val showRightPickerIcon: Boolean = true,
    val inputTextStyle: TextStyle? = null,
    val suffixText: String? = null,
    val inputTextAlignment: Alignment.Vertical = Alignment.CenterVertically,
    val inputTextContainerModifier: Modifier = Modifier,
    val bottomRightCornerPainter: Painter? = null,
    val subTitle: String? = null,
    val subTitleTextStyle: TextStyle = TextStyle(),
    val prefixImageLogo: String? = null,
    val debouncePeriod: Long = -1L,
    val inlineError: String? = null,
    val inlineErrorTextStyle: TextStyle = TextStyle(),
)

@Preview(showSystemUi = true, showBackground = true)
@Composable
fun PreviewInputView() {
    InputBox(
        inputBoxModifier = Modifier.height(LocalDimens.current.dimen88),
        properties = InputProperties(
            inputPrefixIcon = painterResource(id = R.drawable.ic_search),
            title = "Email",
            placeholder = "0.0",
            inputTextAlignment = Alignment.Top,
            inputTextContainerModifier = Modifier.padding(top = LocalDimens.current.dimen16),
            placeholderTextStyle = TextStyle(textAlign = TextAlign.End, color = Color.Green),
            showRequiredStar = true,
            showClearButton = false,
            inputTextStyle = TextStyle(textAlign = TextAlign.End),
            infoTitleIcon = painterResource(id = R.drawable.ic_info),
            suffixText = "USD",
            subTitle = "This is sub title",
            inlineError = "This is Inline Error",
            inlineErrorTextStyle = LocalTypography.current.text10.light.colorTxtNegative(),
            subTitleTextStyle = LocalTypography.current.text10.light.colorTxtInactive()
        )
    )
}