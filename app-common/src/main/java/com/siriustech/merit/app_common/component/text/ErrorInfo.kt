package com.siriustech.merit.app_common.component.text

import androidx.compose.animation.AnimatedVisibility
import androidx.compose.animation.core.tween
import androidx.compose.animation.fadeIn
import androidx.compose.animation.fadeOut
import androidx.compose.foundation.Image
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.painter.Painter
import androidx.compose.ui.res.painterResource
import com.siriustech.merit.app_common.theme.LocalDimens
import com.siriustech.merit.app_common.theme.LocalTypography
import com.siriustech.merit.app_common.ext.colorTxtNegative
import com.siriustech.merit.app_common.R

/**
 * Created by <PERSON><PERSON>tet
 */

@Composable
fun ErrorInfo(painter: Painter? = null, errorMessage: String = "") {
    AnimatedVisibility(
        visible = errorMessage.isNotEmpty(),
        enter = fadeIn(initialAlpha = 0.4f),
        exit = fadeOut(animationSpec = tween(durationMillis = 250)),
    ) {
        Row(modifier = Modifier.fillMaxWidth()) {
            Image(
                painter = painter ?: painterResource(id = R.drawable.ic_error_info),
                contentDescription = "Error info Icon Resource"
            )
            Text(
                text = errorMessage,
                modifier = Modifier.padding(start = LocalDimens.current.dimen8),
                style = LocalTypography.current.text12.medium.colorTxtNegative()
            )
        }
    }
}