package com.siriustech.merit.app_common.theme

import com.siriustech.core_ui_compose.model.ErrorDisplay
import com.siriustech.merit.app_common.component.alert.BannerAlertProperties
import com.siriustech.merit.app_common.typeenum.LanguageType

/**
 * Created by <PERSON><PERSON>
 */

sealed class AppEvent{
    data class BannerAlertEvent(val properties: BannerAlertProperties) : AppEvent()
    data class ChangeLanguageEvent(val languageType: LanguageType) : AppEvent()
    data class ErrorEvent(val errorDisplay: ErrorDisplay) : AppEvent()
}