package com.siriustech.merit.app_common.component.container

import androidx.compose.foundation.Image
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.tooling.preview.Preview
import com.siriustech.merit.app_common.R
import com.siriustech.merit.app_common.ext.colorTxtParagraph
import com.siriustech.merit.app_common.theme.LocalDimens
import com.siriustech.merit.app_common.theme.LocalTypography

/**
 * Created by He<PERSON>tet
 */

@Composable
fun LabelStarRating(
    modifier: Modifier = Modifier,
    properties: LabelStarRatingProperties,
    valueTextStyle: TextStyle? = null,
) {
    Row(
        modifier = modifier.fillMaxWidth(),
        horizontalArrangement = Arrangement.SpaceBetween
    ) {
        Text(
            text = properties.label,
            style = LocalTypography.current.text14.light.colorTxtParagraph()
        )
        Row(
            verticalAlignment = Alignment.CenterVertically,
            horizontalArrangement = Arrangement.End
        ) {
            Text(
                text = properties.ratingDisplayValue,
                style = LocalTypography.current.text14.regular.colorTxtParagraph()
                    .merge(valueTextStyle)
            )
            PaddingStart(value = LocalDimens.current.dimen4)
            for (i in properties.totalRating - 1 downTo 0) {
                Image(
                    painter = painterResource(
                        id = if (i < (properties.totalRating - properties.rating))
                            R.drawable.ic_star_default else R.drawable.ic_star_selected
                    ),
                    contentDescription = "Star Image"
                )
            }
        }
    }
}

data class LabelStarRatingProperties(
    val label: String = "",
    val rating: Int = 0,
    val totalRating: Int = 5,
    val ratingDisplayValue: String = "",
)

@Preview(showBackground = true, showSystemUi = true)
@Composable
fun PreviewLabelStarRating() {
    LabelStarRating(
        properties = LabelStarRatingProperties(
            label = "Performance Rating",
            ratingDisplayValue = "4/5",
            rating = 2,
            totalRating = 5
        )
    )
}