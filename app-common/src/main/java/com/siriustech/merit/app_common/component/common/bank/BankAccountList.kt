package com.siriustech.merit.app_common.component.common.bank

import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.itemsIndexed
import androidx.compose.runtime.Composable
import androidx.compose.runtime.mutableStateListOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.snapshots.SnapshotStateList
import androidx.compose.ui.Modifier
import androidx.compose.ui.tooling.preview.Preview
import com.siriustech.merit.app_common.component.separator.SeparatorLine
import com.siriustech.merit.app_common.theme.LocalDimens

/**
 * Created by <PERSON><PERSON><PERSON>
 */

@Composable
fun BankAccountList(
    modifier: Modifier = Modifier,
    onEdit: (BankAccountItemDisplayModel) -> Unit = {},
    onDelete: (BankAccountItemDisplayModel) -> Unit = {},
    onPrimaryChanged: (BankAccountItemDisplayModel) -> Unit = {},
    bankAccounts: SnapshotStateList<BankAccountItemDisplayModel>,
) {
    val showPrimaryToggle = bankAccounts.size > 1
    LazyColumn(modifier = modifier) {
        itemsIndexed(items = bankAccounts) { index, item ->
            BankAccountItem(
                showEdit = true,
                accountNo = "Bank ${index + 1}",
                showPrimaryToggle = showPrimaryToggle,
                showDelete = showPrimaryToggle,
                bankAccountItemDisplayModel = item,
                onEdit = onEdit,
                onDelete = onDelete,
                onPrimaryChanged = onPrimaryChanged
            )
            SeparatorLine(modifier = Modifier.padding(LocalDimens.current.dimen12))
        }
    }
}


@Preview(showBackground = true, showSystemUi = true)
@Composable
fun PreviewBankAccountList() {
    val items = listOf<BankAccountItemDisplayModel>(
        BankAccountItemDisplayModel(isPrimaryAccount = true),
        BankAccountItemDisplayModel(),
        BankAccountItemDisplayModel(),
        BankAccountItemDisplayModel(),
    )
    val account = remember {
        mutableStateListOf(*items.toTypedArray())
    }
    BankAccountList(
        modifier = Modifier.padding(LocalDimens.current.dimen12),
        bankAccounts = account
    )
}