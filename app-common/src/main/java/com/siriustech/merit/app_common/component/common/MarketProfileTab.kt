package com.siriustech.merit.app_common.component.common

import android.content.Context
import androidx.appcompat.content.res.AppCompatResources
import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.RowScope
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.mutableStateListOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.snapshots.SnapshotStateList
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.ColorFilter
import androidx.compose.ui.graphics.asImageBitmap
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.core.graphics.drawable.toBitmap
import com.siriustech.merit.app_common.component.container.PaddingEnd
import com.siriustech.merit.app_common.component.container.PaddingStart
import com.siriustech.merit.app_common.ext.colorTxtInactive
import com.siriustech.merit.app_common.ext.colorTxtTitle
import com.siriustech.merit.app_common.ext.noRippleClickable
import com.siriustech.merit.app_common.theme.LocalAppColor
import com.siriustech.merit.app_common.theme.LocalDimens
import com.siriustech.merit.app_common.theme.LocalTypography
import com.siriustech.merit.app_common.typeenum.MarketAssetType
import com.siriustech.merit.app_common.typeenum.UIConfig
import com.siriustech.merit.app_common.R as AppCommonR

/**
 * Created by Hein Htet
 */

@Composable
fun MarketProfileTabs(
    modifier: Modifier = Modifier,
    defaultTabs: List<MarketProfileTabModel>,
    currentSelectedTab: MarketProfileTabModel?,
    onTabSelected: (MarketProfileTabModel) -> Unit,
) {
    val context = LocalContext.current
    val tabs = remember { mutableStateListOf(*defaultTabs.toTypedArray()) }

    LaunchedEffect(Unit) {
        tabs.findLast { it.isSelected }?.let(onTabSelected)
    }

    Row(modifier = Modifier.then(modifier)) {
        tabs.forEach {
            MarketProfileTabContent(item = it, onTabSelected = { selectTab ->
                val index = tabs.indexOf(selectTab)
                if (tabs[index].isSelected) return@MarketProfileTabContent
                tabs.forEachIndexed { i, marketProfileTabModel ->
                    tabs[i] = marketProfileTabModel.copy(isSelected = index == i)
                }
                onTabSelected(it)
            })
        }
    }
}


@Composable
fun RowScope.MarketProfileTabContent(
    item: MarketProfileTabModel,
    onTabSelected: (MarketProfileTabModel) -> Unit,
) {
    val context = LocalContext.current
    Box(
        modifier = Modifier
            .weight(1f)
            .height(LocalDimens.current.dimen46)
            .clip(RoundedCornerShape(UIConfig.TOGGLE_BUTTON_CORNER_RADIUS.dp))
            .background(if (item.isSelected) LocalAppColor.current.btn4th else LocalAppColor.current.bgDefault)
            .noRippleClickable { onTabSelected(item) },
        contentAlignment = Alignment.Center
    ) {
        Row(verticalAlignment = Alignment.CenterVertically) {
            if (item.icon != null) {
                Image(
                    bitmap = AppCompatResources.getDrawable(context, item.icon)!!.toBitmap()
                        .asImageBitmap(),
                    contentDescription = "Image",
                    colorFilter = if (item.isSelected) null else ColorFilter.tint(LocalAppColor.current.txtInactive)
                )
                PaddingEnd(value = LocalDimens.current.dimen4)
            }
            Text(
                text = item.title,
                style = if (item.isSelected) LocalTypography.current.text14.medium.colorTxtTitle() else LocalTypography.current.text14.light.colorTxtInactive()
            )
            if (item.rightSuffixImage != null) {
                PaddingStart(value = LocalDimens.current.dimen4)
                Image(
                    bitmap = AppCompatResources.getDrawable(context, item.rightSuffixImage!!)!!
                        .toBitmap()
                        .asImageBitmap(),
                    contentDescription = "Image",
                    colorFilter = if (item.isSelected) null else ColorFilter.tint(LocalAppColor.current.txtInactive)
                )
            }
        }
    }
}


fun onGetMarketTabs(type: String, context: Context): ArrayList<MarketProfileTabModel> {
    val categoryType = MarketAssetType.fromParam(type)
    return when (categoryType) {
        MarketAssetType.MUTUAL_FUNDS, MarketAssetType.PRIVATE_EQUITY_FUNDS, MarketAssetType.BONDS -> arrayListOf(
            MarketProfileTabModel(
                id = MarketProfileTabType.MARKET_DATA.value,
                title = context.getString(AppCommonR.string.key0481),
                isSelected = true
            ),
            MarketProfileTabModel(
                id = MarketProfileTabType.PROFILE.value,
                title = context.getString(AppCommonR.string.key0482)
            )
        )

        else -> arrayListOf(
            MarketProfileTabModel(
                id = MarketProfileTabType.CHART.value,
                title = context.getString(AppCommonR.string.key0480),
            ),
            MarketProfileTabModel(
                id = MarketProfileTabType.MARKET_DATA.value,
                title = context.getString(AppCommonR.string.key0481),
                isSelected = true
            ),
            MarketProfileTabModel(
                id = MarketProfileTabType.PROFILE.value,
                title = context.getString(AppCommonR.string.key0482)
            )
        )
    }
}

fun onGetWealthPlanMenuTabs(context: Context): ArrayList<MarketProfileTabModel> {
    return arrayListOf(
        MarketProfileTabModel(
            id = WealthPlanDetailsTabType.OVERVIEW.value,
            title = context.getString(AppCommonR.string.key0606),
            isSelected = true
        ),
        MarketProfileTabModel(
            id = WealthPlanDetailsTabType.PERFORMANCE.value,
            title = context.getString(AppCommonR.string.key0607),
        ),
    )
}

fun onGetNotificationMenuTabs(context: Context): SnapshotStateList<MarketProfileTabModel> {
    val mutableList = mutableStateListOf<MarketProfileTabModel>()
    mutableList.add(
        MarketProfileTabModel(
            id = NotificationTabType.NOTIFICATIONS.value,
            title = context.getString(AppCommonR.string.key0265),
            isSelected = true,
        )
    )
    mutableList.add(
        MarketProfileTabModel(
            id = NotificationTabType.MESSAGE_CENTER.value,
            title = context.getString(AppCommonR.string.key0643),
        ),

        )
    return mutableList
}

enum class MarketProfileTabType(val value: String) {
    CHART("MARKET_CHART"),
    MARKET_DATA("MARKET_DATA"),
    PROFILE("MARKET_PROFILE"),
}

enum class WealthPlanDetailsTabType(val value: String) {
    OVERVIEW("OVERVIEW"),
    PERFORMANCE("PERFORMANCE")
}


enum class NotificationTabType(val value: String) {
    NOTIFICATIONS("NOTIFICATIONS"),
    MESSAGE_CENTER("MESSAGE")
}

data class MarketProfileTabModel(
    val icon: Int? = null,
    val title: String = "",
    val id: String = "",
    var isSelected: Boolean = false,
    var rightSuffixImage: Int? = null,
)

@Preview(showBackground = true, showSystemUi = true)
@Composable
fun PreviewMarketTabs() {
    MarketProfileTabs(
        defaultTabs = listOf(),
        currentSelectedTab = null,
//        type = MarketAssetType.US_EQUITY.value
    ) {

    }
}