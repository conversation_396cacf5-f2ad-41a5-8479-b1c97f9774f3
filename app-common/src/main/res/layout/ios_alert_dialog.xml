<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="@drawable/ios_dialog_background"
    android:orientation="vertical">

    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/alertTitle"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:gravity="center"
        android:paddingLeft="20dp"
        android:paddingTop="20dp"
        android:paddingRight="20dp"
        android:paddingBottom="10dp"
        android:textAppearance="@style/Text16.TxtTitle.Medium"
        android:textSize="16sp"
        tools:text="Popup message Title" />

    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/alertMessage"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:gravity="center"
        android:paddingHorizontal="16dp"
        android:paddingBottom="20dp"
        android:textAppearance="@style/Text14.TxtTitle.Regular"
        android:textColor="#000000"
        android:textSize="14sp"
        tools:text="Popup message DescriptionPopup message DescriptionPopup message DescriptionPopup message Description" />

    <View
        android:layout_width="match_parent"
        android:layout_height="0.5dp"
        android:background="#E0E0E0" />

    <LinearLayout
        android:id="@+id/buttonPanel"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="4dp"
        android:layout_marginBottom="8dp"
        android:gravity="center"
        android:orientation="horizontal">

        <!-- For a single button, this will be centered -->
        <!-- For two buttons, they will be side by side -->

        <TextView
            android:id="@+id/negativeButton"
            android:layout_width="0dp"
            android:layout_height="44dp"
            android:layout_weight="1"
            android:background="@drawable/ios_button_background"
            android:gravity="center"
            android:textAllCaps="false"
            android:textAppearance="@style/Text14.TextTitle"
            android:textColor="#007AFF"
            android:textSize="14sp"
            tools:text="Cancel" />

        <View
            android:id="@+id/buttonDivider"
            android:layout_width="0.5dp"
            android:layout_height="44dp"
            android:background="#E0E0E0"
            android:visibility="gone"
            tools:visibility="visible" />

        <TextView
            android:id="@+id/positiveButton"
            android:layout_width="0dp"
            android:layout_height="44dp"
            android:layout_gravity="center"
            android:layout_weight="1"
            android:background="@drawable/ios_button_background"
            android:gravity="center"
            android:textAllCaps="false"
            android:textAppearance="@style/Text14.TextTitle"
            android:textColor="#007AFF"
            android:textSize="14sp"
            android:textStyle="bold"
            tools:text="OK" />
    </LinearLayout>
</LinearLayout>