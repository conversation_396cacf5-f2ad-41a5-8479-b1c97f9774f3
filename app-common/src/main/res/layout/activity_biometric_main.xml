<?xml version="1.0" encoding="utf-8"?>
<LinearLayout
    android:orientation="vertical"
    xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <EditText
        android:id="@+id/edInput"
        android:layout_width="match_parent"
        android:hint="enter plain text"
        android:layout_height="wrap_content"/>

    <Button
        android:layout_width="wrap_content"
        android:id="@+id/loginButton"
        android:layout_height="wrap_content"/>

    <TextView
        android:layout_width="wrap_content"
        android:id="@+id/tvEncrypted"
        android:layout_height="wrap_content"/>

    <TextView
        android:layout_width="wrap_content"
        android:id="@+id/tvDecrypted"
        android:layout_height="wrap_content"/>

</LinearLayout>