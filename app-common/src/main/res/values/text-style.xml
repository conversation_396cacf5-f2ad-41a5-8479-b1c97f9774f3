<?xml version="1.0" encoding="utf-8"?>
<resources>

    <style name="AppFontFamily" parent="TextAppearance.AppCompat">
        <item name="android:fontFamily">@font/noto</item>
    </style>

    <style name="Text" parent="AppFontFamily">

    </style>

    <style name="Text12.TextGeneral">
        <item name="android:textColor">@color/textGeneral</item>
    </style>

    <style name="Text12.TextGeneral.SemiBold">
        <item name="android:fontFamily">@font/noto_sans_semi_bold</item>
    </style>

    <style name="Text12.TextInverted">
        <item name="android:textColor">@color/textInverted</item>
    </style>

    <style name="Text12.TextInverted.SemiBold">
        <item name="android:fontFamily">@font/noto_sans_bold</item>
    </style>

    <style name="Text12.TextInverted.Regular">
        <item name="android:fontFamily">@font/noto_sans_regular</item>
    </style>

    <style name="Text12.TxtInverted">
        <item name="android:textColor">@color/txtInverted</item>
    </style>

    <style name="Text12.TxtInverted.SemiBold">
        <item name="android:fontFamily">@font/noto_sans_semi_bold</item>
    </style>

    <style name="Text12.TxtInverted.Regular">
        <item name="android:fontFamily">@font/noto_sans_regular</item>
    </style>

    <style name="Text12.TxtInverted.Bold">
        <item name="android:fontFamily">@font/noto_sans_bold</item>
    </style>

    <style name="Text12.TxtInverted.Medium">
        <item name="android:fontFamily">@font/noto_sans_medium</item>
    </style>


    <style name="Text12.TxtInActive">
        <item name="android:textColor">@color/txtInactive</item>
    </style>


    <style name="Text12.TxtInActive.Medium">
        <item name="android:fontFamily">@font/noto_sans_medium</item>
    </style>

    <style name="Text12.TxtInActive.SemiBold">
        <item name="android:fontFamily">@font/noto_sans_semi_bold</item>
    </style>

    <style name="Text12.TxtInActive.Regular">
        <item name="android:fontFamily">@font/noto_sans_regular</item>
    </style>

    <style name="Text12.TxtInActive.Light">
        <item name="android:fontFamily">@font/noto_sans_light</item>
    </style>

    <style name="Text12.TxtInActive.Bold">
        <item name="android:fontFamily">@font/noto_sans_bold</item>
    </style>

    <style name="Text14.TxtDisable">
        <item name="android:textColor">@color/txtDisabled</item>
    </style>


    <style name="Text12.TxtInfo">
        <item name="android:textColor">@color/txtInfo</item>
    </style>


    <style name="Text12.TxtInfo.Medium">
        <item name="android:fontFamily">@font/noto_sans_medium</item>
    </style>

    <style name="Text12.TxtInfo.SemiBold">
        <item name="android:fontFamily">@font/noto_sans_semi_bold</item>
    </style>

    <style name="Text12.TxtInfo.Regular">
        <item name="android:fontFamily">@font/noto_sans_semi_bold</item>
    </style>

    <style name="Text12.TxtInfo.Bold">
        <item name="android:fontFamily">@font/noto_sans_bold</item>
    </style>


    <style name="Text14.TextLabelDisabled">
        <item name="android:textColor">@color/textLabelDisabled</item>
    </style>

    <style name="Text14.TextLabelDisabled.SemiBold">
        <item name="android:fontFamily">@font/noto_sans_semi_bold</item>
    </style>

    <style name="Text14" parent="Text">
        <item name="android:textSize">@dimen/default_text_14</item>
    </style>

    <style name="Text14.Medium" parent="Text">
        <item name="android:fontFamily">@font/noto_sans_medium</item>
    </style>


    <style name="Text14.TxtTitle">
        <item name="android:textColor">@color/txtTitle</item>
    </style>

    <style name="Text14.TxtTitle.Regular">
        <item name="android:fontFamily">@font/noto_sans_regular</item>
    </style>

    <style name="Text14.TxtTitle.Medium">
        <item name="android:fontFamily">@font/noto_sans_medium</item>
    </style>

    <style name="Text14.TxtTitle.Light">
        <item name="android:fontFamily">@font/noto_sans_light</item>
    </style>

    <style name="Text14.TxtTitle.SemiBold">
        <item name="android:fontFamily">@font/noto_sans_semi_bold</item>
    </style>

    <style name="Text14.TxtTitle.Bold">
        <item name="android:fontFamily">@font/noto_sans_bold</item>
    </style>

    <style name="Text14.TxtDisabled">
        <item name="android:textColor">@color/txtDisabled</item>
    </style>

    <style name="Text14.TxtDisabled.Medium">
        <item name="android:fontFamily">@font/noto_sans_medium</item>
    </style>

    <style name="Text14.TxtNeutral">
        <item name="android:textColor">@color/txtNeutral</item>
    </style>


    <style name="Text14.TxtNeutral.Medium">
        <item name="android:fontFamily">@font/noto_sans_medium</item>
    </style>


    <style name="Text14.TxtNeutral.SemiBold">
        <item name="android:fontFamily">@font/noto_sans_semi_bold</item>
    </style>


    <style name="Text14.TxtNeutral.Bold">
        <item name="android:fontFamily">@font/noto_sans_bold</item>
    </style>

    <style name="Text14.TxtInverted">
        <item name="android:textColor">@color/txtInverted</item>
    </style>

    <style name="Text14.TxtInverted.Light">
        <item name="android:fontFamily">@font/noto_sans_light</item>
    </style>

    <style name="Text14.TxtInverted.Medium">
        <item name="android:fontFamily">@font/noto_sans_medium</item>
    </style>

    <style name="Text14.TxtInverted.Regular">
        <item name="android:fontFamily">@font/noto_sans_regular</item>
    </style>


    <style name="Text14.TxtInverted.SemiBold">
        <item name="android:fontFamily">@font/noto_sans_semi_bold</item>
    </style>

    <style name="Text14.TxtInverted.Bold">
        <item name="android:fontFamily">@font/noto_sans_bold</item>
    </style>


    <style name="Text14.TxtCaution">
        <item name="android:textColor">@color/txtCaution</item>
    </style>

    <style name="Text14.TxtCaution.Light">
        <item name="android:fontFamily">@font/noto_sans_light</item>
    </style>

    <style name="Text14.TxtCaution.Medium">
        <item name="android:fontFamily">@font/noto_sans_medium</item>
    </style>

    <style name="Text14.TxtCaution.Regular">
        <item name="android:fontFamily">@font/noto_sans_regular</item>
    </style>


    <style name="Text14.TxtCaution.SemiBold">
        <item name="android:fontFamily">@font/noto_sans_semi_bold</item>
    </style>

    <style name="Text14.TxtCaution.Bold">
        <item name="android:fontFamily">@font/noto_sans_bold</item>
    </style>

    <style name="Text14.TxtInActive">
        <item name="android:textColor">@color/txtInactive</item>
    </style>

    <style name="Text14.TxtInActive.Light">
        <item name="android:fontFamily">@font/noto_sans_light</item>
    </style>

    <style name="Text14.TxtInActive.Medium">
        <item name="android:fontFamily">@font/noto_sans_medium</item>
    </style>

    <style name="Text14.TxtInActive.Regular">
        <item name="android:fontFamily">@font/noto_sans_regular</item>
    </style>


    <style name="Text14.TxtInActive.SemiBold">
        <item name="android:fontFamily">@font/noto_sans_semi_bold</item>
    </style>

    <style name="Text14.TxtInActive.Bold">
        <item name="android:fontFamily">@font/noto_sans_bold</item>
    </style>


    <style name="Text14.TxtInfo">
        <item name="android:textColor">@color/txtInfo</item>
    </style>

    <style name="Text14.TxtInfo.Light">
        <item name="android:fontFamily">@font/noto_sans_light</item>
    </style>

    <style name="Text14.TxtInfo.Medium">
        <item name="android:fontFamily">@font/noto_sans_medium</item>
    </style>

    <style name="Text14.TxtInfo.Regular">
        <item name="android:fontFamily">@font/noto_sans_regular</item>
    </style>


    <style name="Text14.TxtInfo.SemiBold">
        <item name="android:fontFamily">@font/noto_sans_semi_bold</item>
    </style>

    <style name="Text14.TxtInfo.Bold">
        <item name="android:fontFamily">@font/noto_sans_bold</item>
    </style>


    <!--    Text20-->
    <style name="Text20" parent="Text">
        <item name="android:textSize">@dimen/default_text_20</item>
    </style>

    <style name="Text20.FillPositive" parent="Text20">
        <item name="android:textColor">@color/fillPositive</item>
    </style>

    <style name="Text20.FillPositive.Bold" parent="Text20.FillPositive">
        <item name="android:textStyle">bold</item>
    </style>

    <style name="Text20.PitchBlack">
        <item name="android:textColor">@color/pitchBlack</item>
    </style>

    <style name="Text20.PitchBlack.Bold" parent="Text20.PitchBlack">
        <item name="android:textStyle">bold</item>
    </style>

    <style name="Text20.TextTitle">
        <item name="android:textColor">@color/textTitle</item>
    </style>

    <style name="Text20.TextTitle.Bold">
        <item name="android:textStyle">bold</item>
    </style>


    <style name="Text20.TxtTitle">
        <item name="android:textColor">@color/txtTitle</item>
    </style>

    <style name="Text20.TxtTitle.Bold">
        <item name="android:textStyle">bold</item>
    </style>

    <style name="Text22" parent="Text">
        <item name="android:textSize">@dimen/default_text_22</item>
    </style>

    <style name="Text22.TxtTitle">
        <item name="android:textColor">@color/txtTitle</item>
    </style>

    <style name="Text22.TxtTitle.Bold">
        <item name="android:textStyle">bold</item>
    </style>

    <style name="Text22.TxtDisabled">
        <item name="android:textColor">@color/txtDisabled</item>
    </style>

    <style name="Text22.TxtDisabled.Bold">
        <item name="android:textStyle">bold</item>
    </style>

    <!--    Text24-->
    <style name="Text24" parent="Text">
        <item name="android:textSize">@dimen/default_text_24</item>
    </style>

    <style name="Text24.TxtTitle" parent="Text24">
        <item name="android:textColor">@color/txtTitle</item>
    </style>

    <style name="Text24.TxtTitle.Bold">
        <item name="android:fontFamily">@font/noto_sans_bold</item>
    </style>

    <style name="Text24.TxtTitle.SemiBold">
        <item name="android:fontFamily">@font/noto_sans_semi_bold</item>
    </style>


    <style name="Text24.TxtInverted" parent="Text24">
        <item name="android:textColor">@color/txtInverted</item>
    </style>

    <style name="Text24.TxtInverted.Bold">
        <item name="android:fontFamily">@font/noto_sans_bold</item>
    </style>

    <style name="Text24.TxtInverted.SemiBold">
        <item name="android:fontFamily">@font/noto_sans_semi_bold</item>
    </style>

    <style name="Text24.TxtInverted.Light">
        <item name="android:fontFamily">@font/noto_sans_light</item>
    </style>


    <!--    Text16-->
    <style name="Text16" parent="Text">
        <item name="android:textSize">@dimen/default_text_16</item>
    </style>

    <style name="Text16.FillPositive">
        <item name="android:textColor">@color/fillPositive</item>
    </style>

    <style name="Text16.TextFillActive">
        <item name="android:textColor">@color/fillActive</item>
    </style>

    <style name="Text16.TextFillActive.Bold">
        <item name="android:fontFamily">@font/noto_sans_bold</item>
    </style>

    <style name="Text16.FillPositive.Bold">
        <item name="android:textStyle">bold</item>
    </style>

    <style name="Text16.TxtPositive">
        <item name="android:textColor">@color/txtPositive</item>
    </style>

    <style name="Text16.TxtPositive.Bold">
        <item name="android:textStyle">bold</item>
    </style>

    <style name="Text16.TxtPositive.Regular">
        <item name="android:fontFamily">@font/noto_sans_regular</item>
    </style>

    <style name="Text16.TxtPositive.Medium">
        <item name="android:fontFamily">@font/noto_sans_medium</item>
    </style>

    <style name="Text16.TxtPositive.SemiBold">
        <item name="android:fontFamily">@font/noto_sans_semi_bold</item>
    </style>



    <style name="Text16.TxtLabel">
        <item name="android:textColor">@color/txtLabel</item>
    </style>

    <style name="Text16.TxtLabel.ExtraBold">
        <item name="android:fontFamily">@font/noto_sans_bold</item>
    </style>

    <style name="Text16.TxtLabel.Bold">
        <item name="android:fontFamily">@font/noto_sans_bold</item>
    </style>


    <style name="Text16.TxtLabel.SemiBold">
        <item name="android:fontFamily">@font/noto_sans_semi_bold</item>
    </style>

    <style name="Text16.TextInverted">
        <item name="android:textColor">@color/textInverted</item>
    </style>

    <style name="Text16.TxtInverted">
        <item name="android:textColor">@color/txtInverted</item>
    </style>


    <style name="Text16.TxtInverted.Medium">
        <item name="android:fontFamily">@font/noto_sans_medium</item>
    </style>

    <style name="Text16.TxtInverted.SemiBold">
        <item name="android:fontFamily">@font/noto_sans_semi_bold</item>
    </style>

    <style name="Text16.TxtInverted.Bold">
        <item name="android:fontFamily">@font/noto_sans_bold</item>
    </style>


    <style name="Text16.TxtTitle">
        <item name="android:textColor">@color/txtTitle</item>
    </style>

    <style name="Text16.TxtTitle.Bold">
        <item name="android:fontFamily">@font/noto_sans_bold</item>
    </style>

    <style name="Text16.TxtTitle.SemiBold">
        <item name="android:fontFamily">@font/noto_sans_semi_bold</item>
    </style>


    <style name="Text16.TxtTitle.Regular">
        <item name="android:fontFamily">@font/noto_sans_regular</item>
    </style>


    <style name="Text16.TxtTitle.Medium">
        <item name="android:fontFamily">@font/noto_sans_medium</item>
    </style>


    <style name="Text16.TextInverted.Bold">
        <item name="android:fontFamily">@font/noto_sans_bold</item>
    </style>

    <style name="Text16.TextInverted.SemiBold">
        <item name="android:fontFamily">@font/noto_sans_semi_bold</item>
    </style>

    <style name="Text16.TextInverted.Medium">
        <item name="android:fontFamily">@font/noto_sans_medium</item>
    </style>

    <style name="Text16.TxtParagraph" parent="Text16">
        <item name="android:textColor">@color/txtParagraph</item>
    </style>

    <style name="Text16.TxtParagraph.Medium">
        <item name="android:fontFamily">@font/noto_sans_medium</item>
    </style>

    <style name="Text16.TxtNegative" parent="Text16">
        <item name="android:textColor">@color/txtNegative</item>
    </style>

    <style name="Text16.TxtNegative.Medium">
        <item name="android:fontFamily">@font/noto_sans_medium</item>
    </style>

    <style name="Text16.PitchBlack">
        <item name="color">@color/pitchBlack</item>
    </style>

    <style name="Text16.PitchBlack.SemiBold">
        <item name="android:fontFamily">@font/noto_sans_semi_bold</item>
    </style>

    <style name="Text16.PitchBlack.Bold">
        <item name="android:fontFamily">@font/noto_sans_bold</item>
    </style>

    <style name="Text16.White" parent="Text16">
        <item name="android:textColor">@color/white</item>
    </style>

    <style name="Text16.White.SemiBold">
        <item name="android:fontFamily">@font/noto_sans_semi_bold</item>
    </style>


    <style name="Text16.TxtCaution">
        <item name="android:textColor">@color/txtCaution</item>
    </style>

    <style name="Text16.TxtCaution.Medium">
        <item name="android:fontFamily">@font/noto_sans_medium</item>
    </style>

    <style name="Text16.TxtCaution.Bold">
        <item name="android:fontFamily">@font/noto_sans_bold</item>
    </style>

    <style name="Text16.TxtCaution.SemiBoldBold">
        <item name="android:fontFamily">@font/noto_sans_semi_bold</item>
    </style>


    <style name="Text16.TxtCaution.Light">
        <item name="android:fontFamily">@font/noto_sans_light</item>
    </style>


    <!--    Text14-->

    <style name="Text14.TextGeneral" parent="Text14">
        <item name="android:textColor">@color/textGeneral</item>
    </style>

    <style name="Text14.TextGeneral.Regular">
        <item name="android:fontFamily">@font/noto_sans_regular</item>
    </style>

    <style name="Text14.TextGeneral.Medium">
        <item name="android:fontFamily">@font/noto_sans_medium</item>
    </style>

    <style name="Text14.txtPositive" parent="Text14">
        <item name="android:textColor">@color/fillPositive</item>
    </style>


    <style name="Text14.txtPositive.SemiBold">SemiBold
        <item name="android:fontFamily">@font/noto_sans_semi_bold</item>
    </style>

    <style name="Text14.txtPositive.Bold">
        <item name="android:fontFamily">@font/noto_sans_bold</item>
    </style>

    <style name="Text14.White" parent="Text14">
        <item name="android:textColor">@color/white</item>
    </style>

    <style name="Text14.White.Bold">
        <item name="android:fontFamily">@font/noto_sans_bold</item>
    </style>

    <style name="Text14.White.SemiBold">
        <item name="android:fontFamily">@font/noto_sans_semi_bold</item>
    </style>


    <style name="Text14.TextInverted" parent="Text14">
        <item name="android:textColor">@color/textInverted</item>
    </style>

    <style name="Text14.TextInverted.SemiBold">
        <item name="android:fontFamily">@font/noto_sans_semi_bold</item>
    </style>

    <style name="Text14.TextInverted.Bold">
        <item name="android:textStyle">bold</item>
    </style>

    <style name="Text14.TxtNegative" parent="Text14">
        <item name="android:textColor">@color/txtNegative</item>
    </style>

    <style name="Text14.TxtNegative.Bold">
        <item name="android:fontFamily">@font/noto_sans_bold</item>
    </style>

    <style name="Text14.TxtNegative.Light">
        <item name="android:fontFamily">@font/noto_sans_light</item>
    </style>

    <style name="Text14.TxtNegative.Medium">
        <item name="android:fontFamily">@font/noto_sans_medium</item>
    </style>

    <style name="Text14.TxtNegative.SemiBold">
        <item name="android:fontFamily">@font/noto_sans_semi_bold</item>
    </style>

    <style name="Text14.TxtLabel" parent="Text14">
        <item name="android:textColor">@color/txtLabel</item>
    </style>

    <style name="Text14.TxtLabel.Medium">
        <item name="android:fontFamily">@font/noto_sans_medium</item>
    </style>

    <style name="Text14.TxtLabel.SemiBold">
        <item name="android:fontFamily">@font/noto_sans_semi_bold</item>
    </style>

    <style name="Text14.TxtLabel.Bold">
        <item name="android:fontFamily">@font/noto_sans_bold</item>
    </style>

    <style name="Text14.TxtLabelParent" parent="Text14">
        <item name="android:textColor">@color/txtLabelParent</item>
    </style>

    <style name="Text14.TxtLabelParent.Bold" parent="Text14.TxtLabel">
        <item name="android:textStyle">bold</item>
    </style>

    <style name="Text14.TxtParagraph">
        <item name="android:textColor">@color/txtParagraph</item>
    </style>

    <style name="Text14.TxtParagraph.SemiBold">
        <item name="fontFamily">@font/noto_sans_semi_bold</item>
    </style>

    <style name="Text14.TxtParagraph.Bold">
        <item name="android:fontFamily">@font/noto_sans_bold</item>
    </style>

    <style name="Text14.TxtParagraph.Regular">
        <item name="android:fontFamily">@font/noto_sans_regular</item>
    </style>

    <style name="Text14.TxtParagraph.Light">
        <item name="android:fontFamily">@font/noto_sans_light</item>
    </style>


    <style name="Text14.TxtParagraph.Medium">
        <item name="android:fontFamily">@font/noto_sans_medium</item>
    </style>

    <style name="Text14.Black">
        <item name="android:textColor">@color/black</item>
    </style>

    <style name="Text14.Black.SemiBold">
        <item name="fontFamily">@font/noto_sans_semi_bold</item>
    </style>

    <style name="Text14.Black.Bold">
        <item name="fontFamily">@font/noto_sans_bold</item>
    </style>


    <style name="Text14.BtnMeritPrimary">
        <item name="android:textColor">@color/btnPrimaryMerit</item>
    </style>

    <style name="Text14.BtnMeritPrimary.SemiBold">
        <item name="fontFamily">@font/noto_sans_semi_bold</item>
    </style>

    <style name="Text14.BtnMeritPrimary.Bold">
        <item name="fontFamily">@font/noto_sans_bold</item>
    </style>

    <style name="Text14.BtnMeritPrimary.Regular">
        <item name="fontFamily">@font/noto_sans_regular</item>
    </style>

    <style name="Text14.BtnMeritPrimary.Medium">
        <item name="fontFamily">@font/noto_sans_medium</item>
    </style>


    <style name="Text14.TextLabel">
        <item name="android:textColor">@color/txtLabel</item>
    </style>

    <style name="Text14.TextLabel.Regular">
        <item name="fontFamily">@font/noto_sans_regular</item>
    </style>

    <style name="Text14.TextLabel.SemiBold">
        <item name="android:fontFamily">@font/noto_sans_semi_bold</item>
    </style>


    <style name="Text14.TextLabel.Bold">
        <item name="fontFamily">@font/noto_sans_bold</item>
    </style>


    <style name="Text14.TextTitle">
        <item name="android:textColor">@color/textTitle</item>
    </style>

    <style name="Text14.TextTitle.SemiBold">
        <item name="fontFamily">@font/noto_sans_semi_bold</item>
    </style>


    <style name="Text14.TextTitle.Bold">
        <item name="fontFamily">@font/noto_sans_bold</item>
    </style>

    <style name="Text14.TextTitle.Light">
        <item name="fontFamily">@font/noto_sans_light</item>
    </style>

    <style name="Text14.PitchBlack">
        <item name="color">@color/pitchBlack</item>
    </style>

    <style name="Text14.PitchBlack.SemiBold">
        <item name="fontFamily">@font/noto_sans_semi_bold</item>
    </style>


    <!--    Text8-->
    <style name="Text8" parent="Text">
        <item name="android:textSize">@dimen/default_text_8</item>
    </style>

    <style name="Text8.TextPositive" parent="Text8">
        <item name="android:textColor">@color/textPositive</item>
    </style>

    <style name="Text8.TextPositive.SemiBold" parent="Text8.TextPositive">
        <item name="fontFamily">@font/noto_sans_semi_bold</item>
    </style>

    <style name="Text8.TextPositive.Bold" parent="Text8.TextPositive">
        <item name="android:textStyle">bold</item>
    </style>


    <style name="Text8.White" parent="Text8">
        <item name="android:textColor">@color/white</item>
    </style>

    <style name="Text8.White.Bold" parent="Text8.White">
        <item name="fontFamily">@font/noto_sans_semi_bold</item>
    </style>


    <style name="Text8.TextLabel" parent="Text8">
        <item name="android:textColor">@color/txtLabel</item>
    </style>

    <style name="Text8.TextLabel.Bold" parent="Text8.TextLabel">
        <item name="android:fontFamily">@font/noto_sans_bold</item>
    </style>

    <style name="Text8.TextLabel.SemiBold" parent="Text8.TextLabel">
        <item name="android:fontFamily">@font/noto_sans_semi_bold</item>
    </style>

    <style name="Text8.TxtParagraph" parent="Text8">
        <item name="android:textColor">@color/txtParagraph</item>
    </style>

    <style name="Text8.TxtParagraph.Bold">
        <item name="android:fontFamily">@font/noto_sans_bold</item>
    </style>

    <style name="Text8.TxtParagraph.Regular">
        <item name="android:fontFamily">@font/noto_sans_regular</item>
    </style>

    <style name="Text8.TxtParagraph.SemiBold">
        <item name="android:fontFamily">@font/noto_sans_semi_bold</item>
    </style>

    <style name="Text8.TxtTitle" parent="Text8">
        <item name="android:textColor">@color/txtTitle</item>
    </style>

    <style name="Text8.TxtTitle.SemiBold">
        <item name="android:fontFamily">@font/noto_sans_semi_bold</item>
    </style>

    <style name="Text8.TxtTitle.Bold">
        <item name="android:fontFamily">@font/noto_sans_bold</item>
    </style>


    <!--    Text8-->
    <style name="Text9" parent="Text">
        <item name="android:textSize">9sp</item>
    </style>

    <style name="Text9.TxtParagraph">
        <item name="android:textColor">@color/textPositive</item>
    </style>

    <style name="Text9.TxtParagraph.Regular">
        <item name="fontFamily">@font/noto_sans_regular</item>
    </style>

    <style name="Text9.TxtParagraph.SemiBold">
        <item name="fontFamily">@font/noto_sans_semi_bold</item>
    </style>

    <style name="Text9.TxtParagraph.Bold">
        <item name="android:textStyle">bold</item>
    </style>


    <!--     Text12-->
    <style name="Text12" parent="Text">
        <item name="android:textSize">@dimen/default_text_12</item>
    </style>

    <style name="Text12.Medium">
        <item name="fontFamily">@font/noto_sans_medium</item>
    </style>

    <style name="Text12.TextPositive" parent="Text12">
        <item name="android:textColor">@color/textPositive</item>
    </style>

    <style name="Text12.TextPositive.SemiBold" parent="Text12.TextPositive">
        <item name="fontFamily">@font/noto_sans_semi_bold</item>
    </style>

    <style name="Text12.TextPositive.Bold" parent="Text12.TextPositive">
        <item name="android:textStyle">bold</item>
    </style>

    <style name="Text12.FillPositive" parent="Text12">
        <item name="android:textColor">@color/fillPositive</item>
    </style>

    <style name="Text12.FillPositive.Bold">
        <item name="android:fontFamily">@font/noto_sans_bold</item>
    </style>

    <style name="Text12.FillPositive.SemiBold">
        <item name="android:fontFamily">@font/noto_sans_semi_bold</item>
    </style>

    <style name="Text12.FillPositivePale">
        <item name="android:textColor">@color/fillPositivePale</item>
    </style>

    <style name="Text12.TxtPositive" parent="Text12">
        <item name="android:textColor">@color/txtPositive</item>
    </style>

    <style name="Text12.TxtPositive.SemiBold">
        <item name="android:fontFamily">@font/noto_sans_semi_bold</item>
    </style>

    <style name="Text12.TxtPositive.Regular">
        <item name="android:fontFamily">@font/noto_sans_regular</item>
    </style>

    <style name="Text12.TxtPositive.Bold">
        <item name="android:fontFamily">@font/noto_sans_bold</item>
    </style>


    <style name="Text12.TxtNegative" parent="Text12">
        <item name="android:textColor">@color/txtNegative</item>
    </style>

    <style name="Text12.TxtNegative.Medium">
        <item name="android:fontFamily">@font/noto_sans_medium</item>
    </style>

    <style name="Text12.TxtNegative.Light">
        <item name="android:fontFamily">@font/noto_sans_light</item>
    </style>


    <style name="Text12.TxtNegative.SemiBold">
        <item name="android:fontFamily">@font/noto_sans_semi_bold</item>
    </style>

    <style name="Text12.TxtNegative.Bold">
        <item name="android:fontFamily">@font/noto_sans_bold</item>
    </style>

    <style name="Text12.TxtNegative.Regular">
        <item name="android:fontFamily">@font/noto_sans_regular</item>
    </style>

    <style name="Text12.PitchBlack">
        <item name="android:textColor">@color/pitchBlack</item>
    </style>

    <style name="Text12.TxtLabel">
        <item name="android:textColor">@color/textLabel</item>
    </style>

    <style name="Text12.TxtLabel.Medium">
        <item name="android:fontFamily">@font/noto_sans_medium</item>
    </style>

    <style name="Text12.TxtLabel.Regular">
        <item name="android:fontFamily">@font/noto_sans_regular</item>
    </style>


    <style name="Text12.TxtLabel.SemiBold">
        <item name="android:fontFamily">@font/noto_sans_semi_bold</item>
    </style>


    <style name="Text12.TxtLabel.Bold">
        <item name="android:fontFamily">@font/noto_sans_bold</item>
    </style>


    <style name="Text12.FillNegative">
        <item name="android:textColor">@color/fillNegative</item>
    </style>

    <style name="Text12.FillNegative.SemiBold">
        <item name="android:fontFamily">@font/noto_sans_semi_bold</item>
    </style>

    <style name="Text12.FillNegative.Bold">
        <item name="android:fontFamily">@font/noto_sans_bold</item>
    </style>

    <style name="Text12.txtNeutral">
        <item name="android:textColor">@color/txtNeutral</item>
    </style>

    <style name="Text12.txtNeutral.SemiBold">
        <item name="android:fontFamily">@font/noto_sans_semi_bold</item>
    </style>

    <style name="Text12.txtNeutral.Regular">
        <item name="android:fontFamily">@font/noto_sans_regular</item>
    </style>

    <style name="Text12.txtNeutral.Bold">
        <item name="android:fontFamily">@font/noto_sans_bold</item>
    </style>


    <style name="Text12.FillPositivePale.Bold">
        <item name="android:fontFamily">@font/noto_sans_bold</item>
    </style>

    <style name="Text12.FillPositivePale.SemiBold">
        <item name="android:fontFamily">@font/noto_sans_semi_bold</item>
    </style>


    <style name="Text12.TextTitle">
        <item name="android:textColor">@color/textTitle</item>
    </style>

    <style name="Text12.TextTitle.SemiBold">
        <item name="android:fontFamily">@font/noto_sans_semi_bold</item>
    </style>

    <style name="Text12.TextTitle.Medium">
        <item name="android:fontFamily">@font/noto_sans_medium</item>
    </style>

    <style name="Text12.TextTitle.Bold">
        <item name="android:fontFamily">@font/noto_sans_bold</item>
    </style>


    <style name="Text12.SemiBold">
        <item name="android:fontFamily">@font/noto_sans_semi_bold</item>
    </style>

    <style name="Text12.TxtParagraph">
        <item name="android:textColor">@color/txtParagraph</item>
    </style>

    <style name="Text12.TxtParagraph.Regular">
        <item name="android:fontFamily">@font/noto_sans_regular</item>
    </style>

    <style name="Text12.TxtParagraph.Light">
        <item name="android:fontFamily">@font/noto_sans_light</item>
    </style>

    <style name="Text12.TxtParagraph.Medium">
        <item name="android:fontFamily">@font/noto_sans_medium</item>
    </style>

    <style name="Text12.TxtParagraph.SemiBold">
        <item name="android:fontFamily">@font/noto_sans_semi_bold</item>
    </style>

    <style name="Text12.TxtParagraph.Bold">
        <item name="android:fontFamily">@font/noto_sans_bold</item>
    </style>

    <style name="Text12.TxtTitle">
        <item name="android:textColor">@color/txtTitle</item>
    </style>

    <style name="Text12.TxtTitle.SemiBold">
        <item name="android:fontFamily">@font/noto_sans_semi_bold</item>
    </style>

    <style name="Text12.TxtTitle.Regular">
        <item name="android:fontFamily">@font/noto_sans_regular</item>
    </style>

    <style name="Text12.TxtTitle.Light">
        <item name="android:fontFamily">@font/noto_sans_light</item>
    </style>

    <style name="Text12.TxtTitle.Medium">
        <item name="android:fontFamily">@font/noto_sans_medium</item>
    </style>

    <style name="Text12.TxtTitle.Bold">
        <item name="android:fontFamily">@font/noto_sans_bold</item>
    </style>


    <style name="Text12.TxtLabelNeutral">
        <item name="android:textColor">@color/txtLabelNeutral</item>
    </style>

    <style name="Text12.TxtLabelNeutral.SemiBold">
        <item name="android:fontFamily">@font/noto_sans_semi_bold</item>
    </style>

    <style name="Text12.TxtLabelNeutral.Bold">
        <item name="android:fontFamily">@font/noto_sans_bold</item>
    </style>


    <style name="Text12.TxtDisabled">
        <item name="android:textColor">@color/txtDisabled</item>
    </style>

    <style name="Text12.TxtDisabled.SemiBold">
        <item name="android:fontFamily">@font/noto_sans_semi_bold</item>
    </style>

    <style name="Text12.TxtDisabled.Bold">
        <item name="android:fontFamily">@font/noto_sans_bold</item>
    </style>

    <style name="Text12.TxtDisabled.Regular">
        <item name="android:fontFamily">@font/noto_sans_regular</item>
    </style>

    <style name="Text12.TextLabel">
        <item name="android:textColor">@color/txtLabel</item>
    </style>

    <style name="Text12.TextLabel.Regular">
        <item name="android:fontFamily">@font/noto_sans_regular</item>
    </style>

    <style name="Text12.TextLabel.Medium">
        <item name="android:fontFamily">@font/noto_sans_medium</item>
    </style>

    <style name="Text12.TextLabel.SemiBold">
        <item name="android:fontFamily">@font/noto_sans_semi_bold</item>
    </style>

    <style name="Text12.TextLabel.Bold">
        <item name="android:fontFamily">@font/noto_sans_bold</item>
    </style>

    <style name="Text12.TxtNeutral">
        <item name="android:textColor">@color/txtNeutral</item>
    </style>

    <style name="Text12.TxtNeutral.Medium">
        <item name="android:fontFamily">@font/noto_sans_medium</item>
    </style>

    <style name="Text12.TxtCaution">
        <item name="android:textColor">@color/bgButtonCaution</item>
    </style>

    <style name="Text12.TxtCaution.SemiBold">
        <item name="android:fontFamily">@font/noto_sans_semi_bold</item>
    </style>

    <style name="Text12.MarieBlue">
        <item name="android:textColor">@color/marineBlue</item>
    </style>

    <style name="Text12.MarieBlue.Regular">
        <item name="android:fontFamily">@font/noto_sans_regular</item>
    </style>

    <style name="Text12.MarieBlue.SemiBold">
        <item name="android:fontFamily">@font/noto_sans_semi_bold</item>
    </style>

    <style name="Text12.MarieBlue.Bold">
        <item name="android:fontFamily">@font/noto_sans_bold</item>
    </style>

    <style name="Text12.MarieBlue.Medium">
        <item name="android:fontFamily">@font/noto_sans_medium</item>
    </style>


    <style name="Text12.MossGreen">
        <item name="android:textColor">@color/mossGreen</item>
    </style>

    <style name="Text12.MossGreen.Regular">
        <item name="android:fontFamily">@font/noto_sans_regular</item>
    </style>

    <style name="Text12.MossGreen.SemiBold">
        <item name="android:fontFamily">@font/noto_sans_semi_bold</item>
    </style>

    <style name="Text12.MossGreen.Bold">
        <item name="android:fontFamily">@font/noto_sans_bold</item>
    </style>

    <style name="Text12.MossGreen.Medium">
        <item name="android:fontFamily">@font/noto_sans_medium</item>
    </style>


    <style name="Text12.LotusPurple">
        <item name="android:textColor">@color/lotusPurple</item>
    </style>

    <style name="Text12.LotusPurple.Regular">
        <item name="android:fontFamily">@font/noto_sans_regular</item>
    </style>

    <style name="Text12.LotusPurple.SemiBold">
        <item name="android:fontFamily">@font/noto_sans_semi_bold</item>
    </style>

    <style name="Text12.LotusPurple.Bold">
        <item name="android:fontFamily">@font/noto_sans_bold</item>
    </style>

    <style name="Text12.LotusPurple.Medium">
        <item name="android:fontFamily">@font/noto_sans_medium</item>
    </style>

    <style name="Text12.TxtLabelKgi">
        <item name="android:textColor">@color/txtLabelKgi</item>
    </style>

    <style name="Text12.TxtLabelKgi.SemiBold">
        <item name="android:fontFamily">@font/noto_sans_semi_bold</item>
    </style>

    <style name="Text12.TxtLabelBlue">
        <item name="android:textColor">@color/txtLabelBlue</item>
    </style>

    <style name="Text12.TxtLabelBlue.SemiBold">
        <item name="android:fontFamily">@font/noto_sans_semi_bold</item>
    </style>

    <!--Text10-->
    <style name="Text10" parent="Text">
        <item name="android:textSize">@dimen/default_text_10</item>
    </style>

    <style name="Text10.TxtPositive" parent="Text10">
        <item name="android:textColor">@color/txtPositive</item>
    </style>

    <style name="Text10.TxtPositive.SemiBold">
        <item name="fontFamily">@font/noto_sans_semi_bold</item>
    </style>

    <style name="Text10.TxtNegative" parent="Text10">
        <item name="android:textColor">@color/txtNegative</item>
    </style>

    <style name="Text10.TxtNegative.Medium">
        <item name="android:fontFamily">@font/noto_sans_medium</item>
    </style>

    <style name="Text10.TxtNegative.SemiBold">
        <item name="android:fontFamily">@font/noto_sans_semi_bold</item>
    </style>

    <style name="Text10.TextPositive" parent="Text10">
        <item name="android:textColor">@color/textPositive</item>
    </style>

    <style name="Text10.TextPositive.SemiBold">
        <item name="fontFamily">@font/noto_sans_semi_bold</item>
    </style>

    <style name="Text10.TextPositive.Bold">
        <item name="android:textStyle">bold</item>
    </style>

    <style name="Text10.TxtLabelPositive" parent="Text10">
        <item name="android:textColor">@color/txtLabelPositive</item>
    </style>

    <style name="Text10.TxtLabelPositive.Regular">
        <item name="android:fontFamily">@font/noto_sans_regular</item>
    </style>

    <style name="Text10.TxtLabelPositive.SemiBold">
        <item name="android:fontFamily">@font/noto_sans_semi_bold</item>
    </style>

    <style name="Text10.TxtLabelPositive.Bold">
        <item name="android:fontFamily">@font/noto_sans_bold</item>
    </style>

    <style name="Text10.TextLabel">
        <item name="android:textColor">@color/txtLabel</item>
    </style>

    <style name="Text10.TextLabel.Medium">
        <item name="android:fontFamily">@font/noto_sans_medium</item>
    </style>

    <style name="Text10.TextLabel.Bold">
        <item name="android:fontFamily">@font/noto_sans_bold</item>
    </style>

    <style name="Text10.TextLabel.Regular">
        <item name="android:fontFamily">@font/noto_sans_regular</item>
    </style>


    <style name="Text10.TextLabel.SemiBold">
        <item name="android:fontFamily">@font/noto_sans_semi_bold</item>
    </style>

    <style name="Text10.TextGeneral">
        <item name="android:textColor">@color/textGeneral</item>
    </style>

    <style name="Text10.TextGeneral.SemiBold">
        <item name="android:fontFamily">@font/noto_sans_semi_bold</item>
    </style>

    <style name="Text10.TextGeneral.Bold">
        <item name="android:fontFamily">@font/noto_sans_bold</item>
    </style>

    <style name="Text10.TextInverted">
        <item name="android:textColor">@color/textInverted</item>
    </style>

    <style name="Text10.TextInverted.SemiBold">
        <item name="android:fontFamily">@font/noto_sans_semi_bold</item>
    </style>

    <style name="Text10.TextInverted.Bold">
        <item name="android:fontFamily">@font/noto_sans_bold</item>
    </style>

    <style name="Text10.FillButton">
        <item name="android:textColor">@color/fillButton</item>
    </style>

    <style name="Text10.FillButton.SemiBold">
        <item name="android:fontFamily">@font/noto_sans_semi_bold</item>
    </style>

    <style name="Text10.FillButton.Bold">
        <item name="android:fontFamily">@font/noto_sans_bold</item>
    </style>

    <style name="Text10.txtLabelNeutral">
        <item name="android:textColor">@color/txtLabelNeutral</item>
    </style>

    <style name="Text10.txtLabelNeutral.SemiBold">
        <item name="android:fontFamily">@font/noto_sans_semi_bold</item>
    </style>

    <style name="Text10.txtLabelNeutral.Bold">
        <item name="android:fontFamily">@font/noto_sans_bold</item>
    </style>

    <style name="Text10.TxtDisabled">
        <item name="android:textColor">@color/txtDisabled</item>
    </style>

    <style name="Text10.TxtDisabled.Medium">
        <item name="android:fontFamily">@font/noto_sans_medium</item>
    </style>

    <style name="Text10.TxtDisabled.SemiBold">
        <item name="android:fontFamily">@font/noto_sans_semi_bold</item>
    </style>

    <style name="Text10.TxtParagraph">
        <item name="android:textColor">@color/txtParagraph</item>
    </style>

    <style name="Text10.TxtParagraph.Regular">
        <item name="android:fontFamily">@font/noto_sans_regular</item>
    </style>

    <style name="Text10.TxtParagraph.SemiBold">
        <item name="android:fontFamily">@font/noto_sans_semi_bold</item>
    </style>

    <style name="Text10.TxtParagraph.Bold">
        <item name="android:fontFamily">@font/noto_sans_bold</item>
    </style>

    <style name="Text10.TxtParagraph.Medium">
        <item name="android:fontFamily">@font/noto_sans_medium</item>
    </style>

    <style name="Text10.TxtNeutral">
        <item name="android:textColor">@color/txtNeutral</item>
    </style>

    <style name="Text10.TxtNeutral.Regular">
        <item name="android:fontFamily">@font/noto_sans_regular</item>
    </style>

    <style name="Text10.TxtNeutral.SemiBold">
        <item name="android:fontFamily">@font/noto_sans_semi_bold</item>
    </style>

    <style name="Text10.TxtNeutral.Bold">
        <item name="android:fontFamily">@font/noto_sans_bold</item>
    </style>

    <style name="Text10.TxtNeutral.Medium">
        <item name="android:fontFamily">@font/noto_sans_medium</item>
    </style>


    <style name="Text10.TxtDisabled.Bold">
        <item name="android:fontFamily">@font/noto_sans_bold</item>
    </style>

    <style name="Text10.TxtLabel">
        <item name="android:textColor">@color/txtLabel</item>
    </style>

    <style name="Text10.TxtLabel.Regular">
        <item name="android:fontFamily">@font/noto_sans_regular</item>
    </style>

    <style name="Text10.TxtLabel.Medium">
        <item name="android:fontFamily">@font/noto_sans_medium</item>
    </style>

    <style name="Text10.TxtLabel.SemiBold">
        <item name="android:fontFamily">@font/noto_sans_semi_bold</item>
    </style>

    <style name="Text10.TxtLabel.Bold">
        <item name="android:fontFamily">@font/noto_sans_bold</item>
    </style>


    <style name="Text10.TxtLabelPurple">
        <item name="android:textColor">@color/txtLabelPurple</item>
    </style>

    <style name="Text10.TxtLabelPurple.Regular">
        <item name="android:fontFamily">@font/noto_sans_regular</item>
    </style>

    <style name="Text10.TxtLabelPurple.Medium">
        <item name="android:fontFamily">@font/noto_sans_medium</item>
    </style>

    <style name="Text10.TxtLabelPurple.SemiBold">
        <item name="android:fontFamily">@font/noto_sans_semi_bold</item>
    </style>

    <style name="Text10.TxtLabelPurple.Bold">
        <item name="android:fontFamily">@font/noto_sans_bold</item>
    </style>

    <style name="Text10.TxtTitle">
        <item name="android:textColor">@color/txtTitle</item>
    </style>

    <style name="Text10.TxtTitle.Regular">
        <item name="android:fontFamily">@font/noto_sans_regular</item>
    </style>

    <style name="Text10.TxtTitle.Medium">
        <item name="android:fontFamily">@font/noto_sans_medium</item>
    </style>

    <style name="Text10.TxtTitle.SemiBold">
        <item name="android:fontFamily">@font/noto_sans_semi_bold</item>
    </style>


    <style name="Text10.TxtTitle.Bold">
        <item name="android:fontFamily">@font/noto_sans_bold</item>
    </style>


    <style name="Text10.TxtLabelCaution">
        <item name="android:textColor">@color/txtLabelCaution</item>
    </style>

    <style name="Text10.TxtLabelCaution.Regular">
        <item name="android:fontFamily">@font/noto_sans_regular</item>
    </style>

    <style name="Text10.TxtLabelCaution.SemiBold">
        <item name="android:fontFamily">@font/noto_sans_semi_bold</item>
    </style>

    <style name="Text10.TxtLabelCaution.Bold">
        <item name="android:fontFamily">@font/noto_sans_bold</item>
    </style>

    <style name="Text10.TxtLabelNegative" parent="Text10">
        <item name="android:textColor">@color/txtLabelNegative</item>
    </style>

    <style name="Text10.TxtLabelNegative.Regular">
        <item name="android:fontFamily">@font/noto_sans_regular</item>
    </style>

    <style name="Text10.TxtLabelNegative.SemiBold">
        <item name="android:fontFamily">@font/noto_sans_semi_bold</item>
    </style>

    <style name="Text10.TxtLabelNegative.Bold">
        <item name="android:fontFamily">@font/noto_sans_bold</item>
    </style>

    <style name="Text10.TxtInverted" parent="Text10">
        <item name="android:textColor">@color/txtInverted</item>
    </style>

    <style name="Text10.TxtInverted.SemiBold">
        <item name="android:fontFamily">@font/noto_sans_semi_bold</item>
    </style>

    <style name="Text10.TxtInverted.Bold">
        <item name="android:fontFamily">@font/noto_sans_bold</item>
    </style>


    <style name="Text10.TxtInActive" parent="Text10">
        <item name="android:textColor">@color/txtInactive</item>
    </style>

    <style name="Text10.TxtInActive.SemiBold">
        <item name="android:fontFamily">@font/noto_sans_semi_bold</item>
    </style>

    <style name="Text10.TxtInActive.Bold">
        <item name="android:fontFamily">@font/noto_sans_bold</item>
    </style>

    <style name="Text10.TxtInActive.Medium">
        <item name="android:fontFamily">@font/noto_sans_medium</item>
    </style>

    <style name="Text10.TxtInActive.Regular">
        <item name="android:fontFamily">@font/noto_sans_regular</item>
    </style>


    <style name="Text16.TxtInActive">
        <item name="android:textColor">@color/txtInactive</item>
    </style>

    <style name="Text16.TxtInActive.Regular">
        <item name="android:fontFamily">@font/noto_sans_regular</item>
    </style>

    <style name="Text16.TxtInActive.Medium">
        <item name="android:fontFamily">@font/noto_sans_medium</item>
    </style>

    <style name="Text16.TxtInActive.SemiBold">
        <item name="android:fontFamily">@font/noto_sans_semi_bold</item>
    </style>

    <style name="Text16.TxtInActive.Bold">
        <item name="android:fontFamily">@font/noto_sans_bold</item>
    </style>


    <!--     Text18-->
    <style name="Text18" parent="Text">
        <item name="android:textSize">@dimen/default_text_18</item>
    </style>

    <style name="Text18.TxtTitle">
        <item name="android:textColor">@color/txtTitle</item>
    </style>

    <style name="Text18.TxtTitle.Bold">
        <item name="android:fontFamily">@font/noto_sans_bold</item>
    </style>

    <style name="Text18.TxtTitle.SemiBold">
        <item name="android:fontFamily">@font/noto_sans_semi_bold</item>
    </style>


    <!--     Text28-->
    <style name="Text28" parent="Text">
        <item name="android:textSize">@dimen/default_text_28</item>
    </style>

    <style name="Text28.FillPositive">
        <item name="android:textColor">@color/fillPositive</item>
    </style>

    <style name="Text28.FillPositive.Bold">
        <item name="android:fontFamily">@font/noto_sans_bold</item>
    </style>

    <style name="Text28.FillPositive.SemiBold">
        <item name="android:fontFamily">@font/noto_sans_semi_bold</item>
    </style>

    <style name="Text28.FillActive">
        <item name="android:textColor">@color/fillActive</item>
    </style>

    <style name="Text28.FillActive.Bold">
        <item name="android:fontFamily">@font/noto_sans_bold</item>
    </style>

    <style name="Text28.FillActive.SemiBold">
        <item name="android:fontFamily">@font/noto_sans_semi_bold</item>
    </style>

    <style name="Text28.BgButtonDefault">
        <item name="android:textColor">@color/bgButtonDefault</item>
    </style>

    <style name="Text28.BgButtonDefault.SemiBold">
        <item name="android:fontFamily">@font/noto_sans_semi_bold</item>
    </style>

    <style name="Text28.TxtTitle">
        <item name="android:textColor">@color/txtTitle</item>
    </style>

    <style name="Text28.TxtTitle.SemiBold">
        <item name="android:fontFamily">@font/noto_sans_semi_bold</item>
    </style>

    <style name="Text28.TxtTitle.Medium">
        <item name="android:fontFamily">@font/noto_sans_medium</item>
    </style>

    <style name="Text28.TxtTitle.Bold">
        <item name="android:fontFamily">@font/noto_sans_bold</item>
    </style>


    <style name="Text28.BgButtonDefault.Bold">
        <item name="android:fontFamily">@font/noto_sans_bold</item>
    </style>

    <style name="TextBottomNavigationView.Default" parent="@style/TextAppearance.AppCompat.Caption">
        <item name="android:textSize">@dimen/default_text_12</item>
        <item name="android:fontFamily">@font/noto_sans_regular</item>
        <item name="textAllCaps">false</item>
    </style>

    <style name="BottomNavigationView.Active" parent="@style/TextAppearance.AppCompat.Caption">
        <item name="android:textSize">@dimen/default_12</item>
        <item name="android:fontFamily">@font/noto_sans_semi_bold</item>
        <item name="textAllCaps">false</item>
    </style>

    <style name="Text28.LoginDescriptionText">
        <item name="android:textColor">#FFFFFF</item>
        <item name="android:fontFamily">@font/noto_sans_bold</item>
    </style>

    <style name="Text28.LoginXText">
        <item name="android:textColor">#82ECB6</item>
        <item name="android:fontFamily">@font/noto_sans_bold</item>
    </style>

    <style name="Text36" parent="Text">
        <item name="android:textSize">@dimen/default_text_36</item>
    </style>

    <style name="Text36.TxtTitle">
        <item name="android:textColor">@color/txtTitle</item>
    </style>

    <style name="Text36.TxtTitle.SemiBold">
        <item name="android:fontFamily">@font/noto_sans_semi_bold</item>
    </style>

    <style name="Text36.TxtTitle.Bold">
        <item name="android:fontFamily">@font/noto_sans_bold</item>
    </style>

    <style name="Text60">
        <item name="android:textSize">@dimen/default_text_60</item>
    </style>

    <style name="Text60.OrangeOrange">
        <item name="android:textColor">@color/orangeOrange</item>
    </style>

    <style name="Text60.OrangeOrange.Bold">
        <item name="android:fontFamily">@font/noto_sans_bold</item>
    </style>


    <style name="Text14.TxtParagraph.SC_Light">
        <item name="android:fontFamily">@font/noto_sans_sc_light</item>
    </style>

    <style name="Text14.TxtParagraph.SC_Medium">
        <item name="android:fontFamily">@font/noto_sans_sc_medium</item>
    </style>

</resources>