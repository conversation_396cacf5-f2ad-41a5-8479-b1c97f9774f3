plugins {
    alias(libs.plugins.android.library)
    alias(libs.plugins.jetbrains.kotlin.android)
    alias(libs.plugins.kotlinSerialization)
    id("com.google.dagger.hilt.android")
    id("kotlin-parcelize")
    id("kotlin-kapt")
}

android {
    namespace = "com.siriustech.merit.app_common"
    compileSdk = 34

    defaultConfig {
        minSdk = 24

        testInstrumentationRunner = "androidx.test.runner.AndroidJUnitRunner"
        consumerProguardFiles("consumer-rules.pro")
    }

    buildTypes {
        release {
            isMinifyEnabled = false
            proguardFiles(
                getDefaultProguardFile("proguard-android-optimize.txt"),
                "proguard-rules.pro"
            )
        }
    }
    compileOptions {
        sourceCompatibility = JavaVersion.VERSION_1_8
        targetCompatibility = JavaVersion.VERSION_1_8
    }
    kotlinOptions {
        jvmTarget = "1.8"
    }
    buildFeatures {
        compose = true
    }
    composeOptions {
        kotlinCompilerExtensionVersion = "1.5.1"
    }
    packaging {
        resources {
            excludes += "/META-INF/{AL2.0,LGPL2.1}"
        }
    }
}

dependencies {
    implementation(libs.hilt.android)
    kapt(libs.hilt.compiler)
    api(project(":core-ui-compose"))
    api(project(":core-localstorage"))
    api(project(":core-network"))
    implementation(project(":apiLayer"))
    implementation(libs.lottie.compose)
    api("androidx.constraintlayout:constraintlayout-compose:1.0.1")
    api(libs.androidx.biometric)
    api (libs.compressor)
    api(libs.androidx.camera.camera2)
    api(libs.androidx.camera.view)
    api(libs.androidx.camera.lifecycle)
    api(libs.kotlinx.coroutines.guava)
    api(libs.timber)
    api(libs.threetenabp)
    api("io.siriustech.superapp:util:1.3")
    api(libs.coil.compose)
    api("io.coil-kt.coil3:coil-network-okhttp:3.0.0-rc01")
    api("com.github.PhilJay:MPAndroidChart:v3.1.0")
    implementation("io.github.grizzi91:bouquet:1.1.2")
    implementation("dev.chrisbanes.snapper:snapper:0.3.0")
    implementation("androidx.security:security-crypto:1.1.0-alpha01")
    implementation("androidx.browser:browser:1.8.0")


}