package com.siriustech.core_ui_compose.base

import androidx.compose.animation.fadeIn
import androidx.compose.animation.fadeOut
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.material3.CircularProgressIndicator
import androidx.compose.runtime.Composable
import androidx.compose.runtime.DisposableEffect
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.runtime.rememberUpdatedState
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.lifecycle.Lifecycle
import androidx.lifecycle.LifecycleEventObserver
import androidx.lifecycle.compose.LocalLifecycleOwner
import com.siriustech.core_ui_compose.BaseViewModel

/**
 * Created by Hein Htet
 */


@Composable
fun BaseScreen(
    modifier: Modifier = Modifier,
    vm: BaseViewModel = BaseViewModel(),
    onCreate: () -> Unit = {},
    onResume: () -> Unit = {},
    onPause: () -> Unit = {},
    onStop: () -> Unit = {},
    onDestroy: () -> Unit = {},
    loadingView: @Composable () -> Unit = { DefaultLoadingView() },
    toolbar: @Composable() (() -> Unit)? = null,
    content: @Composable () -> Unit,
) {

    val currentOnResume by rememberUpdatedState(onResume)
    val currentOnPause by rememberUpdatedState(onPause)
    val currentOnCreate by rememberUpdatedState(onCreate)
    val currentOnDestroy by rememberUpdatedState(onDestroy)
    val currentOnStop by rememberUpdatedState(onStop)
    val loading = vm.outputs.loading.collectAsState()

    val lifecycleOwner = LocalLifecycleOwner.current
    DisposableEffect(key1 = lifecycleOwner) {
        val observer = LifecycleEventObserver { _, event ->
            when (event) {
                Lifecycle.Event.ON_CREATE -> {
                    currentOnCreate()
                }

                Lifecycle.Event.ON_RESUME -> {
                    currentOnResume()
                }

                Lifecycle.Event.ON_PAUSE -> {
                    currentOnPause()
                }

                Lifecycle.Event.ON_STOP -> {
                    currentOnStop()
                }

                Lifecycle.Event.ON_DESTROY -> {
                    currentOnDestroy()
                }

                else -> {}
            }
        }
        lifecycleOwner.lifecycle.addObserver(observer)
        onDispose { lifecycleOwner.lifecycle.removeObserver(observer) }
    }
    Column(
        modifier = Modifier
            .fillMaxSize()
            .then(modifier)
    ) {
        if (toolbar != null) {
            toolbar()
        }
        Box(
            modifier = Modifier
                .fillMaxSize()
        ) {
            content()
            androidx.compose.animation.AnimatedVisibility(
                modifier = Modifier.align(Alignment.Center),
                enter = fadeIn(),
                exit = fadeOut(),
                visible = loading.value
            ) {
                loadingView()
            }
        }
    }
}

@Composable
fun DefaultLoadingView() {
    CircularProgressIndicator()
}