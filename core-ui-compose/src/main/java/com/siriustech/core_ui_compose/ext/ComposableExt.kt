package com.siriustech.core_ui_compose.ext

import android.content.Context
import android.content.ContextWrapper
import androidx.activity.ComponentActivity
import androidx.activity.SystemBarStyle
import androidx.activity.enableEdgeToEdge
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.AlertDialog
import androidx.compose.material3.Button
import androidx.compose.material3.ButtonDefaults
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.MutableState
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.saveable.Saver
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.drawBehind
import androidx.compose.ui.geometry.Offset
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.platform.LocalConfiguration
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.platform.LocalSoftwareKeyboardController
import androidx.compose.ui.unit.Dp
import androidx.compose.ui.unit.dp
import androidx.compose.ui.window.DialogProperties
import androidx.fragment.app.FragmentActivity
import com.siriustech.core_ui_compose.model.CommonPopupDisplayData

/**
 * Created by Hein Htet
 */

@Composable
fun ComponentActivity.ChangeSystemBarsTheme(lightTheme: Boolean, barColor: Int) {
    LaunchedEffect(lightTheme) {
        if (lightTheme) {
            enableEdgeToEdge(
                statusBarStyle = SystemBarStyle.light(
                    barColor, barColor,
                ),
                navigationBarStyle = SystemBarStyle.light(
                    barColor, barColor,
                ),
            )
        } else {
            enableEdgeToEdge(
                statusBarStyle = SystemBarStyle.dark(
                    barColor,
                ),
                navigationBarStyle = SystemBarStyle.dark(
                    barColor,
                ),
            )
        }
    }
}


fun Context.getActivity(): ComponentActivity? = when (this) {
    is ComponentActivity -> this
    is ContextWrapper -> baseContext.getActivity()
    else -> null
}

@Composable
fun CommonDialog(
    commonPopupDisplayData: CommonPopupDisplayData,
    isDismissRequest: () -> Unit = {},
) {
    val screenWidth = LocalConfiguration.current.screenWidthDp.dp

    AlertDialog(
        properties = DialogProperties(
            usePlatformDefaultWidth = false
        ),
        modifier = Modifier.width(screenWidth - 40.dp),
        shape = RoundedCornerShape(8.dp),
        onDismissRequest = {
            isDismissRequest()
        },
        title = {
            Text(
                text = commonPopupDisplayData.title,
                style = commonPopupDisplayData.titleTextStyle
            )
        },
        text = {
            Text(commonPopupDisplayData.message, style = commonPopupDisplayData.messageTextStyle)
        },
        confirmButton = {
            Button(
                shape = RoundedCornerShape(8.dp),
                colors = commonPopupDisplayData.rightButtonColor ?: ButtonDefaults.buttonColors(),
                onClick = {
                    commonPopupDisplayData.rightButtonPressed()
                    isDismissRequest()
                }, modifier = Modifier
                    .then(commonPopupDisplayData.rightButtonModifier)
            ) {
                Text(commonPopupDisplayData.rightButtonTextMessage)
            }
        },
        dismissButton = {
            if (commonPopupDisplayData.leftButtonTextMessage != null) {
                Button(
                    shape = RoundedCornerShape(8.dp),
                    onClick = {
                        commonPopupDisplayData.leftButtonPressed()
                        isDismissRequest()
                    },
                    modifier = Modifier
                        .then(commonPopupDisplayData.leftButtonModifier)
                ) {
                    Text(commonPopupDisplayData.leftButtonTextMessage.orEmpty())
                }
            }
        }
    )
}


fun Context.closeActivity() {
    val context = this
    if (context is FragmentActivity) {
        (context).finishAffinity()
    }
}

fun <T> stateSaver() = Saver<MutableState<T>, Any>(
    save = { state -> state.value ?: "null" },
    restore = { value ->
        @Suppress("UNCHECKED_CAST")
        mutableStateOf((if (value == "null") null else value) as T)
    }
)

/**
 * Creates a Modifier that adds a bottom stroke to a Composable.
 *
 * @param color The color of the stroke.
 * @param strokeWidth The thickness of the stroke.
 * @return A Modifier that draws a bottom stroke.
 */
fun Modifier.bottomStroke(color: Color, strokeWidth: Dp = 2.dp): Modifier = this.then(
    Modifier.drawBehind {
        val strokePx = strokeWidth.toPx()
        // Draw a line at the bottom
        drawLine(
            color = color,
            start = Offset(x = 0f, y = size.height - strokePx / 2),
            end = Offset(x = size.width, y = size.height - strokePx / 2),
            strokeWidth = strokePx
        )
    }
)

fun Modifier.topStroke(color: Color, strokeWidth: Dp = 2.dp): Modifier = this.then(
//    Modifier.drawBehind {
//        val strokePx = strokeWidth.toPx()
//        // Draw a line at the bottom
//        drawLine(
//            color = color,
//            start = Offset(x = 0f, y = size.height - strokePx / 2),
//            end = Offset(x = size.width, y = size.height - strokePx / 2),
//            strokeWidth = strokePx
//        )
//    }
    Modifier.drawBehind {
        val strokePx = strokeWidth.toPx()
        drawLine(
            color = color,
            start = Offset(0f, 0f),
            end = Offset(size.width, 0f),
            strokeWidth = strokePx
        )
    }
)


@Composable
fun HideKeyboard() {
    val keyboardController = LocalSoftwareKeyboardController.current
    keyboardController?.hide()
}
