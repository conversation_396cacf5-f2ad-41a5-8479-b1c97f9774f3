package com.siriustech.core_ui_compose

import android.util.Log
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.siriustech.core_ui_compose.base.BaseEvent
import com.siriustech.core_ui_compose.model.ErrorDisplay
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.channels.Channel
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.receiveAsFlow
import kotlinx.coroutines.launch

/**
 * Created by He<PERSON> Htet
 */
open class BaseViewModel : ViewModel() {
    val scope: CoroutineScope
        get() = viewModelScope
    open val inputs: BaseInputs
        get() = BaseInputs()
    open val outputs: BaseOutputs
        get() = BaseOutputs()

    private val _loading = MutableStateFlow(false)

    private val _eventChannel = Channel<BaseEvent>(capacity = Channel.BUFFERED)
    val eventChannel: Flow<BaseEvent>
        get() = _eventChannel.receiveAsFlow()

    open inner class BaseInputs {
        fun emitLoading(value: Boolean) {
            _loading.value = value
        }

        fun emitError(errorDisplay: ErrorDisplay) {
            scope.launch {
                _eventChannel.send(BaseEvent.EventError(errorDisplay))
            }
        }

        protected fun emitEvent(event: BaseEvent) {
            scope.launch {
                Log.d("BaseViewModel", "emitEvent: $event")
                _eventChannel.send(event)
            }
        }
    }

    open inner class BaseOutputs {
        open val loading: StateFlow<Boolean>
            get() = _loading
        open val eventFlow: Flow<BaseEvent>
            get() = _eventChannel.receiveAsFlow()
    }
}