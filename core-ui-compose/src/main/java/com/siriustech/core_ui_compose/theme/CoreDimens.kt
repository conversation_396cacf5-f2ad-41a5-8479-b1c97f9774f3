package com.siriustech.core_ui_compose.theme

import androidx.compose.ui.unit.Dp
import androidx.compose.ui.unit.dp

/**
 * Created by <PERSON><PERSON> Htet
 */


interface CoreDimensI {
    val dimen1: Dp
    val dimen2: Dp
    val dimen4: Dp
    val dimen6: Dp
    val dimen8: Dp
    val dimen10: Dp
    val dimen12: Dp
    val dimen14: Dp
    val dimen16: Dp
    val dimen18: Dp
    val dimen20: Dp
    val dimen22: Dp
    val dimen24: Dp
    val dimen26: Dp
    val dimen28: Dp
    val dimen30: Dp
    val dimen32: Dp
    val dimen34: Dp
    val dimen38: Dp
    val dimen40: Dp
    val dimen42: Dp
    val dimen44: Dp
    val dimen46: Dp
    val dimen48: Dp
    val dimen50: Dp
    val dimen52: Dp
    val dimen54: Dp
    val dimen56: Dp
    val dimen58: Dp
    val dimen60: Dp
    val dimen62: Dp
    val dimen64: Dp
    val dimen66: Dp
    val dimen68: Dp
    val dimen70: Dp
    val dimen72: Dp
    val dimen74: Dp
    val dimen76: Dp
    val dimen78: Dp
    val dimen80: Dp
    val dimen82: Dp
    val dimen84: Dp
    val dimen86: Dp
    val dimen88: Dp
    val dimen90: Dp
    val dimen92: Dp
    val dimen94: Dp
    val dimen96: Dp
    val dimen98: Dp
    val dimen100: Dp
}

data class CoreDimens(
    override val dimen1: Dp = 1.dp,
    override val dimen2: Dp = 2.dp,
    override val dimen4: Dp = 4.dp,
    override val dimen6: Dp = 6.dp,
    override val dimen8: Dp = 8.dp,
    override val dimen10: Dp = 10.dp,
    override val dimen12: Dp = 12.dp,
    override val dimen14: Dp = 14.dp,
    override val dimen16: Dp = 16.dp,
    override val dimen18: Dp = 18.dp,
    override val dimen20: Dp = 20.dp,
    override val dimen22: Dp = 22.dp,
    override val dimen24: Dp = 24.dp,
    override val dimen26: Dp = 26.dp,
    override val dimen28: Dp = 28.dp,
    override val dimen30: Dp = 30.dp,
    override val dimen32: Dp = 32.dp,
    override val dimen34: Dp = 34.dp,
    override val dimen38: Dp = 38.dp,
    override val dimen40: Dp = 40.dp,
    override val dimen42: Dp = 42.dp,
    override val dimen44: Dp = 44.dp,
    override val dimen46: Dp = 46.dp,
    override val dimen48: Dp = 48.dp,
    override val dimen50: Dp = 50.dp,
    override val dimen52: Dp = 52.dp,
    override val dimen54: Dp = 54.dp,
    override val dimen56: Dp = 56.dp,
    override val dimen58: Dp = 58.dp,
    override val dimen60: Dp = 60.dp,
    override val dimen62: Dp = 62.dp,
    override val dimen64: Dp = 64.dp,
    override val dimen66: Dp = 66.dp,
    override val dimen68: Dp = 68.dp,
    override val dimen70: Dp = 70.dp,
    override val dimen72: Dp = 72.dp,
    override val dimen74: Dp = 74.dp,
    override val dimen76: Dp = 76.dp,
    override val dimen78: Dp = 78.dp,
    override val dimen80: Dp = 80.dp,
    override val dimen82: Dp = 82.dp,
    override val dimen84: Dp = 84.dp,
    override val dimen86: Dp = 86.dp,
    override val dimen88: Dp = 88.dp,
    override val dimen90: Dp = 90.dp,
    override val dimen92: Dp = 92.dp,
    override val dimen94: Dp = 94.dp,
    override val dimen96: Dp = 96.dp,
    override val dimen98: Dp = 98.dp,
    override val dimen100: Dp = 100.dp,

    ) : CoreDimensI