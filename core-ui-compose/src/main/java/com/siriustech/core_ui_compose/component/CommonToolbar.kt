package com.siriustech.core_ui_compose.component

import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.width
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.MoreVert
import androidx.compose.material3.DropdownMenu
import androidx.compose.material3.DropdownMenuItem
import androidx.compose.material3.Icon
import androidx.compose.material3.IconButton
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.painter.Painter
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.text.AnnotatedString
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.Dp
import androidx.compose.ui.unit.dp
import com.siriustech.core_ui_compose.ext.bottomStroke

@Composable
fun CommonToolbar(
    modifier: Modifier = Modifier,
    properties: ToolbarProperties = ToolbarProperties(),
    onLeftActionClicked: () -> Unit = {},
    onRightActionClicked: () -> Unit = {},
) {
    Column(
        modifier = modifier
            .fillMaxWidth()
            .height(properties.height)
            .background(properties.backgroundColor)
    ) {
        Row(
            modifier = Modifier
                .fillMaxSize()
                .background(properties.backgroundColor)
                .bottomStroke(color = Color.Gray.copy(alpha = 0.1f))
                .padding(12.dp),
            verticalAlignment = Alignment.CenterVertically,

            ) {
            if (properties.leftActionResId != null) {
                Image(
                    modifier = Modifier
                        .clickable { onLeftActionClicked() },
                    painter = painterResource(id = properties.leftActionResId),
                    contentDescription = "Back Arrow Resource"
                )
            }
            Row(
                modifier = Modifier.weight(1f),
                horizontalArrangement = Arrangement.Center,
                verticalAlignment = Alignment.CenterVertically
            ) {
                if (properties.iconTitle != null) {
                    Image(
                        modifier = properties.iconTitleModifier,
                        painter = properties.iconTitle,
                        contentDescription = "Icon Title Image Resource"
                    )
                    Spacer(modifier = Modifier.width(4.dp))
                }
                if (properties.annotatedTitleString != null) {
                    Text(
                        text = properties.annotatedTitleString,
                        style = properties.titleTextStyle,
                        modifier = Modifier,
                        textAlign = TextAlign.Center,
                        maxLines = 1,
                        overflow = TextOverflow.Ellipsis
                    )
                } else {
                    Text(
                        text = properties.title,
                        style = properties.titleTextStyle,
                        modifier = Modifier,
                        textAlign = TextAlign.Center,
                        maxLines = 1,
                        overflow = TextOverflow.Ellipsis
                    )
                }
            }
            if (properties.rightActionResId != null) {
                Image(
                    modifier = Modifier.clickable { onRightActionClicked() },
                    painter = painterResource(id = properties.rightActionResId),
                    contentDescription = "Back Arrow Resource"
                )
            }
        }
    }
}

@Composable
fun MinimalDropdownMenu() {
    var expanded by remember { mutableStateOf(false) }
    Box(
        modifier = Modifier
            .padding(16.dp)
    ) {
        IconButton(onClick = { expanded = !expanded }) {
            Icon(Icons.Default.MoreVert, contentDescription = "More options")
        }
        DropdownMenu(
            expanded = expanded,
            onDismissRequest = { expanded = false }
        ) {
            DropdownMenuItem(
                text = { Text("Option 1") },
                onClick = { /* Do something... */ }
            )
            DropdownMenuItem(
                text = { Text("Option 2") },
                onClick = { /* Do something... */ }
            )
        }
    }
}

data class ToolbarProperties(
    val leftActionResId: Int? = null,
    val rightActionResId: Int? = null,
    val title: String = "",
    val annotatedTitleString: AnnotatedString? = null,
    val height: Dp = 44.dp,
    val backgroundColor: Color = Color.White,
    val titleTextStyle: TextStyle = TextStyle(),
    val iconTitle: Painter? = null,
    val iconTitleModifier: Modifier = Modifier,
)


data class ToolbarMoreActionModal(
    val title: String,
    val action: () -> Unit = {},
    val iconResId: Int? = null,
)


@Preview(showBackground = true, showSystemUi = true)
@Composable
fun PreviewCommonToolbar() {
    CommonToolbar(
        properties = ToolbarProperties(
            title = "Hello",
            iconTitle = painterResource(id = com.google.android.material.R.drawable.ic_call_answer)
        )
    )
}

