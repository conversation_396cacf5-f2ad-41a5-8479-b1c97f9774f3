package com.siriustech.core_ui_compose.model

import androidx.compose.material3.ButtonColors
import androidx.compose.material3.ButtonDefaults
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.text.TextStyle

data class CommonPopupDisplayData(
    val title: String = "",
    val message: String = "",
    val leftButtonTextMessage: String? = null,
    val rightButtonTextMessage: String ="",
    val leftButtonPressed: () -> Unit = {},
    val rightButtonPressed:() -> Unit = {},
    val titleTextStyle : TextStyle = TextStyle(),
    val messageTextStyle : TextStyle = TextStyle(),
    val leftButtonModifier : Modifier = Modifier,
    val rightButtonModifier : Modifier = Modifier,
    val leftButtonTextStyle : TextStyle = TextStyle(),
    val rightButtonTextStyle : TextStyle = TextStyle(),
    val backgroundColor: Color= Color.White,
    val rightButtonColor: ButtonColors? = null
)