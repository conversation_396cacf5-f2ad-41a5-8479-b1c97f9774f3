package com.sirius.useronboarding.base

import com.core.coreui.base.model.DisplayError
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Job
import kotlinx.coroutines.channels.Channel
import kotlinx.coroutines.flow.receiveAsFlow
import kotlinx.coroutines.launch

/**
 * Created by <PERSON><PERSON>
 */


abstract class BaseEvent {
}


sealed class BaseUIEvent : BaseEvent() {
    data class OnShowErrorEvent(val error: DisplayError) : BaseUIEvent()
    data object OnOrderButtonClickEvent : BaseUIEvent()
    data object OnOrderProcessButtonClickEvent : BaseUIEvent()
    data object OnWalletWithdrawal : BaseUIEvent()
    data object OnWalletDeposit : BaseUIEvent()
    data object OnDoReadyDashboardMenu : BaseUIEvent()
    data object OnAutoOrder : BaseUIEvent()
    data object OnOfflineOrder : BaseUIEvent()
}

class EventState<T>(private val scope: CoroutineScope = CoroutineScope(Job())) {
    private val _eventChannel = Channel<T?>(capacity = Channel.BUFFERED)
    val state = _eventChannel.receiveAsFlow()

    fun emit(value: T) {
        scope.launch {
            _eventChannel.send(value)
        }
    }
}