object App {
    const val id = "com.sup.signup"
    const val appName = "Sign Up POC"
    const val versionCode = 1
    const val versionName = "1.0.0"

    const val compileSdk = 34
    const val minSdk = 23
    const val targetSdk = 34
}
object Modules {
    const val register = ":register"
    const val member = ":member"
    const val router = ":router"
    const val network = ":network"
    const val apiLayer = ":apilayer"
    const val localstorage = ":localstorage"
    const val util = ":util"
    const val core =":core"
    const val coreui = ":coreui"
    const val common = ":common"
    const val testutil = ":testutil"
}

object Versions {
    const val gradle = "7.2.2"
    const val kotlin = "1.8.0"

    const val appCompat = "1.4.1"
    const val materialDesign = "1.5.0"
    const val constraintLayout = "2.1.3"
    const val fragment = "1.3.0"
    const val swipeRefreshLayout = "1.1.0"
    const val compressor = "3.0.1"
    const val koin = "3.4.3"
    const val ktxCore = "1.8.0"
    const val coroutines = "1.3.1"

    const val chucker = "4.0.0"
    const val gson = "2.8.7"
    const val retrofit = "2.9.0"
    const val lifeCycleExt = "2.6.2"
    const val okhttp = "4.9.0"
    const val hilt = "2.44"

    const val okHttp = "4.9.0"
    const val coroutine = "1.3.1"

    const val googleService = "4.3.10"
    const val firebaseCrashlyticsGradle = "2.7.1"
    const val firebaseCrashlyticsKtx = "17.1.0"
    const val firebaseAnalyticsKtx = "17.3.0"
    const val firebaseBom = "29.3.0"

    /* TEST */
    const val junit = "4.13.2"
    const val extjunit = "1.1.3"
    const val mockito = "4.0.0"
    const val mockitoKotlin = "2.1.0"
    const val mockitoInline = "2.19.0"
    const val kotlinTest = "1.6.10"
    const val testCore = "1.4.0"
    const val coreTesting = "2.1.0"
    const val coroutinesTest = "1.6.2"
    const val ioMockk = "1.12.1"
    const val navigationComponent = "2.7.7"
    const val timber = "5.0.1"


    const val lottie = "5.2.0"
    const val biometric = "1.2.0-alpha05"
    const val pdfviewer = "2.0.7"


}

object Libraries {
    const val gradle = "com.android.tools.build:gradle:${Versions.gradle}"
    const val kotlinPlugin = "org.jetbrains.kotlin:kotlin-gradle-plugin:${Versions.kotlin}"
    const val kotlin = "org.jetbrains.kotlin:kotlin-stdlib:${Versions.kotlin}"
    const val ktxCore = "androidx.core:core-ktx:${Versions.ktxCore}"

    const val appcompat = "androidx.appcompat:appcompat:${Versions.appCompat}"
    const val design = "com.google.android.material:material:${Versions.materialDesign}"
    const val constraintLayout = "androidx.constraintlayout:constraintlayout:${Versions.constraintLayout}"
    const val fragment = "androidx.fragment:fragment:${Versions.fragment}"
    const val swipeRefreshLayout = "androidx.swiperefreshlayout:swiperefreshlayout:${Versions.swipeRefreshLayout}"

    const val coroutinesCore = "org.jetbrains.kotlinx:kotlinx-coroutines-core:${Versions.coroutines}"
    const val coroutinesAndroid = "org.jetbrains.kotlinx:kotlinx-coroutines-core:${Versions.coroutines}"

    const val koinCore = "io.insert-koin:koin-core:${Versions.koin}"
    const val koinAndroid = "io.insert-koin:koin-android:${Versions.koin}"

    const val gson = "com.google.code.gson:gson:${Versions.gson}"

    const val lifeCycleExt = "androidx.lifecycle:lifecycle-livedata-ktx:${Versions.lifeCycleExt}"


    const val retrofit = "com.squareup.retrofit2:retrofit:${Versions.retrofit}"
    const val retrofitGson = "com.squareup.retrofit2:converter-gson:${Versions.retrofit}"

    const val okhttp = "com.squareup.okhttp3:logging-interceptor:${Versions.okhttp}"
    const val okhttpLogger = "com.squareup.okhttp3:logging-interceptor:${Versions.okhttp}"

    const val hilt = "com.google.dagger:hilt-android:${Versions.hilt}"
    const val hiltKapt = "com.google.dagger:hilt-android-compiler:${Versions.hilt}"

    const val coroutine =
        "org.jetbrains.kotlinx:kotlinx-coroutines-core:${Versions.coroutine}"
    const val coroutineAndroid =
        "org.jetbrains.kotlinx:kotlinx-coroutines-core:${Versions.coroutine}"
    const val okhttpInterceptor =
        "com.squareup.okhttp3:logging-interceptor:${Versions.okHttp}"

    const val chucker = "com.github.chuckerteam.chucker:library:${Versions.chucker}"
    const val chuckerNoOp = "com.github.chuckerteam.chucker:library-no-op:${Versions.chucker}"

    //fire base
    const val googleService = "com.google.gms:google-services:${Versions.googleService}"
    const val firebaseBOM = "com.google.firebase:firebase-bom:${Versions.firebaseBom}"
    const val firebaseCrashlytics = "com.google.firebase:firebase-crashlytics-ktx:${Versions.firebaseCrashlyticsKtx}"
    const val firebaseAnalytics = "com.google.firebase:firebase-analytics-ktx:${Versions.firebaseAnalyticsKtx}"
    const val firebaseCrashlyticsGradle = "com.google.firebase:firebase-crashlytics-gradle:${Versions.firebaseCrashlyticsGradle}"

    const val navigationComponent =
        "androidx.navigation:navigation-fragment-ktx:${Versions.navigationComponent}"
    const val navigationComponentUi =
        "androidx.navigation:navigation-ui-ktx:${Versions.navigationComponent}"
    const val timber = "com.jakewharton.timber:timber:${Versions.timber}"

    const val lottie = "com.airbnb.android:lottie:${Versions.lottie}"

    const val biometric= "androidx.biometric:biometric-ktx:${Versions.biometric}"
    const val inject = "javax.inject:javax.inject:1"
    const val pdfviewer = "io.github.afreakyelf:Pdf-Viewer:${Versions.pdfviewer}"

    const val compressor = "id.zelory:compressor:${Versions.compressor}"

}

object TestLibraries {
    const val junit = "junit:junit:${Versions.junit}"
    const val extjunit = "androidx.test.ext:junit:${Versions.extjunit}"
    const val mockito = "org.mockito:mockito-core:${Versions.mockito}"
    const val mockitoKotlin = "org.mockito.kotlin:mockito-kotlin:${Versions.mockito}"
    const val mockitoInline = "org.mockito:mockito-inline:${Versions.mockito}"
    const val kotlinTest = "org.jetbrains.kotlin:kotlin-test-junit:${Versions.kotlinTest}"
    const val testCore = "androidx.test:core:${Versions.testCore}"
    const val coreTesting = "androidx.arch.core:core-testing:${Versions.coreTesting}"
    const val coroutinesTest = "org.jetbrains.kotlinx:kotlinx-coroutines-test:${Versions.coroutinesTest}"
    const val ioMockk = "io.mockk:mockk:${Versions.ioMockk}"
    const val robolectric = "org.robolectric:robolectric:4.8.1"
    const val mockWebServer = "com.squareup.okhttp3:mockwebserver:4.7.2"
}
