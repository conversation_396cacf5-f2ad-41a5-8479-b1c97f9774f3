  App    	Libraries    Modules    
TestLibraries    Versions    Versions 	Libraries  Versions 
TestLibraries  	appCompat Versions  	biometric Versions  chucker Versions  
compressor Versions  constraintLayout Versions  coreTesting Versions  	coroutine Versions  
coroutines Versions  coroutinesTest Versions  extjunit Versions  firebaseAnalyticsKtx Versions  firebaseBom Versions  firebaseCrashlyticsGradle Versions  firebaseCrashlyticsKtx Versions  fragment Versions  
googleService Versions  gradle Versions  gson Versions  hilt Versions  ioMockk Versions  junit Versions  koin Versions  kotlin Versions  
kotlinTest Versions  ktxCore Versions  lifeCycleExt Versions  lottie Versions  materialDesign Versions  mockito Versions  navigationComponent Versions  okHttp Versions  okhttp Versions  	pdfviewer Versions  retrofit Versions  swipeRefreshLayout Versions  testCore Versions  timber Versions  Versions 	java.lang  Int kotlin  String kotlin  Versions kotlin  Versions kotlin.annotation  Versions kotlin.collections  Versions kotlin.comparisons  Versions 	kotlin.io  Versions 
kotlin.jvm  Versions 
kotlin.ranges  Versions kotlin.sequences  Versions kotlin.text                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                        